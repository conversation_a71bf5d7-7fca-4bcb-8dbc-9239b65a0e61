#!/bin/bash
# =================================================================
# Coze-Studio WSL2 一键部署脚本 (Fedora适配版)
# =================================================================

# 启动 WSL
wsl

# 1. 网络代理配置 (不需要请注释)
PowerShell 
    ipconfig | findstr "IPv4"
export http_proxy=http://**************:7897
export https_proxy=http://**************:7897
export HTTP_PROXY=http://**************:7897
export HTTPS_PROXY=http://**************:7897

# 2. 基础环境安装 (合并命令加速)
echo "--- 快速更新系统并安装核心工具 ---"
sudo dnf check-update || true
sudo dnf -y upgrade
sudo dnf -y install make
sudo dnf -y install docker docker-compose-plugin bash curl wget dos2unix

# 3. Docker配置
echo "--- 配置Docker权限 ---"
sudo usermod -aG docker $USER
echo "--- 请重启WSL终端，然后从第4步继续执行 ---"
exit

# =================================================================
# 重启WSL后执行以下部分
# =================================================================

# 4. 获取项目源码
echo "--- 克隆项目源码 ---"
cd ~ || exit
git clone https://github.com/coze-studio/coze-studio_1.git
cd coze-studio || exit

# 5. Go环境配置 (快速安装)
echo "--- 配置Go环境 ---"
sudo dnf install -y wget
sudo rm -rf /usr/local/go
wget -q https://go.dev/dl/go1.24.4.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.24.4.linux-amd64.tar.gz
rm go1.24.4.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
go env -w GOPROXY=https://mirrors.aliyun.com/goproxy/,direct
go version

# 6. Node.js环境配置 (NVM方式)
echo "--- 配置Node.js环境 ---"
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash
source ~/.bashrc
nvm install --lts
nvm use --lts
npm config set registry https://registry.npmmirror.com
npm install -g @microsoft/rush
node --version && npm --version

# 7. Python环境配置
echo "--- 配置Python环境 ---"
sudo dnf check-update
sudo dnf -y install python3.11
sudo dnf -y install python3.11-pip python3.11-setuptools
sudo dnf -y install python3.11-libs python3.11-distutils
python3.11 --version
curl -s https://bootstrap.pypa.io/get-pip.py -o get-pip.py
sudo python3.11 get-pip.py
pip3.11 config set global.index-url https://mirrors.aliyun.com/pypi/simple/
pip3.11 config set global.trusted-host mirrors.aliyun.com
pip3.11 --version

# 8. 文件格式转换(项目在本地必要,wsl中不需要)
echo "--- 转换文件格式 ---"
find ./scripts -type f -exec dos2unix {} +
find ./docker -type f -exec dos2unix {} +

# 9. 初始化配置
echo "--- 初始化项目 ---"
cp backend/conf/model/template/model_template_ark_doubao-seed-1.6.yaml backend/conf/model/ark_doubao-seed-1.6.yaml

# 10. 构建与启动
echo "--- 构建并启动服务 ---"
make debug
cd docker || exit
cp .env.example .env
docker compose up -d

echo "--- 部署完成！服务已启动 ---"


# 注意
# podman和docker冲突情况 临时指定使用Docker
export DOCKER_HOST=unix:///var/run/docker.sock
# 核心问题是缺少 setuptools 工具
pip3.11 install --upgrade setuptools wheel
# 安装系统级编译工具（numpy 等包需要编译C扩展）
sudo dnf -y install gcc gcc-c++ python3.11-devel openblas-devel
# 设置用户名（例如你的名字或 GitHub 用户名）
git config --global user.name "sjx"
# 设置邮箱（例如你的常用邮箱或 GitHub 绑定邮箱）
git config --global user.email "<EMAIL>"
