namespace go mcp

/**
 * MCP 信息结构体，对应数据库表 mcp_info
 */
struct McpInfo {
  1: required i64 id,                      // MCP ID
  2: required i64 space_id,                // 空间 ID
  3: required i64 developer_id,            // 开发者 ID
  4: required i64 app_id,                  // 应用 ID
  5: required string icon_uri,             // 图标 URI
  6: required string server_url,           // MCP 服务地址
  7: required string mcp_name,             // MCP 名称，如 “中台 MCP”
  8: required i64 created_at,              // 创建时间（毫秒）
  9: required i64 updated_at,              // 更新时间（毫秒）
 10: required string version,              // 插件版本，如 v1.0.0
 11: optional string version_desc,         // 插件版本描述
 12: optional string manifest,             // 插件 Manifest（JSON 字符串）
 13: optional string openapi_doc,          // OpenAPI 文档根（JSON 字符串）
 14: optional string description,          // 补充说明
 15: required bool online_status           // 在线状态：true 表示在线，false 表示离线
}

/**
 * MCP 服务接口
 */
service McpService {
  McpInfo getMcpInfoById(1: i64 id),                      // 根据 MCP ID 获取信息
  list<McpInfo> listMcpInfoBySpaceId(1: i64 space_id),    // 根据 Space ID 获取 MCP 列表
  void createMcpInfo(1: McpInfo info),                    // 创建 MCP 信息
  void updateMcpInfo(1: McpInfo info),                    // 更新 MCP 信息
  void deleteMcpInfo(1: i64 id)                           // 删除 MCP 信息
}
