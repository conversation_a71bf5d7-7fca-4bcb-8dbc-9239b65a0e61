namespace go task

include "../base.thrift"

// 任务状态枚举
enum TaskStatus {
    PENDING = 1
    IN_PROGRESS = 2
    COMPLETED = 3
    CANCELLED = 4
}

// 任务优先级枚举
enum TaskPriority {
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
}

// 创建任务请求
struct CreateTaskRequest {
    1: required string name
    2: optional string description
    3: optional TaskPriority priority
    4: optional i64 due_date
    5: optional i64 space_id
    6: optional i64 project_id
}

// 创建任务响应
struct CreateTaskResponse {
    1: required i32 code
    2: optional string message
    3: optional TaskInfo task
}

// 任务信息
struct TaskInfo {
    1: required i64 id
    2: required string name
    3: optional string description
    4: optional TaskStatus status
    5: optional TaskPriority priority
    6: optional i64 due_date
    7: optional i64 created_at
    8: optional i64 updated_at
    9: optional i64 space_id
    10: optional i64 project_id
    11: optional i64 creator_id
}

// 获取任务列表请求
struct ListTaskRequest {
    1: optional i64 space_id
    2: optional i64 project_id
    3: optional TaskStatus status
    4: optional TaskPriority priority
    5: optional i32 page
    6: optional i32 page_size
}

// 获取任务列表响应
struct ListTaskResponse {
    1: required i32 code
    2: optional string message
    3: optional list<TaskInfo> tasks
    4: optional i32 total
    5: optional i32 page
    6: optional i32 page_size
}

// 更新任务请求
struct UpdateTaskRequest {
    1: required i64 id
    2: optional string name
    3: optional string description
    4: optional TaskStatus status
    5: optional TaskPriority priority
    6: optional i64 due_date
}

// 更新任务响应
struct UpdateTaskResponse {
    1: required i32 code
    2: optional string message
    3: optional TaskInfo task
}

// 删除任务请求
struct DeleteTaskRequest {
    1: required i64 id
}

// 删除任务响应
struct DeleteTaskResponse {
    1: required i32 code
    2: optional string message
}

// 任务服务
service TaskService {
    // @http POST /api/task/create
    CreateTaskResponse create_task(1: CreateTaskRequest req)
    // @http POST /api/task/list
    ListTaskResponse list_task(1: ListTaskRequest req)
    // @http POST /api/task/update
    UpdateTaskResponse update_task(1: UpdateTaskRequest req)
    // @http POST /api/task/delete
    DeleteTaskResponse delete_task(1: DeleteTaskRequest req)
}
