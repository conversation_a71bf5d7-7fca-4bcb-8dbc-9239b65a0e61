## Prerequisites

1. Ensure the agent has been published to the Chat SDK channel.
2. Configure [Personal Access Tokens](https://github.com/coze-dev/coze-studio/wiki/6.-API-Reference#prerequisites).



## Install ChatSDK

1.  Install:

```Bash
npm install @coze/chat-sdk
# or
pnpm install @coze/chat-sdk
```


2.  Once the SDK is installed, configure the parameters:
    1.  `appId`, `apiBaseUrl`, and `token` are required. For other parameters, see: [Parameters Description](https://www.npmjs.com/package/@coze/chat-sdk)
    2.  See the example below:

```TypeScript
import "@coze/chat-sdk/webCss";
import ChatSdk from "@coze/chat-sdk/webJs";
const { ChatFramework, ChatSlot, ChatType, Language } = ChatSdk;

export default function Index() {
  return (
    <div className="height-100">
      <ChatFramework
        chat={{
          appId: "xxx", // agent id
          type: ChatType.Bot,
        }}
        setting={{
          apiBaseUrl: "http://localhost:8888",  //  coze studio openapi url
          language: Language.EN,
          requestHeader: {},
          logLevel: "debug",
        }}
        auth={{
          token: "##############", // pat
          onRefreshToken: (oldToken) => {
            return "##############"; // refresh token
          },
        }}
        user={{
          id: "UserId123",
          name: "Mr.XXX",
          avatar:
            "https://sf16-passport-sg.ibytedtos.com/obj/user-avatar-alisg/e0622b06d99df6ead022ca4533ca631f.png",
        }}
      >
        <ChatSlot className={"chat-slot"} />
      </ChatFramework>
    </div>
  );
}
```


