## Requirements
Before installing Coze Studio, ensure that your software and hardware environment meets the following requirements:
| <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"><strong>Requirements</strong></span></span> | <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"><strong>Description</strong></span></span> |
| --- | --- |
| CPU | 2 Core |
| RAM | 4 GiB |
| Docker | Install Docker and Docker Compose in advance, and start the Docker service. For detailed instructions, refer to the Docker documentation: <br>  <br> * **macOS**: It is recommended to use Docker Desktop for installation. Refer to the [Docker Desktop For Mac](https://docs.docker.com/desktop/setup/install/mac-install/) installation guide. <br> * **Linux**: Refer to the [Docker installation guide](https://docs.docker.com/engine/install/) and the [Docker Compose](https://docs.docker.com/compose/install/) installation guide. <br> * **Windows**: It is recommended to install using Docker Desktop. Refer to the [Docker Desktop For Windows](https://docs.docker.com/desktop/setup/install/windows-install/) installation guide. |
## Install Coze Studio
### Step 1: Clone the source code
Run the following commands in your local project to clone the latest version of the Coze Studio source code.
```Bash
Clone code
git clone https://github.com/coze-dev/coze-studio.git
```

### Step 2: **Configure a model**
Coze Studio is an AI app development platform based on large language models (LLMs). Before deploying and starting the open-source version of Coze Studio for the first time, you need to configure the model service in your Coze Studio project; otherwise, you will not be able to properly select a model when creating an agent or workflow.
This document uses the Volcengine Ark model as an example to demonstrate how to configure a model service for Coze Studio. If you plan to use OpenAI or other online model services, you should refer to the [model configuration](3.-Model-Configuration) documentation to correctly fill in the configuration file.

1. Copy the template files for the doubao-seed-1.6 model from the template directory and paste them into the configuration file directory.
   ```Bash
   cd coze-studio
   Copy a model configuration template
   cp backend/conf/model/template/model_template_ark_doubao-seed-1.6.yaml backend/conf/model/ark_doubao-seed-1.6.yaml
   ```

2. In the configuration file directory, modify the template file.
   1. Go to the `backend/conf/model` directory. Open the file`ark_doubao-seed-1.6.yaml`.
   2. Set the `id`, `meta.conn_config.api_key`, and `meta.conn_config.model` fields, and save the file.
   ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/4d2cceb0f0544a1b94d6a92d147290a4~tplv-goo7wpa0wc-image.image)
      * **id**: The model ID in Coze Studio is defined by the developer. It must be a nonzero integer and must be globally unique. Do not modify the model ID after the model goes live.
      * **meta.conn_config.api_key**: The API Key for the online model service. In this example, it refers to the API Key for Volcengine Ark. For information on how to obtain it, see [Get Volcengine Ark API Key](https://www.volcengine.com/docs/82379/1541594).
      * **meta.conn_config.model**: The model ID for the online model service. In this example, it is the Endpoint ID of the Volcengine doubao-seed-1.6 model access point. For information on how to obtain it, see [Get Endpoint ID](https://www.volcengine.com/docs/82379/1099522).

### Step 3: Deploy and start services
The initial deployment and startup of Coze Studio require retrieving images and building local images. This process may take some time, so please be patient. During the deployment process, you will see the following log information. If you see the message "Container coze-server Started", it means that the Coze Studio service has started successfully.
```Bash
Start the service
cd docker
cp .env.example .env
docker compose --profile '*' up -d
```

After the service starts, the coze-elasticsearch-setup, coze-minio-setup, coze-mysql-setup-init-sql, and coze-mysql-setup-schema containers are in an exited state (exit 0), which is normal.
### Step 4: Log in
<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">After starting the </span></span><span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">service,</span></span><span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"> use a browser to visit </span></span>[http://localhost:8888/](http://localhost:8888/)<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"> to open Coze Studio.</span></span> <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">8888 is the backend listening port.</span></span>

1. <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">Enter your email and password.</span></span>
2. <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">Click the </span></span><span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"><strong>Register</strong></span></span><span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"> button to complete registration.</span></span>
   <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">After registration, the page will automatically log you in, and you can start experiencing all the features and services of Coze Studio.</span></span>
   <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">At this point, you have successfully deployed and logged in to Coze Studio.</span></span>

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/c05109bc278e4a7b87164c2db2738b41~tplv-goo7wpa0wc-image.image)
## What's next
<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">After successfully deploying Coze Studio, if you need to use functions such as plugins and knowledge bases, you also need to:</span></span>

* [Configure Plugins](https://github.com/coze-dev/coze-studio/wiki/4.-Plugin-Configuration)<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">: Some official plugins require authentication through the keys of third-party services, such as Feishu Cloud Document series components. If the key is not configured, the plugin will be displayed as "Unauthorized".</span></span>
* [Configure Basic Components](https://github.com/coze-dev/coze-studio/wiki/5.-Basic-component-configuration)<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">: The core components are as follows</span></span>
   * <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"><strong>Knowledge Base</strong></span></span><span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">: To use the knowledge base function, you must configure the </span></span>[Embedding Component](https://github.com/coze-dev/coze-studio/wiki/2.-Quickstart#vectorization-model-embedding)<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">; for image knowledge bases, you also need to set up the </span></span>[OCR Component](https://github.com/coze-dev/coze-studio/wiki/2.-Quickstart#text-recognition-ocr)<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"> to recognize text in images.</span></span>
   * <span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328"><strong>Image Upload</strong></span></span><span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">: When you need to use the multimodal input of the large model, the upload component needs to be configured with a public network domain name or IP address. Otherwise, in the debug console and when conversing with the model, the model cannot read the uploaded images.</span></span>
* [Configure Models](https://github.com/coze-dev/coze-studio/wiki/3.-Model-configuration)<span style="background-color: rgb(255, 255, 255)"><span style="color: #1F2328">: Add model services as needed so that your agents, workflows or applications can use more models.</span></span>
