After deploying the open-source version of Coze Studio, if you need to use the image upload functionality or knowledge base-related features, you should configure the basic components related to these functions as described in this document. These components usually rely on third-party services such as Volcengine, and when configuring the components, you need to enter authentication configurations such as the account token of the third-party service.
## Upload components
In multimodal chat scenarios, it is often necessary to upload images, files, and other information within the chat, such as sending image messages in the agent debugging area:

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/2ad267c1dadd4381b54cf09192355198~tplv-goo7wpa0wc-image.image)

This functionality is provided by the upload components. Coze Studio's upload components currently support the following three options; you can **choose any one** as the upload component.

* **(Default) minio**: Images and files are automatically uploaded to the local host's minio service, which can be accessed through the specified port number. However, the local host must be configured with a public domain name or IP; otherwise, the uploaded images and files can only generate internal network access links, which cannot be read or recognized by large models.
* **Volcano Engine Object Storage TOS**: Images and files are automatically uploaded to the specified Volcano Engine Object Storage TOS service, and a public-accessible URL is generated. If TOS is selected, TOS must first be enabled, and the Volcano token must be configured in Coze Studio.
* **Volcano Engine ImageX**: Images and files are automatically uploaded to the specified Volcano Engine ImageX, and a public-accessible URL is generated. If ImageX is selected, ImageX must first be enabled, and the Volcano token must be configured in Coze Studio.

The configuration method for the upload component is as follows:

1. Set the upload component type.
   Open the `.env` file in the docker directory, and configure the value for the "FILE_UPLOAD_COMPONENT_TYPE" item, which indicates the upload component type.
   * **storage** (default): Indicates the storage component configured with `STORAGE_TYPE`. The default for `STORAGE_TYPE` is minio, but it also supports configuration as tos.
   * **imagex**: Indicates the use of the Volcano ImageX component.
   ```Bash
   # This Upload component used in Agent / workflow File/Image With LLM  , support the component of imagex / storage
   # default: storage, use the settings of storage component
   # if imagex, you must finish the configuration of <VolcEngine ImageX>
   export FILE_UPLOAD_COMPONENT_TYPE="storage"
   ```

2. Add tokens and other configurations for the upload component.
   In the `.env` file within the same docker directory, fill in the following configuration based on the component type.
   | **The type of the component.** | **Configure the authentication method** | **Example** |
   | --- | --- | --- |
   | minio <br> (Default) | 1. In the `docker/.env` file of the Coze studio project, set FILE_UPLOAD_COMPONENT_TYPE to storage. <br> 2. In the Storage component area, set STORAGE_TYPE to minio. <br> 3. In the MiniO area, maintain the default configuration. <br>  <br> If you choose to deploy Coze Studio on public clouds such as Volcano Engine, you will need to pre-open access permissions for ports 8888 and 8889 on your cloud server. <br>  | ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/64578ddbcbb94dafa875cf266e49a0fc~tplv-goo7wpa0wc-image.image) |
   | tos | 1. Activate the Volcano Engine TOS product. <br> 2. In the `docker/.env` file of the Coze studio project, set FILE_UPLOAD_COMPONENT_TYPE to storage. <br> 3. In the Storage component area, set STORAGE_TYPE to tos. <br> 4. In the TOS area, fill in the following parameters: <br>    * TOS_ACCESS_KEY: VolcEngine token AK. Refer to [Obtaining Volcanic Ark API Key](https://www.volcengine.com/docs/82379/1541594) for the acquisition method. <br>    * TOS_SECRET_KEY: VolcEngine token SK. Refer to [Obtaining Volcanic Ark API Key](https://www.volcengine.com/docs/82379/1541594) for the acquisition method. <br>    * TOS_ENDPOINT: Endpoint of the TOS service. Refer to [Regions and Access Domain Names](https://www.volcengine.com/docs/6349/107356) for the acquisition method. <br>    * TOS_REGION: Region where the TOS service is located. Refer to [Regions and Access Domain Names](https://www.volcengine.com/docs/6349/107356) for the acquisition method. | ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/9230b5a67296447ca807ea97ae6fdb62~tplv-goo7wpa0wc-image.image) |
   | imagex | 1. Activate the VolcEngine veImageX product and create a **Material Hosting** service. The operation steps can be found in the [Volc veImageX Official Documentation](https://www.volcengine.com/docs/508/8084). Note that when creating the **content hosting** service, you need to fill in the domain name. It is recommended to obtain a usable public domain name in advance. <br> 2. In the Coze studio project's `docker/.env` file, set FILE_UPLOAD_COMPONENT_TYPE to imagex. <br> 3. In the VolcEngine ImageX region, fill in the following parameters: <br>    * VE_IMAGEX_AK: VolcEngine token AK. Refer to [Obtaining the VolcAries API Key](https://www.volcengine.com/docs/82379/1541594) for the method. <br>    * VE_IMAGEX_SK: VolcEngine token SK. Refer to [Obtaining the VolcAries API Key](https://www.volcengine.com/docs/82379/1541594) for the method. <br>    * VE_IMAGEX_SERVER_ID: The service ID displayed on the **service management** page of the VolcEngine veImageX product. <br>    * VE_IMAGEX_DOMAIN: The domain name specified when creating the service. <br>    * VE_IMAGEX_TEMPLATE: The template name of the image processing configuration. | ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/30ef93d304d542159141c4a00777c363~tplv-goo7wpa0wc-image.image) |
3. Execute the following command to restart the service and apply the configuration above.
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


## Knowledge base-related components
### Text recognition (OCR)
When creating a knowledge base, if the uploaded local file is a scanned document, the OCR function often needs to be enabled to recognize text information in images or files. The configuration example is as follows:

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/4386d834bd4847dd85e8765e16fbf5a5~tplv-goo7wpa0wc-image.image)

The OCR capability of the Coze Studio knowledge base is provided by the Volcano Engine OCR product. Before enabling the OCR function in the open-source version of Coze Studio, you should first activate the Volcano Engine OCR product, and fill in the OCR configuration in the local project, configuring the OCR component's AK and SK as the token pair of the Volcano Engine. The detailed steps are as follows:

1. Activate the Volcano Engine [General Text Recognition](https://console.volcengine.com/ai/ability/info/3) product.
   You can complete the activation based on the prompts on the page. After activating the product, the [General Text Recognition](https://console.volcengine.com/ai/ability/info/3) product page displays the **access status** as **officially activated**. An example is as follows:
   ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/e097aa2c27ae41d0a5b8a5cf6af15aeb~tplv-goo7wpa0wc-image.image)
2. Obtain the Volcano account's AK and SK, which will be needed in subsequent steps.
   The acquisition method can be referenced in the [official Volcano documentation](https://www.volcengine.com/docs/6291/65568).
3. Fill in the OCR configuration in the local project.
   1. Navigate to the `docker/` directory.
   2. Open the file `.env` and modify the VE_OCR_AK and VE_OCR_SK configuration items.
      * VE_OCR_AK: Set it as the Volcano account AK obtained in Step 2.
      * VE_OCR_SK: Set it as the Volcano account SK obtained in Step 2.
   3. Save the configuration.
      ```YAML
      # Settings for OCR
      # If you want to use the OCR-related functions in the knowledge base feature，You need to set up the OCR configuration.
      # Currently, Coze Studio has built-in Volcano OCR.
      # ocr_type: default type `ve`
      export OCR_TYPE="ve"
      # ve ocr
      export VE_OCR_AK=""
      export VE_OCR_SK=""
      ```

4. Execute the following command to restart the service
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


### Vectorized storage (VectorStore)
The Coze Studio knowledge base functionality relies on vectorized storage components and currently supports the following two components:

* **Milvus**: An open-source high-performance vector database focused on solving large-scale vector similarity search problems (e.g., semantic retrieval of images, videos, and text). Milvus does not have an embedded embedding model by default; you need to add a locally deployed embedding model separately.
* **VikingDB**: A next-generation distributed vector database provided by Volcano Engine, designed for ultra-large-scale vector data management and retrieval. VikingDB comes with multiple selectable vectorized models. The current list of optional embedding models can be found in [VikingDB model list](https://www.volcengine.com/docs/84313/1254542#%E6%A8%A1%E5%9E%8B%E5%88%97%E8%A1%A8). You can also choose locally deployed embedding models.

Configuration process is as follows:

1. Navigate to the `docker/` directory, open the file `.env`, and locate the vectorstore configuration module.
   The following section is the vectorstore configuration module.
   ```YAML
   # Settings for VectorStore
   # VectorStore type: milvus / vikingdb
   # If you want to use vikingdb, you need to set up the vikingdb configuration.
   export VECTOR_STORE_TYPE="milvus"
   # milvus vector store
   export MILVUS_ADDR="localhost:19530"
   # vikingdb vector store for Volcengine
   export VIKING_DB_HOST=""
   export VIKING_DB_REGION=""
   export VIKING_DB_AK=""
   export VIKING_DB_SK=""
   export VIKING_DB_SCHEME=""
   export VIKING_DB_MODEL_NAME=""  # if vikingdb model name is not set, you need to set Embedding settings
   ```

2. Select the vector library type you need and modify the corresponding configuration items.
   * milvus: Fill in the following configuration.
      * Set VECTOR_STORE_TYPE to `milvus`.
      * Set MILVUS_ADDR to `localhost:19530`.
   * vikingdb: Activate the Volcano Engine Ark platform's knowledge base module, obtain the Volcano account's tokens (AK, SK), and fill in the following configuration.
      * <span style="color: #D83931">VIKING_DB_HOST</span>: The domain name of the Ark knowledge base. For detailed instructions, refer to [Volcano official documentation](https://www.volcengine.com/docs/84313/1254488#%E5%88%9D%E5%A7%8B%E5%8C%96-sdk).
      * VIKING_DB_REGION: The region where the Ark knowledge base is located, North China: cn-beijing, East China: cn-shanghai, Johor: ap-southeast-1.
      * VIKING_DB_AK: Volcano Engine account AK. The method to obtain it can be found in [Volcano official documentation](https://www.volcengine.com/docs/6291/65568).
      * VIKING_DB_SK: Volcano Engine account SK. You can refer to the [official Volcano documentation](https://www.volcengine.com/docs/6291/65568) for the retrieval method.
      * <span style="color: #D83931">VIKING_DB_SCHEME:</span> http or https.
      * VIKING_DB_MODEL_NAME: The optional embedding model list can be found in the [VikingDB model list](https://www.volcengine.com/docs/84313/1254542#%E6%A8%A1%E5%9E%8B%E5%88%97%E8%A1%A8). If you choose to use a locally deployed embedding model, leave this blank and complete the configuration for the Embedding model section. For detailed explanations, refer to the vectorization model (Embedding) section below.
3. Save the file.
4. Run the following command to restart the service.
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


### Vectorization model (Embedding)
The open-source version of Coze Studio supports customization of the Embedding model dependency for knowledge base vectorization, allowing the vectorization process to better meet the business requirements of specific scenarios.

* If the vectorized storage module uses **milvus**, you must refer to this document to configure the Embedding model.
* The VikingDB vector library comes with an Embedding feature. If the vectorized storage module uses **milvus**, you can choose to use the preset [VikingDB model](https://www.volcengine.com/docs/84313/1254542#%E6%A8%A1%E5%9E%8B%E5%88%97%E8%A1%A8) or opt for OpenAI or other models.

Coze Studio supports three integration methods: OpenAI, Ark, and HTTP (locally deployed model services).
The configuration is as follows:

1. Go to the `docker/` directory, open the file: `.env`, and find the Embedding configuration module.
2. Select the type of vectorized model and fill in the corresponding configuration parameters.
   The detailed configurations are described as follows:
   ```YAML
   # Settings for Embedding
   # The Embedding model relied on by knowledge base vectorization does not need to be configured
   # if the vector database comes with built-in Embedding functionality (such as VikingDB). Currently,
   # Coze Studio supports three access methods: openai, ark, and custom http. Users can simply choose one of them when using
   # embedding type: openai / ark / http
   export EMBEDDING_TYPE="openai"
   # openai embedding
   export OPENAI_EMBEDDING_BASE_URL="https://search.bytedance.net/gpt/openapi/online/v2/crawl"
   export OPENAI_EMBEDDING_MODEL=""
   export OPENAI_EMBEDDING_API_KEY=""
   export OPENAI_EMBEDDING_BY_AZURE=true
   export OPENAI_EMBEDDING_API_VERSION=""
   export OPENAI_EMBEDDING_DIMS=1024
   # ark embedding
   export ARK_EMBEDDING_MODEL=""
   export ARK_EMBEDDING_AK=""
   export ARK_EMBEDDING_DIMS=""
   # http embedding
   export HTTP_EMBEDDING_ADDR="http://127.0.0.1:6543"
   ```

3. Save the file.
4. Run the following commands to restart the service.
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


### AI Generated Model (Model)
Coze Studio knowledge base provides text-to-SQL (NL2SQL), one-sentence query generation (Message to Query), and image annotation features, all of which rely on Coze Studio's pre-configured AI generation models. Before using these features in the open-source version of Coze Studio, you need to configure this AI generation model first.
* The AI-generated model configured here is only effective in three scenarios: NL2SQL, Message to Query, and Image Annotation.
* If different models need to be used in different scenarios, you can add a prefix to apply specific configurations for a particular scenario. For example, in the image annotation scenario, you need to configure a visual model while other scenarios do not require it. In this case, you can add `export IA_BUILTIN_CM_OPENAI_MODEL="xxx"` to configure a visual model dedicated to the image annotation scenario.

To configure this app, follow these steps:

1. Go to the `docker/` directory.
2. Open the file: `.env`, and locate the Model for knowledge configuration module. Make the following configurations:
   * **Set the model type**: Use the parameter BUILTIN_CM_TYPE to configure the model for NL2SQL and other AI generation scenarios. Supported settings include openai, ark, deepseek, ollama, or qwen.
   * **Set the model authentication and other parameters**: Different models require different configuration parameters, which you can find in the configuration file under each model's parameter section. For example, the ark model should have the following parameters set:
      * BUILTIN_CM_ARK_API_KEY: Volcanic Ark API Key, you can refer to [How to obtain the Volcanic Ark API Key](https://www.volcengine.com/docs/82379/1541594) for details.
      * BUILTIN_CM_ARK_MODEL: Ark Model ID, refer to [Volcanic Ark Model List](https://www.volcengine.com/docs/82379/1330310).
      ```YAML
      # Settings for Model
      # Model for knowledge nl2sql, messages2query (rewrite), image annotation
      # add prefix to assign specific model, downgrade to default config when prefix is not configured:
      # 1. nl2sql:            NL2SQL_ (e.g. NL2SQL_BUILTIN_CM_TYPE)
      # 2. messages2query:    M2Q_    (e.g. M2Q_BUILTIN_CM_TYPE)
      # 3. image annotation:  IA_     (e.g. IA_BUILTIN_CM_TYPE)
      # supported chat model type: openai / ark / deepseek / ollama / qwen
      export BUILTIN_CM_TYPE="openai"
      # type openai
      export BUILTIN_CM_OPENAI_BASE_URL=""
      export BUILTIN_CM_OPENAI_API_KEY=""
      export BUILTIN_CM_OPENAI_BY_AZURE=true
      export BUILTIN_CM_OPENAI_MODEL=""
      
      # type ark
      export BUILTIN_CM_ARK_API_KEY =""
      export BUILTIN_CM_ARK_MODEL =""
      
      # type deepseek
      export BUILTIN_CM_DEEPSEEK_BASE_URL = ""
      export BUILTIN_CM_DEEPSEEK_API_KEY = ""
      export BUILTIN_CM_DEEPSEEK_MODEL = ""
      
      # type ollama
      export BUILTIN_CM_OLLAMA_BASE_URL = ""
      export BUILTIN_CM_OLLAMA_MODEL = ""
      
      # type qwen
      export BUILTIN_CM_QWEN_BASE_URL = ""
      export BUILTIN_CM_QWEN_API_KEY = ""
      export BUILTIN_CM_QWEN_MODEL = ""
      ```

3. Save the file.
4. Execute the following command to restart the service.
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```

### Workflow Code Execution Sandbox
The sandbox feature provides a secure and isolated environment for running user-configured Python code in Workflow code nodes.
By default, the code runs directly in the service environment's Python virtual environment (venv). If you require isolation, you can configure it by modifying the following environment variables:

1. Go to the docker/ directory.
2. Open the .env file, find the CODE_RUNNER_TYPE configuration item, change it to sandbox , configure the dependent sandbox settings, and save the file.

```Bash
# CODE_RUNNER_TYPE supports sandbox / local, defaults to local
export CODE_RUNNER_TYPE="sandbox"

# Note: The following configuration is only effective when CODE_RUNNER_TYPE=sandbox

export CODE_RUNNER_ALLOW_ENV=""   # Restrict environment variable access, separated by commas, e.g., "PATH,USERNAME"
export CODE_RUNNER_ALLOW_READ=""  # Read permissions, separated by commas, e.g., "/tmp,./data"
export CODE_RUNNER_ALLOW_WRITE="" # Write permissions, separated by commas, e.g., "/tmp,./data"
export CODE_RUNNER_ALLOW_RUN=""   # Execution permissions, separated by commas, e.g., "python,git"

# Network access permissions, separated by commas, e.g., "api.test.com,api.test.org:8080"
# The default CDN configuration supports downloading packages required by pyodide. The sandbox may not run correctly if this is removed.
export CODE_RUNNER_ALLOW_NET="cdn.jsdelivr.net"

export CODE_RUNNER_ALLOW_FFI=""        # Foreign Function Interface (FFI) access, separated by commas, e.g., "/usr/lib/libm.so"
export CODE_RUNNER_NODE_MODULES_DIR="" # Deno modules path, defaults to the project path, e.g., "/tmp/path/node_modules"
export CODE_RUNNER_TIMEOUT_SECONDS=""  # Code execution timeout in seconds, default is 60s, e.g., "2.56"
export CODE_RUNNER_MEMORY_LIMIT_MB=""  # Memory limit for code execution in MB, default is 100MB, e.g., "256"
```

3. Execute the following command to restart the service.
```Shell
docker compose --profile '*' up -d --force-recreate --no-deps coze-server
```
