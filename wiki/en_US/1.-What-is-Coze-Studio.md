## What is Coze Studio
[Coze Studio](https://www.coze.cn/home) is a one-stop AI agent development tool. Providing the latest large language models (LLMs) and tools, multiple development modes and frameworks, from development to deployment, it offers you the most convenient AI agent development environment. Tens of thousands of enterprises and millions of developers are using Coze Studio.

* **Provides all the core technologies required for AI agent development**: prompt, RAG, plugin, and workflow, enabling developers to focus on creating core value of AI.
* **Ready-to-use, developing the most professional AI agent at the lowest cost:** Coze Studio provides developers with robust app templates and build frameworks, allowing you to quickly construct various AI agents based on them and turn creativity into reality.

Coze Studio is the open-source edition of ByteDance's new generation AI agent development platform **Coze**. Through Coze Studio's visual design and build tools, developers can quickly create and debug agents, apps, and workflows using no-code or low-code methods, enabling powerful AI app development and more customized business logic. It is the ideal choice for building low-code AI products. Coze Studio is committed to lowering the threshold for AI agent development and applications, encouraging community co-creation and shared communication, helping you explore and practice more deeply in the AI field.

The backend of Coze Studio is developed using Golang, the frontend is built with React + TypeScript, and the entire structure is based on a microservices architecture following domain-driven design (DDD) principles. It provides developers with a high-performance, highly scalable, and easily customizable underlying framework to help address complex business needs.
## What can Coze Studio do?
With Coze Coze's visual design and build tools, you can quickly build various AI projects using no-code or low-code approaches with the capabilities of large language models (LLMs), tailored to your needs and achieving commercial value.

* **Build an agent**: An agent is a conversation-based AI project that interacts with users through conversations. It automatically calls plugins or workflows with LLMs to execute specified business processes and generate responses. Smart customer service, virtual companions, personal assistants, and English tutors are all typical use cases for agents.
* **Build AI apps**: Apps refer to application programs developed using large language model (LLM) technology. The AI app built in Coze has complete business logic and a visual user interface, making it a standalone AI project. AI applications built on Coze have definite inputs and outputs and can perform a range of simple or complex tasks based on predefined business logic and workflows. For example, AI search, translation tools, and dietary tracking.
* **Build workflows**: Coze's workflow functionality can be used to handle complex logic and tasks that require high stability. Coze offers a wide range of flexible and combinable nodes, including large language models (LLM), custom code, condition logic, and more. Whether you have programming experience or not, you can quickly build a workflow using the drag-and-drop approach. For example, you can create a workflow to write an industry research report, enabling the agent to generate a 20-page report.

## Quickstart
Refer to [Quick start](2.-Quickstart) to learn how to obtain and deploy the open-source edition of Coze Studio, quickly build projects, and experience Coze Studio open-source edition.
## Feature list
| **Module** | **Feature** |
| --- | --- |
| Model services | Manage the model list, allowing integration of online model services such as OpenAI and VolcEngine. |
| Build agent | * Build, publish, and manage agents <br> * Support configuration of workflows, knowledge bases, and other resources |
| Build apps | * Create and publish apps <br> * Build business logic through workflows |
| Build a workflow | Create, modify, publish, and delete workflows |
| Develop resources | Support creation and management of the following resources: <br>  <br> * Plugins <br> * Knowledge base <br> * Databases <br> * Prompt |
| API and SDK | Create conversations, initiate chats, and other OpenAPI functions |
|  | Integrate agents or apps into your own app via the Chat SDK |

