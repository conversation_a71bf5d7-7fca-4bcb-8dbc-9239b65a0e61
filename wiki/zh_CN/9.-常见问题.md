## 1、部署时，“coze-elasticsearch” 容器启动报错？
部署过程中如果出现以下报错：

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/d57ba44fbed74bc58e90c1846ab5804c~tplv-goo7wpa0wc-image.image)

使用代码编辑器（如 VS Code）打开 `docker/volumes/elasticsearch/setup_es.sh`。在编辑器的右下角，你会看到 CRLF 或 LF 的标识，点击它并选择 LF。保存文件后再重新启动
`docker compose --profile "*" up -d`。以及相关[issue](https://github.com/coze-dev/coze-studio/issues/1#issuecomment-3120960174)

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/d116fa56bed24414a8ec6f6b28c73241~tplv-goo7wpa0wc-image.image)

## 2、windows本地部署，“Error response from daemon: Ports are not available: exposing port TCP [0.0.0.0:2379](http://0.0.0.0:2379/) -＞ [127.0.0.1:0](http://127.0.0.1:0/):2379” 如何解决？

```bash
# 查看占用
netstat -ano | findstr :2379

net stop winnat
net start winnat
```


## 3、Agent 对话调试 “Something error:Internal server error” 错误？

可以通过以下命令查询具体错误日志

```bash
docker logs coze-server | grep -i 'node execute failed'
```

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/d1d490a6cac74051a222f51a110fad08~tplv-goo7wpa0wc-image.image)

## 4、知识库上传文档之后，显示处理中0%？

当文本/表格/图片知识库创建成功，但上传文件后长时间显示‘处理中’，如下图所示：

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/78f57d715e924eeb9cef65a291c6e8b6~tplv-goo7wpa0wc-image.image)

请参考下述步骤检查 `docker/.env` 文件中的基础组件配置，配置介绍文档 [5. 基础组件配置](https://github.com/coze-dev/coze-studio/wiki/5.-%E5%9F%BA%E7%A1%80%E7%BB%84%E4%BB%B6%E9%85%8D%E7%BD%AE)。

1. 检查 .env 文件中的 embedding 配置。
   | <span style="background-color: rgb(242, 243, 245)">模型</span> | <span style="background-color: rgb(242, 243, 245)">检查方式</span> |
   | --- | --- |
   | EMBEDDING_TYPE="openai" | 1. OPENAI_EMBEDDING_BASE_URL：coze-server 通常使用各平台协议提供的 sdk 调用 api，此时不要求包含 `/embeddings` 后缀，请参考具体 api 提供方的文档进行配置 <br> 2. OPENAI_EMBEDDING_BY_AZURE：非微软 azure api 请设置为 false <br> 3. OPENAI_EMBEDDING_DIMS / OPENAI_EMBEDDING_REQUEST_DIMS： <br>    1. 请根据模型所支持的向量维度，将两个 key 设置为相同的数值 <br>    2. 部分模型 api 传入向量维度会报错，可以将 OPENAI_EMBEDDING_REQUEST_DIMS 注释掉 <br>    3. 部分模型不支持设定向量维度，请对照模型文档进行配置 |
   | EMBEDDING_TYPE="ark" | 1. ARK_EMBEDDING_MODEL： <br>    1. 使用模型名称时需要携带具体版本，例如 `doubao-embedding-large-text-250515`；使用 endpoint 时（诸如 ep-xxxx) 完整复制即可 <br>    2. 目前（0728）暂不支持图像向量化模型（doubao-embedding-vision）调用，请使用文本向量化模型，近期会支持上 <br> 2. ARK_EMBEDDING_DIMS：请参考官方文档配置支持的嵌入维度 |
   | EMBEDDING_TYPE="http" | 1. http 协议目前有自己独立的输入输出格式，可以参考 [https://github.com/coze-dev/coze-studio/blob/main/backend/infra/impl/embedding/http/http.go](https://github.com/coze-dev/coze-studio/blob/main/backend/infra/impl/embedding/http/http.go) 进行封装 <br> 2. 目前暂不支持 ollama，敬请期待 |
2. 检查 .env 文件 ocr 配置
   检查 VE_OCR_AK 和 VE_OCR_SK 是否正确填写，如果未配置的话请在文档处理时关闭 ocr 功能
3. 检查 .env 文件模型（BUILTIN_CM_）配置。
   智能体查询转 SQL、查询改写、图片知识库智能标注这三个功能依赖这部分配置，如果不需要用到的话可以不配置。参考【模型配置】章节提到的内容进行检查即可。
4. 上述所有配置都正确无误后，重启服务并重试。
   1. 重启服务：`docker compose --profile "*" up -d`
   2. 新建知识库，上传文档。
   3. 如果依旧失败，获取下面两部分信息后，在用户群 / issues 提问联系开发者排查
      1. `container_id=$(docker ps -aqf "name=coze-server") && docker logs "$container_id" 2>&1 | grep 'HandleMessage'` 获取文档处理日志。
         1. 如果出现 `the num_rows (300) of field (dense_text_content) is not equal to passed num_rows (100): invalid parameter[expected=100][actual=300]"` 类似的错误，说明是模型维度问题，请检查所配置的模型是否支持配置维度，按照参数手动调用模型输出的维度是否符合预期，无需提问
         2. 如果出现 http 超时 / connection refused 等报错，请检查容器网络和 env 配置。
      2. `container_id=$(docker ps -aqf "name=coze-server") && docker exec -it $container_id /bin/sh`，进入 container 后 `cat .env` 获取配置信息，注意密钥脱敏。


## 5、Agent对话或者workflow大模型节点上传图片/文件后调试时模型报错？

大模型访问的图片/文件链接需要是公网可访问的链接，图片存组件需要在公网部署，具体可以参考：[上传组件配置](https://github.com/coze-dev/coze-studio/wiki/5.-%E5%9F%BA%E7%A1%80%E7%BB%84%E4%BB%B6%E9%85%8D%E7%BD%AE#%E4%B8%8A%E4%BC%A0%E7%BB%84%E4%BB%B6)

## 6、服务启动后，“setup”相关的容器处于退出状态是正常的吗？

是正常的 ，setup相关的容器是做一些脚本初始化工作，执行完毕后会自动退出。

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/9b65261c5be14e7aa74e84dd53543d52~tplv-goo7wpa0wc-image.image)


## 7、模型列表无法切换模型，如何处理？

* **问题现象**：用户在搭建智能体、工作流和扣子应用时，无法在模型列表中选择模型、切换模型。例如：
   ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/a3ff3545de71427f833fb7faeb0d0d7e~tplv-goo7wpa0wc-image.image)
* **问题原因**：在配置模型时，开发者在不同的模型文件中配置了相同的 id，导致模型 id 冲突。
* **解决方式**：
   1. 在`backend/conf/model` 下打开所有的模型配置 yaml 文件。
   1. 找到重复的 id，为各个模型另外设置一个 id，确保每个模型的 id 均为唯一的非 0 整数。
   1. 执行命令重启服务：`docker compose --profile "*" up -d`

## 8、模型报错（Internal server error 等），如何处理？
当和智能体对话、调试智能体，或运行工作流大模型节点时，如果模型返回 Internal server error 等报错信息，或者在系统日志中查看到类似信息，表示模型配置文件填写错误。
此场景常见的报错信息和对应日志信息如下：

* 对话报错 `Something error:Internal server error`，对应的日志信息为`status code: 404, status: 404 Not Found, message: Invalid URL (POST xxxx/chat/completions)` 或者 `can't fetch endpoint sts token without endpoint`。
* 对话或日志中报错 `connection refused`。

以上报错信息表示模型配置文件填写错误，建议按照以下操作步骤检查 `backend/conf/model` 下的 yaml 文件配置。完整的模型配置可参考文档 [3. 模型配置](https://github.com/coze-dev/coze-studio/wiki/3.-%E6%A8%A1%E5%9E%8B%E9%85%8D%E7%BD%AE)。

1. 检查模型通用配置：
   * **id**：所有文件中的 id 唯一，无重复 id。
   * **base_url**：coze-server 通常使用各模型厂商协议提供的 SDK 调用模型，请参考具体模型厂商的文档进行配置。注意 Coze Studio 不要求 base_url 包含 `/chat/completions` 后缀。
   * **api_key 和 model**：应正确配置 api_key 和 model，否则无法这些配置调用模型进行对话。
   * **yaml 文件**：检查 yaml 语法是否合法，不合法时，系统会阻止 coze-server 启动。
2. 检查各个模型的专有配置：

以下是通常需要重点关注的模型配置清单，完整的配置方式可参考文档 [3. 模型配置](https://github.com/coze-dev/coze-studio/wiki/3.-%E6%A8%A1%E5%9E%8B%E9%85%8D%E7%BD%AE)。
| **模型/平台** | **检查方式** |
| --- | --- |
| OpenAI | 检查 by_azure 字段配置，如果模型是微软 azure 提供的模型服务，此参数应设置为 true。 |
| Ollama | 1. 检查 base_url： <br>    1. 镜像网络模式是 bridge，coze-server 镜像内 localhost 不是主机的 localhost。需要修改为 ollama 部署机器的 ip，或 `http://host.docker.internal:11434`。 <br>    2. ip:port 后不需要加 path，例如 `/v1`。 <br> 2. 检查 api_key：未设置 api_key 时，此参数置空。 <br> 3. 确认部署 Ollama 主机的防火墙是否已开放 11434 端口。 <br> 4. 确认 ollama 网络已开启**对外暴露**。 <br>  <br> ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/ddc89983c4e84b7fa3035564370c73d4~tplv-goo7wpa0wc-image.image) |
| 阿里百炼平台 <br>  | 阿里百炼平台兼容 OpenAI 协议调用模型，此时： <br>  <br> * Protocol 设置为 `openai`。 <br> * base_url 设置为 `https://dashscope.aliyuncs.com/compatible-mode/v1`。 <br> * api_key 和 model 自行配置为阿里百炼提供的 api_key 和 model 即可。 |

3. 全部检查完毕后，执行以下步骤重启服务并重试。
   1. 执行命令重启服务：`docker compose --profile "*" up -d`
   2. 新建智能体创建对话。
      如果模型依旧报错，参考以下步骤获取日志，并在用户群 / issues 提问联系开发者排查。获取日志的详细方式可参考[服务日志](7.-开发规范#服务日志)。
      ```Bash
      docker logs coze-server | grep -i 'node execute failed'
      ```



