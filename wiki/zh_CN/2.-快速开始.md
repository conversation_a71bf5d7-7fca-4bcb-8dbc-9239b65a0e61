## 环境要求
在参考本文安装 Coze Studio 之前，确保您的软硬件环境满足以下要求： 
| **项目** | **说明** |
| --- | --- |
| CPU | 2 Core |
| RAM | 4 GiB |
| Docker  | 提前安装 Docker、Docker Compose，并启动 Docker 服务，详细操作请参考 Docker 文档：  <br>  <br> * **macOS**：推荐使用 Docker Desktop 安装，参考 [Docker Desktop For Mac](https://docs.docker.com/desktop/setup/install/mac-install/) 安装指南。  <br> * **Linux**：参考 [Docker 安装指南](https://docs.docker.com/engine/install/) 和 [Docker Compose](https://docs.docker.com/compose/install/) 安装指南。  <br> * **Windows**：推荐使用 Docker Desktop 安装，参考 [Docker Desktop For Windows](https://docs.docker.com/desktop/setup/install/windows-install/) 安装指南。  |
## 安装 Coze Studio
### 步骤一：获取源码
在本地项目中执行以下命令，获取 Coze Studio 最新版本的源码。
```Bash
# 克隆代码 
git clone https://github.com/coze-dev/coze-studio.git
```

### 步骤二：**配置模型**
Coze Studio 是基于大语言模型的 AI 应用开发平台，首次部署并启动 Coze Studio 开源版之前，你需要先在 Coze Studio 项目里配置模型服务，否则创建智能体或者工作流时，无法正常选择模型。
本文档以火山方舟模型为例，演示如何为 Coze Studio 配置模型服务。如果你准备使用 OpenAI 等其他在线模型服务，应参考[模型配置](3.-模型配置)文档正确填写配置文件。

1. 从模板目录复制 doubao-seed-1.6 模型的模版文件，并粘贴到配置文件目录。
   ```Bash
   cd coze-studio
   # 复制模型配置模版
   cp backend/conf/model/template/model_template_ark_doubao-seed-1.6.yaml backend/conf/model/ark_doubao-seed-1.6.yaml
   ```

2. 在配置文件目录下，修改模版文件。
   1. 进入目录 `backend/conf/model`。打开文件`ark_doubao-seed-1.6.yaml`。
   2. 设置 `id`、`meta.conn_config.api_key`、`meta.conn_config.model` 字段，并保存文件。
   ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/4d2cceb0f0544a1b94d6a92d147290a4~tplv-goo7wpa0wc-image.image)
      * **id**：Coze Studio 中的模型 ID，由开发者自行定义，必须是非 0 的整数，且全局唯一。模型上线后请勿修改模型 id。
      * **meta.conn_config.api_key**：在线模型服务的 API Key，在本示例中为火山方舟的 API Key，获取方式可参考[获取火山方舟 API Key](https://www.volcengine.com/docs/82379/1541594)。
      * **meta.conn_config.model**：在线模型服务的 model ID，在本示例中为火山方舟 doubao-seed-1.6 模型接入点的 Endpoint ID，获取方式可参考[获取 Endpoint ID](https://www.volcengine.com/docs/82379/1099522)。

### 步骤三：部署并启动服务

首次部署并启动 Coze Studio 需要拉取镜像、构建本地镜像，可能耗时较久，请耐心等待。如果看到提示 "Container coze-server Started"，表示 Coze Studio 服务已成功启动。
 
```Bash
# 启动服务
cd docker
cp .env.example .env
docker compose --profile "*" up -d
```
服务启动之后，coze-elasticsearch-setup、coze-minio-setup、coze-mysql-setup-init-sql、coze-mysql-setup-schema 这几个容器处于退出状态（exit 0），是正常现象。

### 步骤四：登录访问
启动服务后，通过浏览器访问 [http://localhost:8888/](http://localhost:8888/) 即可打开 Coze Studio。其中 8888 为后端监听端口。 

1. 输入你的邮箱和密码。
2. 单击**注册**按钮，完成注册。

   注册后页面将自动完成登录，你可以开始体验 Coze Studio 的各项功能与服务。

至此，你已成功部署并登录 Coze Studio。

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/088ba3638c1b49c1a02bad091e039a22~tplv-goo7wpa0wc-image.image)

## 后续操作
成功部署 Coze Studio 后，如需使用插件、知识库等功能，你还需要：

* [配置插件](4.-插件配置)：部分官方插件需要通过第三方服务的密钥鉴权，例如飞书云文档系列组件。若未配置密钥，插件将显示为”未授权“。
* [配置基础组件](5.-基础组件配置)：核心组件如下
   * **知识库**：如需使用知识库功能，则必须配置 [Embedding 组件](5.-基础组件配置#向量化模型embedding)；对于图片知识库，还需要额外设置 [OCR 组件](5.-基础组件配置#文字识别ocr)以识别图片中的文字。
   * **图片上传**：当需要使用大模型多模态输入时，上传组件需要配置公网域名或者 IP 地址，否则在调试台和模型对话时，模型无法读取已上传的图片。
* [配置模型](3.-模型配置)：按需增加模型服务，使你的智能体、工作流或应用能使用更多模型。
