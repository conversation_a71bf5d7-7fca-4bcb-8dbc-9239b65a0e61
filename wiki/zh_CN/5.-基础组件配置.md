部署 Coze Studio 开源版之后，如需使用图片上传功能，或知识库相关功能，应参考本文档配置功能相关的基础组件。这些组件通常依赖火山引擎等第三方服务，配置组件时需要填写第三方服务的账号密钥等鉴权配置。
## 上传组件
在多模态对话场景，往往需要在对话中上传图片、文件等信息，例如在智能体调试区域中发送图片消息：

![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/6d18160e25a54359b3db9ceee90ec25a~tplv-goo7wpa0wc-image.image)

此功能由上传组件提供。Coze Studio 上传组件目前支持以下三种，你可以**任选一种**作为上传组件。

* **（默认）minio**：图片和文件自动上传至本地主机的 minio 服务，通过指定端口号可访问。但本地主机必须配置公网域名或者IP，否则已上传的图片和文件只能生成内网访问链接，无法被大模型读取和识别。
* **火山引擎对象存储 TOS**：图片和文件自动上传至指定的火山引擎对象存储 TOS 服务，并生成一个公网可访问的 URL。如果选择 TOS，必须先开通 TOS 并在 Coze Studio 中配置火山密钥。
* **火山引擎 ImageX**：图片和文件自动上传至指定的火山引擎 ImageX，并生成一个公网可访问的 URL。如果选择 ImageX，必须先开通 ImageX 并在 Coze Studio 中配置火山密钥。

上传组件的配置方式如下：

1. 设置上传组件类型。
   在 docker 目录中打开 `.env` 文件，配置项 "FILE_UPLOAD_COMPONENT_TYPE" 的值表示上传组件类型。
   * **storage**（默认）：表示使用`STORAGE_TYPE` 配置的存储组件，`STORAGE_TYPE` 默认为 minio，也支持配置为 tos。
   * **imagex**：表示使用火山 ImageX 组件。
   ```Bash
   # This Upload component used in Agent / workflow File/Image With LLM  , support the component of imagex / storage
   # default: storage, use the settings of storage component
   # if imagex, you must finish the configuration of <VolcEngine ImageX> 
   export FILE_UPLOAD_COMPONENT_TYPE="storage"
   ```

2. 为上传组件添加秘钥等配置。
   同样在 docker 目录的 `.env` 文件中，根据组件类型填写以下配置。
   | **组件类型** | **配置方式** | **示例** |
   | --- | --- | --- |
   | minio <br> （默认） | 1. 在 Coze studio 项目的 `docker/.env` 文件中，FILE_UPLOAD_COMPONENT_TYPE 设置为 storage。 <br> 2. 在 Storage component 区域，设置 STORAGE_TYPE 为 minio。 <br> 3. 在 MiniO 区域，维持默认配置即可。 <br>  <br> 如果你选择在火山引擎等公共云上部署 Coze Studio，则需要提前为你的云服务器开放 8888、8889 端口的访问权限。 <br>  | ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/64578ddbcbb94dafa875cf266e49a0fc~tplv-goo7wpa0wc-image.image) <br>  |
   | tos | 1. 开通火山引擎 TOS 产品。 <br> 2. 在 Coze studio 项目的 `docker/.env` 文件中，FILE_UPLOAD_COMPONENT_TYPE 设置为 storage。 <br> 3. 在 Storage component 区域，设置 STORAGE_TYPE 为 tos。 <br> 4. 在 TOS 区域，填写以下参数： <br>    * TOS_ACCESS_KEY：火山引擎密钥 AK。获取方式可参考[获取火山方舟 API Key](https://www.volcengine.com/docs/82379/1541594)。 <br>    * TOS_SECRET_KEY：火山引擎密钥 SK。获取方式可参考[获取火山方舟 API Key](https://www.volcengine.com/docs/82379/1541594)。 <br>    * TOS_ENDPOINT：TOS 服务的 Endpoint，获取方式可参考[地域和访问域名](https://www.volcengine.com/docs/6349/107356)。 <br>    * TOS_REGION：TOS 服务所在的地域，获取方式可参考[地域和访问域名](https://www.volcengine.com/docs/6349/107356)。 |  <br> ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/9230b5a67296447ca807ea97ae6fdb62~tplv-goo7wpa0wc-image.image) <br>  |
   | imagex | 1. 开通火山引擎 veImageX 产品，并创建**素材托管**服务。操作步骤可参考[火山 veImageX 官方文档](https://www.volcengine.com/docs/508/8084)。注意创建**素材托管**服务时需要填写域名，建议提前获取一个可用的公开域名。 <br> 2. 在 Coze studio 项目的 `docker/.env` 文件中，FILE_UPLOAD_COMPONENT_TYPE 设置为 imagex。 <br> 3. 在 VolcEngine ImageX 区域，填写以下参数： <br>    * VE_IMAGEX_AK：火山引擎密钥 AK。获取方式可参考[获取火山方舟 API Key](https://www.volcengine.com/docs/82379/1541594)。 <br>    * VE_IMAGEX_SK：火山引擎密钥 SK。获取方式可参考[获取火山方舟 API Key](https://www.volcengine.com/docs/82379/1541594)。 <br>    * VE_IMAGEX_SERVER_ID：火山引擎 veImageX 产品**服务管理**页面展示的服务 ID。 <br>    * VE_IMAGEX_DOMAIN：创建服务时指定的域名。 <br>    * VE_IMAGEX_TEMPLATE：图片处理配置的模板名称。 |  <br> ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/30ef93d304d542159141c4a00777c363~tplv-goo7wpa0wc-image.image) <br>  |
3. 执行以下命令重启服务，使以上配置生效。
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


## 知识库相关组件
### 文字识别（OCR）
在创建知识库时，如果上传的本地文件是扫描件，往往需要开启 ORC 功能来识别图片或文件中的文字信息。配置示例如下：
![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/d299887e511f4936a9efb5dea27a0479~tplv-goo7wpa0wc-image.image)
Coze Studio 知识库的 ORC 能力由火山引擎 OCR 产品提供服务。在 Coze Studio 开源版中开启 OCR 功能前，应先开通火山引擎 OCR 产品，并在本地项目中填写 ORC 配置，将 OCR 组件的 AK、SK 配置为火山引擎的密钥对。详细操作步骤如下：

1. 开通火山引擎[通用文字识别](https://console.volcengine.com/ai/ability/info/3)产品。
   你可以根据页面提示完成开通。开通产品后，[通用文字识别](https://console.volcengine.com/ai/ability/info/3)产品页面显示**接入状态**为**正式开通**，示例如下：
   ![Image](https://p9-arcosite.byteimg.com/tos-cn-i-goo7wpa0wc/e097aa2c27ae41d0a5b8a5cf6af15aeb~tplv-goo7wpa0wc-image.image)
2. 获取火山账号的 AK 和 SK，后续步骤中需要使用。
   获取方式可参考[火山官方文档](https://www.volcengine.com/docs/6291/65568)。
3. 在本地项目中填写 ORC 配置。
   1. 进入目录`docker/`目录。
   2. 打开文件 `.env` ，修改 VE_OCR_AK、VE_OCR_SK 配置项。
      * VE_OCR_AK：配置为步骤二中获取的火山账号 AK。
      * VE_OCR_SK：配置为步骤二中获取的火山账号 SK。
   3. 保存配置。
      ```YAML
      # Settings for OCR
      # If you want to use the OCR-related functions in the knowledge base feature，You need to set up the OCR configuration. 
      # Currently, Coze Studio has built-in Volcano OCR.
      # ocr_type: default type `ve`
      export OCR_TYPE="ve"
      # ve ocr
      export VE_OCR_AK=""
      export VE_OCR_SK=""
      ```

4. 执行以下命令重启服务。
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


### 向量化存储（VectorStore）
Coze Studio  知识库功能依赖向量化存储组件，目前支持以下两种组件：

* **milvus**：开源的高性能向量数据库，专注于解决大规模向量相似度搜索（如图片、视频、文本的语义检索）问题。milvus 未内置 embedding 模型，需要另外添加本地部署的 embedding 模型。
* **vikingdb**：火山引擎提供的新一代分布式向量数据库，专为超大规模向量数据管理与检索设计。vikingdb 内置多个可选的向量化模型，目前可选的 embedding 模型列表可参考[VikingDB 模型列表](https://www.volcengine.com/docs/84313/1254542#%E6%A8%A1%E5%9E%8B%E5%88%97%E8%A1%A8)，你也可以选择本地部署的 embedding 模型。

配置流程如下：

1. 进入`docker/`目录，打开文件 `.env` ，找到 vectorstore 配置模块。
   以下部分为 vectorstore 配置模块。
   ```YAML
   # Settings for VectorStore
   # VectorStore type: milvus / vikingdb
   # If you want to use vikingdb, you need to set up the vikingdb configuration.
   export VECTOR_STORE_TYPE="milvus"
   # milvus vector store
   export MILVUS_ADDR="localhost:19530"
   # vikingdb vector store for Volcengine
   export VIKING_DB_HOST=""
   export VIKING_DB_REGION=""
   export VIKING_DB_AK=""
   export VIKING_DB_SK=""
   export VIKING_DB_SCHEME=""
   export VIKING_DB_MODEL_NAME=""  # if vikingdb model name is not set, you need to set Embedding settings
   ```

2. 选择你需要的向量库类型，修改对应配置项。
   * milvus：填写以下配置
      * VECTOR_STORE_TYPE 设置为 `milvus`。
      * MILVUS_ADDR 设置为 `localhost:19530`。
   * vikingdb：开通火山引擎方舟平台的知识库模块，获取火山账号的密钥（AK、SK），并填写以下配置
      * <span style="color: #D83931">VIKING_DB_HOST</span>：方舟知识库的域名。详细说明可参考[火山官方文档](https://www.volcengine.com/docs/84313/1254488#%E5%88%9D%E5%A7%8B%E5%8C%96-sdk)。
      * VIKING_DB_REGION：方舟知识库所在地域，华北：cn-beijing，华东：cn-shanghai，柔佛：ap-southeast-1。
      * VIKING_DB_AK：火山引擎账号 AK。获取方式可参考[火山官方文档](https://www.volcengine.com/docs/6291/65568)。
      * VIKING_DB_SK：火山引擎账号 SK。获取方式可参考[火山官方文档](https://www.volcengine.com/docs/6291/65568)。
      * <span style="color: #D83931">VIKING_DB_SCHEME：</span>http 或 https。
      * VIKING_DB_MODEL_NAME：可选的 embedding 模型列表可参考 [VikingDB 模型列表](https://www.volcengine.com/docs/84313/1254542#%E6%A8%A1%E5%9E%8B%E5%88%97%E8%A1%A8)。如需选用本地部署的 embedding 模型，此处留空，并填写 Embedding 模型部分的配置。详细说明可参考下文向量化模型（Embedding）。
3. 保存文件。
4. 执行以下命令重启服务。
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


### 向量化模型（Embedding）
Coze Studio 开源版支持自定义设置知识库向量化依赖的 Embedding 模型，使知识库的向量化环节效果更符合指定场景的业务需求。

* 如果向量化存储模块使用 **milvus**，则必须参考本文档设置 Embedding 模型。
* VikingDB 向量库自带 Embedding 功能，如果向量化存储模块使用 **milvus**，则可以自行选择使用预置的 [VikingDB 模型](https://www.volcengine.com/docs/84313/1254542#%E6%A8%A1%E5%9E%8B%E5%88%97%E8%A1%A8)或者使用 OpenAI 或其他模型。

Coze Studio 支持三种方式接入，openai、ark、http（本地部署的模型服务）。
配置流程如下：

1. 进入`docker/`目录，打开文件：`.env` ，找到 Embedding 配置模块。
2. 选择向量化模型类型，并填写对应的配置参数。
   详细配置如下：
   ```YAML
   # Settings for Embedding
   # The Embedding model relied on by knowledge base vectorization does not need to be configured 
   # if the vector database comes with built-in Embedding functionality (such as VikingDB). Currently, 
   # Coze Studio supports three access methods: openai, ark, and custom http. Users can simply choose one of them when using
   # embedding type: openai / ark / http
   export EMBEDDING_TYPE="openai"
   # openai embedding
   export OPENAI_EMBEDDING_BASE_URL="https://search.bytedance.net/gpt/openapi/online/v2/crawl"
   export OPENAI_EMBEDDING_MODEL=""
   export OPENAI_EMBEDDING_API_KEY=""
   export OPENAI_EMBEDDING_BY_AZURE=true
   export OPENAI_EMBEDDING_API_VERSION=""
   export OPENAI_EMBEDDING_DIMS=1024
   # ark embedding
   export ARK_EMBEDDING_MODEL=""
   export ARK_EMBEDDING_AK=""
   export ARK_EMBEDDING_DIMS=""
   # http embedding
   export HTTP_EMBEDDING_ADDR="http://127.0.0.1:6543"
   ```

3. 保存文件。
4. 执行以下命令重启服务。
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```


### AI 生成模型（Model）
Coze Studio 知识库提供文本转 SQL（NL2SQL）、一句话生成 Query（Message to Query）、图像标注（Image Annotation）功能，这些功能统一依赖于 Coze Studio 预配置的 AI 生成模型。在 Coze Studio 开源版中使用这些功能前，你需要先配置这个AI 生成模型。
* 此处配置的 AI 生成模型只在 NL2SQL、Message to Query、Image Annotation 三个场景中生效。
* 如果需要在不同场景中使用不同模型，你可以通过添加前缀来针对特定场景应用特定配置，例如图片标注场景需要配置视觉模型，其余场景不需要，则可以新增 `export IA_BUILTIN_CM_OPENAI_MODEL="xxx"` 来配置图片标注场景专用的视觉模型。

配置步骤如下：

1. 进入`docker/`目录。
2. 打开文件：`.env` ，找到 Model for knowledge 配置模块。修改以下配置：
   * **设置模型类型**：通过参数 BUILTIN_CM_TYPE 设置 NL2SQL 等 AI 生成场景的模型。支持设置为 openai、ark、deepseek、ollama 或 qwen。
   * **设置模型鉴权等参数**：不同的模型对应不同的参数配置，你可以在配置文件中找到各个模型的配置参数区域。例如 ark 模型应设置以下参数：
      * BUILTIN_CM_ARK_API_KEY：火山方舟 API Key，获取方式可参考[获取火山方舟 API Key](https://www.volcengine.com/docs/82379/1541594)。
      * BUILTIN_CM_ARK_MODEL：方舟模型 ID，可参考 [火山方舟模型列表](https://www.volcengine.com/docs/82379/1330310)。
      ```YAML
      # Settings for Model
      # Model for knowledge nl2sql, messages2query (rewrite), image annotation
      # add prefix to assign specific model, downgrade to default config when prefix is not configured:
      # 1. nl2sql:            NL2SQL_ (e.g. NL2SQL_BUILTIN_CM_TYPE)
      # 2. messages2query:    M2Q_    (e.g. M2Q_BUILTIN_CM_TYPE)
      # 3. image annotation:  IA_     (e.g. IA_BUILTIN_CM_TYPE)
      # supported chat model type: openai / ark / deepseek / ollama / qwen
      export BUILTIN_CM_TYPE="openai"
      # type openai
      export BUILTIN_CM_OPENAI_BASE_URL=""
      export BUILTIN_CM_OPENAI_API_KEY=""
      export BUILTIN_CM_OPENAI_BY_AZURE=true
      export BUILTIN_CM_OPENAI_MODEL=""
      
      # type ark
      export BUILTIN_CM_ARK_API_KEY =""
      export BUILTIN_CM_ARK_MODEL =""
      
      # type deepseek
      export BUILTIN_CM_DEEPSEEK_BASE_URL = ""
      export BUILTIN_CM_DEEPSEEK_API_KEY = ""
      export BUILTIN_CM_DEEPSEEK_MODEL = ""
      
      # type ollama
      export BUILTIN_CM_OLLAMA_BASE_URL = ""
      export BUILTIN_CM_OLLAMA_MODEL = ""
      
      # type qwen
      export BUILTIN_CM_QWEN_BASE_URL = ""
      export BUILTIN_CM_QWEN_API_KEY = ""
      export BUILTIN_CM_QWEN_MODEL = ""
      ```

3. 保存文件。
4. 执行以下命令重启服务。
   ```Shell
   docker compose --profile '*' up -d --force-recreate --no-deps coze-server
   ```
   
### Workflow 代码运行沙箱（Sandbox)
沙箱功能提供了一个安全隔离的沙箱环境，供 Workflow 代码节点运行用户配置的 python 代码。
默认情况下，代码会在服务环境的 Python 虚拟环境 (venv) 中直接运行。如果你有隔离需求，可以通过修改下面的环境变量来配置：

1. 进入`docker/`目录。
2. 打开文件：`.env` ，找到`CODE_RUNNER_TYPE`配置项，将其修改为sandbox，并配置sandbox的依赖配置项，保存

```Bash
# CODE_RUNNER_TYPE 支持 sandbox / local，默认为 local
export CODE_RUNNER_TYPE="sandbox"

# 注意：下面的配置仅在 CODE_RUNNER_TYPE=sandbox 时生效

export CODE_RUNNER_ALLOW_ENV=""   # 限制环境变量读取，用逗号分隔，例如: "PATH,USERNAME"
export CODE_RUNNER_ALLOW_READ=""  # 读权限，用逗号分隔，例如: "/tmp,./data"
export CODE_RUNNER_ALLOW_WRITE="" # 写权限，用逗号分隔，例如："/tmp,./data"
export CODE_RUNNER_ALLOW_RUN=""   # 运行权限，用逗号分隔，例如："python,git"

# 网络访问权限，用逗号分隔，例如："api.test.com,api.test.org:8080"
# 默认配置的 CDN 支持下载运行 pyodide 所需的包，如果删除的话沙箱可能无法正常运行。
export CODE_RUNNER_ALLOW_NET="cdn.jsdelivr.net"

export CODE_RUNNER_ALLOW_FFI=""        # 外部函数接口，用逗号分隔，例如："/usr/lib/libm.so"
export CODE_RUNNER_NODE_MODULES_DIR="" # deno modules 路径，默认使用项目路径，例如："/tmp/path/node_modules"
export CODE_RUNNER_TIMEOUT_SECONDS=""  # 代码执行超时时间，默认 60s，例如："2.56"
export CODE_RUNNER_MEMORY_LIMIT_MB=""  # 代码运行内存限制，默认 100MB，例如："256"
```

3. 执行以下命令重启服务。

```Shell
docker compose --profile '*' up -d --force-recreate --no-deps coze-server
```
