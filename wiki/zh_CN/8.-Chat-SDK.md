## 准备工作

1. 确保agent已发布至chat sdk渠道
2. 配置[Personal Access Tokens](https://github.com/coze-dev/coze-studio/wiki/6.-API-%E5%8F%82%E8%80%83#%E5%87%86%E5%A4%87%E5%B7%A5%E4%BD%9C)


## 安装ChatSDK

1.  安装：

```Bash
npm install @coze/chat-sdk
# or
pnpm install @coze/chat-sdk
```


2. SDK安装成功，配置参数：
   1. appId、apiBaseUrl、token 必须配置，其他参数配置参考示：[Parameters Description](https://www.npmjs.com/package/@coze/chat-sdk)
   2. 参考示例：

```TypeScript
import "@coze/chat-sdk/webCss";
import ChatSdk from "@coze/chat-sdk/webJs";
const { ChatFramework, ChatSlot, ChatType, Language } = ChatSdk;

export default function Index() {
  return (
    <div className="height-100">
      <ChatFramework
        chat={{
          appId: "xxx", // agent id
          type: ChatType.Bot,
        }}
        setting={{
          apiBaseUrl: "http://localhost:8888",  //  coze studio openapi url
          language: Language.EN,
          requestHeader: {},
          logLevel: "debug",
        }}
        auth={{
          token: "##############", // pat
          onRefreshToken: (oldToken) => {
            return "##############"; // 刷新后的token
          },
        }}
        user={{
          id: "UserId123",
          name: "Mr.XXX",
          avatar:
            "https://sf16-passport-sg.ibytedtos.com/obj/user-avatar-alisg/e0622b06d99df6ead022ca4533ca631f.png",
        }}
      >
        <ChatSlot className={"chat-slot"} />
      </ChatFramework>
    </div>
  );
}
```


