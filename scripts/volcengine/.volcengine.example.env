export VE_AK=""
export VE_SK=""
export VE_REGION=""

export VE_PROJECT_NAME="default"
export VE_VPC_NAME="opencoze-vpc"

# MySQL
export VE_MYSQL_DB_NAME="opencoze"
export VE_MYSQL_USER_NAME="coze"
export VE_MYSQL_USER_PASSWORD="Opencoze123"
export VE_MYSQL_PORT="3306"
export VE_MYSQL_DB_ENGINE_VERSION="MySQL_8_0"
export VE_MYSQL_INSTANCE_CLASS="vedb.mysql.g2.medium"

# The intermediate variable is empty by default; no input is required.

# ZoneID
export VE_ZONE_ID=""

# network
export VE_VPC_ID=""
export VE_SUBNET_ID=""
export VE_ACL_ID=""
export VE_SAFE_GROUP_ID=""

# mysql
export VE_MYSQL_INSTANCE_ID=""
export VE_MYSQL_WHITELIST_ID=""

# redis
export VE_REDIS_INSTANCE_ID=""
export VE_REDIS_ALLOWLIST_ID=""

# rocketmq
export VE_ROCKETMQ_ALLOWLIST_ID=""
export VE_ROCKETMQ_INSTANCE_ID=""

# elasticsearch
export VE_ES_INSTANCE_ID=""

# ECS
export VE_ECS_INSTANCE_ID=""
