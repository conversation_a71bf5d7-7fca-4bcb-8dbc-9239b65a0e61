# =================================================================
# Coze-Studio WSL2 一键部署脚本
# =================================================================

# 启动 WSL
wsl

# 1. 网络代理配置 (如果不需要，请删除或注释掉以下四行)
export http_proxy=http://host.docker.internal:7897
export https_proxy=http://host.docker.internal:7897
export HTTP_PROXY=http://host.docker.internal:7897
export HTTPS_PROXY=http://host.docker.internal:7897

# 2. WSL 基础环境与核心工具安装
echo "--- 更新系统并安装核心工具 ---"
sudo apt update && sudo apt upgrade -y
sudo apt install make docker.io docker-compose bash -y

# 3. Docker 配置
echo "--- 配置 Docker ---"
sudo usermod -aG docker $USER
echo "--- Docker 组配置完成，请重新启动 WSL 终端后，从'git clone'开始执行剩余脚本 ---"
exit

# =================================================================
# 注意：请在新开启的 WSL 终端中执行以下剩余脚本
# =================================================================

# 4. 获取项目源码
echo "--- 克隆项目源码 ---"
cd ~
git clone https://github.com/coze-studio/coze-studio_1.git
cd ~/coze-studio

# 5. Go 环境配置 (Go 1.24.4)
echo "--- 安装和配置 Go 环境 ---"
sudo rm -rf /usr/local/go
wget https://go.dev/dl/go1.24.4.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.24.4.linux-amd64.tar.gz
rm go1.24.4.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
go env -w GOPROXY=https://mirrors.aliyun.com/goproxy/,direct
go version

# 6. Node.js 环境配置 (via NVM)
echo "--- 安装和配置 Node.js 环境 ---"
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash
source ~/.bashrc
nvm install --lts
nvm use --lts
npm config set registry https://registry.npmmirror.com
npm install -g @microsoft/rush
node --version
npm --version

# 7. Python 环境配置 (Python 3.11)
echo "--- 安装和配置 Python 环境 ---"
sudo apt update
sudo apt install python3.11-venv -y
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
sudo python3.11 get-pip.py
rm get-pip.py
pip install --upgrade pip
pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
pip config set global.trusted-host mirrors.aliyun.com

# 8. 文件格式转换 (可选)
echo "--- 转换脚本文件格式 ---"
sudo apt install dos2unix -y
find ./scripts -type f -exec dos2unix {} \;
find ./docker -type f -exec dos2unix {} \;

# 9. 初始化项目配置
echo "--- 初始化项目配置 ---"
cp backend/conf/model/template/model_template_ark_doubao-seed-1.6.yaml backend/conf/model/ark_doubao-seed-1.6.yaml

# 10. 首次构建 (Debug 模式)
echo "--- 执行首次构建 (make debug)，此过程可能需要一些时间 ---"
make debug

# 11. 启动服务(本地)
echo "--- 准备并启动 Docker 服务 ---"
cd docker
cp .env.example .env
docker compose up -d

echo "--- 部署完成！服务已在后台启动。---"