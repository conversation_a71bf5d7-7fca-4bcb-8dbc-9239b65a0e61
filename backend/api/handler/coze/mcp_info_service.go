/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package coze

import (
	"context"
	"log"
	"unicode/utf8"

	//mcpModel "github.com/coze-dev/coze-studio/backend/api/model/mcp"
	application "github.com/coze-dev/coze-studio/backend/application/mcp"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/dto"

	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/protocol/consts"
)

// CreateMcpInfo 创建 MCP 实例
// @Router /api/mcp/create [POST]
func CreateMcpInfo(ctx context.Context, c *app.RequestContext) {
	var req dto.CreateMcpInfoRequest
	log.Println("开始处理 CreateMcpInfo 请求")
	if err := c.BindAndValidate(&req); err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}
	if utf8.RuneCountInString(req.McpName) > 50 {
		invalidParamRequestResponse(c, "name is too long")
		return
	}

	if utf8.RuneCountInString(req.Description) > 2000 {
		invalidParamRequestResponse(c, "description is too long")
		return
	}
	log.Println("请求参数: %+v\n", req)
	//resp := new(mcpModel.CreateMcpInfoResponse)
	resp, err := application.McpInfoSVC.CreateMcpInfo(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	log.Printf("响应参数: %+v\n", resp)
	c.JSON(consts.StatusOK, resp)
}

// GetByMcpId 根据 mcpId获取MCP工具集合
// @Router /api/mcp/getByMcpId [GET]
func GetByMcpId(ctx context.Context, c *app.RequestContext) {
	var req dto.GetByMcpIdRequest
	log.Println("开始处理 CreateMcpInfo ")
	if err := c.BindAndValidate(&req); err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}
	log.Printf("req 的值: %+v\n", req)
	log.Println("开始处理 CreateMcpInfo 请求")
	id, err := application.McpInfoSVC.GetByMcpId(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, id)
}

// UpdateMcpToolByMcpId 根据mcpId更新MCP工具集合
// @Router /api/mcp/UpdateMcpToolByMcpId [GET]
func UpdateMcpToolByMcpId(ctx context.Context, c *app.RequestContext) {
	var req dto.GetByMcpIdRequest
	log.Println("开始处理 UpdateMcpToolByMcpId ")
	if err := c.BindAndValidate(&req); err != nil {
		invalidParamRequestResponse(c, err.Error())
		return
	}
	log.Printf("req 的值: %+v\n", req)
	res, err := application.McpInfoSVC.UpdateMcpToolByMcpId(ctx, &req)
	if err != nil {
		internalServerErrorResponse(ctx, c, err)
		return
	}
	c.JSON(consts.StatusOK, res)
}
