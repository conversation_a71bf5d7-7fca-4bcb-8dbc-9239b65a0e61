package coze

import (
    "context"
    "github.com/cloudwego/hertz/pkg/app"
    "github.com/cloudwego/hertz/pkg/protocol/consts"
    taskModel "github.com/coze-dev/coze-studio/backend/api/model/task"
    taskApp "github.com/coze-dev/coze-studio/backend/application/task"
)

// CreateTask .
// @router /api/task/create [POST]
func CreateTask(ctx context.Context, c *app.RequestContext) {
    var err error
    var req taskModel.CreateTaskRequest
    err = c.BindAndValidate(&req)
    if err != nil {
        c.String(consts.StatusBadRequest, err.Error())
        return
    }

    resp := new(taskModel.CreateTaskResponse)
    resp, err = taskApp.TaskSVC.CreateTask(ctx, &req)
    if err != nil {
        internalServerErrorResponse(ctx, c, err)
        return
    }
    c.JSON(consts.StatusOK, resp)
}

// ListTask .
// @router /api/task/list [POST]
func ListTask(ctx context.Context, c *app.RequestContext) {
    var err error
    var req taskModel.ListTaskRequest
    err = c.BindAndValidate(&req)
    if err != nil {
        c.String(consts.StatusBadRequest, err.Error())
        return
    }

    resp := new(taskModel.ListTaskResponse)
    resp, err = taskApp.TaskSVC.ListTasks(ctx, &req)
    if err != nil {
        internalServerErrorResponse(ctx, c, err)
        return
    }
    c.JSON(consts.StatusOK, resp)
}

// UpdateTask .
// @router /api/task/update [POST]
func UpdateTask(ctx context.Context, c *app.RequestContext) {
    var err error
    var req taskModel.UpdateTaskRequest
    err = c.BindAndValidate(&req)
    if err != nil {
        c.String(consts.StatusBadRequest, err.Error())
        return
    }

    resp := new(taskModel.UpdateTaskResponse)
    resp, err = taskApp.TaskSVC.UpdateTask(ctx, &req)
    if err != nil {
        internalServerErrorResponse(ctx, c, err)
        return
    }
    c.JSON(consts.StatusOK, resp)
}

// DeleteTask .
// @router /api/task/delete [POST]
func DeleteTask(ctx context.Context, c *app.RequestContext) {
    var err error
    var req taskModel.DeleteTaskRequest
    err = c.BindAndValidate(&req)
    if err != nil {
        c.String(consts.StatusBadRequest, err.Error())
        return
    }

    resp := new(taskModel.DeleteTaskResponse)
    resp, err = taskApp.TaskSVC.DeleteTask(ctx, &req)
    if err != nil {
        internalServerErrorResponse(ctx, c, err)
        return
    }
    c.JSON(consts.StatusOK, resp)
}
