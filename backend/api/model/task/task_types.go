package task

// 任务状态枚举
type TaskStatus int32

const (
	TaskStatusPending    TaskStatus = 1
	TaskStatusInProgress TaskStatus = 2
	TaskStatusCompleted  TaskStatus = 3
	TaskStatusCancelled  TaskStatus = 4
)

// 任务优先级枚举
type TaskPriority int32

const (
	TaskPriorityLow    TaskPriority = 1
	TaskPriorityMedium TaskPriority = 2
	TaskPriorityHigh   TaskPriority = 3
	TaskPriorityUrgent TaskPriority = 4
)

// 任务信息
type TaskInfo struct {
	ID          int64        `json:"id,string"`
	Name        string       `json:"name"`
	Description string       `json:"description,omitempty"`
	Status      TaskStatus   `json:"status,omitempty"`
	Priority    TaskPriority `json:"priority,omitempty"`
	DueDate     *int64       `json:"due_date,omitempty"`
	CreatedAt   *int64       `json:"created_at,omitempty"`
	UpdatedAt   *int64       `json:"updated_at,omitempty"`
	SpaceID     *int64       `json:"space_id,omitempty"`
	ProjectID   *int64       `json:"project_id,omitempty"`
	CreatorID   *int64       `json:"creator_id,omitempty"`
}

func NewTaskInfo() *TaskInfo {
	return &TaskInfo{}
}

// 创建任务请求
type CreateTaskRequest struct {
	Name        string       `json:"name"`
	Description string       `json:"description,omitempty"`
	Priority    TaskPriority `json:"priority,omitempty"`
	DueDate     *int64       `json:"due_date,omitempty"`
	SpaceID     *int64       `json:"space_id,omitempty"`
	ProjectID   *int64       `json:"project_id,omitempty"`
}

func NewCreateTaskRequest() *CreateTaskRequest {
	return &CreateTaskRequest{}
}

// 创建任务响应
type CreateTaskResponse struct {
	Code    int32     `json:"code"`
	Message string    `json:"message,omitempty"`
	Task    *TaskInfo `json:"task,omitempty"`
}

func NewCreateTaskResponse() *CreateTaskResponse {
	return &CreateTaskResponse{}
}

// 获取任务列表请求
type ListTaskRequest struct {
	SpaceID   *int64       `json:"space_id,omitempty"`
	ProjectID *int64       `json:"project_id,omitempty"`
	Status    *TaskStatus  `json:"status,omitempty"`
	Priority  *TaskPriority `json:"priority,omitempty"`
	Page      *int32       `json:"page,omitempty"`
	PageSize  *int32       `json:"page_size,omitempty"`
}

func NewListTaskRequest() *ListTaskRequest {
	return &ListTaskRequest{}
}

// 获取任务列表响应
type ListTaskResponse struct {
	Code     int32      `json:"code"`
	Message  string     `json:"message,omitempty"`
	Tasks    []*TaskInfo `json:"tasks,omitempty"`
	Total    *int32     `json:"total,omitempty"`
	Page     *int32     `json:"page,omitempty"`
	PageSize *int32     `json:"page_size,omitempty"`
}

func NewListTaskResponse() *ListTaskResponse {
	return &ListTaskResponse{}
}

// 更新任务请求
type UpdateTaskRequest struct {
	ID          int64        `json:"id,string"`
	Name        *string      `json:"name,omitempty"`
	Description *string      `json:"description,omitempty"`
	Status      *TaskStatus  `json:"status,omitempty"`
	Priority    *TaskPriority `json:"priority,omitempty"`
	DueDate     *int64       `json:"due_date,omitempty"`
	SpaceID     *int64       `json:"space_id,omitempty"`
}

func NewUpdateTaskRequest() *UpdateTaskRequest {
	return &UpdateTaskRequest{}
}

// 更新任务响应
type UpdateTaskResponse struct {
	Code    int32     `json:"code"`
	Message string    `json:"message,omitempty"`
	Task    *TaskInfo `json:"task,omitempty"`
}

func NewUpdateTaskResponse() *UpdateTaskResponse {
	return &UpdateTaskResponse{}
}

// 删除任务请求
type DeleteTaskRequest struct {
	ID      int64  `json:"id,string"`
	SpaceID *int64 `json:"space_id,omitempty"`
}

func NewDeleteTaskRequest() *DeleteTaskRequest {
	return &DeleteTaskRequest{}
}

// 删除任务响应
type DeleteTaskResponse struct {
	Code    int32  `json:"code"`
	Message string `json:"message,omitempty"`
}

func NewDeleteTaskResponse() *DeleteTaskResponse {
	return &DeleteTaskResponse{}
} 