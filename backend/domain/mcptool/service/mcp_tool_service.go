package service

import (
	"context"

	"github.com/coze-dev/coze-studio/backend/domain/mcptool/entity"
)

type McpToolService interface {
	Create(ctx context.Context, tool *entity.McpTool) (int64, error)
	GetByID(ctx context.Context, id int64) (*entity.McpTool, error)
	Update(ctx context.Context, tool *entity.McpTool) (int64, error)
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, offset, limit int) ([]*entity.McpTool, int64, error)
	UpdateFields(ctx context.Context, id int64, updates map[string]interface{}) error
}
