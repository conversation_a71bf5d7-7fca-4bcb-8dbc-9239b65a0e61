package repository

import (
    "context"
    "errors"
    "github.com/coze-dev/coze-studio/backend/domain/task/entity"
)

var (
    ErrTaskPermissionDenied = errors.New("task permission denied")
    ErrTaskNotFound         = errors.New("task not found")
)

type TaskRepository interface {
    Create(ctx context.Context, task *entity.Task) error
    GetByID(ctx context.Context, id int64) (*entity.Task, error)
    List(ctx context.Context, spaceID int64, filters map[string]interface{}) ([]*entity.Task, int, error)
    Update(ctx context.Context, task *entity.Task) error
    Delete(ctx context.Context, id int64) error
}
