package service

import (
	"context"
	"github.com/coze-dev/coze-studio/backend/domain/task/entity"
	"github.com/coze-dev/coze-studio/backend/domain/task/repository"
	"time"
)

type TaskService struct {
	repo repository.TaskRepository
}

func NewTaskService(repo repository.TaskRepository) *TaskService {
	return &TaskService{repo: repo}
}

type CreateTaskRequest struct {
	Name        string
	Description string
	Priority    entity.TaskPriority
	DueDate     *time.Time
	SpaceID     int64
	ProjectID   *int64
	CreatorID   int64
}

func (s *TaskService) CreateTask(ctx context.Context, req *CreateTaskRequest) (*entity.Task, error) {
	task := &entity.Task{
		Name:        req.Name,
		Description: req.Description,
		Status:      entity.TaskStatusPending,
		Priority:    req.Priority,
		DueDate:     req.DueDate,
		SpaceID:     req.SpaceID,
		ProjectID:   req.ProjectID,
		CreatorID:   req.CreatorID,
	}

	err := s.repo.Create(ctx, task)
	if err != nil {
		return nil, err
	}

	return task, nil
}

type UpdateTaskRequest struct {
	ID          int64
	Name        *string
	Description *string
	Status      *entity.TaskStatus
	Priority    *entity.TaskPriority
	DueDate     *time.Time
	SpaceID     int64
	CreatorID   int64
}

func (s *TaskService) UpdateTask(ctx context.Context, req *UpdateTaskRequest) (*entity.Task, error) {
	// 先获取现有任务
	task, err := s.repo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 检查权限：只有创建者可以更新任务
	if task.CreatorID != req.CreatorID {
		return nil, repository.ErrTaskPermissionDenied
	}

	// 更新字段
	if req.Name != nil {
		task.Name = *req.Name
	}
	if req.Description != nil {
		task.Description = *req.Description
	}
	if req.Status != nil {
		task.Status = *req.Status
	}
	if req.Priority != nil {
		task.Priority = *req.Priority
	}
	if req.DueDate != nil {
		task.DueDate = req.DueDate
	}

	err = s.repo.Update(ctx, task)
	if err != nil {
		return nil, err
	}

	return task, nil
}

type DeleteTaskRequest struct {
	ID        int64
	SpaceID   int64
	CreatorID int64
}

func (s *TaskService) DeleteTask(ctx context.Context, req *DeleteTaskRequest) error {
	// 先获取现有任务
	task, err := s.repo.GetByID(ctx, req.ID)
	if err != nil {
		return err
	}

	// 检查权限：只有创建者可以删除任务
	if task.CreatorID != req.CreatorID {
		return repository.ErrTaskPermissionDenied
	}

	// 检查空间权限
	if task.SpaceID != req.SpaceID {
		return repository.ErrTaskPermissionDenied
	}

	return s.repo.Delete(ctx, req.ID)
}

type ListTaskRequest struct {
	SpaceID   int64
	ProjectID *int64
	Status    *entity.TaskStatus
	Priority  *entity.TaskPriority
	Page      int
	PageSize  int
}

func (s *TaskService) ListTasks(ctx context.Context, req *ListTaskRequest) ([]*entity.Task, int, error) {
	filters := make(map[string]interface{})
	if req.ProjectID != nil {
		filters["project_id"] = *req.ProjectID
	}
	if req.Status != nil {
		filters["status"] = *req.Status
	}
	if req.Priority != nil {
		filters["priority"] = *req.Priority
	}

	return s.repo.List(ctx, req.SpaceID, filters)
}
