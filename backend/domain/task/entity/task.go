package entity

import (
    "time"
)

type TaskStatus int32
type TaskPriority int32

const (
    TaskStatusPending     TaskStatus = 1
    TaskStatusInProgress  TaskStatus = 2
    TaskStatusCompleted   TaskStatus = 3
    TaskStatusCancelled   TaskStatus = 4
)

const (
    TaskPriorityLow    TaskPriority = 1
    TaskPriorityMedium TaskPriority = 2
    TaskPriorityHigh   TaskPriority = 3
    TaskPriorityUrgent TaskPriority = 4
)

type Task struct {
    ID          int64       `json:"id" gorm:"primaryKey;autoIncrement"`
    Name        string      `json:"name" gorm:"not null"`
    Description string      `json:"description"`
    Status      TaskStatus  `json:"status" gorm:"default:1"`
    Priority    TaskPriority `json:"priority" gorm:"default:2"`
    DueDate     *time.Time  `json:"due_date"`
    SpaceID     int64       `json:"space_id" gorm:"not null"`
    ProjectID   *int64      `json:"project_id"`
    CreatorID   int64       `json:"creator_id" gorm:"not null"`
    CreatedAt   time.Time   `json:"created_at" gorm:"autoCreateTime"`
    UpdatedAt   time.Time   `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (Task) TableName() string {
    return "tasks"
}
