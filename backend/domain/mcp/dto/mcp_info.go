package dto

import "gorm.io/datatypes"

type GetByMcpIdRequest struct {
	McpId int64 `json:"mcpId" form:"mcpId"`
}

type GetByMcpIdResponse struct {
	Id          int64                    `json:"id"` // 主键
	IconURI     string                   `json:"icon_uri,omitempty"`
	ServerURL   string                   `json:"server_url,omitempty"`
	McpName     string                   `json:"mcp_name,omitempty"`
	Description string                   `json:"description,omitempty"`
	Tools       []GetToolByMcpIdResponse `json:"tools,omitempty"`
}

type CreateMcpInfoRequest struct {
	SpaceId      int64  `json:"space_id"`
	DeveloperId  int64  `json:"developer_id"`
	AppId        int64  `json:"app_id"`
	IconURI      string `json:"icon_uri"`
	ServerURL    string `json:"server_url"`
	McpName      string `json:"mcp_name"`
	Version      string `json:"version"`
	VersionDesc  string `json:"version_desc"`
	Manifest     string `json:"manifest"`
	OpenapiDoc   string `json:"openapi_doc"`
	Description  string `json:"description"`
	OnlineStatus bool   `json:"online_status"`
}

type UpdateMcpInfoRequest struct {
	Id           int64           `json:"id"` // 主键
	IconURI      *string         `json:"icon_uri,omitempty"`
	ServerURL    *string         `json:"server_url,omitempty"`
	McpName      *string         `json:"mcp_name,omitempty"`
	Version      *string         `json:"version,omitempty"`
	VersionDesc  *string         `json:"version_desc,omitempty"`
	Manifest     *datatypes.JSON `json:"manifest,omitempty"`
	OpenapiDoc   *datatypes.JSON `json:"openapi_doc,omitempty"`
	Description  *string         `json:"description,omitempty"`
	OnlineStatus *bool           `json:"online_status,omitempty"`
}

type GetToolByMcpIdResponse struct {
	Id          int64  `json:"id"` // 主键
	ToolName    string `json:"tool_name"`
	Description string `json:"description"`
	Params      string `json:"params"`
	McpId       int64  `json:"mcp_id"`
}
