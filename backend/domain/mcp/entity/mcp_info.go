package entity

/**
 * MCP 信息结构体，对应数据库表 mcp_info
 */
type McpInfo struct {
	// MCP ID
	ID int64 `thrift:"id,1,required" form:"id,required" json:"id,required" query:"id,required"`
	// 空间 ID
	SpaceId int64 `thrift:"space_id,2,required" form:"space_id,required" json:"space_id,required" query:"space_id,required"`
	// 开发者 ID
	DeveloperId int64 `thrift:"developer_id,3,required" form:"developer_id,required" json:"developer_id,required" query:"developer_id,required"`
	// 应用 ID
	AppId int64 `thrift:"app_id,4,required" form:"app_id,required" json:"app_id,required" query:"app_id,required"`
	// 图标 URI
	IconURI string `thrift:"icon_uri,5,required" form:"icon_uri,required" json:"icon_uri,required" query:"icon_uri,required"`
	// MCP 服务地址
	ServerURL string `thrift:"server_url,6,required" form:"server_url,required" json:"server_url,required" query:"server_url,required"`
	// MCP 名称，如 “中台 MCP”
	McpName string `thrift:"mcp_name,7,required" form:"mcp_name,required" json:"mcp_name,required" query:"mcp_name,required"`
	// 创建时间（毫秒）
	CreatedAt int64 `thrift:"created_at,8,required" form:"created_at,required" json:"created_at,required" query:"created_at,required"`
	// 更新时间（毫秒）
	UpdatedAt int64 `thrift:"updated_at,9,required" form:"updated_at,required" json:"updated_at,required" query:"updated_at,required"`
	// 插件版本，如 v1.0.0
	Version string `thrift:"version,10,required" form:"version,required" json:"version,required" query:"version,required"`
	// 插件版本描述
	VersionDesc string `thrift:"version_desc,11,optional" form:"version_desc" json:"version_desc,omitempty" query:"version_desc"`
	// 插件 Manifest（JSON 字符串）
	Manifest string `thrift:"manifest,12,optional" form:"manifest" json:"manifest,omitempty" query:"manifest"`
	// OpenAPI 文档根（JSON 字符串）
	OpenapiDoc string `thrift:"openapi_doc,13,optional" form:"openapi_doc" json:"openapi_doc,omitempty" query:"openapi_doc"`
	// 补充说明
	Description string `thrift:"description,14,optional" form:"description" json:"description,omitempty" query:"description"`
	// 在线状态：true 表示在线，false 表示离线
	OnlineStatus bool `thrift:"online_status,15,required" form:"online_status,required" json:"online_status,required" query:"online_status,required"`
}

func (McpInfo) TableName() string {
	return "mcp_info"
}

func NewMcpInfo() *McpInfo {
	return &McpInfo{}
}

func (p *McpInfo) InitDefault() {
}

func (p *McpInfo) GetID() (v int64) {
	return p.ID
}

func (p *McpInfo) GetSpaceID() (v int64) {
	return p.SpaceId
}

func (p *McpInfo) GetDeveloperID() (v int64) {
	return p.DeveloperId
}

func (p *McpInfo) GetAppID() (v int64) {
	return p.AppId
}

func (p *McpInfo) GetIconURI() (v string) {
	return p.IconURI
}

func (p *McpInfo) GetServerURL() (v string) {
	return p.ServerURL
}

func (p *McpInfo) GetMcpName() (v string) {
	return p.McpName
}

func (p *McpInfo) GetCreatedAt() (v int64) {
	return p.CreatedAt
}

func (p *McpInfo) GetUpdatedAt() (v int64) {
	return p.UpdatedAt
}

func (p *McpInfo) GetVersion() (v string) {
	return p.Version
}

func (p *McpInfo) GetOnlineStatus() (v bool) {
	return p.OnlineStatus
}

var fieldIDToName_McpInfo = map[int16]string{
	1:  "id",
	2:  "space_id",
	3:  "developer_id",
	4:  "app_id",
	5:  "icon_uri",
	6:  "server_url",
	7:  "mcp_name",
	8:  "created_at",
	9:  "updated_at",
	10: "version",
	11: "version_desc",
	12: "manifest",
	13: "openapi_doc",
	14: "description",
	15: "online_status",
}
