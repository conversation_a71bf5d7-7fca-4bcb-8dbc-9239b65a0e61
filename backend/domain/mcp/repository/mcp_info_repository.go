package repository

import (
	"context"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/dto"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/entity"
)

type McpInfoRepository interface {
	Create(ctx context.Context, mcpInfo *entity.McpInfo) error
	GetByID(ctx context.Context, id int64) (*entity.McpInfo, error)
	List(ctx context.Context, filters map[string]interface{}) ([]*entity.McpInfo, int, error)
	Update(ctx context.Context, mcpInfo *entity.McpInfo) error
	Delete(ctx context.Context, id int64) error
	UpdateFields(ctx context.Context, id int64, updates map[string]interface{}) error
	GetMcpInfoByMcpId(ctx context.Context, id int64) (*dto.GetByMcpIdResponse, error)
}
