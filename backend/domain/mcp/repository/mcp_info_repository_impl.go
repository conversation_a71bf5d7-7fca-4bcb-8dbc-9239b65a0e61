package repository

import (
	"context"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/dto"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/entity"
	"gorm.io/gorm"
)

type McpInfoRepo struct {
	db *gorm.DB
}

func NewMcpInfoRepo(db *gorm.DB) *McpInfoRepo {
	return &McpInfoRepo{db: db}
}

// 创建记录
func (r *McpInfoRepo) Create(ctx context.Context, mcpInfo *entity.McpInfo) error {
	return r.db.WithContext(ctx).Create(mcpInfo).Error
}

// 根据 ID 获取记录
func (r *McpInfoRepo) GetByID(ctx context.Context, id int64) (*entity.McpInfo, error) {
	var mcp entity.McpInfo
	if err := r.db.WithContext(ctx).First(&mcp, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &mcp, nil
}

// 根据条件查询列表（带分页计数）
func (r *McpInfoRepo) List(ctx context.Context, filters map[string]interface{}) ([]*entity.McpInfo, int, error) {
	var (
		list  []*entity.McpInfo
		count int64
	)

	query := r.db.WithContext(ctx).Model(&entity.McpInfo{})
	for key, val := range filters {
		query = query.Where(key+" = ?", val)
	}

	// 先获取总数
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// 查询数据（如需分页可以加 .Offset().Limit()）
	if err := query.Order("created_at desc").Find(&list).Error; err != nil {
		return nil, 0, err
	}

	return list, int(count), nil
}

// 全量更新
func (r *McpInfoRepo) Update(ctx context.Context, mcpInfo *entity.McpInfo) error {
	return r.db.WithContext(ctx).Save(mcpInfo).Error
}

// 删除
func (r *McpInfoRepo) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&entity.McpInfo{}, id).Error
}

// 局部字段更新
func (r *McpInfoRepo) UpdateFields(ctx context.Context, id int64, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).
		Model(&entity.McpInfo{}).
		Where("id = ?", id).
		Updates(updates).Error
}

func (r *McpInfoRepo) GetMcpInfoByMcpId(ctx context.Context, id int64) (*dto.GetByMcpIdResponse, error) {
	var mcp entity.McpInfo

	// 用GORM根据id查询mcp_info表的记录
	if err := r.db.WithContext(ctx).First(&mcp, "id = ?", id).Error; err != nil {
		return nil, err
	}

	// 把entity.McpInfo转换成dto.GetByMcpIdResponse
	resp := &dto.GetByMcpIdResponse{
		Id:          mcp.ID,
		IconURI:     mcp.IconURI,
		ServerURL:   mcp.ServerURL,
		McpName:     mcp.McpName,
		Description: mcp.Description,
	}
	return resp, nil
}
