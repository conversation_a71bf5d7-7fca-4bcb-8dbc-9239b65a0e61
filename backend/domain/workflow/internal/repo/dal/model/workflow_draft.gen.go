// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameWorkflowDraft = "workflow_draft"

// WorkflowDraft workflow 画布草稿表，用于记录workflow最新的草稿画布信息
type WorkflowDraft struct {
	ID             int64          `gorm:"column:id;primaryKey;comment:workflow ID" json:"id"`                             // workflow ID
	Canvas         string         `gorm:"column:canvas;not null;comment:前端 schema" json:"canvas"`                         // 前端 schema
	InputParams    string         `gorm:"column:input_params;comment: 入参 schema" json:"input_params"`                     //  入参 schema
	OutputParams   string         `gorm:"column:output_params;comment: 出参 schema" json:"output_params"`                   //  出参 schema
	TestRunSuccess bool           `gorm:"column:test_run_success;not null;comment:0 未运行, 1 运行成功" json:"test_run_success"` // 0 未运行, 1 运行成功
	Modified       bool           `gorm:"column:modified;not null;comment:0 未被修改, 1 已被修改" json:"modified"`                // 0 未被修改, 1 已被修改
	UpdatedAt      int64          `gorm:"column:updated_at;autoUpdateTime:milli" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	CommitID       string         `gorm:"column:commit_id;not null;comment:used to uniquely identify a draft snapshot" json:"commit_id"` // used to uniquely identify a draft snapshot
}

// TableName WorkflowDraft's table name
func (*WorkflowDraft) TableName() string {
	return TableNameWorkflowDraft
}
