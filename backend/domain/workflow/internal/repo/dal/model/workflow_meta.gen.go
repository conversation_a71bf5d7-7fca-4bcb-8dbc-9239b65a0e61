// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"gorm.io/gorm"
)

const TableNameWorkflowMeta = "workflow_meta"

// WorkflowMeta workflow 元信息表，用于记录workflow基本的元信息
type WorkflowMeta struct {
	ID              int64          `gorm:"column:id;primaryKey;comment:workflow id" json:"id"`                                                                                                                                                                                                                      // workflow id
	Name            string         `gorm:"column:name;not null;comment:workflow name" json:"name"`                                                                                                                                                                                                                  // workflow name
	Description     string         `gorm:"column:description;not null;comment:workflow description" json:"description"`                                                                                                                                                                                             // workflow description
	IconURI         string         `gorm:"column:icon_uri;not null;comment:icon uri" json:"icon_uri"`                                                                                                                                                                                                               // icon uri
	Status          int32          `gorm:"column:status;not null;comment:0:未发布过, 1:已发布过" json:"status"`                                                                                                                                                                                                             // 0:未发布过, 1:已发布过
	ContentType     int32          `gorm:"column:content_type;not null;comment:0用户 1官方" json:"content_type"`                                                                                                                                                                                                        // 0用户 1官方
	Mode            int32          `gorm:"column:mode;not null;comment:0:workflow, 3:chat_flow" json:"mode"`                                                                                                                                                                                                        // 0:workflow, 3:chat_flow
	CreatedAt       int64          `gorm:"column:created_at;not null;autoCreateTime:milli;comment:create time in millisecond" json:"created_at"`                                                                                                                                                                    // create time in millisecond
	UpdatedAt       int64          `gorm:"column:updated_at;autoUpdateTime:milli;comment:update time in millisecond" json:"updated_at"`                                                                                                                                                                             // update time in millisecond
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;comment:delete time in millisecond" json:"deleted_at"`                                                                                                                                                                                                  // delete time in millisecond
	CreatorID       int64          `gorm:"column:creator_id;not null;comment:user id for creator" json:"creator_id"`                                                                                                                                                                                                // user id for creator
	Tag             int32          `gorm:"column:tag;comment:template tag: Tag: 1=All, 2=Hot, 3=Information, 4=Music, 5=Picture, 6=UtilityTool, 7=Life, 8=Traval, 9=Network, 10=System, 11=Movie, 12=Office, 13=Shopping, 14=Education, 15=Health, 16=Social, 17=Entertainment, 18=Finance, 100=Hidden" json:"tag"` // template tag: Tag: 1=All, 2=Hot, 3=Information, 4=Music, 5=Picture, 6=UtilityTool, 7=Life, 8=Traval, 9=Network, 10=System, 11=Movie, 12=Office, 13=Shopping, 14=Education, 15=Health, 16=Social, 17=Entertainment, 18=Finance, 100=Hidden
	AuthorID        int64          `gorm:"column:author_id;not null;comment:原作者用户 ID" json:"author_id"`                                                                                                                                                                                                             // 原作者用户 ID
	SpaceID         int64          `gorm:"column:space_id;not null;comment: 空间 ID" json:"space_id"`                                                                                                                                                                                                                 //  空间 ID
	UpdaterID       int64          `gorm:"column:updater_id;comment: 更新元信息的用户 ID" json:"updater_id"`                                                                                                                                                                                                                //  更新元信息的用户 ID
	SourceID        int64          `gorm:"column:source_id;comment: 复制来源的 workflow ID" json:"source_id"`                                                                                                                                                                                                            //  复制来源的 workflow ID
	AppID           int64          `gorm:"column:app_id;comment:应用 ID" json:"app_id"`                                                                                                                                                                                                                               // 应用 ID
	LatestVersion   string         `gorm:"column:latest_version;comment:the version of the most recent publish" json:"latest_version"`                                                                                                                                                                              // the version of the most recent publish
	LatestVersionTs int64          `gorm:"column:latest_version_ts;comment:create time of latest version" json:"latest_version_ts"`                                                                                                                                                                                 // create time of latest version
}

// TableName WorkflowMeta's table name
func (*WorkflowMeta) TableName() string {
	return TableNameWorkflowMeta
}
