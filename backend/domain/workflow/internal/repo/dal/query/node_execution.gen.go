// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/coze-dev/coze-studio/backend/domain/workflow/internal/repo/dal/model"
)

func newNodeExecution(db *gorm.DB, opts ...gen.DOOption) nodeExecution {
	_nodeExecution := nodeExecution{}

	_nodeExecution.nodeExecutionDo.UseDB(db, opts...)
	_nodeExecution.nodeExecutionDo.UseModel(&model.NodeExecution{})

	tableName := _nodeExecution.nodeExecutionDo.TableName()
	_nodeExecution.ALL = field.NewAsterisk(tableName)
	_nodeExecution.ID = field.NewInt64(tableName, "id")
	_nodeExecution.ExecuteID = field.NewInt64(tableName, "execute_id")
	_nodeExecution.NodeID = field.NewString(tableName, "node_id")
	_nodeExecution.NodeName = field.NewString(tableName, "node_name")
	_nodeExecution.NodeType = field.NewString(tableName, "node_type")
	_nodeExecution.CreatedAt = field.NewInt64(tableName, "created_at")
	_nodeExecution.Status = field.NewInt32(tableName, "status")
	_nodeExecution.Duration = field.NewInt64(tableName, "duration")
	_nodeExecution.Input = field.NewString(tableName, "input")
	_nodeExecution.Output = field.NewString(tableName, "output")
	_nodeExecution.RawOutput = field.NewString(tableName, "raw_output")
	_nodeExecution.ErrorInfo = field.NewString(tableName, "error_info")
	_nodeExecution.ErrorLevel = field.NewString(tableName, "error_level")
	_nodeExecution.InputTokens = field.NewInt64(tableName, "input_tokens")
	_nodeExecution.OutputTokens = field.NewInt64(tableName, "output_tokens")
	_nodeExecution.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_nodeExecution.CompositeNodeIndex = field.NewInt64(tableName, "composite_node_index")
	_nodeExecution.CompositeNodeItems = field.NewString(tableName, "composite_node_items")
	_nodeExecution.ParentNodeID = field.NewString(tableName, "parent_node_id")
	_nodeExecution.SubExecuteID = field.NewInt64(tableName, "sub_execute_id")
	_nodeExecution.Extra = field.NewString(tableName, "extra")

	_nodeExecution.fillFieldMap()

	return _nodeExecution
}

// nodeExecution node 节点运行记录，用于记录每次workflow执行时，每个节点的状态信息
type nodeExecution struct {
	nodeExecutionDo

	ALL                field.Asterisk
	ID                 field.Int64  // node execution id
	ExecuteID          field.Int64  // the workflow execute id this node execution belongs to
	NodeID             field.String // node key
	NodeName           field.String // name of the node
	NodeType           field.String // the type of the node, in string
	CreatedAt          field.Int64  // create time in millisecond
	Status             field.Int32  // 1=waiting 2=running 3=success 4=fail
	Duration           field.Int64  // execution duration in millisecond
	Input              field.String // actual input of the node
	Output             field.String // actual output of the node
	RawOutput          field.String // the original output of the node
	ErrorInfo          field.String // error info
	ErrorLevel         field.String // level of the error
	InputTokens        field.Int64  // number of input tokens
	OutputTokens       field.Int64  // number of output tokens
	UpdatedAt          field.Int64  // update time in millisecond
	CompositeNodeIndex field.Int64  // loop or batch's execution index
	CompositeNodeItems field.String // the items extracted from parent composite node for this index
	ParentNodeID       field.String // when as inner node for loop or batch, this is the parent node's key
	SubExecuteID       field.Int64  // if this node is sub_workflow, the exe id of the sub workflow
	Extra              field.String // extra info

	fieldMap map[string]field.Expr
}

func (n nodeExecution) Table(newTableName string) *nodeExecution {
	n.nodeExecutionDo.UseTable(newTableName)
	return n.updateTableName(newTableName)
}

func (n nodeExecution) As(alias string) *nodeExecution {
	n.nodeExecutionDo.DO = *(n.nodeExecutionDo.As(alias).(*gen.DO))
	return n.updateTableName(alias)
}

func (n *nodeExecution) updateTableName(table string) *nodeExecution {
	n.ALL = field.NewAsterisk(table)
	n.ID = field.NewInt64(table, "id")
	n.ExecuteID = field.NewInt64(table, "execute_id")
	n.NodeID = field.NewString(table, "node_id")
	n.NodeName = field.NewString(table, "node_name")
	n.NodeType = field.NewString(table, "node_type")
	n.CreatedAt = field.NewInt64(table, "created_at")
	n.Status = field.NewInt32(table, "status")
	n.Duration = field.NewInt64(table, "duration")
	n.Input = field.NewString(table, "input")
	n.Output = field.NewString(table, "output")
	n.RawOutput = field.NewString(table, "raw_output")
	n.ErrorInfo = field.NewString(table, "error_info")
	n.ErrorLevel = field.NewString(table, "error_level")
	n.InputTokens = field.NewInt64(table, "input_tokens")
	n.OutputTokens = field.NewInt64(table, "output_tokens")
	n.UpdatedAt = field.NewInt64(table, "updated_at")
	n.CompositeNodeIndex = field.NewInt64(table, "composite_node_index")
	n.CompositeNodeItems = field.NewString(table, "composite_node_items")
	n.ParentNodeID = field.NewString(table, "parent_node_id")
	n.SubExecuteID = field.NewInt64(table, "sub_execute_id")
	n.Extra = field.NewString(table, "extra")

	n.fillFieldMap()

	return n
}

func (n *nodeExecution) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := n.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (n *nodeExecution) fillFieldMap() {
	n.fieldMap = make(map[string]field.Expr, 21)
	n.fieldMap["id"] = n.ID
	n.fieldMap["execute_id"] = n.ExecuteID
	n.fieldMap["node_id"] = n.NodeID
	n.fieldMap["node_name"] = n.NodeName
	n.fieldMap["node_type"] = n.NodeType
	n.fieldMap["created_at"] = n.CreatedAt
	n.fieldMap["status"] = n.Status
	n.fieldMap["duration"] = n.Duration
	n.fieldMap["input"] = n.Input
	n.fieldMap["output"] = n.Output
	n.fieldMap["raw_output"] = n.RawOutput
	n.fieldMap["error_info"] = n.ErrorInfo
	n.fieldMap["error_level"] = n.ErrorLevel
	n.fieldMap["input_tokens"] = n.InputTokens
	n.fieldMap["output_tokens"] = n.OutputTokens
	n.fieldMap["updated_at"] = n.UpdatedAt
	n.fieldMap["composite_node_index"] = n.CompositeNodeIndex
	n.fieldMap["composite_node_items"] = n.CompositeNodeItems
	n.fieldMap["parent_node_id"] = n.ParentNodeID
	n.fieldMap["sub_execute_id"] = n.SubExecuteID
	n.fieldMap["extra"] = n.Extra
}

func (n nodeExecution) clone(db *gorm.DB) nodeExecution {
	n.nodeExecutionDo.ReplaceConnPool(db.Statement.ConnPool)
	return n
}

func (n nodeExecution) replaceDB(db *gorm.DB) nodeExecution {
	n.nodeExecutionDo.ReplaceDB(db)
	return n
}

type nodeExecutionDo struct{ gen.DO }

type INodeExecutionDo interface {
	gen.SubQuery
	Debug() INodeExecutionDo
	WithContext(ctx context.Context) INodeExecutionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() INodeExecutionDo
	WriteDB() INodeExecutionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) INodeExecutionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) INodeExecutionDo
	Not(conds ...gen.Condition) INodeExecutionDo
	Or(conds ...gen.Condition) INodeExecutionDo
	Select(conds ...field.Expr) INodeExecutionDo
	Where(conds ...gen.Condition) INodeExecutionDo
	Order(conds ...field.Expr) INodeExecutionDo
	Distinct(cols ...field.Expr) INodeExecutionDo
	Omit(cols ...field.Expr) INodeExecutionDo
	Join(table schema.Tabler, on ...field.Expr) INodeExecutionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) INodeExecutionDo
	RightJoin(table schema.Tabler, on ...field.Expr) INodeExecutionDo
	Group(cols ...field.Expr) INodeExecutionDo
	Having(conds ...gen.Condition) INodeExecutionDo
	Limit(limit int) INodeExecutionDo
	Offset(offset int) INodeExecutionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) INodeExecutionDo
	Unscoped() INodeExecutionDo
	Create(values ...*model.NodeExecution) error
	CreateInBatches(values []*model.NodeExecution, batchSize int) error
	Save(values ...*model.NodeExecution) error
	First() (*model.NodeExecution, error)
	Take() (*model.NodeExecution, error)
	Last() (*model.NodeExecution, error)
	Find() ([]*model.NodeExecution, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.NodeExecution, err error)
	FindInBatches(result *[]*model.NodeExecution, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.NodeExecution) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) INodeExecutionDo
	Assign(attrs ...field.AssignExpr) INodeExecutionDo
	Joins(fields ...field.RelationField) INodeExecutionDo
	Preload(fields ...field.RelationField) INodeExecutionDo
	FirstOrInit() (*model.NodeExecution, error)
	FirstOrCreate() (*model.NodeExecution, error)
	FindByPage(offset int, limit int) (result []*model.NodeExecution, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) INodeExecutionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (n nodeExecutionDo) Debug() INodeExecutionDo {
	return n.withDO(n.DO.Debug())
}

func (n nodeExecutionDo) WithContext(ctx context.Context) INodeExecutionDo {
	return n.withDO(n.DO.WithContext(ctx))
}

func (n nodeExecutionDo) ReadDB() INodeExecutionDo {
	return n.Clauses(dbresolver.Read)
}

func (n nodeExecutionDo) WriteDB() INodeExecutionDo {
	return n.Clauses(dbresolver.Write)
}

func (n nodeExecutionDo) Session(config *gorm.Session) INodeExecutionDo {
	return n.withDO(n.DO.Session(config))
}

func (n nodeExecutionDo) Clauses(conds ...clause.Expression) INodeExecutionDo {
	return n.withDO(n.DO.Clauses(conds...))
}

func (n nodeExecutionDo) Returning(value interface{}, columns ...string) INodeExecutionDo {
	return n.withDO(n.DO.Returning(value, columns...))
}

func (n nodeExecutionDo) Not(conds ...gen.Condition) INodeExecutionDo {
	return n.withDO(n.DO.Not(conds...))
}

func (n nodeExecutionDo) Or(conds ...gen.Condition) INodeExecutionDo {
	return n.withDO(n.DO.Or(conds...))
}

func (n nodeExecutionDo) Select(conds ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.Select(conds...))
}

func (n nodeExecutionDo) Where(conds ...gen.Condition) INodeExecutionDo {
	return n.withDO(n.DO.Where(conds...))
}

func (n nodeExecutionDo) Order(conds ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.Order(conds...))
}

func (n nodeExecutionDo) Distinct(cols ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.Distinct(cols...))
}

func (n nodeExecutionDo) Omit(cols ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.Omit(cols...))
}

func (n nodeExecutionDo) Join(table schema.Tabler, on ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.Join(table, on...))
}

func (n nodeExecutionDo) LeftJoin(table schema.Tabler, on ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.LeftJoin(table, on...))
}

func (n nodeExecutionDo) RightJoin(table schema.Tabler, on ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.RightJoin(table, on...))
}

func (n nodeExecutionDo) Group(cols ...field.Expr) INodeExecutionDo {
	return n.withDO(n.DO.Group(cols...))
}

func (n nodeExecutionDo) Having(conds ...gen.Condition) INodeExecutionDo {
	return n.withDO(n.DO.Having(conds...))
}

func (n nodeExecutionDo) Limit(limit int) INodeExecutionDo {
	return n.withDO(n.DO.Limit(limit))
}

func (n nodeExecutionDo) Offset(offset int) INodeExecutionDo {
	return n.withDO(n.DO.Offset(offset))
}

func (n nodeExecutionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) INodeExecutionDo {
	return n.withDO(n.DO.Scopes(funcs...))
}

func (n nodeExecutionDo) Unscoped() INodeExecutionDo {
	return n.withDO(n.DO.Unscoped())
}

func (n nodeExecutionDo) Create(values ...*model.NodeExecution) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Create(values)
}

func (n nodeExecutionDo) CreateInBatches(values []*model.NodeExecution, batchSize int) error {
	return n.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (n nodeExecutionDo) Save(values ...*model.NodeExecution) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Save(values)
}

func (n nodeExecutionDo) First() (*model.NodeExecution, error) {
	if result, err := n.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.NodeExecution), nil
	}
}

func (n nodeExecutionDo) Take() (*model.NodeExecution, error) {
	if result, err := n.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.NodeExecution), nil
	}
}

func (n nodeExecutionDo) Last() (*model.NodeExecution, error) {
	if result, err := n.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.NodeExecution), nil
	}
}

func (n nodeExecutionDo) Find() ([]*model.NodeExecution, error) {
	result, err := n.DO.Find()
	return result.([]*model.NodeExecution), err
}

func (n nodeExecutionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.NodeExecution, err error) {
	buf := make([]*model.NodeExecution, 0, batchSize)
	err = n.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (n nodeExecutionDo) FindInBatches(result *[]*model.NodeExecution, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return n.DO.FindInBatches(result, batchSize, fc)
}

func (n nodeExecutionDo) Attrs(attrs ...field.AssignExpr) INodeExecutionDo {
	return n.withDO(n.DO.Attrs(attrs...))
}

func (n nodeExecutionDo) Assign(attrs ...field.AssignExpr) INodeExecutionDo {
	return n.withDO(n.DO.Assign(attrs...))
}

func (n nodeExecutionDo) Joins(fields ...field.RelationField) INodeExecutionDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Joins(_f))
	}
	return &n
}

func (n nodeExecutionDo) Preload(fields ...field.RelationField) INodeExecutionDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Preload(_f))
	}
	return &n
}

func (n nodeExecutionDo) FirstOrInit() (*model.NodeExecution, error) {
	if result, err := n.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.NodeExecution), nil
	}
}

func (n nodeExecutionDo) FirstOrCreate() (*model.NodeExecution, error) {
	if result, err := n.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.NodeExecution), nil
	}
}

func (n nodeExecutionDo) FindByPage(offset int, limit int) (result []*model.NodeExecution, count int64, err error) {
	result, err = n.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = n.Offset(-1).Limit(-1).Count()
	return
}

func (n nodeExecutionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = n.Count()
	if err != nil {
		return
	}

	err = n.Offset(offset).Limit(limit).Scan(result)
	return
}

func (n nodeExecutionDo) Scan(result interface{}) (err error) {
	return n.DO.Scan(result)
}

func (n nodeExecutionDo) Delete(models ...*model.NodeExecution) (result gen.ResultInfo, err error) {
	return n.DO.Delete(models)
}

func (n *nodeExecutionDo) withDO(do gen.Dao) *nodeExecutionDo {
	n.DO = *do.(*gen.DO)
	return n
}
