package mcptool

import (
	"github.com/coze-dev/coze-studio/backend/domain/mcptool/repository"
	"github.com/coze-dev/coze-studio/backend/domain/mcptool/service"
	"gorm.io/gorm"
)

type McpToolApplicationService struct {
	DomainSVC *service.McpToolServiceImpl
}

var McpToolSVC = &McpToolApplicationService{}

func InitMcpToolService(db *gorm.DB) {
	// 初始化任务服务的领域服务
	// 这里需要注入数据库连接和领域服务

	// 创建 TaskRepository 实例
	mcpIfnRepo := repository.NewMcpToolRepo(db)

	// 创建 TaskService 实例并注入 repository
	toolService := service.NewMcpToolService(mcpIfnRepo)

	// 设置到全局变量
	McpToolSVC.DomainSVC = toolService
}

//func (p *McpApplicationService) CreateMcpInfo(ctx context.Context, req *service.CreateMcpToolRequest) (*entity.McpInfo, error) {
//	return p.DomainSVC.CreateMcpInfo(ctx, req)
//}
