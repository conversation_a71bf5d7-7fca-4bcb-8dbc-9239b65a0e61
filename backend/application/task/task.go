package task

import (
    "context"
    "time"
    taskModel "github.com/coze-dev/coze-studio/backend/api/model/task"
    "github.com/coze-dev/coze-studio/backend/application/base/ctxutil"
    "github.com/coze-dev/coze-studio/backend/domain/task/entity"
    "github.com/coze-dev/coze-studio/backend/domain/task/service"
    taskRepoImpl "github.com/coze-dev/coze-studio/backend/infra/impl/task"
    "github.com/coze-dev/coze-studio/backend/pkg/errorx"
    "github.com/coze-dev/coze-studio/backend/types/errno"
    "gorm.io/gorm"
)

type TaskApplicationService struct {
    DomainSVC *service.TaskService
}

var TaskSVC = &TaskApplicationService{}

func InitTaskService(db *gorm.DB) {
    // 初始化任务服务的领域服务
    // 这里需要注入数据库连接和领域服务
    
    // 创建 TaskRepository 实例
    taskRepo := taskRepoImpl.NewTaskRepository(db)
    
    // 创建 TaskService 实例并注入 repository
    taskService := service.NewTaskService(taskRepo)
    
    // 设置到全局变量
    TaskSVC.DomainSVC = taskService
}

func (t *TaskApplicationService) CreateTask(ctx context.Context, req *taskModel.CreateTaskRequest) (*taskModel.CreateTaskResponse, error) {
    uid := ctxutil.GetUIDFromCtx(ctx)
    if uid == nil {
        return nil, errorx.New(errno.ErrTaskPermissionCode, errorx.KV("msg", "session required"))
    }

    domainReq := &service.CreateTaskRequest{
        Name:        req.Name,
        Description: req.Description,
        Priority:    convertPriority(req.Priority),
        DueDate:     convertTime(req.DueDate),
        SpaceID:     *req.SpaceID,
        ProjectID:   req.ProjectID,
        CreatorID:   *uid,
    }

    domainTask, err := t.DomainSVC.CreateTask(ctx, domainReq)
    if err != nil {
        return taskModel.NewCreateTaskResponse(), err
    }

    resp := taskModel.NewCreateTaskResponse()
    resp.Task = convertTaskToResponse(domainTask)
    return resp, nil
}

// 转换 Thrift 优先级到领域优先级
func convertPriority(thriftPriority taskModel.TaskPriority) entity.TaskPriority {
    return entity.TaskPriority(thriftPriority)
}

// 转换 Thrift 优先级指针到领域优先级
func convertPriorityPtr(thriftPriority *taskModel.TaskPriority) *entity.TaskPriority {
    if thriftPriority == nil {
        return nil
    }
    priority := entity.TaskPriority(*thriftPriority)
    return &priority
}

// 转换 Thrift 时间（int64 时间戳）到 time.Time
func convertTime(timestamp *int64) *time.Time {
    if timestamp == nil {
        return nil
    }
    // 前端发送的是毫秒级时间戳，需要转换为秒级
    t := time.Unix(*timestamp/1000, 0)
    return &t
}

// 转换 Thrift 状态到领域状态
func convertStatus(thriftStatus *taskModel.TaskStatus) *entity.TaskStatus {
    if thriftStatus == nil {
        return nil
    }
    status := entity.TaskStatus(*thriftStatus)
    return &status
}

// 转换领域 Task 到 Thrift 响应的 TaskInfo
func convertTaskToResponse(domainTask *entity.Task) *taskModel.TaskInfo {
    if domainTask == nil {
        return nil
    }
    // 转换时间戳（假设领域模型的 DueDate 是 *time.Time）
    var dueDate *int64
    if domainTask.DueDate != nil {
        ts := domainTask.DueDate.Unix()
        dueDate = &ts
    }
    createdAt := domainTask.CreatedAt.Unix()
    updatedAt := domainTask.UpdatedAt.Unix()
    spaceID := domainTask.SpaceID
    projectID := domainTask.ProjectID
    creatorID := domainTask.CreatorID
    
    return &taskModel.TaskInfo{
        ID:          domainTask.ID,
        Name:        domainTask.Name,
        Description: domainTask.Description,
        Status:      taskModel.TaskStatus(domainTask.Status),
        Priority:    taskModel.TaskPriority(domainTask.Priority),
        DueDate:     dueDate,
        CreatedAt:   &createdAt,
        UpdatedAt:   &updatedAt,
        SpaceID:     &spaceID,
        ProjectID:   projectID,
        CreatorID:   &creatorID,
    }
}

func (t *TaskApplicationService) ListTasks(ctx context.Context, req *taskModel.ListTaskRequest) (*taskModel.ListTaskResponse, error) {
    uid := ctxutil.GetUIDFromCtx(ctx)
    if uid == nil {
        return nil, errorx.New(errno.ErrTaskPermissionCode, errorx.KV("msg", "session required"))
    }

    // 设置默认值
    spaceID := int64(1) // 默认空间ID
    if req.SpaceID != nil {
        spaceID = *req.SpaceID
    }
    
    page := int32(1) // 默认页码
    if req.Page != nil {
        page = *req.Page
    }
    
    pageSize := int32(20) // 默认页面大小
    if req.PageSize != nil {
        pageSize = *req.PageSize
    }

    domainReq := &service.ListTaskRequest{
        SpaceID:   spaceID,
        ProjectID: req.ProjectID,
        Status:    convertStatus(req.Status),
        Priority:  convertPriorityPtr(req.Priority),
        Page:      int(page),
        PageSize:  int(pageSize),
    }

    domainTasks, total, err := t.DomainSVC.ListTasks(ctx, domainReq)
    if err != nil {
        return taskModel.NewListTaskResponse(), err
    }

    resp := taskModel.NewListTaskResponse()
    resp.Tasks = make([]*taskModel.TaskInfo, len(domainTasks))
    for i, domainTask := range domainTasks {
        resp.Tasks[i] = convertTaskToResponse(domainTask)
    }
    totalInt32 := int32(total)
    resp.Total = &totalInt32
    resp.Page = &page
    resp.PageSize = &pageSize

    return resp, nil
}

func (t *TaskApplicationService) UpdateTask(ctx context.Context, req *taskModel.UpdateTaskRequest) (*taskModel.UpdateTaskResponse, error) {
    uid := ctxutil.GetUIDFromCtx(ctx)
    if uid == nil {
        return nil, errorx.New(errno.ErrTaskPermissionCode, errorx.KV("msg", "session required"))
    }

    // 设置默认空间ID
    spaceID := int64(1)
    if req.SpaceID != nil {
        spaceID = *req.SpaceID
    }

    domainReq := &service.UpdateTaskRequest{
        ID:          req.ID,
        Name:        req.Name,
        Description: req.Description,
        Status:      convertStatus(req.Status),
        Priority:    convertPriorityPtr(req.Priority),
        DueDate:     convertTime(req.DueDate),
        SpaceID:     spaceID,
        CreatorID:   *uid,
    }

    domainTask, err := t.DomainSVC.UpdateTask(ctx, domainReq)
    if err != nil {
        return nil, err
    }

    resp := taskModel.NewUpdateTaskResponse()
    resp.Task = convertTaskToResponse(domainTask)
    return resp, nil
}

func (t *TaskApplicationService) DeleteTask(ctx context.Context, req *taskModel.DeleteTaskRequest) (*taskModel.DeleteTaskResponse, error) {
    uid := ctxutil.GetUIDFromCtx(ctx)
    if uid == nil {
        return nil, errorx.New(errno.ErrTaskPermissionCode, errorx.KV("msg", "session required"))
    }

    // 设置默认空间ID
    spaceID := int64(1)
    if req.SpaceID != nil {
        spaceID = *req.SpaceID
    }

    domainReq := &service.DeleteTaskRequest{
        ID:        req.ID,
        SpaceID:   spaceID,
        CreatorID: *uid,
    }

    err := t.DomainSVC.DeleteTask(ctx, domainReq)
    if err != nil {
        return nil, err
    }

    resp := taskModel.NewDeleteTaskResponse()
    resp.Code = 0
    resp.Message = "删除任务成功"
    return resp, nil
}
