package mcp

import (
	"context"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/dto"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/entity"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/repository"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/service"
	mcpService "github.com/coze-dev/coze-studio/backend/domain/mcptool/service"
	"gorm.io/gorm"
)

type McpApplicationService struct {
	DomainSVC *service.McpInfoService
}

var McpInfoSVC = &McpApplicationService{}

func InitMcpInfoService(db *gorm.DB, mcpToolService *mcpService.McpToolServiceImpl) {
	// 初始化任务服务的领域服务
	// 这里需要注入数据库连接和领域服务

	// 创建 TaskRepository 实例
	mcpIfnRepo := repository.NewMcpInfoRepo(db)

	// 创建 TaskService 实例并注入 repository
	mcpInfoService := service.NewMcpInfoService(mcpIfnRepo, mcpToolService)

	// 设置到全局变量
	McpInfoSVC.DomainSVC = mcpInfoService
}

func (p *McpApplicationService) CreateMcpInfo(ctx context.Context, req *dto.CreateMcpInfoRequest) (*entity.McpInfo, error) {
	return p.DomainSVC.CreateMcpInfo(ctx, req)
}

func (p *McpApplicationService) GetByMcpId(ctx context.Context, s *dto.GetByMcpIdRequest) (*dto.GetByMcpIdResponse, error) {
	return p.DomainSVC.GetByMcpId(ctx, s)
}

func (p *McpApplicationService) UpdateMcpToolByMcpId(ctx context.Context, s *dto.GetByMcpIdRequest) (*dto.GetByMcpIdResponse, error) {
	return p.DomainSVC.UpdateMcpToolByMcpId(ctx, s)
}
