package task

import (
    "context"
    "github.com/coze-dev/coze-studio/backend/domain/task/entity"
    "github.com/coze-dev/coze-studio/backend/domain/task/repository"
    "gorm.io/gorm"
)

type TaskRepositoryImpl struct {
    db *gorm.DB
}

func NewTaskRepository(db *gorm.DB) repository.TaskRepository {
    return &TaskRepositoryImpl{db: db}
}

func (r *TaskRepositoryImpl) Create(ctx context.Context, task *entity.Task) error {
    result := r.db.WithContext(ctx).Create(task)
    if result.Error != nil {
        return result.Error
    }
    
    // GORM 会自动设置 ID
    return nil
}

func (r *TaskRepositoryImpl) GetByID(ctx context.Context, id int64) (*entity.Task, error) {
    var task entity.Task
    result := r.db.WithContext(ctx).Model(&entity.Task{}).Where("id = ?", id).First(&task)
    if result.Error != nil {
        return nil, result.Error
    }
    return &task, nil
}

func (r *TaskRepositoryImpl) List(ctx context.Context, spaceID int64, filters map[string]interface{}) ([]*entity.Task, int, error) {
    query := r.db.WithContext(ctx).Model(&entity.Task{}).Where("space_id = ?", spaceID)
    
    // 添加过滤条件
    if projectID, ok := filters["project_id"]; ok && projectID != nil {
        query = query.Where("project_id = ?", projectID)
    }
    if status, ok := filters["status"]; ok && status != nil {
        query = query.Where("status = ?", status)
    }
    if priority, ok := filters["priority"]; ok && priority != nil {
        query = query.Where("priority = ?", priority)
    }
    
    // 获取总数
    var total int64
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // 添加分页
    pageSize := 20
    page := 0
    if pageSizeVal, ok := filters["page_size"]; ok && pageSizeVal != nil {
        pageSize = pageSizeVal.(int)
    }
    if pageVal, ok := filters["page"]; ok && pageVal != nil {
        page = pageVal.(int)
    }
    
    var tasks []*entity.Task
    result := query.Order("created_at DESC").
        Limit(pageSize).
        Offset(page * pageSize).
        Find(&tasks)
    
    if result.Error != nil {
        return nil, 0, result.Error
    }
    
    return tasks, int(total), nil
}

func (r *TaskRepositoryImpl) Update(ctx context.Context, task *entity.Task) error {
    result := r.db.WithContext(ctx).Save(task)
    return result.Error
}

func (r *TaskRepositoryImpl) Delete(ctx context.Context, id int64) error {
    result := r.db.WithContext(ctx).Model(&entity.Task{}).Delete(&entity.Task{}, id)
    return result.Error
}
