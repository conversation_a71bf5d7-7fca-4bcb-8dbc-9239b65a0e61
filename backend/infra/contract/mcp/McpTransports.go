package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/modelcontextprotocol/go-sdk/mcp"
)

// 获取 MCP 工具列表
func FetchMCPTools(httpAddr string) (*mcp.ListToolsResult, error) {
	fmt.Println("正在连接 MCP 服务:", httpAddr)

	impl := &mcp.Implementation{
		Name:    "ibi_ddzg",
		Title:   "ibi",
		Version: "1.0.0",
	}

	opts := &mcp.ClientOptions{
		KeepAlive: 30 * time.Second,
	}

	client := mcp.NewClient(impl, opts)
	optss := &mcp.SSEClientTransportOptions{
		HTTPClient: &http.Client{
			Timeout: 5 * time.Second,
		},
	}

	transport := mcp.NewSSEClientTransport(httpAddr, optss)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	session, err := client.Connect(ctx, transport)
	if err != nil {
		return nil, fmt.Errorf("连接 MCP 服务失败: %w", err)
	}
	defer session.Close()

	toolsResult, err := session.ListTools(ctx, &mcp.ListToolsParams{})
	if err != nil {
		return nil, fmt.Errorf("调用 ListTools 失败: %w", err)
	}

	return toolsResult, nil
}

// 执行 MCP 工具
func CallMCPTool(httpAddr string, params *mcp.CallToolParams) (*mcp.CallToolResult, error) {
	fmt.Println("正在连接 MCP 服务:", httpAddr)

	impl := &mcp.Implementation{
		Name:    "ibi_ddzg",
		Title:   "ibi",
		Version: "1.0.0",
	}

	opts := &mcp.ClientOptions{
		KeepAlive: 30 * time.Second,
	}

	client := mcp.NewClient(impl, opts)
	optss := &mcp.SSEClientTransportOptions{
		HTTPClient: &http.Client{
			Timeout: 5 * time.Second,
		},
	}

	transport := mcp.NewSSEClientTransport(httpAddr, optss)

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	session, err := client.Connect(ctx, transport)
	if err != nil {
		return nil, fmt.Errorf("连接 MCP 服务失败: %w", err)
	}
	defer session.Close()

	// 执行工具
	result, err := session.CallTool(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("调用工具失败: %w", err)
	}

	return result, nil
}

func GetFirstTextContent[T any](result *mcp.CallToolResultFor[T]) (string, error) {
	if len(result.Content) == 0 {
		return "", fmt.Errorf("Content 为空")
	}
	textContent, ok := result.Content[0].(*mcp.TextContent)
	if !ok {
		return "", fmt.Errorf("第一个 Content 不是 TextContent 类型")
	}
	return textContent.Text, nil
}

func main() {
	// MCP 服务地址
	addr := "https://testcompanymanage.ptdplat.com/api/sse"

	tools, err2 := FetchMCPTools(addr)
	if err2 != nil {
		log.Fatalf("获取 MCP 工具列表失败: %v", err2)
	}
	marshal, err2 := json.Marshal(tools)
	str := string(marshal)
	fmt.Println("工具列表:", str)

	// 构造测试参数
	params := &mcp.CallToolParams{
		Name: "companySupplyMarketingPage", // MCP 工具名称
		Arguments: map[string]interface{}{
			"current": 1,  // 页码，默认1
			"size":    10, // 页大小，默认10
			"sCompanySupplyMarketing": map[string]interface{}{ // 查询条件
				"transactionType": 1, // 1为采购 | 2为销售
				//"commodityBrand":          "测试品牌",           // 商品品牌
				//"commodityCategory":       "测试品类",           // 商品品类
				//"whetherPermitTransaction": 1,                  // 1为允许交易
				//"companyInfoName":         "测试企业",           // 企业名称
				//"commodityName":           "测试商品",           // 商品名称
			},
		},
	}

	// 调用工具
	result, err := CallMCPTool(addr, params)
	if err != nil {
		log.Fatalf("执行工具失败: %v", err)
	}

	content, err := GetFirstTextContent(result)
	fmt.Println("工具执行结果:", content)
}
