package mcp

import (
	"context"
	"github.com/coze-dev/coze-studio/backend/crossdomain/contract/crossmcp"
	"github.com/coze-dev/coze-studio/backend/domain/mcp/dto"
	mcp "github.com/coze-dev/coze-studio/backend/domain/mcp/service"
)

var defaultSVC crossmcp.McpService

type impl struct {
	DomainSVC *mcp.McpInfoService
}

func InitDomainService(c *mcp.McpInfoService) crossmcp.McpService {
	defaultSVC = &impl{
		DomainSVC: c,
	}

	return defaultSVC
}

func (s *impl) GetOlineTools(ctx context.Context, toolIDs []int64) ([]dto.GetToolByMcpIdResponse, error) {
	return s.DomainSVC.GetByIds(ctx, toolIDs)
}
