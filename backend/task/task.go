// Code generated by thriftgo (0.4.2). DO NOT EDIT.

package task

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	"github.com/apache/thrift/lib/go/thrift"
)

type TaskStatus int64

const (
	TaskStatus_PENDING     TaskStatus = 1
	TaskStatus_IN_PROGRESS TaskStatus = 2
	TaskStatus_COMPLETED   TaskStatus = 3
	TaskStatus_CANCELLED   TaskStatus = 4
)

func (p TaskStatus) String() string {
	switch p {
	case TaskStatus_PENDING:
		return "PENDING"
	case TaskStatus_IN_PROGRESS:
		return "IN_PROGRESS"
	case TaskStatus_COMPLETED:
		return "COMPLETED"
	case TaskStatus_CANCELLED:
		return "CANCELLED"
	}
	return "<UNSET>"
}

func TaskStatusFromString(s string) (TaskStatus, error) {
	switch s {
	case "PENDING":
		return TaskStatus_PENDING, nil
	case "IN_PROGRESS":
		return TaskStatus_IN_PROGRESS, nil
	case "COMPLETED":
		return TaskStatus_COMPLETED, nil
	case "CANCELLED":
		return TaskStatus_CANCELLED, nil
	}
	return TaskStatus(0), fmt.Errorf("not a valid TaskStatus string")
}

func TaskStatusPtr(v TaskStatus) *TaskStatus { return &v }
func (p *TaskStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TaskStatus(result.Int64)
	return
}

func (p *TaskStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type TaskPriority int64

const (
	TaskPriority_LOW    TaskPriority = 1
	TaskPriority_MEDIUM TaskPriority = 2
	TaskPriority_HIGH   TaskPriority = 3
	TaskPriority_URGENT TaskPriority = 4
)

func (p TaskPriority) String() string {
	switch p {
	case TaskPriority_LOW:
		return "LOW"
	case TaskPriority_MEDIUM:
		return "MEDIUM"
	case TaskPriority_HIGH:
		return "HIGH"
	case TaskPriority_URGENT:
		return "URGENT"
	}
	return "<UNSET>"
}

func TaskPriorityFromString(s string) (TaskPriority, error) {
	switch s {
	case "LOW":
		return TaskPriority_LOW, nil
	case "MEDIUM":
		return TaskPriority_MEDIUM, nil
	case "HIGH":
		return TaskPriority_HIGH, nil
	case "URGENT":
		return TaskPriority_URGENT, nil
	}
	return TaskPriority(0), fmt.Errorf("not a valid TaskPriority string")
}

func TaskPriorityPtr(v TaskPriority) *TaskPriority { return &v }
func (p *TaskPriority) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = TaskPriority(result.Int64)
	return
}

func (p *TaskPriority) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type CreateTaskRequest struct {
	Name        string        `thrift:"name,1,required" json:"name"`
	Description *string       `thrift:"description,2,optional" json:"description,omitempty"`
	Priority    *TaskPriority `thrift:"priority,3,optional" json:"priority,omitempty"`
	DueDate     *int64        `thrift:"due_date,4,optional" json:"due_date,omitempty"`
	SpaceID     *int64        `thrift:"space_id,5,optional" json:"space_id,omitempty"`
	ProjectID   *int64        `thrift:"project_id,6,optional" json:"project_id,omitempty"`
}

func NewCreateTaskRequest() *CreateTaskRequest {
	return &CreateTaskRequest{}
}

func (p *CreateTaskRequest) InitDefault() {
}

func (p *CreateTaskRequest) GetName() (v string) {
	return p.Name
}

var CreateTaskRequest_Description_DEFAULT string

func (p *CreateTaskRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return CreateTaskRequest_Description_DEFAULT
	}
	return *p.Description
}

var CreateTaskRequest_Priority_DEFAULT TaskPriority

func (p *CreateTaskRequest) GetPriority() (v TaskPriority) {
	if !p.IsSetPriority() {
		return CreateTaskRequest_Priority_DEFAULT
	}
	return *p.Priority
}

var CreateTaskRequest_DueDate_DEFAULT int64

func (p *CreateTaskRequest) GetDueDate() (v int64) {
	if !p.IsSetDueDate() {
		return CreateTaskRequest_DueDate_DEFAULT
	}
	return *p.DueDate
}

var CreateTaskRequest_SpaceID_DEFAULT int64

func (p *CreateTaskRequest) GetSpaceID() (v int64) {
	if !p.IsSetSpaceID() {
		return CreateTaskRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var CreateTaskRequest_ProjectID_DEFAULT int64

func (p *CreateTaskRequest) GetProjectID() (v int64) {
	if !p.IsSetProjectID() {
		return CreateTaskRequest_ProjectID_DEFAULT
	}
	return *p.ProjectID
}

var fieldIDToName_CreateTaskRequest = map[int16]string{
	1: "name",
	2: "description",
	3: "priority",
	4: "due_date",
	5: "space_id",
	6: "project_id",
}

func (p *CreateTaskRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *CreateTaskRequest) IsSetPriority() bool {
	return p.Priority != nil
}

func (p *CreateTaskRequest) IsSetDueDate() bool {
	return p.DueDate != nil
}

func (p *CreateTaskRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *CreateTaskRequest) IsSetProjectID() bool {
	return p.ProjectID != nil
}

func (p *CreateTaskRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetName {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTaskRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTaskRequest[fieldId]))
}

func (p *CreateTaskRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *CreateTaskRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *CreateTaskRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *TaskPriority
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskPriority(v)
		_field = &tmp
	}
	p.Priority = _field
	return nil
}
func (p *CreateTaskRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DueDate = _field
	return nil
}
func (p *CreateTaskRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SpaceID = _field
	return nil
}
func (p *CreateTaskRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectID = _field
	return nil
}

func (p *CreateTaskRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTaskRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTaskRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateTaskRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateTaskRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPriority() {
		if err = oprot.WriteFieldBegin("priority", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Priority)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *CreateTaskRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetDueDate() {
		if err = oprot.WriteFieldBegin("due_date", thrift.I64, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.DueDate); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *CreateTaskRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpaceID() {
		if err = oprot.WriteFieldBegin("space_id", thrift.I64, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SpaceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *CreateTaskRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectID() {
		if err = oprot.WriteFieldBegin("project_id", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ProjectID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *CreateTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTaskRequest(%+v)", *p)

}

type CreateTaskResponse struct {
	Code    int32     `thrift:"code,1,required" json:"code"`
	Message *string   `thrift:"message,2,optional" json:"message,omitempty"`
	Task    *TaskInfo `thrift:"task,3,optional" json:"task,omitempty"`
}

func NewCreateTaskResponse() *CreateTaskResponse {
	return &CreateTaskResponse{}
}

func (p *CreateTaskResponse) InitDefault() {
}

func (p *CreateTaskResponse) GetCode() (v int32) {
	return p.Code
}

var CreateTaskResponse_Message_DEFAULT string

func (p *CreateTaskResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return CreateTaskResponse_Message_DEFAULT
	}
	return *p.Message
}

var CreateTaskResponse_Task_DEFAULT *TaskInfo

func (p *CreateTaskResponse) GetTask() (v *TaskInfo) {
	if !p.IsSetTask() {
		return CreateTaskResponse_Task_DEFAULT
	}
	return p.Task
}

var fieldIDToName_CreateTaskResponse = map[int16]string{
	1: "code",
	2: "message",
	3: "task",
}

func (p *CreateTaskResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *CreateTaskResponse) IsSetTask() bool {
	return p.Task != nil
}

func (p *CreateTaskResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateTaskResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateTaskResponse[fieldId]))
}

func (p *CreateTaskResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *CreateTaskResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *CreateTaskResponse) ReadField3(iprot thrift.TProtocol) error {
	_field := NewTaskInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Task = _field
	return nil
}

func (p *CreateTaskResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("CreateTaskResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateTaskResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *CreateTaskResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *CreateTaskResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTask() {
		if err = oprot.WriteFieldBegin("task", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Task.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateTaskResponse(%+v)", *p)

}

type TaskInfo struct {
	ID          int64         `thrift:"id,1,required" json:"id"`
	Name        string        `thrift:"name,2,required" json:"name"`
	Description *string       `thrift:"description,3,optional" json:"description,omitempty"`
	Status      *TaskStatus   `thrift:"status,4,optional" json:"status,omitempty"`
	Priority    *TaskPriority `thrift:"priority,5,optional" json:"priority,omitempty"`
	DueDate     *int64        `thrift:"due_date,6,optional" json:"due_date,omitempty"`
	CreatedAt   *int64        `thrift:"created_at,7,optional" json:"created_at,omitempty"`
	UpdatedAt   *int64        `thrift:"updated_at,8,optional" json:"updated_at,omitempty"`
	SpaceID     *int64        `thrift:"space_id,9,optional" json:"space_id,omitempty"`
	ProjectID   *int64        `thrift:"project_id,10,optional" json:"project_id,omitempty"`
	CreatorID   *int64        `thrift:"creator_id,11,optional" json:"creator_id,omitempty"`
}

func NewTaskInfo() *TaskInfo {
	return &TaskInfo{}
}

func (p *TaskInfo) InitDefault() {
}

func (p *TaskInfo) GetID() (v int64) {
	return p.ID
}

func (p *TaskInfo) GetName() (v string) {
	return p.Name
}

var TaskInfo_Description_DEFAULT string

func (p *TaskInfo) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return TaskInfo_Description_DEFAULT
	}
	return *p.Description
}

var TaskInfo_Status_DEFAULT TaskStatus

func (p *TaskInfo) GetStatus() (v TaskStatus) {
	if !p.IsSetStatus() {
		return TaskInfo_Status_DEFAULT
	}
	return *p.Status
}

var TaskInfo_Priority_DEFAULT TaskPriority

func (p *TaskInfo) GetPriority() (v TaskPriority) {
	if !p.IsSetPriority() {
		return TaskInfo_Priority_DEFAULT
	}
	return *p.Priority
}

var TaskInfo_DueDate_DEFAULT int64

func (p *TaskInfo) GetDueDate() (v int64) {
	if !p.IsSetDueDate() {
		return TaskInfo_DueDate_DEFAULT
	}
	return *p.DueDate
}

var TaskInfo_CreatedAt_DEFAULT int64

func (p *TaskInfo) GetCreatedAt() (v int64) {
	if !p.IsSetCreatedAt() {
		return TaskInfo_CreatedAt_DEFAULT
	}
	return *p.CreatedAt
}

var TaskInfo_UpdatedAt_DEFAULT int64

func (p *TaskInfo) GetUpdatedAt() (v int64) {
	if !p.IsSetUpdatedAt() {
		return TaskInfo_UpdatedAt_DEFAULT
	}
	return *p.UpdatedAt
}

var TaskInfo_SpaceID_DEFAULT int64

func (p *TaskInfo) GetSpaceID() (v int64) {
	if !p.IsSetSpaceID() {
		return TaskInfo_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var TaskInfo_ProjectID_DEFAULT int64

func (p *TaskInfo) GetProjectID() (v int64) {
	if !p.IsSetProjectID() {
		return TaskInfo_ProjectID_DEFAULT
	}
	return *p.ProjectID
}

var TaskInfo_CreatorID_DEFAULT int64

func (p *TaskInfo) GetCreatorID() (v int64) {
	if !p.IsSetCreatorID() {
		return TaskInfo_CreatorID_DEFAULT
	}
	return *p.CreatorID
}

var fieldIDToName_TaskInfo = map[int16]string{
	1:  "id",
	2:  "name",
	3:  "description",
	4:  "status",
	5:  "priority",
	6:  "due_date",
	7:  "created_at",
	8:  "updated_at",
	9:  "space_id",
	10: "project_id",
	11: "creator_id",
}

func (p *TaskInfo) IsSetDescription() bool {
	return p.Description != nil
}

func (p *TaskInfo) IsSetStatus() bool {
	return p.Status != nil
}

func (p *TaskInfo) IsSetPriority() bool {
	return p.Priority != nil
}

func (p *TaskInfo) IsSetDueDate() bool {
	return p.DueDate != nil
}

func (p *TaskInfo) IsSetCreatedAt() bool {
	return p.CreatedAt != nil
}

func (p *TaskInfo) IsSetUpdatedAt() bool {
	return p.UpdatedAt != nil
}

func (p *TaskInfo) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *TaskInfo) IsSetProjectID() bool {
	return p.ProjectID != nil
}

func (p *TaskInfo) IsSetCreatorID() bool {
	return p.CreatorID != nil
}

func (p *TaskInfo) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField7(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField8(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField9(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField10(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField11(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_TaskInfo[fieldId]))
}

func (p *TaskInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *TaskInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Name = _field
	return nil
}
func (p *TaskInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *TaskInfo) ReadField4(iprot thrift.TProtocol) error {

	var _field *TaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskStatus(v)
		_field = &tmp
	}
	p.Status = _field
	return nil
}
func (p *TaskInfo) ReadField5(iprot thrift.TProtocol) error {

	var _field *TaskPriority
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskPriority(v)
		_field = &tmp
	}
	p.Priority = _field
	return nil
}
func (p *TaskInfo) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DueDate = _field
	return nil
}
func (p *TaskInfo) ReadField7(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreatedAt = _field
	return nil
}
func (p *TaskInfo) ReadField8(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.UpdatedAt = _field
	return nil
}
func (p *TaskInfo) ReadField9(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SpaceID = _field
	return nil
}
func (p *TaskInfo) ReadField10(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectID = _field
	return nil
}
func (p *TaskInfo) ReadField11(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.CreatorID = _field
	return nil
}

func (p *TaskInfo) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("TaskInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
		if err = p.writeField7(oprot); err != nil {
			fieldId = 7
			goto WriteFieldError
		}
		if err = p.writeField8(oprot); err != nil {
			fieldId = 8
			goto WriteFieldError
		}
		if err = p.writeField9(oprot); err != nil {
			fieldId = 9
			goto WriteFieldError
		}
		if err = p.writeField10(oprot); err != nil {
			fieldId = 10
			goto WriteFieldError
		}
		if err = p.writeField11(oprot); err != nil {
			fieldId = 11
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *TaskInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Name); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *TaskInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *TaskInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err = oprot.WriteFieldBegin("status", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Status)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *TaskInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPriority() {
		if err = oprot.WriteFieldBegin("priority", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Priority)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *TaskInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDueDate() {
		if err = oprot.WriteFieldBegin("due_date", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.DueDate); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}
func (p *TaskInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreatedAt() {
		if err = oprot.WriteFieldBegin("created_at", thrift.I64, 7); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.CreatedAt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 7 end error: ", p), err)
}
func (p *TaskInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetUpdatedAt() {
		if err = oprot.WriteFieldBegin("updated_at", thrift.I64, 8); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.UpdatedAt); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 8 end error: ", p), err)
}
func (p *TaskInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpaceID() {
		if err = oprot.WriteFieldBegin("space_id", thrift.I64, 9); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SpaceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 9 end error: ", p), err)
}
func (p *TaskInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectID() {
		if err = oprot.WriteFieldBegin("project_id", thrift.I64, 10); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ProjectID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 10 end error: ", p), err)
}
func (p *TaskInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreatorID() {
		if err = oprot.WriteFieldBegin("creator_id", thrift.I64, 11); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.CreatorID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 11 end error: ", p), err)
}

func (p *TaskInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskInfo(%+v)", *p)

}

type ListTaskRequest struct {
	SpaceID   *int64        `thrift:"space_id,1,optional" json:"space_id,omitempty"`
	ProjectID *int64        `thrift:"project_id,2,optional" json:"project_id,omitempty"`
	Status    *TaskStatus   `thrift:"status,3,optional" json:"status,omitempty"`
	Priority  *TaskPriority `thrift:"priority,4,optional" json:"priority,omitempty"`
	Page      *int32        `thrift:"page,5,optional" json:"page,omitempty"`
	PageSize  *int32        `thrift:"page_size,6,optional" json:"page_size,omitempty"`
}

func NewListTaskRequest() *ListTaskRequest {
	return &ListTaskRequest{}
}

func (p *ListTaskRequest) InitDefault() {
}

var ListTaskRequest_SpaceID_DEFAULT int64

func (p *ListTaskRequest) GetSpaceID() (v int64) {
	if !p.IsSetSpaceID() {
		return ListTaskRequest_SpaceID_DEFAULT
	}
	return *p.SpaceID
}

var ListTaskRequest_ProjectID_DEFAULT int64

func (p *ListTaskRequest) GetProjectID() (v int64) {
	if !p.IsSetProjectID() {
		return ListTaskRequest_ProjectID_DEFAULT
	}
	return *p.ProjectID
}

var ListTaskRequest_Status_DEFAULT TaskStatus

func (p *ListTaskRequest) GetStatus() (v TaskStatus) {
	if !p.IsSetStatus() {
		return ListTaskRequest_Status_DEFAULT
	}
	return *p.Status
}

var ListTaskRequest_Priority_DEFAULT TaskPriority

func (p *ListTaskRequest) GetPriority() (v TaskPriority) {
	if !p.IsSetPriority() {
		return ListTaskRequest_Priority_DEFAULT
	}
	return *p.Priority
}

var ListTaskRequest_Page_DEFAULT int32

func (p *ListTaskRequest) GetPage() (v int32) {
	if !p.IsSetPage() {
		return ListTaskRequest_Page_DEFAULT
	}
	return *p.Page
}

var ListTaskRequest_PageSize_DEFAULT int32

func (p *ListTaskRequest) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return ListTaskRequest_PageSize_DEFAULT
	}
	return *p.PageSize
}

var fieldIDToName_ListTaskRequest = map[int16]string{
	1: "space_id",
	2: "project_id",
	3: "status",
	4: "priority",
	5: "page",
	6: "page_size",
}

func (p *ListTaskRequest) IsSetSpaceID() bool {
	return p.SpaceID != nil
}

func (p *ListTaskRequest) IsSetProjectID() bool {
	return p.ProjectID != nil
}

func (p *ListTaskRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *ListTaskRequest) IsSetPriority() bool {
	return p.Priority != nil
}

func (p *ListTaskRequest) IsSetPage() bool {
	return p.Page != nil
}

func (p *ListTaskRequest) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *ListTaskRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListTaskRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ListTaskRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.SpaceID = _field
	return nil
}
func (p *ListTaskRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.ProjectID = _field
	return nil
}
func (p *ListTaskRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *TaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskStatus(v)
		_field = &tmp
	}
	p.Status = _field
	return nil
}
func (p *ListTaskRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *TaskPriority
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskPriority(v)
		_field = &tmp
	}
	p.Priority = _field
	return nil
}
func (p *ListTaskRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Page = _field
	return nil
}
func (p *ListTaskRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *ListTaskRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListTaskRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListTaskRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpaceID() {
		if err = oprot.WriteFieldBegin("space_id", thrift.I64, 1); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.SpaceID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListTaskRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetProjectID() {
		if err = oprot.WriteFieldBegin("project_id", thrift.I64, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.ProjectID); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListTaskRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err = oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Status)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ListTaskRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPriority() {
		if err = oprot.WriteFieldBegin("priority", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Priority)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ListTaskRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPage() {
		if err = oprot.WriteFieldBegin("page", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Page); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ListTaskRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("page_size", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTaskRequest(%+v)", *p)

}

type ListTaskResponse struct {
	Code     int32       `thrift:"code,1,required" json:"code"`
	Message  *string     `thrift:"message,2,optional" json:"message,omitempty"`
	Tasks    []*TaskInfo `thrift:"tasks,3,optional" json:"tasks,omitempty"`
	Total    *int32      `thrift:"total,4,optional" json:"total,omitempty"`
	Page     *int32      `thrift:"page,5,optional" json:"page,omitempty"`
	PageSize *int32      `thrift:"page_size,6,optional" json:"page_size,omitempty"`
}

func NewListTaskResponse() *ListTaskResponse {
	return &ListTaskResponse{}
}

func (p *ListTaskResponse) InitDefault() {
}

func (p *ListTaskResponse) GetCode() (v int32) {
	return p.Code
}

var ListTaskResponse_Message_DEFAULT string

func (p *ListTaskResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return ListTaskResponse_Message_DEFAULT
	}
	return *p.Message
}

var ListTaskResponse_Tasks_DEFAULT []*TaskInfo

func (p *ListTaskResponse) GetTasks() (v []*TaskInfo) {
	if !p.IsSetTasks() {
		return ListTaskResponse_Tasks_DEFAULT
	}
	return p.Tasks
}

var ListTaskResponse_Total_DEFAULT int32

func (p *ListTaskResponse) GetTotal() (v int32) {
	if !p.IsSetTotal() {
		return ListTaskResponse_Total_DEFAULT
	}
	return *p.Total
}

var ListTaskResponse_Page_DEFAULT int32

func (p *ListTaskResponse) GetPage() (v int32) {
	if !p.IsSetPage() {
		return ListTaskResponse_Page_DEFAULT
	}
	return *p.Page
}

var ListTaskResponse_PageSize_DEFAULT int32

func (p *ListTaskResponse) GetPageSize() (v int32) {
	if !p.IsSetPageSize() {
		return ListTaskResponse_PageSize_DEFAULT
	}
	return *p.PageSize
}

var fieldIDToName_ListTaskResponse = map[int16]string{
	1: "code",
	2: "message",
	3: "tasks",
	4: "total",
	5: "page",
	6: "page_size",
}

func (p *ListTaskResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *ListTaskResponse) IsSetTasks() bool {
	return p.Tasks != nil
}

func (p *ListTaskResponse) IsSetTotal() bool {
	return p.Total != nil
}

func (p *ListTaskResponse) IsSetPage() bool {
	return p.Page != nil
}

func (p *ListTaskResponse) IsSetPageSize() bool {
	return p.PageSize != nil
}

func (p *ListTaskResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ListTaskResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ListTaskResponse[fieldId]))
}

func (p *ListTaskResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *ListTaskResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *ListTaskResponse) ReadField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*TaskInfo, 0, size)
	values := make([]TaskInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.Tasks = _field
	return nil
}
func (p *ListTaskResponse) ReadField4(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Total = _field
	return nil
}
func (p *ListTaskResponse) ReadField5(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Page = _field
	return nil
}
func (p *ListTaskResponse) ReadField6(iprot thrift.TProtocol) error {

	var _field *int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.PageSize = _field
	return nil
}

func (p *ListTaskResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("ListTaskResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ListTaskResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *ListTaskResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *ListTaskResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTasks() {
		if err = oprot.WriteFieldBegin("tasks", thrift.LIST, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
			return err
		}
		for _, v := range p.Tasks {
			if err := v.Write(oprot); err != nil {
				return err
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *ListTaskResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTotal() {
		if err = oprot.WriteFieldBegin("total", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Total); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *ListTaskResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPage() {
		if err = oprot.WriteFieldBegin("page", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.Page); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *ListTaskResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPageSize() {
		if err = oprot.WriteFieldBegin("page_size", thrift.I32, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(*p.PageSize); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *ListTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTaskResponse(%+v)", *p)

}

type UpdateTaskRequest struct {
	ID          int64         `thrift:"id,1,required" json:"id"`
	Name        *string       `thrift:"name,2,optional" json:"name,omitempty"`
	Description *string       `thrift:"description,3,optional" json:"description,omitempty"`
	Status      *TaskStatus   `thrift:"status,4,optional" json:"status,omitempty"`
	Priority    *TaskPriority `thrift:"priority,5,optional" json:"priority,omitempty"`
	DueDate     *int64        `thrift:"due_date,6,optional" json:"due_date,omitempty"`
}

func NewUpdateTaskRequest() *UpdateTaskRequest {
	return &UpdateTaskRequest{}
}

func (p *UpdateTaskRequest) InitDefault() {
}

func (p *UpdateTaskRequest) GetID() (v int64) {
	return p.ID
}

var UpdateTaskRequest_Name_DEFAULT string

func (p *UpdateTaskRequest) GetName() (v string) {
	if !p.IsSetName() {
		return UpdateTaskRequest_Name_DEFAULT
	}
	return *p.Name
}

var UpdateTaskRequest_Description_DEFAULT string

func (p *UpdateTaskRequest) GetDescription() (v string) {
	if !p.IsSetDescription() {
		return UpdateTaskRequest_Description_DEFAULT
	}
	return *p.Description
}

var UpdateTaskRequest_Status_DEFAULT TaskStatus

func (p *UpdateTaskRequest) GetStatus() (v TaskStatus) {
	if !p.IsSetStatus() {
		return UpdateTaskRequest_Status_DEFAULT
	}
	return *p.Status
}

var UpdateTaskRequest_Priority_DEFAULT TaskPriority

func (p *UpdateTaskRequest) GetPriority() (v TaskPriority) {
	if !p.IsSetPriority() {
		return UpdateTaskRequest_Priority_DEFAULT
	}
	return *p.Priority
}

var UpdateTaskRequest_DueDate_DEFAULT int64

func (p *UpdateTaskRequest) GetDueDate() (v int64) {
	if !p.IsSetDueDate() {
		return UpdateTaskRequest_DueDate_DEFAULT
	}
	return *p.DueDate
}

var fieldIDToName_UpdateTaskRequest = map[int16]string{
	1: "id",
	2: "name",
	3: "description",
	4: "status",
	5: "priority",
	6: "due_date",
}

func (p *UpdateTaskRequest) IsSetName() bool {
	return p.Name != nil
}

func (p *UpdateTaskRequest) IsSetDescription() bool {
	return p.Description != nil
}

func (p *UpdateTaskRequest) IsSetStatus() bool {
	return p.Status != nil
}

func (p *UpdateTaskRequest) IsSetPriority() bool {
	return p.Priority != nil
}

func (p *UpdateTaskRequest) IsSetDueDate() bool {
	return p.DueDate != nil
}

func (p *UpdateTaskRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField6(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateTaskRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateTaskRequest[fieldId]))
}

func (p *UpdateTaskRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *UpdateTaskRequest) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Name = _field
	return nil
}
func (p *UpdateTaskRequest) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Description = _field
	return nil
}
func (p *UpdateTaskRequest) ReadField4(iprot thrift.TProtocol) error {

	var _field *TaskStatus
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskStatus(v)
		_field = &tmp
	}
	p.Status = _field
	return nil
}
func (p *UpdateTaskRequest) ReadField5(iprot thrift.TProtocol) error {

	var _field *TaskPriority
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		tmp := TaskPriority(v)
		_field = &tmp
	}
	p.Priority = _field
	return nil
}
func (p *UpdateTaskRequest) ReadField6(iprot thrift.TProtocol) error {

	var _field *int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.DueDate = _field
	return nil
}

func (p *UpdateTaskRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateTaskRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
		if err = p.writeField6(oprot); err != nil {
			fieldId = 6
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateTaskRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateTaskRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetName() {
		if err = oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Name); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateTaskRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetDescription() {
		if err = oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Description); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}
func (p *UpdateTaskRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err = oprot.WriteFieldBegin("status", thrift.I32, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Status)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}
func (p *UpdateTaskRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPriority() {
		if err = oprot.WriteFieldBegin("priority", thrift.I32, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI32(int32(*p.Priority)); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}
func (p *UpdateTaskRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDueDate() {
		if err = oprot.WriteFieldBegin("due_date", thrift.I64, 6); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteI64(*p.DueDate); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 6 end error: ", p), err)
}

func (p *UpdateTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTaskRequest(%+v)", *p)

}

type UpdateTaskResponse struct {
	Code    int32     `thrift:"code,1,required" json:"code"`
	Message *string   `thrift:"message,2,optional" json:"message,omitempty"`
	Task    *TaskInfo `thrift:"task,3,optional" json:"task,omitempty"`
}

func NewUpdateTaskResponse() *UpdateTaskResponse {
	return &UpdateTaskResponse{}
}

func (p *UpdateTaskResponse) InitDefault() {
}

func (p *UpdateTaskResponse) GetCode() (v int32) {
	return p.Code
}

var UpdateTaskResponse_Message_DEFAULT string

func (p *UpdateTaskResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return UpdateTaskResponse_Message_DEFAULT
	}
	return *p.Message
}

var UpdateTaskResponse_Task_DEFAULT *TaskInfo

func (p *UpdateTaskResponse) GetTask() (v *TaskInfo) {
	if !p.IsSetTask() {
		return UpdateTaskResponse_Task_DEFAULT
	}
	return p.Task
}

var fieldIDToName_UpdateTaskResponse = map[int16]string{
	1: "code",
	2: "message",
	3: "task",
}

func (p *UpdateTaskResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *UpdateTaskResponse) IsSetTask() bool {
	return p.Task != nil
}

func (p *UpdateTaskResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateTaskResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateTaskResponse[fieldId]))
}

func (p *UpdateTaskResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *UpdateTaskResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}
func (p *UpdateTaskResponse) ReadField3(iprot thrift.TProtocol) error {
	_field := NewTaskInfo()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Task = _field
	return nil
}

func (p *UpdateTaskResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateTaskResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateTaskResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *UpdateTaskResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}
func (p *UpdateTaskResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTask() {
		if err = oprot.WriteFieldBegin("task", thrift.STRUCT, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Task.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UpdateTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTaskResponse(%+v)", *p)

}

type DeleteTaskRequest struct {
	ID int64 `thrift:"id,1,required" json:"id"`
}

func NewDeleteTaskRequest() *DeleteTaskRequest {
	return &DeleteTaskRequest{}
}

func (p *DeleteTaskRequest) InitDefault() {
}

func (p *DeleteTaskRequest) GetID() (v int64) {
	return p.ID
}

var fieldIDToName_DeleteTaskRequest = map[int16]string{
	1: "id",
}

func (p *DeleteTaskRequest) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteTaskRequest[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteTaskRequest[fieldId]))
}

func (p *DeleteTaskRequest) ReadField1(iprot thrift.TProtocol) error {

	var _field int64
	if v, err := iprot.ReadI64(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}

func (p *DeleteTaskRequest) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteTaskRequest"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteTaskRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI64(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DeleteTaskRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTaskRequest(%+v)", *p)

}

type DeleteTaskResponse struct {
	Code    int32   `thrift:"code,1,required" json:"code"`
	Message *string `thrift:"message,2,optional" json:"message,omitempty"`
}

func NewDeleteTaskResponse() *DeleteTaskResponse {
	return &DeleteTaskResponse{}
}

func (p *DeleteTaskResponse) InitDefault() {
}

func (p *DeleteTaskResponse) GetCode() (v int32) {
	return p.Code
}

var DeleteTaskResponse_Message_DEFAULT string

func (p *DeleteTaskResponse) GetMessage() (v string) {
	if !p.IsSetMessage() {
		return DeleteTaskResponse_Message_DEFAULT
	}
	return *p.Message
}

var fieldIDToName_DeleteTaskResponse = map[int16]string{
	1: "code",
	2: "message",
}

func (p *DeleteTaskResponse) IsSetMessage() bool {
	return p.Message != nil
}

func (p *DeleteTaskResponse) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16
	var issetCode bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetCode = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetCode {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DeleteTaskResponse[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DeleteTaskResponse[fieldId]))
}

func (p *DeleteTaskResponse) ReadField1(iprot thrift.TProtocol) error {

	var _field int32
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Code = _field
	return nil
}
func (p *DeleteTaskResponse) ReadField2(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Message = _field
	return nil
}

func (p *DeleteTaskResponse) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("DeleteTaskResponse"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DeleteTaskResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(p.Code); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}
func (p *DeleteTaskResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMessage() {
		if err = oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Message); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DeleteTaskResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteTaskResponse(%+v)", *p)

}

type TaskService interface {
	CreateTask(ctx context.Context, req *CreateTaskRequest) (r *CreateTaskResponse, err error)

	ListTask(ctx context.Context, req *ListTaskRequest) (r *ListTaskResponse, err error)

	UpdateTask(ctx context.Context, req *UpdateTaskRequest) (r *UpdateTaskResponse, err error)

	DeleteTask(ctx context.Context, req *DeleteTaskRequest) (r *DeleteTaskResponse, err error)
}

type TaskServiceClient struct {
	c thrift.TClient
}

func NewTaskServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *TaskServiceClient {
	return &TaskServiceClient{
		c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
	}
}

func NewTaskServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *TaskServiceClient {
	return &TaskServiceClient{
		c: thrift.NewTStandardClient(iprot, oprot),
	}
}

func NewTaskServiceClient(c thrift.TClient) *TaskServiceClient {
	return &TaskServiceClient{
		c: c,
	}
}

func (p *TaskServiceClient) Client_() thrift.TClient {
	return p.c
}

func (p *TaskServiceClient) CreateTask(ctx context.Context, req *CreateTaskRequest) (r *CreateTaskResponse, err error) {
	var _args TaskServiceCreateTaskArgs
	_args.Req = req
	var _result TaskServiceCreateTaskResult
	if err = p.Client_().Call(ctx, "create_task", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *TaskServiceClient) ListTask(ctx context.Context, req *ListTaskRequest) (r *ListTaskResponse, err error) {
	var _args TaskServiceListTaskArgs
	_args.Req = req
	var _result TaskServiceListTaskResult
	if err = p.Client_().Call(ctx, "list_task", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *TaskServiceClient) UpdateTask(ctx context.Context, req *UpdateTaskRequest) (r *UpdateTaskResponse, err error) {
	var _args TaskServiceUpdateTaskArgs
	_args.Req = req
	var _result TaskServiceUpdateTaskResult
	if err = p.Client_().Call(ctx, "update_task", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
func (p *TaskServiceClient) DeleteTask(ctx context.Context, req *DeleteTaskRequest) (r *DeleteTaskResponse, err error) {
	var _args TaskServiceDeleteTaskArgs
	_args.Req = req
	var _result TaskServiceDeleteTaskResult
	if err = p.Client_().Call(ctx, "delete_task", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

type TaskServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      TaskService
}

func (p *TaskServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *TaskServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *TaskServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewTaskServiceProcessor(handler TaskService) *TaskServiceProcessor {
	self := &TaskServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self.AddToProcessorMap("create_task", &taskServiceProcessorCreateTask{handler: handler})
	self.AddToProcessorMap("list_task", &taskServiceProcessorListTask{handler: handler})
	self.AddToProcessorMap("update_task", &taskServiceProcessorUpdateTask{handler: handler})
	self.AddToProcessorMap("delete_task", &taskServiceProcessorDeleteTask{handler: handler})
	return self
}
func (p *TaskServiceProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(ctx, seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush(ctx)
	return false, x
}

type taskServiceProcessorCreateTask struct {
	handler TaskService
}

func (p *taskServiceProcessorCreateTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := TaskServiceCreateTaskArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("create_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := TaskServiceCreateTaskResult{}
	var retval *CreateTaskResponse
	if retval, err2 = p.handler.CreateTask(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing create_task: "+err2.Error())
		oprot.WriteMessageBegin("create_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("create_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type taskServiceProcessorListTask struct {
	handler TaskService
}

func (p *taskServiceProcessorListTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := TaskServiceListTaskArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("list_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := TaskServiceListTaskResult{}
	var retval *ListTaskResponse
	if retval, err2 = p.handler.ListTask(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing list_task: "+err2.Error())
		oprot.WriteMessageBegin("list_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("list_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type taskServiceProcessorUpdateTask struct {
	handler TaskService
}

func (p *taskServiceProcessorUpdateTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := TaskServiceUpdateTaskArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := TaskServiceUpdateTaskResult{}
	var retval *UpdateTaskResponse
	if retval, err2 = p.handler.UpdateTask(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_task: "+err2.Error())
		oprot.WriteMessageBegin("update_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("update_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type taskServiceProcessorDeleteTask struct {
	handler TaskService
}

func (p *taskServiceProcessorDeleteTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := TaskServiceDeleteTaskArgs{}
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("delete_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return false, err
	}

	iprot.ReadMessageEnd()
	var err2 error
	result := TaskServiceDeleteTaskResult{}
	var retval *DeleteTaskResponse
	if retval, err2 = p.handler.DeleteTask(ctx, args.Req); err2 != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing delete_task: "+err2.Error())
		oprot.WriteMessageBegin("delete_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush(ctx)
		return true, err2
	} else {
		result.Success = retval
	}
	if err2 = oprot.WriteMessageBegin("delete_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 = result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type TaskServiceCreateTaskArgs struct {
	Req *CreateTaskRequest `thrift:"req,1" json:"req"`
}

func NewTaskServiceCreateTaskArgs() *TaskServiceCreateTaskArgs {
	return &TaskServiceCreateTaskArgs{}
}

func (p *TaskServiceCreateTaskArgs) InitDefault() {
}

var TaskServiceCreateTaskArgs_Req_DEFAULT *CreateTaskRequest

func (p *TaskServiceCreateTaskArgs) GetReq() (v *CreateTaskRequest) {
	if !p.IsSetReq() {
		return TaskServiceCreateTaskArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_TaskServiceCreateTaskArgs = map[int16]string{
	1: "req",
}

func (p *TaskServiceCreateTaskArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TaskServiceCreateTaskArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceCreateTaskArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceCreateTaskArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewCreateTaskRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TaskServiceCreateTaskArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("create_task_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceCreateTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TaskServiceCreateTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceCreateTaskArgs(%+v)", *p)

}

type TaskServiceCreateTaskResult struct {
	Success *CreateTaskResponse `thrift:"success,0,optional" json:"success,omitempty"`
}

func NewTaskServiceCreateTaskResult() *TaskServiceCreateTaskResult {
	return &TaskServiceCreateTaskResult{}
}

func (p *TaskServiceCreateTaskResult) InitDefault() {
}

var TaskServiceCreateTaskResult_Success_DEFAULT *CreateTaskResponse

func (p *TaskServiceCreateTaskResult) GetSuccess() (v *CreateTaskResponse) {
	if !p.IsSetSuccess() {
		return TaskServiceCreateTaskResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_TaskServiceCreateTaskResult = map[int16]string{
	0: "success",
}

func (p *TaskServiceCreateTaskResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TaskServiceCreateTaskResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceCreateTaskResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceCreateTaskResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewCreateTaskResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TaskServiceCreateTaskResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("create_task_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceCreateTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TaskServiceCreateTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceCreateTaskResult(%+v)", *p)

}

type TaskServiceListTaskArgs struct {
	Req *ListTaskRequest `thrift:"req,1" json:"req"`
}

func NewTaskServiceListTaskArgs() *TaskServiceListTaskArgs {
	return &TaskServiceListTaskArgs{}
}

func (p *TaskServiceListTaskArgs) InitDefault() {
}

var TaskServiceListTaskArgs_Req_DEFAULT *ListTaskRequest

func (p *TaskServiceListTaskArgs) GetReq() (v *ListTaskRequest) {
	if !p.IsSetReq() {
		return TaskServiceListTaskArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_TaskServiceListTaskArgs = map[int16]string{
	1: "req",
}

func (p *TaskServiceListTaskArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TaskServiceListTaskArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceListTaskArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceListTaskArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewListTaskRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TaskServiceListTaskArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("list_task_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceListTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TaskServiceListTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceListTaskArgs(%+v)", *p)

}

type TaskServiceListTaskResult struct {
	Success *ListTaskResponse `thrift:"success,0,optional" json:"success,omitempty"`
}

func NewTaskServiceListTaskResult() *TaskServiceListTaskResult {
	return &TaskServiceListTaskResult{}
}

func (p *TaskServiceListTaskResult) InitDefault() {
}

var TaskServiceListTaskResult_Success_DEFAULT *ListTaskResponse

func (p *TaskServiceListTaskResult) GetSuccess() (v *ListTaskResponse) {
	if !p.IsSetSuccess() {
		return TaskServiceListTaskResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_TaskServiceListTaskResult = map[int16]string{
	0: "success",
}

func (p *TaskServiceListTaskResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TaskServiceListTaskResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceListTaskResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceListTaskResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewListTaskResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TaskServiceListTaskResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("list_task_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceListTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TaskServiceListTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceListTaskResult(%+v)", *p)

}

type TaskServiceUpdateTaskArgs struct {
	Req *UpdateTaskRequest `thrift:"req,1" json:"req"`
}

func NewTaskServiceUpdateTaskArgs() *TaskServiceUpdateTaskArgs {
	return &TaskServiceUpdateTaskArgs{}
}

func (p *TaskServiceUpdateTaskArgs) InitDefault() {
}

var TaskServiceUpdateTaskArgs_Req_DEFAULT *UpdateTaskRequest

func (p *TaskServiceUpdateTaskArgs) GetReq() (v *UpdateTaskRequest) {
	if !p.IsSetReq() {
		return TaskServiceUpdateTaskArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_TaskServiceUpdateTaskArgs = map[int16]string{
	1: "req",
}

func (p *TaskServiceUpdateTaskArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TaskServiceUpdateTaskArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceUpdateTaskArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceUpdateTaskArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewUpdateTaskRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TaskServiceUpdateTaskArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("update_task_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceUpdateTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TaskServiceUpdateTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceUpdateTaskArgs(%+v)", *p)

}

type TaskServiceUpdateTaskResult struct {
	Success *UpdateTaskResponse `thrift:"success,0,optional" json:"success,omitempty"`
}

func NewTaskServiceUpdateTaskResult() *TaskServiceUpdateTaskResult {
	return &TaskServiceUpdateTaskResult{}
}

func (p *TaskServiceUpdateTaskResult) InitDefault() {
}

var TaskServiceUpdateTaskResult_Success_DEFAULT *UpdateTaskResponse

func (p *TaskServiceUpdateTaskResult) GetSuccess() (v *UpdateTaskResponse) {
	if !p.IsSetSuccess() {
		return TaskServiceUpdateTaskResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_TaskServiceUpdateTaskResult = map[int16]string{
	0: "success",
}

func (p *TaskServiceUpdateTaskResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TaskServiceUpdateTaskResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceUpdateTaskResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceUpdateTaskResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewUpdateTaskResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TaskServiceUpdateTaskResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("update_task_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceUpdateTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TaskServiceUpdateTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceUpdateTaskResult(%+v)", *p)

}

type TaskServiceDeleteTaskArgs struct {
	Req *DeleteTaskRequest `thrift:"req,1" json:"req"`
}

func NewTaskServiceDeleteTaskArgs() *TaskServiceDeleteTaskArgs {
	return &TaskServiceDeleteTaskArgs{}
}

func (p *TaskServiceDeleteTaskArgs) InitDefault() {
}

var TaskServiceDeleteTaskArgs_Req_DEFAULT *DeleteTaskRequest

func (p *TaskServiceDeleteTaskArgs) GetReq() (v *DeleteTaskRequest) {
	if !p.IsSetReq() {
		return TaskServiceDeleteTaskArgs_Req_DEFAULT
	}
	return p.Req
}

var fieldIDToName_TaskServiceDeleteTaskArgs = map[int16]string{
	1: "req",
}

func (p *TaskServiceDeleteTaskArgs) IsSetReq() bool {
	return p.Req != nil
}

func (p *TaskServiceDeleteTaskArgs) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceDeleteTaskArgs[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceDeleteTaskArgs) ReadField1(iprot thrift.TProtocol) error {
	_field := NewDeleteTaskRequest()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Req = _field
	return nil
}

func (p *TaskServiceDeleteTaskArgs) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("delete_task_args"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceDeleteTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := p.Req.Write(oprot); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *TaskServiceDeleteTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceDeleteTaskArgs(%+v)", *p)

}

type TaskServiceDeleteTaskResult struct {
	Success *DeleteTaskResponse `thrift:"success,0,optional" json:"success,omitempty"`
}

func NewTaskServiceDeleteTaskResult() *TaskServiceDeleteTaskResult {
	return &TaskServiceDeleteTaskResult{}
}

func (p *TaskServiceDeleteTaskResult) InitDefault() {
}

var TaskServiceDeleteTaskResult_Success_DEFAULT *DeleteTaskResponse

func (p *TaskServiceDeleteTaskResult) GetSuccess() (v *DeleteTaskResponse) {
	if !p.IsSetSuccess() {
		return TaskServiceDeleteTaskResult_Success_DEFAULT
	}
	return p.Success
}

var fieldIDToName_TaskServiceDeleteTaskResult = map[int16]string{
	0: "success",
}

func (p *TaskServiceDeleteTaskResult) IsSetSuccess() bool {
	return p.Success != nil
}

func (p *TaskServiceDeleteTaskResult) Read(iprot thrift.TProtocol) (err error) {
	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err = p.ReadField0(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_TaskServiceDeleteTaskResult[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *TaskServiceDeleteTaskResult) ReadField0(iprot thrift.TProtocol) error {
	_field := NewDeleteTaskResponse()
	if err := _field.Read(iprot); err != nil {
		return err
	}
	p.Success = _field
	return nil
}

func (p *TaskServiceDeleteTaskResult) Write(oprot thrift.TProtocol) (err error) {
	var fieldId int16
	if err = oprot.WriteStructBegin("delete_task_result"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField0(oprot); err != nil {
			fieldId = 0
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *TaskServiceDeleteTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err = oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			goto WriteFieldBeginError
		}
		if err := p.Success.Write(oprot); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 0 end error: ", p), err)
}

func (p *TaskServiceDeleteTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskServiceDeleteTaskResult(%+v)", *p)

}
