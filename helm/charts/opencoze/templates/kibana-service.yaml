{{- if .Values.kibana.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "opencoze.fullname" . }}-kibana
  labels:
    {{- include "opencoze.labels" . | nindent 4 }}
spec:
  type: {{ .Values.kibana.service.type }}
  ports:
    - port: {{ .Values.kibana.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/component: kibana
    app.kubernetes.io/name: {{ include "opencoze.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}