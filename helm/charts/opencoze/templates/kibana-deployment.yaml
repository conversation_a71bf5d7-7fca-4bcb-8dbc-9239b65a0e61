{{- if .Values.kibana.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "opencoze.fullname" . }}-kibana
  labels:
    {{- include "opencoze.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: kibana
      app.kubernetes.io/name: {{ include "opencoze.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/component: kibana
        app.kubernetes.io/name: {{ include "opencoze.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      securityContext:
        runAsUser: 0
        fsGroup: 1001
      initContainers:
      - name: wait-for-elasticsearch
        image: {{ .Values.images.curl }}
        env:
        - name: ES_HOST
          value: '{{ include "opencoze.fullname" . }}-elasticsearch'
        - name: ES_PORT
          value: "9200"
        - name: ES_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{ .Release.Name }}-es-secret
              key: username
        - name: ES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ .Release.Name }}-es-secret
              key: password
        command: ['sh', '-c', 'until curl -s -u "$ES_USERNAME:$ES_PASSWORD" "http://$ES_HOST:$ES_PORT/_cluster/health?wait_for_status=yellow&timeout=1s"; do echo "waiting for elasticsearch"; sleep 2; done;']
      containers:
        - name: kibana
          image: "{{ .Values.kibana.image.repository }}:{{ .Values.kibana.image.tag }}"
          env:
          - name: KIBANA_ELASTICSEARCH_URL
            value: "{{ include "opencoze.fullname" . }}-elasticsearch"
          command:
            - /bin/bash
            - -c
            - |
              /opt/bitnami/scripts/kibana/setup.sh
              chown -R kibana:kibana /bitnami/kibana
              chmod g+s /bitnami/kibana
              exec /opt/bitnami/scripts/kibana/entrypoint.sh /opt/bitnami/scripts/kibana/run.sh
              /opt/bitnami/scripts/kibana/setup.sh
              chown -R 1001:1001 /bitnami/kibana
              chmod -R g+s /bitnami/kibana
              exec /opt/bitnami/scripts/kibana/entrypoint.sh /opt/bitnami/scripts/kibana/run.sh
          env:
            - name: KIBANA_ELASTICSEARCH_URL
              value: '{{ include "opencoze.fullname" . }}-elasticsearch'
            - name: ELASTICSEARCH_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-es-secret
                  key: username
            - name: ELASTICSEARCH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ .Release.Name }}-es-secret
                  key: password
          ports:
            - name: http
              containerPort: {{ .Values.kibana.service.port }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/status
              port: http
            initialDelaySeconds: 120
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 6
          readinessProbe:
            httpGet:
              path: /api/status
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 6
          volumeMounts:
            - name: kibana-data
              mountPath: /bitnami/kibana
      volumes:
        - name: kibana-data
          emptyDir: {}
{{- end }}