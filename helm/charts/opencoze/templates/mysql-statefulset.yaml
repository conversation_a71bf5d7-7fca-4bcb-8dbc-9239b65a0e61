apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "opencoze.fullname" . }}-mysql
  labels:
    {{- include "opencoze.labels" . | nindent 4 }}
spec:
  serviceName: {{ .Release.Name }}-mysql
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: mysql
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/name: {{ include "opencoze.name" . }}
  template:
    metadata:
      labels:
        app.kubernetes.io/component: mysql
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/name: {{ include "opencoze.name" . }}
    spec:
      containers:
        - name: mysql
          image: "{{ .Values.mysql.image.repository }}:{{ .Values.mysql.image.tag }}"
          env:
            - name: MYSQL_ROOT_PASSWORD
              value: {{ .Values.mysql.rootPassword | quote }}
            - name: MYSQL_DATABASE
              value: {{ .Values.mysql.database | quote }}
            - name: MYSQL_USER
              value: {{ .Values.mysql.user | quote }}
            - name: MYSQL_PASSWORD
              value: {{ .Values.mysql.password | quote }}
          ports:
            - containerPort: 3306
          args:
            - "--character-set-server=utf8mb4"
            - "--collation-server=utf8mb4_unicode_ci"
          readinessProbe:
            exec:
              command:
              - mysqladmin
              - ping
              - -uroot
              - -p{{ .Values.mysql.rootPassword }}
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
          volumeMounts:
          - name: mysql-data
            mountPath: /var/lib/mysql

  volumeClaimTemplates:
    - metadata:
        name: mysql-data
      spec:
        accessModes: [ "ReadWriteOnce" ]
        storageClassName: {{ .Values.mysql.persistence.storageClassName | quote }}
        resources:
          requests:
            storage: {{ .Values.mysql.persistence.size | quote }}