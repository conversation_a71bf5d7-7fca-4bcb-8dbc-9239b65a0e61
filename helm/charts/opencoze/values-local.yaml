mysql:
  persistence:
    storageClassName: "hostpath"

redis:
  persistence:
    storageClassName: "hostpath"

# -- coze-server configuration
cozeServer:
  env:
    MINIO_ENDPOINT: "localhost:9000"


rocketmq:
  namesrv:
    persistence:
      store:
        storageClassName: "hostpath"
      logs:
        storageClassName: "hostpath"
  broker:
    persistence:
      store:
        storageClassName: "hostpath"
      logs:
        storageClassName: "hostpath"

elasticsearch:
  persistence:
    storageClassName: "hostpath"


minio:
  persistence:
    storageClassName: "hostpath"

etcd:
  persistence:
    storageClassName: "hostpath"

milvus:
  persistence:
    storageClassName: "hostpath"

