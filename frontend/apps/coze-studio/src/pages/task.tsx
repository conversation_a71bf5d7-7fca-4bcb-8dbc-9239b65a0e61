/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import React, { useState, useEffect } from 'react';

// 任务接口定义 - 匹配后端 TaskInfo 结构
interface Task {
    id: string; // 后端返回的是 string 类型
    name: string;
    description?: string;
    status?: number;
    priority?: number;
    due_date?: number; // 后端使用 int64 时间戳
    created_at?: number;
    updated_at?: number;
    space_id?: number;
    project_id?: number;
    creator_id?: number;
}

// API 响应接口 - 匹配后端响应格式
interface ApiResponse<T> {
    code: number;
    message?: string;
    data?: T;
}

interface ListTaskResponse {
    code: number;
    message?: string;
    tasks?: Task[];
    total?: number;
    page?: number;
    page_size?: number;
}

interface CreateTaskResponse {
    code: number;
    message?: string;
    task?: Task;
}

interface UpdateTaskResponse {
    code: number;
    message?: string;
    task?: Task;
}

interface DeleteTaskResponse {
    code: number;
    message?: string;
}

// Task API 服务类 - 按照项目标准模式
class TaskApiService {
    // 获取任务列表
    static async getTaskList(params: {
        space_id?: number;
        project_id?: number;
        status?: number;
        priority?: number;
        page?: number;
        page_size?: number;
    }): Promise<ListTaskResponse> {
        const response = await fetch('/api/task/list', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    }

    // 创建任务
    static async createTask(params: {
        name: string;
        description?: string;
        priority?: number;
        due_date?: number;
        space_id?: number;
        project_id?: number;
    }): Promise<CreateTaskResponse> {
        const response = await fetch('/api/task/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    }

    // 更新任务
    static async updateTask(params: {
        id: string;
        name?: string;
        description?: string;
        status?: number;
        priority?: number;
        due_date?: number;
        space_id?: number;
    }): Promise<UpdateTaskResponse> {
        const response = await fetch('/api/task/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    }

    // 删除任务
    static async deleteTask(params: { id: string; space_id?: number }): Promise<DeleteTaskResponse> {
        const response = await fetch('/api/task/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(params)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.json();
    }
}

const TaskListPage: React.FC = () => {
    const [tasks, setTasks] = useState<Task[]>([]);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [editingTask, setEditingTask] = useState<Task | null>(null);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        priority: 2,
        due_date: ''
    });

    // 获取任务列表
    const fetchTasks = async () => {
        setLoading(true);
        try {
            const result = await TaskApiService.getTaskList({
                space_id: 1,
                page: 1,
                page_size: 50
            });
            
            if (result.code === 0) {
                setTasks(result.tasks || []);
            } else {
                alert(`获取任务列表失败: ${result.message}`);
            }
        } catch (error) {
            console.error('获取任务列表失败:', error);
            alert('获取任务列表失败，请检查网络连接');
        } finally {
            setLoading(false);
        }
    };

    // 创建任务
    const createTask = async (taskData: any) => {
        try {
            const requestBody: any = {
                name: taskData.name,
                description: taskData.description || '',
                priority: taskData.priority || 2,
                space_id: 1
            };

            // 如果有截止日期，转换为时间戳
            if (taskData.due_date) {
                requestBody.due_date = new Date(taskData.due_date).getTime();
            }

            const result = await TaskApiService.createTask(requestBody);
            
            if (result.code === 0) {
                alert('创建任务成功！');
                await fetchTasks(); // 重新获取列表
                return true;
            } else {
                alert(`创建任务失败: ${result.message}`);
                return false;
            }
        } catch (error) {
            console.error('创建任务失败:', error);
            alert('创建任务失败，请重试');
            return false;
        }
    };

    // 更新任务
    const updateTask = async (taskId: string, taskData: any) => {
        try {
            const requestBody: any = {
                id: taskId
            };

            // 只有当字段有值时才添加到请求中
            if (taskData.name && taskData.name.trim()) {
                requestBody.name = taskData.name;
            }
            if (taskData.description !== undefined) {
                requestBody.description = taskData.description;
            }
            if (taskData.priority !== undefined) {
                requestBody.priority = taskData.priority;
            }

            // 如果有截止日期，转换为时间戳
            if (taskData.due_date) {
                requestBody.due_date = new Date(taskData.due_date).getTime();
            }

            const result = await TaskApiService.updateTask(requestBody);
            
            if (result.code === 0) {
                alert('更新任务成功！');
                await fetchTasks(); // 重新获取列表
                return true;
            } else {
                alert(`更新任务失败: ${result.message}`);
                return false;
            }
        } catch (error) {
            console.error('更新任务失败:', error);
            alert('更新任务失败，请重试');
            return false;
        }
    };

    // 删除任务
    const deleteTask = async (taskId: string) => {
        try {
            const result = await TaskApiService.deleteTask({ 
                id: taskId,
                space_id: 1 // 默认空间ID
            });
            
            if (result.code === 0) {
                alert('删除任务成功！');
                await fetchTasks(); // 重新获取列表
                return true;
            } else {
                alert(`删除任务失败: ${result.message}`);
                return false;
            }
        } catch (error) {
            console.error('删除任务失败:', error);
            alert('删除任务失败，请重试');
            return false;
        }
    };

    // 更新任务状态
    const updateTaskStatus = async (taskId: string, newStatus: number) => {
        try {
            const result = await TaskApiService.updateTask({
                id: taskId,
                status: newStatus,
                space_id: 1 // 默认空间ID
            });
            
            if (result.code === 0) {
                await fetchTasks(); // 重新获取列表
            } else {
                alert(`更新任务状态失败: ${result.message}`);
            }
        } catch (error) {
            console.error('更新任务状态失败:', error);
            alert('更新任务状态失败，请重试');
        }
    };

    // 表单提交处理
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!formData.name.trim()) {
            alert('请输入任务名称');
            return;
        }

        const success = editingTask 
            ? await updateTask(editingTask.id, formData)
            : await createTask(formData);

        if (success) {
            setModalVisible(false);
            setEditingTask(null);
            setFormData({ name: '', description: '', priority: 2, due_date: '' });
        }
    };

    // 打开编辑模态框
    const openEditModal = (task: Task) => {
        setEditingTask(task);
        setFormData({
            name: task.name,
            description: task.description || '',
            priority: task.priority || 2,
            due_date: task.due_date ? new Date(task.due_date).toISOString().split('T')[0] : ''
        });
        setModalVisible(true);
    };

    // 获取状态文本和颜色
    const getStatusInfo = (status: number) => {
        const statusMap = {
            1: { text: '待处理', color: '#faad14' },
            2: { text: '进行中', color: '#1890ff' },
            3: { text: '已完成', color: '#52c41a' },
            4: { text: '已取消', color: '#ff4d4f' }
        };
        return statusMap[status] || { text: '未知', color: '#d9d9d9' };
    };

    // 获取优先级文本和颜色
    const getPriorityInfo = (priority: number) => {
        const priorityMap = {
            1: { text: '低', color: '#52c41a' },
            2: { text: '中', color: '#faad14' },
            3: { text: '高', color: '#ff7a45' },
            4: { text: '紧急', color: '#ff4d4f' }
        };
        return priorityMap[priority] || { text: '未知', color: '#d9d9d9' };
    };

    // 格式化时间戳为日期字符串
    const formatTimestamp = (timestamp?: number) => {
        if (!timestamp) return '-';
        return new Date(timestamp).toLocaleDateString();
    };

    // 组件加载时获取任务列表
    useEffect(() => {
        fetchTasks();
    }, []);

    return (
        <div style={{ 
            padding: '24px', 
            background: '#f5f5f5', 
            minHeight: '100vh',
            fontFamily: 'Arial, sans-serif'
        }}>
            <div style={{ 
                background: '#fff', 
                borderRadius: '8px', 
                padding: '24px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                maxWidth: '1200px',
                margin: '0 auto'
            }}>
                {/* 页面标题和操作按钮 */}
                <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center', 
                    marginBottom: '24px' 
                }}>
                    <h1 style={{ 
                        margin: 0, 
                        fontSize: '24px', 
                        fontWeight: 600,
                        color: '#333'
                    }}>
                        任务管理
                    </h1>
                    <button
                        onClick={() => {
                            setEditingTask(null);
                            setFormData({ name: '', description: '', priority: 2, due_date: '' });
                            setModalVisible(true);
                        }}
                        style={{
                            background: '#1890ff',
                            color: '#fff',
                            border: 'none',
                            padding: '8px 16px',
                            borderRadius: '6px',
                            cursor: 'pointer',
                            fontSize: '14px'
                        }}
                    >
                        新建任务
                    </button>
                </div>

                {/* 任务列表 */}
                <div style={{ 
                    border: '1px solid #e8e8e8', 
                    borderRadius: '6px', 
                    overflow: 'hidden'
                }}>
                    {/* 表格头部 */}
                    <div style={{ 
                        background: '#fafafa', 
                        padding: '12px 16px',
                        borderBottom: '1px solid #e8e8e8',
                        display: 'grid',
                        gridTemplateColumns: '2fr 3fr 1fr 1fr 1fr 1fr 1.5fr',
                        gap: '16px',
                        fontWeight: 600,
                        color: '#333'
                    }}>
                        <div>任务名称</div>
                        <div>描述</div>
                        <div>状态</div>
                        <div>优先级</div>
                        <div>截止日期</div>
                        <div>创建时间</div>
                        <div>操作</div>
                    </div>

                    {/* 加载状态 */}
                    {loading && (
                        <div style={{ 
                            padding: '40px', 
                            textAlign: 'center', 
                            color: '#666' 
                        }}>
                            加载中...
                        </div>
                    )}

                    {/* 任务列表 */}
                    {!loading && tasks.length === 0 && (
                        <div style={{ 
                            padding: '40px', 
                            textAlign: 'center', 
                            color: '#666' 
                        }}>
                            暂无任务，点击"新建任务"开始创建
                        </div>
                    )}

                    {!loading && tasks.map(task => (
                        <div key={task.id} style={{ 
                            padding: '12px 16px',
                            borderBottom: '1px solid #f0f0f0',
                            display: 'grid',
                            gridTemplateColumns: '2fr 3fr 1fr 1fr 1fr 1fr 1.5fr',
                            gap: '16px',
                            alignItems: 'center'
                        }}>
                            <div style={{ fontWeight: 500 }}>{task.name}</div>
                            <div style={{ 
                                color: '#666', 
                                fontSize: '14px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                            }}>
                                {task.description || '-'}
                            </div>
                            <div>
                                <select
                                    value={task.status || 1}
                                    onChange={(e) => updateTaskStatus(task.id, Number(e.target.value))}
                                    style={{
                                        padding: '4px 8px',
                                        borderRadius: '4px',
                                        border: '1px solid #d9d9d9',
                                        fontSize: '12px'
                                    }}
                                >
                                    <option value={1}>待处理</option>
                                    <option value={2}>进行中</option>
                                    <option value={3}>已完成</option>
                                    <option value={4}>已取消</option>
                                </select>
                            </div>
                            <div>
                                <span style={{
                                    background: getPriorityInfo(task.priority || 2).color,
                                    color: '#fff',
                                    padding: '2px 6px',
                                    borderRadius: '4px',
                                    fontSize: '12px'
                                }}>
                                    {getPriorityInfo(task.priority || 2).text}
                                </span>
                            </div>
                            <div style={{ fontSize: '14px', color: '#666' }}>
                                {formatTimestamp(task.due_date)}
                            </div>
                            <div style={{ fontSize: '14px', color: '#666' }}>
                                {formatTimestamp(task.created_at)}
                            </div>
                            <div style={{ display: 'flex', gap: '8px' }}>
                                <button
                                    onClick={() => openEditModal(task)}
                                    style={{
                                        background: '#1890ff',
                                        color: '#fff',
                                        border: 'none',
                                        padding: '4px 8px',
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                    }}
                                >
                                    编辑
                                </button>
                                <button
                                    onClick={() => {
                                        if (confirm('确定要删除这个任务吗？')) {
                                            deleteTask(task.id);
                                        }
                                    }}
                                    style={{
                                        background: '#ff4d4f',
                                        color: '#fff',
                                        border: 'none',
                                        padding: '4px 8px',
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                    }}
                                >
                                    删除
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* 创建/编辑任务模态框 */}
                {modalVisible && (
                    <div style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: 'rgba(0,0,0,0.5)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 1000
                    }}>
                        <div style={{
                            background: '#fff',
                            borderRadius: '8px',
                            padding: '24px',
                            width: '500px',
                            maxWidth: '90vw'
                        }}>
                            <h3 style={{ margin: '0 0 20px 0', color: '#333' }}>
                                {editingTask ? '编辑任务' : '新建任务'}
                            </h3>
                            
                            <form onSubmit={handleSubmit}>
                                <div style={{ marginBottom: '16px' }}>
                                    <label style={{ 
                                        display: 'block', 
                                        marginBottom: '8px', 
                                        fontWeight: 500,
                                        color: '#333'
                                    }}>
                                        任务名称 *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                        style={{
                                            width: '100%',
                                            padding: '8px 12px',
                                            border: '1px solid #d9d9d9',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                        placeholder="请输入任务名称"
                                        required
                                    />
                                </div>

                                <div style={{ marginBottom: '16px' }}>
                                    <label style={{ 
                                        display: 'block', 
                                        marginBottom: '8px', 
                                        fontWeight: 500,
                                        color: '#333'
                                    }}>
                                        描述
                                    </label>
                                    <textarea
                                        value={formData.description}
                                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                        style={{
                                            width: '100%',
                                            padding: '8px 12px',
                                            border: '1px solid #d9d9d9',
                                            borderRadius: '6px',
                                            fontSize: '14px',
                                            minHeight: '80px',
                                            resize: 'vertical'
                                        }}
                                        placeholder="请输入任务描述（可选）"
                                    />
                                </div>

                                <div style={{ marginBottom: '16px' }}>
                                    <label style={{ 
                                        display: 'block', 
                                        marginBottom: '8px', 
                                        fontWeight: 500,
                                        color: '#333'
                                    }}>
                                        优先级
                                    </label>
                                    <select
                                        value={formData.priority}
                                        onChange={(e) => setFormData(prev => ({ ...prev, priority: Number(e.target.value) }))}
                                        style={{
                                            width: '100%',
                                            padding: '8px 12px',
                                            border: '1px solid #d9d9d9',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    >
                                        <option value={1}>低</option>
                                        <option value={2}>中</option>
                                        <option value={3}>高</option>
                                        <option value={4}>紧急</option>
                                    </select>
                                </div>

                                <div style={{ marginBottom: '24px' }}>
                                    <label style={{ 
                                        display: 'block', 
                                        marginBottom: '8px', 
                                        fontWeight: 500,
                                        color: '#333'
                                    }}>
                                        截止日期
                                    </label>
                                    <input
                                        type="date"
                                        value={formData.due_date}
                                        onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
                                        style={{
                                            width: '100%',
                                            padding: '8px 12px',
                                            border: '1px solid #d9d9d9',
                                            borderRadius: '6px',
                                            fontSize: '14px'
                                        }}
                                    />
                                </div>

                                <div style={{ 
                                    display: 'flex', 
                                    gap: '12px', 
                                    justifyContent: 'flex-end' 
                                }}>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setModalVisible(false);
                                            setEditingTask(null);
                                            setFormData({ name: '', description: '', priority: 2, due_date: '' });
                                        }}
                                        style={{
                                            padding: '8px 16px',
                                            border: '1px solid #d9d9d9',
                                            borderRadius: '6px',
                                            background: '#fff',
                                            cursor: 'pointer',
                                            fontSize: '14px'
                                        }}
                                    >
                                        取消
                                    </button>
                                    <button
                                        type="submit"
                                        style={{
                                            padding: '8px 16px',
                                            border: 'none',
                                            borderRadius: '6px',
                                            background: '#1890ff',
                                            color: '#fff',
                                            cursor: 'pointer',
                                            fontSize: '14px'
                                        }}
                                    >
                                        {editingTask ? '更新' : '创建'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                )}

                {/* API 调用说明 */}
                <div style={{ 
                    marginTop: '24px', 
                    padding: '16px', 
                    background: '#f6ffed', 
                    border: '1px solid #b7eb8f', 
                    borderRadius: '6px'
                }}>
                    <h4 style={{ margin: '0 0 12px 0', color: '#52c41a' }}>
                        🔧 前后端交互说明
                    </h4>
                    <div style={{ fontSize: '14px', color: '#666', lineHeight: '1.6' }}>
                        <p><strong>当前状态：</strong>已使用项目标准的 TaskApiService 类封装 API 调用</p>
                        <p><strong>API 服务：</strong>TaskApiService 类封装了所有 task 相关 API</p>
                        <p><strong>API 端点：</strong></p>
                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                            <li><code>POST /api/task/list</code> - 获取任务列表</li>
                            <li><code>POST /api/task/create</code> - 创建任务</li>
                            <li><code>POST /api/task/update</code> - 更新任务</li>
                            <li><code>POST /api/task/delete</code> - 删除任务</li>
                        </ul>
                        <p><strong>错误处理：</strong>统一的错误处理和用户提示</p>
                        <p><strong>数据格式：</strong>已调整为匹配后端 TaskInfo 结构</p>
                        <p><strong>时间处理：</strong>前端日期 ↔ 后端时间戳转换</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TaskListPage; 