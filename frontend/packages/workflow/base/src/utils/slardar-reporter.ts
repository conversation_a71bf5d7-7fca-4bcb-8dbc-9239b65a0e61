/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { reporter as infraReporter } from '@coze-arch/logger';
const namespace = 'workflow';

/**
 * 流程使用的 slardar 上报实例
 */
export const reporter = infraReporter.createReporterWithPreset({
  namespace,
});

/**
 * 异常捕获，会被当js error上报
 * @param exception
 * @param importErrorInfo
 */
export function captureException(exception: Error) {
  infraReporter.slardarInstance?.('captureException', exception, {
    isErrorBoundary: 'false',
    namespace,
  });
}
