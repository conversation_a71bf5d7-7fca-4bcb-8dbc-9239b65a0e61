{"name": "@coze-workflow/base", "version": "0.0.1", "description": "workflow 基础包", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./api": "./src/api/index.ts", "./types": "./src/types/index.ts", "./store": "./src/store/index.ts", "./constants": "./src/constants/index.ts", "./services": "./src/services/index.ts"}, "main": "src/index.ts", "typesVersions": {"*": {".": ["./src/index.ts"], "api": ["./src/api/index.ts"], "types": ["./src/types/index.ts"], "store": ["./src/store/index.ts"], "constants": ["./src/constants/index.ts"], "services": ["./src/services/index.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/logger": "workspace:*", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "@tanstack/react-query": "~5.13.4", "io-ts": "2.2.20", "lodash-es": "^4.17.21", "react": "~18.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.26.0", "@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/lodash-es": "^4.17.10", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "fp-ts": "^2.5.0", "less": "^3.13.1", "react-dom": "~18.2.0", "react-is": ">= 16.8.0", "scheduler": ">=0.19.0", "styled-components": ">=4", "typescript": "~5.8.2", "vitest": "~3.0.5"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}