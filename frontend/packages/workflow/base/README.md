# @coze-workflow/base

workflow 基础包

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes hook, store, api.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/base": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/base';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook
- Store
- Api

## API Reference

### Exports

- `*`
- `*`
- `*`
- `*`
- `*`
- `*`
- `WorkflowNode`
- `WorkflowNodeContext`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- React
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
