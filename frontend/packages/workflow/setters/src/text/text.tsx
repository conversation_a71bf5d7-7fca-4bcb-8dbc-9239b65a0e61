/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import cx from 'classnames';
import { TextArea } from '@coze-arch/coze-design';

import { type Setter } from '../types';

import styles from './text.module.less';

export interface TextOptions {
  placeholder?: string;
  width?: number | string;
  maxCount?: number;
}

export const Text: Setter<string, TextOptions> = ({
  value,
  onChange,
  readonly = false,
  width = '100%',
  placeholder,
  maxCount,
}) => {
  const handleChange = (newValue: string) => {
    onChange?.(newValue);
  };

  return (
    <TextArea
      className={cx({ [styles.readonly]: readonly })}
      style={{
        width,
      }}
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      maxLength={maxCount}
      maxCount={maxCount}
    />
  );
};
