{"name": "@coze-workflow/setters", "version": "0.0.1", "description": "workflow setters", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "types": "src/index.ts", "scripts": {"build": "exit 0", "dev": "storybook dev -p 6006", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@douyinfe/semi-ui": "~2.72.3", "@flowgram-adapter/free-layout-editor": "workspace:*", "@tanstack/react-query": "~5.13.4", "classnames": "^2.3.2", "nanoid": "^4.0.2"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/semi-theme-hand01": "0.0.6-alpha.346d77", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@douyinfe/semi-webpack-plugin": "2.61.0", "@rsbuild/core": "1.1.13", "@storybook/addon-essentials": "^7.6.7", "@storybook/addon-interactions": "^7.6.7", "@storybook/addon-links": "^7.6.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.7", "@storybook/preview-api": "^7.6.7", "@storybook/react": "^7.6.7", "@storybook/react-vite": "^7.6.7", "@storybook/test": "^7.6.7", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "~14.5.2", "@types/node": "18.18.9", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "humps": "2.0.1", "i18next": ">= 19.0.0", "react": "~18.2.0", "react-dom": "~18.2.0", "react-is": ">= 16.8.0", "storybook": "^7.6.7", "styled-components": ">= 2", "stylelint": "^15.11.0", "typescript": "~5.8.2", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vite-tsconfig-paths": "^4.2.1", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}