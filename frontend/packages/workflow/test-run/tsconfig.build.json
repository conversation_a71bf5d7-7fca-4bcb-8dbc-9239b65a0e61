{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"jsx": "preserve", "strictNullChecks": true, "noImplicitReturns": false, "useUnknownInCatchVariables": false, "strictPropertyInitialization": false, "module": "ESNext", "baseUrl": ".", "types": [], "paths": {"@/*": ["./src/*"]}, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo"}, "include": ["./src", "./src/**/*.json"], "references": [{"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-error/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-md-box-adapter/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../../arch/report-events/tsconfig.build.json"}, {"path": "../base/tsconfig.build.json"}, {"path": "../../common/flowgram-adapter/common/tsconfig.build.json"}, {"path": "../../common/flowgram-adapter/free-layout-editor/tsconfig.build.json"}, {"path": "../../common/md-editor-adapter/tsconfig.build.json"}, {"path": "../../components/json-viewer/tsconfig.build.json"}, {"path": "../components/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../nodes/tsconfig.build.json"}, {"path": "../variable/tsconfig.build.json"}]}