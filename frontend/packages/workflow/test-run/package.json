{"name": "@coze-workflow/test-run", "version": "0.0.1", "description": "workflow test run", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./plugins": "./src/plugins/test-run-plugin/index.ts", "./log": "./src/features/log/index.ts", "./input": "./src/features/input/index.ts", "./constants": "./src/constants/index.ts", "./formily": "./src/formily.ts"}, "main": "src/index.ts", "typesVersions": {"*": {"plugins": ["./src/plugins/test-run-plugin/index.ts"], "log": ["./src/features/log/index.ts"], "input": ["./src/features/input/index.ts"], "constants": ["./src/constants/index.ts"], "formily": ["./src/formily.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "exit 0", "test:cov": "exit 0"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-common/json-viewer": "workspace:*", "@coze-common/md-editor-adapter": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/components": "workspace:*", "@coze-workflow/nodes": "workspace:*", "@coze-workflow/variable": "workspace:*", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "@formily/core": "^2.3.0", "@formily/react": "^2.3.0", "@tanstack/react-query": "~5.13.4", "ahooks": "^3.7.8", "ajv": "~8.12.0", "bignumber.js": "~9.0.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.7", "inversify": "^6.0.1", "json-bigint": "~1.0.0", "lodash-es": "^4.17.21", "mime-types": "2.1.35", "nanoid": "^4.0.2", "query-string": "^8.1.0", "react-click-away-listener": "^2.2.3", "remark-parse": "^10.0.0", "unified": "^10.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "less": "^3.13.1", "react": "~18.2.0", "react-dom": "~18.2.0", "react-is": ">=16.8.0", "scheduler": ">=0.19.0", "typescript": "~5.8.2"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0", "utility-types": "^3.10.0"}}