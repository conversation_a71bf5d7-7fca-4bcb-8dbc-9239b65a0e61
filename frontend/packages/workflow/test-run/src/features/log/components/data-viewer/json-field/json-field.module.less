.json-viewer-field {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  display: flex;

  .field-content {
    flex: 1 1 0;
    width: 0;
    padding: 2px 0;
    user-select: auto;

    position: relative;

    &.is-error .field-key,
    &.is-error .field-value {
      color: #FF441E;
    }

    &.is-warning .field-key,
    &.is-warning .field-value {
      color: #FF9600;
    }

    .field-value-number {
      color: #03B6D0;
    }

    .field-value-boolean {
      color: #BB2BC9;
    }
  }

  .field-icon {
    font-size: 12px;
    color: var(--coz-fg-secondary);
    margin-right: 2px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;

    position: absolute;
    top: 4px;
    left: -12px;

    &.expand>svg {
      transform: rotate(90deg);
    }
  }

  .field-block {
    padding-left: 14px;
  }

  .field-key {
    color: var(--coz-fg-hglt);
    word-break: break-word;
  }

  .field-value {
    overflow-wrap: anywhere;
    white-space: pre-wrap;
    line-height: 16px;
  }

  .field-len {
    color: var(--coz-fg-dim);
  }
}

.field-space {
  display: inline-block;
  flex-grow: 0;
  flex-shrink: 0;
}

.value-button {
  margin-left: 4px;
}
