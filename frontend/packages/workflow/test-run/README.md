# @coze-workflow/test-run

workflow test run

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes component, hook, adapter and more.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/test-run": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/test-run';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component
- Hook
- Adapter
- Store
- Service
- Plugin
- Api

## API Reference

### Exports

- `FormPanelLayout,
  BaseTestButton,
  TraceIconButton,
  LogDetail,
  Collapse,
  ResizablePanel,
  TestsetManageProvider,
  TestsetSelect,
  TestsetEditPanel,
  InputFormEmpty,
  FileIcon,
  FileItemStatus,
  isImageFile,
  type TestsetSelectProps,
  type TestsetSelectAPI,
  useTestsetManageStore,`
- `InputNumberV2Adapter,
  InputNumberV2Props,`
- `LazyFormCore`
- `FormItemSchemaType, TESTSET_BOT_NAME, FieldName`
- `Tracker`
- `useDocumentContentChange`
- `QuestionForm`
- `InputForm`
- `//   TraceListPanel,
//   TraceDetailPanel,
//   type CustomTab,
//`
- `ProblemPanel`

*And more...*

For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
