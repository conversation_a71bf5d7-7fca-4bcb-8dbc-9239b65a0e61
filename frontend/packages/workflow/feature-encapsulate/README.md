# @coze-workflow/feature-encapsulate

封装解封逻辑

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes service, plugin.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/feature-encapsulate": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/feature-encapsulate';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Service
- Plugin

## API Reference

### Exports

- `createWorkflowEncapsulatePlugin`
- `EncapsulateService`
- `EncapsulatePanel`
- `ENCAPSULATE_SHORTCUTS`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- React
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
