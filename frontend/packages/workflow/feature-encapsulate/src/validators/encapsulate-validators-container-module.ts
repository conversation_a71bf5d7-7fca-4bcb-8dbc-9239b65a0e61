/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { ContainerModule } from 'inversify';
import { bindContributions } from '@flowgram-adapter/free-layout-editor';

import {
  EncapsulateNodesValidator,
  EncapsulateWorkflowJSONValidator,
  EncapsulateNodeValidator,
} from '../validate';
import { SubCanvasValidator } from './sub-canvas-validator';
import { StartEndValidator } from './start-end-validator';
import { LoopNodesValidator } from './loop-nodes-validator';
import { EncapsulateSchemaValidator } from './encapsulate-schema-validator';
import { EncapsulatePortsValidator } from './encapsulate-ports-validator';
import { EncapsulateOutputLinesValidator } from './encapsulate-output-lines-validator';
import { EncapsulateInputLinesValidator } from './encapsulate-input-lines-validator';
import { EncapsulateFormValidator } from './encapsulate-form-validator';

export const EncapsulateValidatorsContainerModule = new ContainerModule(
  bind => {
    // json validators
    bindContributions(bind, EncapsulateSchemaValidator, [
      EncapsulateWorkflowJSONValidator,
    ]);
    // nodes validators
    [
      EncapsulatePortsValidator,
      EncapsulateInputLinesValidator,
      EncapsulateOutputLinesValidator,
      StartEndValidator,
      LoopNodesValidator,
      SubCanvasValidator,
    ].forEach(Validator => {
      bindContributions(bind, Validator, [EncapsulateNodesValidator]);
    });
    // node validator
    [EncapsulateFormValidator].forEach(Validator => {
      bindContributions(bind, Validator, [EncapsulateNodeValidator]);
    });
  },
);
