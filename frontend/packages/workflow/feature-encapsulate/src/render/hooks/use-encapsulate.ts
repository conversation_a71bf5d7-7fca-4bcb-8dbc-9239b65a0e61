/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect, useState } from 'react';

import { useService } from '@flowgram-adapter/free-layout-editor';

import { EncapsulateRenderService } from '../encapsulate-render-service';
import { EncapsulateService } from '../../encapsulate';

export const useEncapsulate = () => {
  const encapsulateService = useService<EncapsulateService>(EncapsulateService);
  const encapsulateRenderService = useService<EncapsulateRenderService>(
    EncapsulateRenderService,
  );
  const [loading, setLoading] = useState(encapsulateRenderService.loading);

  useEffect(() => {
    const disposable = encapsulateRenderService.onLoadingChange(setLoading);
    return () => {
      disposable.dispose();
    };
  }, []);

  const handleEncapsulate = async () => {
    encapsulateRenderService.setLoading(true);
    try {
      await encapsulateService.encapsulate();
      encapsulateRenderService.closeModal();
    } catch (e) {
      console.error(e);
    }
    encapsulateRenderService.setLoading(false);
  };

  return {
    handleEncapsulate,
    loading,
  };
};
