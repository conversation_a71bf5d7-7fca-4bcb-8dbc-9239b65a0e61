/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type WorkflowNodeJSON } from '@flowgram-adapter/free-layout-editor';
import { type IPoint } from '@flowgram-adapter/common';

import { type Rect } from '../types';

/**
 * 设置节点坐标
 * @param node
 * @returns
 */
export function setNodePosition(
  node: WorkflowNodeJSON,
  position: IPoint,
): void {
  if (!node.meta) {
    node.meta = {};
  }

  node.meta.position = position;
}

/**
 * 根据矩形设置节点坐标
 * @param node
 * @param rect
 */
export function setNodePositionByRect(node: WorkflowNodeJSON, rect: Rect) {
  // eslint-disable-next-line @typescript-eslint/no-magic-numbers
  setNodePosition(node, { x: rect.x + rect.width / 2, y: rect.y });
}
