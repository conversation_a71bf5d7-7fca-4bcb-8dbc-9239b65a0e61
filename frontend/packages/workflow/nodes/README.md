# @coze-workflow/nodes

A workflow package for the Coze Studio monorepo

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes service.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/nodes": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/nodes';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Service

## API Reference

### Exports

- `*`
- `*`
- `*`
- `*`
- `*`
- `*`
- `nodeMetaValidator,
  settingOnErrorValidator,
  outputTreeValidator,
  inputTreeValidator,`
- `*`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
