{"name": "@coze-workflow/nodes", "version": "0.1.0", "author": "<EMAIL>", "sideEffects": ["**/*.css", "**/*.less", "**/*.sass", "**/*.scss"], "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "exit 0", "build:watch": "edenx build --watch", "dev": "edenx dev", "lint": "eslint ./ --cache --quiet", "new": "edenx new", "storybook": "edenx dev storybook", "test": "vitest --run", "test:cov": "npm run test -- --coverage", "upgrade": "edenx upgrade"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/components": "workspace:*", "@coze-workflow/setters": "workspace:*", "@coze-workflow/variable": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "@tanstack/react-query": "~5.13.4", "ajv": "~8.12.0", "immer": "^10.0.3", "inversify": "^6.0.1", "io-ts": "2.2.20", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "zod": "3.22.4"}, "devDependencies": {"@babel/core": "^7.26.0", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@modern-js/plugin-router-v5": "^2.38.0", "@modern-js/runtime": "^2.38.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "fp-ts": "^2.5.0", "happy-dom": "^12.10.3", "less": "^3.13.1", "react": "~18.2.0", "react-dom": "~18.2.0", "react-router-dom": "^6.22.0", "reflect-metadata": "^0.1.13", "require-from-string": "^2.0.2", "scheduler": ">=0.19.0", "styled-components": ">=4", "typescript": "~5.8.2", "vitest": "~3.0.5", "webpack": "~5.91.0"}}