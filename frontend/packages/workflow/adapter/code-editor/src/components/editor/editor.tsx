/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React, { type FC, lazy, Suspense, useMemo } from 'react';

import { type EditorOtherProps, type EditorProps } from '../../interface';

const LazyPythonEditor: FC<EditorProps> = lazy(async () => {
  const { PythonEditor } = await import('./python-editor');
  return { default: PythonEditor };
});

const PythonEditor: FC<EditorProps> = props => (
  <Suspense>
    <LazyPythonEditor {...props} />
  </Suspense>
);

const LazyTypescriptEditor: FC<EditorProps> = lazy(async () => {
  const { TypescriptEditor } = await import('./typescript-editor');
  return { default: TypescriptEditor };
});

const TypescriptEditor: FC<EditorProps> = props => (
  <Suspense>
    <LazyTypescriptEditor {...props} />
  </Suspense>
);

export const Editor = (props: EditorProps & EditorOtherProps) => {
  const language = useMemo(
    () => props.language || props.defaultLanguage,
    [props.defaultLanguage, props.language],
  );

  if (language === 'python') {
    return <PythonEditor {...props} />;
  }

  return <TypescriptEditor {...props} />;
};
