/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/**
 * get asset upload cdn address
 * @param assetName asset name
 * @returns asset name with cdn address
 */
export function getUploadCDNAsset(assetName: string) {
  // If the `UPLOAD_CDN` environment variable exists, use it
  if (UPLOAD_CDN) {
    return `${UPLOAD_CDN}/${assetName}`;
  }

  throw new Error('The UPLOAD_CDN environment variable is not configured');
}
