# @coze-workflow/history

A workflow package for the Coze Studio monorepo

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes hook, adapter, service and more.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/history": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/history';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook
- Adapter
- Service
- Editor
- Plugin

## API Reference

### Exports

- `WorkflowHistoryContainerModule`
- `useClearHistory`
- `WorkflowHistoryConfig`
- `createOperationReportPlugin`
- `HistoryService,
  createFreeHistoryPlugin,`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript

- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
