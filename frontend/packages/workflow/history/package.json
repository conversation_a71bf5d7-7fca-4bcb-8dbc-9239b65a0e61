{"name": "@coze-workflow/history", "version": "0.1.0", "author": "<EMAIL>", "sideEffects": ["**/*.css", "**/*.less", "**/*.sass", "**/*.scss"], "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache"}, "dependencies": {"@coze-arch/bot-flags": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/render": "workspace:*", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "inversify": "^6.0.1", "lodash-es": "^4.17.21"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "less": "^3.13.1", "react": "~18.2.0", "react-dom": "~18.2.0", "reflect-metadata": "^0.1.13", "rimraf": "~3.0.2", "scheduler": ">=0.19.0"}}