.workflow-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  height: 100%;
  background-color: #eceeef;

  .workflow-content {
    position: relative;
    display: flex;
    flex-direction: row;
    flex: 1;
    overflow: auto;

    .workflow-playground {
      display: flex;
      flex-direction: column;
      flex: 1;

      :global {
        .semi-banner-extra {
          position: absolute;
          top: 14px;
          right: 40px;
          margin-top: 0;
        }
      }
    }

    .workflow-sidesheet {
      overflow: hidden;
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      z-index: 100;
    }

    // 三期可能会需要，暂时先不删除
    // .workflow-running {
    //   position: absolute;
    //   left: 50%;
    //   top: 30px;
    //   padding: 12px;
    //   display: inline-flex;
    //   align-items: center;
    //   border-radius: 4px;
    //   background: var(
    //     --light-usage-info-light-color-info-light-default,
    //     #eff4ff
    //   );
    //   /* shadow-knob */
    //   box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.1),
    //     0px 0px 1px 0px rgba(0, 0, 0, 0.3);

    //   color: var(--light-usage-text-color-text-0, #1c1f23);
    //   font-size: 14px;
    //   font-style: normal;
    //   font-weight: 400;
    //   line-height: 20px;

    //   .spin-container {
    //     display: flex;
    //     margin-right: 12px;

    //     :global {
    //       .semi-spin-wrapper {
    //         display: flex;
    //       }
    //     }
    //   }
    // }
  }

  :global {
    .semi-form-vertical .semi-form-field {
      padding: 0;
    }
  }

  :global {

    // 定义线条的样式
    .workflow-custom-edge {
      &>path {
        stroke: #4d53e8 !important;
        stroke-width: 2;
      }

      &.selected {
        &>.react-flow__edge-path {
          stroke: #66b1ff !important;
        }
      }
    }
  }
}
