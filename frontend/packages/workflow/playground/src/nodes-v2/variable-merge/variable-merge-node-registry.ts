/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type WorkflowNodeRegistry } from '@coze-workflow/nodes';
import { StandardNodeType } from '@coze-workflow/base';

import { VARIABLE_MERGE_FORM_META } from './variable-merge-form-meta';

export const VARIABLE_MERGE_NODE_REGISTRY: WorkflowNodeRegistry = {
  type: StandardNodeType.VariableMerge,
  meta: {
    nodeDTOType: StandardNodeType.VariableMerge,
    style: {
      width: 360,
    },
    size: { width: 360, height: 130.7 },
    hideTest: true,
    helpLink: '/open/docs/guides/variable_merge_node',
  },
  formMeta: VARIABLE_MERGE_FORM_META,
};
