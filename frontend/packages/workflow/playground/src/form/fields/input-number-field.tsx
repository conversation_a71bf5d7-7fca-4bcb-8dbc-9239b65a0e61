/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useNodeTestId } from '@coze-workflow/base';
import {
  CozInputNumber,
  type InputNumberProps as BaseInputNumberProps,
} from '@coze-arch/coze-design';

import { useField } from '../hooks';
import { withField } from '../hocs';
import { type FieldProps } from '../components';

type InputNumberProps = Omit<
  BaseInputNumberProps,
  'value' | 'onChange' | 'onBlur' | 'onFocus'
>;

export const InputNumberField: React.FC<InputNumberProps & FieldProps> =
  withField<InputNumberProps>(props => {
    const { name, value, onChange, onBlur, errors, readonly } = useField<
      number | string
    >();
    const { getNodeSetterId } = useNodeTestId();

    return (
      <CozInputNumber
        {...props}
        disabled={readonly}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        size="small"
        error={errors && errors.length > 0}
        data-testid={getNodeSetterId(name)}
      />
    );
  });
