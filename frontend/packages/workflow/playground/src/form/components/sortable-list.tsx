/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { DragDropContext, Droppable } from 'react-beautiful-dnd';
import { type PropsWithChildren } from 'react';

export function SortableList({
  children,
  onSortEnd,
}: PropsWithChildren<{
  onSortEnd: ({ from, to }: { from: number; to: number }) => void;
}>) {
  return (
    <DragDropContext
      onDragEnd={result => {
        onSortEnd({ from: result.source.index, to: result.destination.index });
      }}
    >
      <Droppable droppableId="droppable">
        {provided => (
          <div ref={provided.innerRef} {...provided.droppableProps}>
            {children}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
}
