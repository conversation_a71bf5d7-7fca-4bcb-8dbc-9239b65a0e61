/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
const workflowPath = 'work_flow';

/**
 * 获取 Workflow 页面 url
 * @param params 相关参数
 * @returns Workflow 页面 url
 */
export const getWorkflowUrl = (params: {
  space_id: string;
  workflow_id: string;
  version?: string;
}) => {
  const urlParams = new URLSearchParams(params);
  return `/${workflowPath}?${urlParams.toString()}`;
};
