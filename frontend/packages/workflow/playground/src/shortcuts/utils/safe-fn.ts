/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { reporter, logger } from '@coze-arch/logger';
import { Toast } from '@coze-arch/coze-design';

export const safeFn =
  (fn: Function) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- no need to check type
  (...args: any[]) => {
    try {
      return fn(...args);
    } catch (e) {
      Toast.error({
        content: `[Coze Workflow] Failed to run function: ${fn.name || '() => any'}`,
      });
      console.error('Failed to run function: ', e);
      reporter.errorEvent({
        namespace: 'workflow',
        eventName: 'workflow_shortcuts_error',
        error: e,
      });
      logger.error(e);
    }
  };
