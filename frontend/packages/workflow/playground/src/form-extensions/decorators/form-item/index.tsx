/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type FC } from 'react';

import { type DecoratorComponentProps } from '@flowgram-adapter/free-layout-editor';

import { FormItem, type FormItemProps } from '../../components/form-item';

const FormItem2Decorator: FC<
  DecoratorComponentProps<
    { key: string } & Omit<FormItemProps, 'title' | 'required'>
  >
> = props => {
  const { children, feedbackText, feedbackStatus, formItemMeta, options } =
    props;
  const { title, required, description } = formItemMeta;

  const { key, ...others } = options;
  return (
    <FormItem
      label={title}
      required={required}
      tooltip={description}
      feedbackText={feedbackText}
      feedbackStatus={feedbackStatus}
      {...others}
    >
      {children}
    </FormItem>
  );
};

export const formItem = {
  key: 'FormItem',
  component: FormItem2Decorator,
};
