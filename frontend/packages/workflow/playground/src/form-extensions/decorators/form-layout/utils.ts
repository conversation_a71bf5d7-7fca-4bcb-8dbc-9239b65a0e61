/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  return array.reduce((previous, current, index) => {
    if (index % chunkSize === 0) {
      previous.push([current]);
    } else {
      previous[previous.length - 1].push(current);
    }
    return previous;
  }, [] as T[][]);
}
