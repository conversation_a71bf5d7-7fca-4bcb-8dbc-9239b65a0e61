/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { I18n } from '@coze-arch/i18n';
import { type DecoratorComponentProps } from '@flowgram-adapter/free-layout-editor';

import { ColumnsTitle } from '../../components/columns-title';

import styles from './index.module.less';

const COLUMNS = [
  {
    title: I18n.t('workflow_detail_node_parameter_name'),
    style: {
      width: 160,
    },
  },
  {
    title: I18n.t('workflow_detail_node_parameter_value'),
    style: {
      width: 160,
    },
  },
];

type ColumnsTitleProps = DecoratorComponentProps<{
  columns?: Array<{
    title: string;
    style: Record<string, unknown>;
  }>;
}>;

const ColumnsTitleDecorator = ({ options, children }: ColumnsTitleProps) => {
  const { columns } = options;
  return (
    <div className={styles['column-title-dec-wrapper']}>
      <ColumnsTitle columns={columns || COLUMNS} />
      {children}
    </div>
  );
};

export const columnsTitle = {
  key: 'ColumnsTitle',
  component: ColumnsTitleDecorator,
};
