/* stylelint-disable no-descending-specificity */
.triangle {
  cursor: pointer;

  transform: translate(-20%, 0%);

  display: flex;
  align-items: center;
  justify-content: center;

  @apply coz-fg-secondary text-sm;

}

.empty-block {
  flex: 1;
}

.help-line-block {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 0;
    left: 0;

    width: 1px;
    height: 100%;

    background: rgba(218, 218, 218, 100%);
  }
}

/** 业务组件样式 */
.half-top-root {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-5px);

    width: 100%;
    height: 32px;

    border-bottom: 1px solid rgba(218, 218, 218, 100%);
    border-left: 1px solid rgba(218, 218, 218, 100%);
    border-radius: 0 0 0 4px;
  }

  .triangle {
    display: none;
  }

  &.children {
    .triangle {
      position: absolute;
      top: 27px;
      left: 70%;
      transform: translate(-17%, -54%);

      display: flex;

      &>svg {
        width: 12px;
      }
    }
  }
}

.half-bottom-root {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 27px;
    left: 0;

    width: 100%;
    height: calc(100% - 12px);

    border-top: 1px solid rgba(218, 218, 218, 100%);
    border-left: 1px solid rgba(218, 218, 218, 100%);
    border-radius: 4px 0 0;
  }

  .triangle {
    display: none;
  }

  &.children {
    .triangle {
      position: absolute;
      top: 27px;
      left: 70%;
      transform: translate3d(0, -40%, 0);

      display: flex;

      &>svg {
        width: 12px;
      }
    }
  }
}

.full-root {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;

  .top-line {
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-5px);

    width: 50%;
    height: 32px;


    border-bottom: 1px solid rgba(218, 218, 218, 100%);
    border-left: 1px solid rgba(218, 218, 218, 100%);
    border-radius: 0 0 0 4px;
  }

  .bottom-line {
    position: absolute;
    top: 19px;
    left: 0;

    width: 1px;
    height: calc(100% - 19px);

    background: rgba(218, 218, 218, 100%);
  }

  .triangle {
    display: none;
  }

  &.children {
    .triangle {
      position: absolute;
      top: 27px;
      left: 70%;
      transform: translate(-17%, -54%);

      display: flex;

      &>svg {
        width: 12px;
      }
    }
  }
}

.half-top-child {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-5px);

    width: 100%;
    height: 32px;

    border-bottom: 1px solid rgba(218, 218, 218, 100%);
    border-left: 1px solid rgba(218, 218, 218, 100%);
    border-radius: 0 0 0 4px;
  }

  .triangle {
    display: none;
  }

  &.children {
    .top-line {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateY(-5px);

      width: 50%;
      height: 32px;


      border-bottom: 1px solid rgba(218, 218, 218, 100%);
      border-left: 1px solid rgba(218, 218, 218, 100%);
      border-radius: 0 0 0 4px;
    }

    .triangle {
      position: absolute;
      top: 27px;
      left: 70%;
      transform: translate(-15%, -54%);

      display: flex;

      &>svg {
        width: 12px;
      }
    }
  }
}

.full-child {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 0;
    left: 0;
    transform: translateY(-5px);

    width: 100%;
    height: 32px;

    border-bottom: 1px solid rgba(218, 218, 218, 100%);
    border-left: 1px solid rgba(218, 218, 218, 100%);
    border-radius: 0 0 0 4px;
  }

  .bottom-line {
    position: absolute;
    top: 19px;
    left: 0;

    width: 1px;
    height: calc(100% - 19px);

    background: rgba(218, 218, 218, 100%);
  }

  .triangle {
    display: none;
  }

  &.children {
    .triangle {
      position: absolute;
      top: 27px;
      left: 70%;
      transform: translate(-15%, -54%);

      display: flex;

      &>svg {
        width: 12px;
      }
    }

    .line {
      width: 50%;
    }
  }
}

.multiline {
  .line {
    position: absolute;
    top: -51px;
    height: 73px;
  }

  .top-line {
    position: absolute;
    top: -51px;
    height: 73px;
  }

  &.with-name-error {
    .line {
      position: absolute;
      top: -7px;
      height: 29px;
    }

    .top-line {
      position: absolute;
      top: -7px;
      height: 29px;
    }
  }
}



.root-with-children {
  position: relative;
  transform: translate(-4px, 16px);

  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;

  width: 20px;
  height: 20px;

  .triangle>svg {
    width: 12px;
  }
}

.level-line {
  transform: translateY(-6px);
  display: flex;
  align-self: stretch;

  // 包含对象和其下钻类型时，设置 flex: 1 0 auto 撑开宽度
  &.could-collapse:not(.readonly) {
    flex: 0 0 auto;
  }

  .expand-line {
    position: absolute;
    top: 30px;
    left: 0;

    width: 1px;
    height: calc(100% - 35px);

    background: rgba(218, 218, 218, 100%);
  }
}
