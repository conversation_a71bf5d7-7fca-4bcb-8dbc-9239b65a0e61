/* stylelint-disable max-nesting-depth */
.container {
  display: flex;
  flex: 1 0 0;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
  align-self: stretch;

  height: 100%;
  padding: 12px 16px 16px;

  background: linear-gradient(180deg, #FFF 0%, #FFF 100%);

  .header {
    display: flex;
    gap: 8px;
    align-items: center;
    align-self: stretch;
    justify-content: space-between;

    height: 32px;

    span {
      display: inline-flex;
      gap: 8px;
      align-items: center;

      height: 32px;
      margin: 0;
      padding: 0;
    }

    .left-side {
      .title {
        font-size: 16px;
        font-weight: 600;
        font-style: normal;
        line-height: 24px;
        color: var(--light-color-grey-grey-9, #1C1F23);
      }

      .info {
        color: var(--light-color-grey-grey-3, #A7A7B0);

        svg {
          width: 12px;
          height: 12px;
        }
      }
    }

    // .right-side {}

    .tooltip-pop-container {
      pointer-events: none;

      position: absolute;
      top: 0;
      left: 0;

      width: 0;
      height: 0;
    }
  }

  .content {
    display: flex;
    flex: 1 0 0;
    flex-direction: column;
    align-items: flex-end;
    align-self: stretch;

    min-height: 0;

    border-radius: 6px;
  }
}

.tip {
  z-index: 9999;

  :global {
    .semi-tooltip-content {
      white-space: pre-line;
    }
  }
}
