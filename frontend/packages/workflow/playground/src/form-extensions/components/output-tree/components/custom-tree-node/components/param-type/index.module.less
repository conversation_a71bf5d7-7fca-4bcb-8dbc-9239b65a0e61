/* stylelint-disable max-nesting-depth */
.container {
  // align-items: center;
  position: relative;
  flex-direction: column;
  align-self: stretch;

  .pop-container {
    align-self: self-start;
  }

  .param-type {
    height: 32px;
    padding: 0 1px;
  }

  // 已经无用，可以删除，添加子项按钮已经放在单独组件中了
  .param-operator {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    align-self: stretch;
    justify-content: flex-start;

    width: 86px;
    height: 32px;
    margin-left: 8px;

    .icon-no {
      display: flex;
      align-items: center;

      .icon {
        cursor: pointer;
        width: 20px;
        height: 20px;
        color: #888D92;

        &.disabled {
          cursor: not-allowed;
        }

        &>svg {
          width: 20px;
          height: 20px;
        }
      }
    }

    .add {
      cursor: pointer;

      margin-left: 8px;

      font-size: 12px;
      font-weight: 600;
      font-style: normal;
      line-height: 16px;
      color: #4D53E8;
    }
  }

  :global {
    .semi-cascader {
      width: 0;
      min-width: 100%;
    }

    .semi-cascader-selection {
      padding-right: 0;
      padding-left: 4px;

      .icon-icon {
        flex-shrink: 0;
        font-size: 16px;
      }
    }

    .semi-cascader-arrow {
      flex-shrink: 0;
    }

    .semi-input-wrapper.semi-input-wrapper-small {
      background-color: #FFF;

      &:not(.semi-input-wrapper-focus, .semi-input-wrapper-disabled) {
        &:hover {
          background-color: rgba(var(--coze-bg-5),var(--coze-bg-5-alpha));
        }

        &:active {
          background-color: rgba(var(--coze-bg-6), var(--coze-bg-6-alpha));
        }
      }
    }
  }
}
