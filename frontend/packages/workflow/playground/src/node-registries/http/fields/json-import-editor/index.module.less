/* stylelint-disable selector-class-pattern, no-descending-specificity */

.container {
  position: relative;

  .content {
    overflow-x: auto;

    &.readonly {
      overflow: visible;

      :global {
        .semi-tree-option-list-block .semi-tree-option:hover {
          background-color: inherit;
        }
      }
    }

    .add-hot-area {
      cursor: pointer;
      height: 16px;
    }

    .empty-placeholder {
      font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont,
        'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
        'Helvetica Neue', Helvetica, Arial, sans-serif;
      font-size: 16px;
      font-weight: 400;
      font-style: normal;
      line-height: 44px;
      color: var(--light-usage-text-color-text-2, rgba(29, 28, 35, 60%));
    }

    :global {
      .semi-tree-option-list {
        overflow: initial;
        padding: 0 0 8px;

        & > div:first-child {
          margin-top: 0;
        }
      }
    }
  }

  :global {
    .semi-tree-option-list {
      .semi-tree-option {
        padding-left: 8px;
      }
    }
  }

  &.not-readonly {
    :global {
      .semi-tree-option-list {
        overflow: inherit;

        .semi-tree-option {
          padding-left: 8px;
        }
      }

      .semi-tree-option-list-block .semi-tree-option {
        cursor: default;

        &:hover,
        &:active {
          background: transparent;
        }
      }
    }

    &:not(.could-collapse) {
      :global {
        .semi-tree-option.semi-tree-option-fullLabel-level-1 {
          padding-left: 0;
        }
      }
    }
  }
}

// 导入按钮样式
.import-button {
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 4px 8px;

  border-radius: 6px;

  &:not(.semi-button-disabled):hover {
    background-color: var(--semi-color-fill-1, rgba(46, 46, 56, 8%));
  }

  &:not(.semi-button-disabled):active {
    background-color: var(--semi-color-fill-2, rgba(46, 46, 56, 12%));
  }

  :global {
    p {
      font-size: 12px;
      font-weight: 500;
      font-style: normal;
      line-height: 16px;
    }

    .semi-button-content{
      line-height: 16px;
    }

    .semi-button-content-right {
      margin-left: 4px;
    }
  }
}

.import-button-disabled {
  opacity: 0.5;
  filter: grayscale(100%);
}

.button-icon {
  color: var(--semi-color-primary);
}

.json-editor-container {
  position: relative;

  display: inline-block;

  box-sizing: border-box;
  width: 100%;

  word-break: break-all;
  vertical-align: bottom;

  background-color: var(--semi-color-white);
  border-radius: 8px;

  transition:
    background-color var(--semi-transition_duration-none)
      var(--semi-transition_function-easeIn) var(--semi-transition_delay-none),
    border var(--semi-transition_duration-none)
      var(--semi-transition_function-easeIn) var(--semi-transition_delay-none)
}

.expand-header{
  height: 48px;

  font-size: 16px;
  font-weight: 500;
  font-style: normal;
  line-height: 22px;

  border-bottom: 1px solid var(--Stroke-COZ-stroke-plus, rgba(153, 182, 255, 17%));
}

.expand-header-dark{
  --Fg-COZ-fg-secondary: rgba(255, 255, 255, 39%);

  background: var(--Bg-COZ-bg-plus, #1F2533);
}

.expand-content{
  height: calc(100% - 48px);
}

.expand-content-dark{
  background: var(--Bg-COZ-bg-plus, #1F2533);
}

.editor-wrapper{
  width: 100%;
  height: 100%;

  & > div{
    height: 100%;
  }
}

.theme-icon-dark {
  color: var(--Fg-COZ-fg-primary,#FFFFFFC9);
}

.theme-icon-light{
  color: var(--Fg-COZ-fg-primary, rgba(15, 21, 41, 82%));
}
