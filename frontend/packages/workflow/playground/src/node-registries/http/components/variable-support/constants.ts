/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export enum MenuType {
  Nodes = 'nodes',
  Variables = 'variables',
}

export enum DropdownType {
  /**
   * 用户输入唤起
   */
  Input = 'Input',
  /**
   * 点击变量唤起
   */
  Update = 'Update',
}

export const iconCozWarningCircleFillPaletteSvg = (
  color: string,
) => `<svg width="14" height="14" viewBox="0 0 14 14" fill="none"  xmlns="http://www.w3.org/2000/svg">
<path d="M7.0026 13.4154C10.5464 13.4154 13.4193 10.5425 13.4193 6.9987C13.4193 3.45487 10.5464 0.582031 7.0026 0.582031C3.45878 0.582031 0.585938 3.45487 0.585938 6.9987C0.585938 10.5425 3.45878 13.4154 7.0026 13.4154Z" fill="#FF7300"/>
<path d="M6.9974 4.08203C6.67523 4.08203 6.41406 4.3432 6.41406 4.66536V7.58203C6.41406 7.9042 6.67523 8.16536 6.9974 8.16536C7.31956 8.16536 7.58073 7.9042 7.58073 7.58203V4.66536C7.58073 4.3432 7.31956 4.08203 6.9974 4.08203Z" fill="white"/>
<path d="M6.9974 8.7487C6.67523 8.7487 6.41406 9.00987 6.41406 9.33203C6.41406 9.6542 6.67523 9.91537 6.9974 9.91537C7.31956 9.91537 7.58073 9.6542 7.58073 9.33203C7.58073 9.00987 7.31956 8.7487 6.9974 8.7487Z" fill="white"/>
</svg>
`;

export const iconCozFolderUrlSvg = (
  color: string,
) => `<svg width="14" height="14" viewBox="0 0 16 16" fill="none"  xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.00008 1.33301C1.2637 1.33301 0.666748 1.92996 0.666748 2.66634V12.6663C0.666748 13.4027 1.2637 13.9997 2.00008 13.9997H14.0001C14.7365 13.9997 15.3334 13.4027 15.3334 12.6663V3.99967C15.3334 3.26329 14.7365 2.66634 14.0001 2.66634H8.00008L7.05727 1.72353C6.80722 1.47348 6.46809 1.33301 6.11446 1.33301H2.00008ZM8.00008 3.99967C7.64646 3.99967 7.30732 3.8592 7.05727 3.60915L6.11446 2.66634L2.00008 2.66634L2.00008 12.6663H14.0001V3.99967H8.00008Z" fill="${color}" fill-opacity="0.9" style="fill:${color};fill-opacity:0.9;"/>
</svg>
`;

export const iconCozPeopleUrlSvg = (
  color: string,
) => `<svg width="14" height="14" viewBox="0 0 16 16" fill="none"  xmlns="http://www.w3.org/2000/svg">
<path d="M6 8.66634H10C11.841 8.66634 14 9.95205 14 12.2663V13.333C14 14.0663 13.4 14.6663 12.6667 14.6663H3.33333C2.6 14.6663 2 14.0663 2 13.333V12.2663C2 9.95399 4.15905 8.66634 6 8.66634ZM12.6667 13.333V12.2219C12.6667 10.8209 11.2473 9.99967 10 9.99967H6C4.77998 9.99967 3.33333 10.7808 3.33333 12.2219V13.333H12.6667ZM8 7.99967C6.15905 7.99967 4.66667 6.50729 4.66667 4.66634C4.66667 2.82539 6.15905 1.33301 8 1.33301C9.84095 1.33301 11.3333 2.82539 11.3333 4.66634C11.3333 6.50729 9.84095 7.99967 8 7.99967ZM8 6.66634C9.10457 6.66634 10 5.77091 10 4.66634C10 3.56177 9.10457 2.66634 8 2.66634C6.89543 2.66634 6 3.56177 6 4.66634C6 5.77091 6.89543 6.66634 8 6.66634Z" fill="${color}" fill-opacity="0.9" style="fill:${color};fill-opacity:0.9;"/>
</svg>
`;
export const iconCozSettingUrlSvg = (
  color: string,
) => `<svg width="14" height="14" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.88563 13.156L2.67821 12.9301C1.95205 12.1391 1.40724 11.1957 1.08784 10.1654L0.997803 9.875L2.36317 8.00027L0.997803 6.12554L1.08784 5.83509C1.40724 4.80479 1.95205 3.86139 2.67821 3.07043L2.88563 2.84451L5.18017 3.0953L6.11215 0.970749L6.41099 0.90347C6.92883 0.786888 7.46082 0.727539 8.00006 0.727539C8.5393 0.727539 9.07129 0.786888 9.58912 0.90347L9.88797 0.970749L10.8199 3.0953L13.1145 2.84451L13.3219 3.07043C14.0481 3.86139 14.5929 4.80479 14.9123 5.83509L15.0023 6.12554L13.6369 8.00027L15.0023 9.875L14.9123 10.1654C14.5929 11.1957 14.0481 12.1391 13.3219 12.9301L13.1145 13.156L10.8199 12.9052L9.88797 15.0298L9.58912 15.0971C9.07129 15.2136 8.5393 15.273 8.00006 15.273C7.46082 15.273 6.92883 15.2136 6.41099 15.0971L6.11215 15.0298L5.18017 12.9052L2.88563 13.156ZM5.20903 11.6827C5.63977 11.6356 6.05124 11.8735 6.22531 12.2704L6.97227 13.9731C7.30917 14.0313 7.65258 14.0608 8.00006 14.0608C8.34754 14.0608 8.69095 14.0313 9.02785 13.9731L9.77481 12.2704C9.94888 11.8735 10.3603 11.6356 10.7911 11.6827L12.6272 11.8834C13.0707 11.3496 13.4204 10.7431 13.659 10.0895L12.5628 8.58427C12.3093 8.23618 12.3093 7.76428 12.5628 7.41619L13.659 5.91097C13.4204 5.25737 13.0707 4.65084 12.6272 4.11707L10.7911 4.31776C10.3603 4.36484 9.94888 4.12691 9.77481 3.7301L9.02785 2.02731C8.69095 1.96913 8.34754 1.93962 8.00006 1.93962C7.65258 1.93962 7.30917 1.96913 6.97227 2.02731L6.22531 3.7301C6.05124 4.12691 5.63977 4.36484 5.20903 4.31776L3.37288 4.11707C2.92942 4.65084 2.57977 5.25737 2.34108 5.91097L3.43733 7.41619C3.69085 7.76428 3.69085 8.23618 3.43733 8.58427L2.34108 10.0895C2.57977 10.7431 2.92942 11.3496 3.37288 11.8834L5.20903 11.6827ZM8.00006 11.0306C6.3322 11.0306 4.9813 9.6732 4.9813 8.00026C4.9813 6.32732 6.3322 4.96996 8.00006 4.96996C9.66792 4.96996 11.0188 6.32732 11.0188 8.00026C11.0188 9.6732 9.66792 11.0306 8.00006 11.0306ZM8.00006 9.81849C8.99719 9.81849 9.8067 9.0051 9.8067 8.0003C9.8067 6.9955 8.99719 6.18212 8.00006 6.18212C7.00293 6.18212 6.19342 6.9955 6.19342 8.0003C6.19342 9.0051 7.00293 9.81849 8.00006 9.81849Z" fill="${color}" fill-opacity="0.9" style="fill:${color};fill-opacity:0.9;"/>
</svg>
`;
