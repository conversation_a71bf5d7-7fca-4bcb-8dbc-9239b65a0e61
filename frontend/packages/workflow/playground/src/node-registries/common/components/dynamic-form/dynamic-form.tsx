/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useMemo, type FC } from 'react';

import { Field } from '@/form';

import { type DynamicComponentProps, type FormMeta } from './types';

export interface DynamicFormProps {
  formMeta: FormMeta;
  name: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  components: Record<string, FC<DynamicComponentProps<any>>>;
  // 禁用做触发
  onChange?: () => void;
}

export const DynamicForm: FC<DynamicFormProps> = ({
  formMeta,
  name,
  components,
  onChange,
}) => {
  const fields = useMemo(
    () =>
      formMeta.map(field => {
        const Component =
          components[field.setter] ??
          (() => <div>component {field.setter} not exist</div>);

        return (
          <Field
            {...field}
            key={field.name}
            name={`${name}.${field.name}`}
            layout={field.layout ?? 'vertical'}
          >
            {({ value, onChange: _onChange, readonly }) => (
              <Component
                {...field.setterProps}
                value={value}
                onChange={(...args) => {
                  _onChange(...args);
                  onChange?.();
                }}
                readonly={readonly}
              />
            )}
          </Field>
        );
      }),
    [formMeta, name, components],
  );
  return <>{fields}</>;
};
