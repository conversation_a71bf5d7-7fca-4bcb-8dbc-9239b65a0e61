/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useCallback, useEffect, useState } from 'react';

import classNames from 'classnames';
import {
  useCurrentEntity,
  useService,
} from '@flowgram-adapter/free-layout-editor';
import { WorkflowVariableFacadeService } from '@coze-workflow/variable';
import { type RefExpression, useNodeTestId } from '@coze-workflow/base';
import { I18n } from '@coze-arch/i18n';
import { IconCozInfoCircle } from '@coze-arch/coze-design/icons';
import { Input, Tooltip, Typography } from '@coze-arch/coze-design';

import { useReadonly } from '@/nodes-v2/hooks/use-readonly';
import {
  getUniqueName,
  getVariableName,
} from '@/form-extensions/setters/node-input-name/utils';
import { useField } from '@/form';

import type { NodeInputNameProps } from './type';

export const NodeInputName = ({
  style,
  input,
  inputParameters,
  initValidate = false,
  isPureText = false,
  prefix = '',
  suffix = '',
  placeholder = I18n.t('workflow_detail_node_input_entername'),
  format,
  tooltip,
  readonly: customReadonly,
}: NodeInputNameProps) => {
  const { name, value, onChange, onBlur, errors } = useField<string>();

  const [initialized, setInitialized] = useState<boolean>(false);
  const [userEdited, setUserEdited] = useState<boolean>(false);
  const [variableName, setVariableName] = useState<string | undefined>(value);
  const [text, setText] = useState<string | undefined>(value);
  const formReadonly = useReadonly();
  const readonly = formReadonly || customReadonly;

  const node = useCurrentEntity();
  const variableService = useService(WorkflowVariableFacadeService);
  const { getNodeSetterId } = useNodeTestId();

  // text 状态受控（删除节点时联动 text 的值）
  useEffect(() => {
    if (value !== text) {
      setText(value);
    }
  }, [value]);

  const computedVariableName = getVariableName({
    input: input as RefExpression,
    prefix,
    suffix,
    format,
    node,
    variableService,
  });

  const onInputChange = useCallback((newInputValue: string): void => {
    setUserEdited(true);
    setText(newInputValue || '');
    setVariableName(undefined);
  }, []);

  const handleOnBlur = () => {
    onChange(text || '');
    onBlur?.();
  };

  useEffect(() => {
    if (initValidate) {
      // 初始化写值触发校验
      onChange(value as string);
    }
    if (value) {
      setUserEdited(true);
    }
    setInitialized(true);
  }, []);

  if (initialized && !readonly && !userEdited) {
    if (computedVariableName && computedVariableName !== variableName) {
      const computedUniqueName = getUniqueName({
        variableName: computedVariableName,
        inputParameters,
      });
      onChange(computedUniqueName);
      setVariableName(computedVariableName);
      setText(computedUniqueName);
    } else if (!computedVariableName && variableName) {
      setVariableName(undefined);
      setText(undefined);
    }
  }

  return (
    <div
      className="flex flex-col items-start"
      style={{
        ...style,
        pointerEvents: readonly ? 'none' : 'auto',
      }}
    >
      {isPureText ? (
        <>
          <Typography.Text className="h-8 leading-8 overflow-hidden whitespace-nowrap text-ellipsis">
            {value}
          </Typography.Text>
          {tooltip ? (
            <Tooltip content={tooltip}>
              <IconCozInfoCircle
                className="ml-1"
                style={{
                  fontSize: 12,
                }}
              />
            </Tooltip>
          ) : null}
        </>
      ) : (
        <>
          <Input
            className={classNames({
              'semi-input-wrapper-error': errors?.length,
            })}
            size={'small'}
            data-testid={getNodeSetterId(name)}
            value={text}
            onChange={onInputChange}
            onBlur={handleOnBlur}
            validateStatus={errors?.length ? 'error' : undefined}
            placeholder={placeholder}
            readonly={readonly}
          />
          {tooltip ? (
            <Tooltip content={tooltip}>
              <IconCozInfoCircle
                className="ml-1"
                style={{
                  fontSize: 12,
                }}
              />
            </Tooltip>
          ) : null}
        </>
      )}
    </div>
  );
};
