/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { useNodeRenderScene } from '@/hooks';
import type { ConditionValue } from '@/form-extensions/setters/condition/multi-condition/types';
import { useField, withField } from '@/form';

import { MultiCondition } from './multi-condition';
import { HiddenCondition } from './hidden-condition';

export const ConditionField = withField(() => {
  const { value, onChange, readonly } = useField<ConditionValue>();

  const { isNewNodeRender } = useNodeRenderScene();

  if (isNewNodeRender) {
    return <HiddenCondition value={value} onChange={onChange} />;
  } else {
    return (
      <MultiCondition
        value={value}
        onChange={onChange}
        readonly={readonly ?? false}
      />
    );
  }
});
