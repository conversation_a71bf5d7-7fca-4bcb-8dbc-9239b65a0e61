/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { useDataSetInfos } from '@/hooks';
import { type DataSetInfo } from '@/form-extensions/components/dataset-setting/type';
import { DataSetSetting as BaseDatasetSetting } from '@/form-extensions/components/dataset-setting';
import { useField, useWatch, withField } from '@/form';

const DatasetSetting = () => {
  const { value, onChange, onBlur, readonly } = useField<DataSetInfo>();
  const selectDataSet = useWatch<string[]>(
    'inputs.datasetParameters.datasetParam',
  );

  const { dataSets, isReady } = useDataSetInfos({ ids: selectDataSet });

  return (
    <BaseDatasetSetting
      dataSetInfo={value as DataSetInfo}
      onDataSetInfoChange={(v: DataSetInfo) => {
        onChange(v);
        onBlur?.();
      }}
      readonly={!!readonly}
      dataSets={dataSets}
      isReady={isReady}
    />
  );
};

export const DatasetSettingField = withField(DatasetSetting);
