/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import type { FormItemMaterialContext } from '@flowgram-adapter/free-layout-editor';
import {
  WorkflowNode,
  type InputValueVO,
  getFormValueByPathEnds,
} from '@coze-workflow/base';

export const getLoopInputNames = (
  context: FormItemMaterialContext,
): string[] => {
  const workflowNode = new WorkflowNode(context.node);
  const loopInputParameters: InputValueVO[] =
    workflowNode?.inputParameters ?? [];
  const loopVariables: InputValueVO[] =
    getFormValueByPathEnds<InputValueVO[]>(
      context.node,
      '/variableParameters',
    ) ?? [];
  const loopInputs = [...loopInputParameters, ...loopVariables];
  return loopInputs.map(input => input.name).filter(Boolean) as string[];
};
