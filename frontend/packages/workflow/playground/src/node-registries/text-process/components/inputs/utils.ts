/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export const getMaxIndex = (values: string[], prefixStr: string): number => {
  if (!Array.isArray(values)) {
    return 1;
  }

  const maxIndex =
    values.length === 0
      ? 0
      : Math.max(
          ...values
            .map(item => Number(item?.split(prefixStr)[1] ?? 0))
            .filter(v => !isNaN(v)),
        );

  return maxIndex + 1;
};
