/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export enum FileItemStatus {
  Success = 'success',
  UploadFail = 'uploadFail',
  ValidateFail = 'validateFail',
  Validating = 'validating',
  Uploading = 'uploading',
  Wait = 'wait',
}

export interface FileItem extends File {
  // 唯一标识
  uid?: string;
  // 文件地址
  url?: string;
  // 上传进度
  percent?: number;
  // 校验信息
  validateMessage?: string;
  status?: FileItemStatus;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}
