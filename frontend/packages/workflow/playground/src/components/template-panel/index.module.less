.list-container{
  scrollbar-width: none;
}

.list-container::-webkit-scrollbar{
  display: none;
}

.template-card{
  transform: translateY(100%);

  opacity: 0;
  border: 1px solid var(--Stroke-COZ-stroke-plus, rgba(6, 7, 9, 15%));
  border-radius: var(--default, 8px);
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 4%), 0 4px 12px 0 rgba(0, 0, 0, 2%);

  transition: transform 0.4s cubic-bezier(0.32, 0.1, 0.67, 0), opacity 0.4s, box-shadow 0.2s;

  .card-header{
    box-sizing: content-box;
    border-radius: var(--small, 6px);

    img{
      border: 1px solid var(--Stroke-COZ-stroke-plus, rgba(6, 7, 9, 15%));
    }
  }

  &:hover {
    transform: translateY(-8px);
  }
}

.slide-up {
  transform: translateY(0);
  opacity: 1;
  transition: transform 0.2s cubic-bezier(0.25, 0.1, 0.25, 1), opacity 0.4s;
}

.slide-button{
  top: calc((100% - 24px)/2);
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 1s;
}

.slide-button-visible{
  opacity: 1;
}

.template-panel-container{
  opacity: 1;
  transition: transform 0.2s cubic-bezier(0.32, 0.1, 0.67, 0), opacity 0.2s;
}

.template-slide-down{
  transform: translateY(100%);
  opacity: 0;
}
