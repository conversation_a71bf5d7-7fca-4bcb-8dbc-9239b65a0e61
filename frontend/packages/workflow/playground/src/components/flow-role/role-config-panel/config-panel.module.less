.config-panel {
  position: relative;

  width: 360px;
  height: 100%;

  background: var(--coz-bg-plus);
  border: 1px solid var(--coz-stroke-plus);
  border-radius: 8px;

  :global .coz-icon-button {
    line-height: 0;
  }
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  height: 48px;
  padding: 0 12px;

  border-bottom: 1px solid var(--coz-stroke-primary);
}

.panel-content {
  overflow-y: auto;
  height: calc(100% - 48px);
}
