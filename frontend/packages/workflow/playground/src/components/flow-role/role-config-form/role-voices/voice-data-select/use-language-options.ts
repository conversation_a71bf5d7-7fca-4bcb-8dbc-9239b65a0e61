/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useState } from 'react';

import { useRequest } from 'ahooks';
import { PlaygroundApi } from '@coze-arch/bot-api';

export interface LanguageOption {
  languageCode: string;
  languageName: string;
}

export const useLanguageOptions = () => {
  const [options, setOptions] = useState<LanguageOption[]>([]);

  const { loading } = useRequest(() => PlaygroundApi.GetSupportLanguage(), {
    onSuccess: res =>
      setOptions(
        res.language_list?.map(item => ({
          languageCode: item.language_code ?? '',
          languageName: item.language_name ?? '',
        })) ?? [],
      ),
    onError: () => setOptions([]),
  });

  return { loading, options };
};
