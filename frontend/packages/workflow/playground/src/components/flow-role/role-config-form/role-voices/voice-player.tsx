/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { IconCozPauseFill, IconCozVolume } from '@coze-arch/coze-design/icons';
import { IconButton } from '@coze-arch/coze-design';
import { useAudioPlayer } from '@coze-workflow/resources-adapter';

interface VoicePlayerProps {
  preview?: string;
}

export const VoicePlayer: React.FC<VoicePlayerProps> = ({ preview }) => {
  const { isPlaying, togglePlayPause } = useAudioPlayer(preview);

  return (
    <IconButton
      disabled={!preview}
      onClick={e => {
        e.stopPropagation();
        togglePlayPause();
      }}
      icon={isPlaying ? <IconCozPauseFill /> : <IconCozVolume />}
      size="small"
      color="secondary"
    />
  );
};
