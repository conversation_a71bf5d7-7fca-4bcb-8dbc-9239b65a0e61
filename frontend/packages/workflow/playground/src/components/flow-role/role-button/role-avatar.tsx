/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { CozAvatar } from '@coze-arch/coze-design';

import { EmptyRoleAvatar } from '../empty-role-avatar';

import css from './role-avatar.module.less';

interface RoleAvatarProps {
  url?: string;
}

export const RoleAvatar: React.FC<RoleAvatarProps> = ({ url }) => {
  if (!url) {
    return <EmptyRoleAvatar size="small" className={css['role-avatar']} />;
  }
  return <CozAvatar src={url} className={css['role-avatar']} size="small" />;
};
