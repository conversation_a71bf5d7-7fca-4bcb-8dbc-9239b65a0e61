.container {
  box-sizing: border-box;
  height: 40px;
  padding: 8px;

  background: var(--coz-bg-max, #FFF);
  border: 1px solid var(--coz-stroke-plus, rgba(68, 83, 130, 25%));
  border-radius: var(--default, 8px);

  &:hover {
    background: var(--coz-mg-secondary-hovered);
  }

  .name {
    overflow: hidden;
    flex: 1;

    margin-left: 12px;

    font-size: 14px;
    line-height: 20px;
  }

  .progress {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
    margin: 0;
  }

  .progress-inner {
    height: 100%;
    background: linear-gradient(
            to bottom,
            rgba(161, 170, 255, 38%) 0%,
            rgba(161, 170, 255, 0%) 100%
    );
  }

  .file-icon-loading {
    :global {
      .semi-spin-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }
    }
  }


}