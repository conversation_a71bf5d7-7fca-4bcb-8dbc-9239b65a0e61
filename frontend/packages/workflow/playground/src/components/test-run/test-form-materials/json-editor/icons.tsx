/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
function IconLight() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 18 18"
      fill="none"
    >
      <path
        d="M8.32834 0.96886C8.32834 0.597733 8.6292 0.296875 9.00032 0.296875C9.37145 0.296875 9.67231 0.597733 9.67231 0.968861V1.81512C9.67231 2.18624 9.37145 2.4871 9.00032 2.4871C8.6292 2.4871 8.32834 2.18624 8.32834 1.81512V0.96886Z"
        fill="currentColor"
      />
      <path
        d="M14.2043 2.84603C14.4667 2.5836 14.8922 2.5836 15.1546 2.84603C15.417 3.10846 15.417 3.53393 15.1546 3.79636L14.5562 4.39475C14.2938 4.65718 13.8683 4.65718 13.6059 4.39475C13.3435 4.13233 13.3435 3.70685 13.6059 3.44442L14.2043 2.84603Z"
        fill="currentColor"
      />
      <path
        d="M17.7038 9.00032C17.7038 8.6292 17.4029 8.32834 17.0318 8.32834H16.1856C15.8144 8.32834 15.5136 8.6292 15.5136 9.00032C15.5136 9.37145 15.8144 9.67231 16.1856 9.67231H17.0318C17.4029 9.67231 17.7038 9.37145 17.7038 9.00032Z"
        fill="currentColor"
      />
      <path
        d="M15.1546 14.2043C15.4171 14.4667 15.4171 14.8922 15.1546 15.1546C14.8922 15.417 14.4667 15.417 14.2043 15.1546L13.6059 14.5562C13.3435 14.2938 13.3435 13.8683 13.6059 13.6059C13.8683 13.3435 14.2938 13.3435 14.5562 13.6059L15.1546 14.2043Z"
        fill="currentColor"
      />
      <path
        d="M9.00035 17.7038C9.37148 17.7038 9.67233 17.4029 9.67233 17.0318V16.1856C9.67233 15.8144 9.37148 15.5136 9.00035 15.5136C8.62922 15.5136 8.32836 15.8144 8.32836 16.1856V17.0318C8.32836 17.4029 8.62922 17.7038 9.00035 17.7038Z"
        fill="currentColor"
      />
      <path
        d="M3.79639 15.1546C3.53397 15.4171 3.10849 15.4171 2.84606 15.1546C2.58364 14.8922 2.58364 14.4667 2.84606 14.2043L3.44446 13.6059C3.70688 13.3435 4.13236 13.3435 4.39479 13.6059C4.65721 13.8683 4.65721 14.2938 4.39479 14.5562L3.79639 15.1546Z"
        fill="currentColor"
      />
      <path
        d="M0.296875 9.00035C0.296875 9.37148 0.597733 9.67233 0.96886 9.67233H1.81512C2.18624 9.67233 2.4871 9.37148 2.4871 9.00035C2.4871 8.62922 2.18624 8.32836 1.81512 8.32836H0.968861C0.597733 8.32836 0.296875 8.62922 0.296875 9.00035Z"
        fill="currentColor"
      />
      <path
        d="M2.84603 3.79639C2.5836 3.53397 2.5836 3.10849 2.84603 2.84606C3.10846 2.58364 3.53393 2.58364 3.79636 2.84606L4.39475 3.44446C4.65718 3.70688 4.65718 4.13236 4.39475 4.39479C4.13233 4.65721 3.70685 4.65721 3.44442 4.39479L2.84603 3.79639Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.5913 9.00033C14.5913 12.0881 12.0882 14.5913 9.00036 14.5913C5.91257 14.5913 3.40942 12.0881 3.40942 9.00033C3.40942 5.91253 5.91257 3.40937 9.00036 3.40937C12.0882 3.40937 14.5913 5.91253 14.5913 9.00033ZM13.2484 9.00034C13.2484 11.3465 11.3465 13.2484 9.00034 13.2484C6.6542 13.2484 4.75229 11.3465 4.75229 9.00034C4.75229 6.6542 6.6542 4.75229 9.00034 4.75229C11.3465 4.75229 13.2484 6.6542 13.2484 9.00034Z"
        fill="currentColor"
      />
    </svg>
  );
}

function IconDark() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 16 18"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.4236 11.2192C8.85626 10.5313 7.33271 7.89246 8.02062 5.32516C8.27455 4.37747 8.66403 3.83272 9.25911 2.92849C10.0132 1.78265 9.83575 0.952788 8.91536 0.865985C5.47786 0.541789 1.56343 2.86076 0.490191 6.86615C-0.689079 11.2672 1.92273 15.791 6.32382 16.9703C10.9128 18.1999 14.6785 15.0743 15.6758 12.8972C16.0608 12.0567 15.332 11.2192 14.1862 11.2931C13.106 11.3627 12.3713 11.4732 11.4236 11.2192ZM13.8042 12.6547C14.0081 12.6165 14.1709 12.8367 14.0501 13.0054C12.4306 15.266 9.52439 16.4044 6.6797 15.6421C3.01212 14.6594 0.835614 10.8896 1.81834 7.22202C2.58057 4.37733 5.01954 2.42967 7.78701 2.15746C7.99355 2.13715 8.10286 2.38822 7.96776 2.54577C7.38427 3.22628 6.94014 4.04499 6.69247 4.96928C5.80802 8.2701 7.76687 11.6629 11.0677 12.5474C11.992 12.7951 12.9231 12.8198 13.8042 12.6547Z"
        fill="currentColor"
      />
    </svg>
  );
}

export { IconLight, IconDark };
