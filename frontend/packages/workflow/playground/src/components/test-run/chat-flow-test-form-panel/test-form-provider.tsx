/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { createContext, useContext, useMemo } from 'react';

import { createWithEqualityFn } from 'zustand/traditional';
import { shallow } from 'zustand/shallow';

interface ChatFlowTestFormState {
  visible: boolean;
  hasForm: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formData: null | Record<string, any>;
}

interface ChatFlowTestFormAction {
  patch: (next: Partial<ChatFlowTestFormState>) => void;
  getFormData: () => ChatFlowTestFormState['formData'];
}

const createChatFlowTestFormStore = () =>
  createWithEqualityFn<ChatFlowTestFormState & ChatFlowTestFormAction>(
    (set, get) => ({
      visible: false,
      hasForm: false,
      formData: null,
      patch: next => set(() => next),
      getFormData: () => get().formData,
    }),
    shallow,
  );

type ChatFlowTestFormStore = ReturnType<typeof createChatFlowTestFormStore>;

const chatFlowTestFormContext = createContext<ChatFlowTestFormStore>(
  {} as unknown as ChatFlowTestFormStore,
);

const useChatFlowTestFormStore = <T,>(
  selector: (s: ChatFlowTestFormState & ChatFlowTestFormAction) => T,
) => {
  const store = useContext(chatFlowTestFormContext);
  return store(selector);
};

const ChatFlowTestFormProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const store = useMemo(() => createChatFlowTestFormStore(), []);

  return (
    <chatFlowTestFormContext.Provider value={store}>
      {children}
    </chatFlowTestFormContext.Provider>
  );
};

export { ChatFlowTestFormProvider, useChatFlowTestFormStore };
