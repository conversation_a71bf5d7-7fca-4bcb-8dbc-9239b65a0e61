/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useToolbarHandlers } from '../hooks';
import { Tools } from './tools';
import { Minimap } from './minimap';

import css from './tools.module.less';

export const ToolbarContainer = ({
  disableTraceAndTestRun,
}: {
  disableTraceAndTestRun?: boolean;
}) => {
  const handlers = useToolbarHandlers();

  if (disableTraceAndTestRun) {
    return <></>;
  }

  return (
    <div className={css.tools}>
      <div>
        <Minimap handlers={handlers} />
        <Tools handlers={handlers} />
      </div>
    </div>
  );
};
