.json-viewer {
  max-height: unset;
  padding: 0 6px;
  background-color: unset;
  border: unset;

  :global {
    // .flow-test-run-line {
    //   width: 38px;

    //   // &::before {}
    //   &::after {
    //     width: 30px;
    //     border-bottom-left-radius: 2px;
    //   }
    // }

    .field-icon {
      color: var(--Light-usage-text---color-text-1, rgba(29, 28, 35, 80%));
    }

    .field-key {
      color: var(--Light-usage-text---color-text-1, rgba(28, 31, 35, 80%));
    }

    .field-value {
      color: var(--Light-usage-text---color-text-1, rgba(28, 31, 35, 80%));

    }
  }
}
