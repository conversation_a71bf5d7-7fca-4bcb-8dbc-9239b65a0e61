/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { I18n } from '@coze-arch/i18n';
import { Typography } from '@coze-arch/bot-semi';

import styles from './index.module.less';

export const CollaborationCloseIntroduction = () => (
  <div className={styles['intro-container']}>
    <Typography.Paragraph className={styles.text}>
      {I18n.t('wmv_publish_multibranch_IntroTitle')}
    </Typography.Paragraph>

    <Typography.Paragraph strong className={styles['intro-title']}>
      {I18n.t('devops_publish_multibranch_PersionDrafts')}
    </Typography.Paragraph>
    <Typography.Paragraph>
      {I18n.t('devops_publish_multibranch_PersionDraftsInfo')}
    </Typography.Paragraph>

    <Typography.Paragraph strong className={styles['intro-title']}>
      {I18n.t('devops_publish_multibranch_VersionControl')}
    </Typography.Paragraph>
    <Typography.Paragraph>
      {I18n.t('devops_publish_multibranch_VersionControlInfo')}
    </Typography.Paragraph>

    <Typography.Paragraph strong className={styles['intro-title']}>
      {I18n.t('devops_publish_multibranch_RetrieveAndMerge')}
    </Typography.Paragraph>
    <Typography.Paragraph>
      {I18n.t('devops_publish_multibranch_RetrieveAndMergeInfo')}
    </Typography.Paragraph>
  </div>
);
