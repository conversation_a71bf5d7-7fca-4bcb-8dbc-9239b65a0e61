.version-form {
  width: 392px;
  padding: 0 16px 12px;

  .submit {
    width: 100%;
  }
}

.version-form-v2 {
  width: 320px;
  padding: 8px 12px 12px;

  .force-push-tips {
    display: flex;
    flex-direction: column;

    margin-bottom: 4px;
    padding: 12px 12px 12px 16px;

    background: var(--Mg-COZ-mg-hglt-secondary-yellow, rgba(237, 179, 130, 23%));
    border-radius: var(--default, 8px);

    .force-push-tips-label {
      font-size: 14px;
      font-weight: 500;
      font-style: normal;
      line-height: 20px; /* 142.857% */
      color: var(--coz-fg-plus, rgba(8, 13, 30, 90%));
    }

    .force-push-tips-content {
      font-size: 14px;
      font-weight: 400;
      font-style: normal;
      line-height: 20px; /* 142.857% */
      color: var(--coz-fg-primary, rgba(15, 21, 40, 82%));
    }
  }

  :global {
    .semi-form-field {
      padding-top: 8px;
      padding-bottom: 0;
    }

    input::placeholder {
      color: var(--coz-fg-dim, rgba(55, 67, 106, 38%));
    }

    textarea::placeholder {
      color: var(--coz-fg-dim, rgba(55, 67, 106, 38%));
    }

    .semi-form-field-label-text {
      font-size: 14px;
      font-weight: 500;
      font-style: normal;
      line-height: 20px; /* 142.857% */
      color: var(--coz-fg-primary, rgba(15, 21, 40, 82%));
    }

    .semi-input-wrapper, .semi-input-textarea-wrapper {
      border: 1px solid var(--Stroke-COZ-stroke-plus, rgba(68, 83, 130, 25%));
      border-radius: var(--default, 8px);
    }
  }


}

