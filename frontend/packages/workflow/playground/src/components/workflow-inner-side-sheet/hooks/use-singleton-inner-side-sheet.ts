/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useInnerSideSheetStoreShallow } from './use-inner-side-sheet-store';

export const useSingletonInnerSideSheet = (sideSheetId: string) => {
  const { activeId, openSideSheet, closeSideSheet, forceUpdateActiveId } =
    useInnerSideSheetStoreShallow();

  const visible = activeId === sideSheetId;

  const handleOpen = async (id?: string) =>
    await openSideSheet(id || sideSheetId);

  const handleClose = async (id?: string) =>
    await closeSideSheet(id || sideSheetId);

  const forceClose = () => {
    forceUpdateActiveId();
  };

  return {
    visible,
    handleOpen,
    handleClose,
    forceClose,
  };
};
