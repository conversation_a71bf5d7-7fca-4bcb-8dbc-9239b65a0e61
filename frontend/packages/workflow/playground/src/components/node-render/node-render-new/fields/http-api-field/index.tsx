/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { PublicScopeProvider } from '@coze-workflow/variable';
import { useWorkflowNode } from '@coze-workflow/base';

import { Field } from '../field';
import { UrlContainer } from './url-container';

export function HttpApiField() {
  const { data } = useWorkflowNode();
  const apiInfo = data?.inputs?.apiInfo;
  const apiUrl = data?.inputs?.apiInfo?.url;

  return (
    <Field
      labelClassName="h-full"
      label={<div className="leading-[22px]">{apiInfo?.method}</div>}
      isEmpty={!apiUrl}
      customEmptyLabel={'URL'}
    >
      <PublicScopeProvider>
        <UrlContainer apiUrl={apiUrl} />
      </PublicScopeProvider>
    </Field>
  );
}
