/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect, startTransition } from 'react';

import type { FlowNodeEntity } from '@flowgram-adapter/free-layout-editor';
import { useRefresh } from '@flowgram-adapter/free-layout-editor';
import { FlowNodeVariableData } from '@coze-workflow/variable';

import { useVariableService } from '@/hooks';

/**
 * 获取变量服务，且监听变量变化，保证重渲染
 */
export function useAvailableNodeVariables(node: FlowNodeEntity) {
  const refresh = useRefresh();
  const variableService = useVariableService();

  useEffect(() => {
    const disposable = node
      .getData(FlowNodeVariableData)
      .public.available.onDataChange(() => startTransition(() => refresh()));
    return () => {
      disposable?.dispose();
    };
  }, []);

  return variableService;
}
