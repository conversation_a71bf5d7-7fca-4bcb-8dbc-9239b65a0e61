.node-render {
  position: relative;

  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;

  width: 360px;

  background-color: var(--coz-bg-plus);
  border: 1px solid var(--coz-stroke-plus, rgba(6, 7, 9, 15%));
  border-radius: 8px;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 4%), 0 4px 12px 0 rgba(0, 0, 0, 2%);

  &.activated,
  &.selected {
    border: 1px solid var(--coz-stroke-hglt, #4E40E5);
  }
}
