.skeleton {
  width: 100%;
  padding: 12px;
  background-color: var(--coz-bg-plus);
  border-radius: var(--coze-8);

  :global {
    .semi-skeleton-avatar {
      background-color: var(--coz-mg-plus-pressed, rgba(6, 7, 9, 20%));
    }

    .semi-skeleton-title {
      height: 16px;
      background-color: var(--coz-mg-plus, rgba(6,7,8, 8%));
      border-radius: 4px;
    }
  }

  .hd {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .avatar {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      border-radius: 6px;
    }
  }

}
