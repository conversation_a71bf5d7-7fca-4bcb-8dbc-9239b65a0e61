/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { I18n } from '@coze-arch/i18n';
import { isString } from 'lodash-es';

import styles from './field-empty.module.less';

interface FieldEmptyProps {
  fieldName: string | React.ReactNode;
}

export function FieldEmpty({ fieldName }: FieldEmptyProps) {
  return (
    <div className={styles['field-empty']}>
      <span className="flex-1 overflow-hidden truncate nowrap">
        {isString(fieldName)
          ? `${I18n.t('workflow_240919_01')}${fieldName}`
          : I18n.t('workflow_240919_01')}
        {!isString(fieldName) && fieldName}
      </span>
    </div>
  );
}
