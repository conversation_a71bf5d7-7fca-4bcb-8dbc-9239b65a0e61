.float-layout {
  pointer-events: none;

  position: absolute;
  // TODO：目前对层级没有太多规范，先写死 10
  z-index: 10;
  top: 0;
  left: 0;

  display: flex;

  width: 100%;
  height: 100%;
}

.left-panel {
  display: flex;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 1;

  width: 100%;
  min-width: 0;
}

.right-panel {
  flex-grow: 1;
  flex-shrink: 0;
  min-width: 0;
}

.left-main-panel {

  // 主面板都是无序的定位元素，所以设置 relative 作为基准面板
  position: relative;

  overflow: hidden;
  flex-grow: 0;
  flex-shrink: 1;

  width: 100%;
  height: 100%;
}

.left-bottom-panel {
  position: relative;

  flex-grow: 1;
  flex-shrink: 0;

  width: 100%;
  min-height: 0;
}
