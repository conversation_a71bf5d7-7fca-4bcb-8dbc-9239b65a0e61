/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { forwardRef, type Ref } from 'react';

import classnames from 'classnames';
import { type WithCustomStyle } from '@coze-workflow/base/types';
import { IconHandle } from '@douyinfe/semi-icons';

export const DragHandle = forwardRef<
  HTMLElement,
  WithCustomStyle<{
    testId?: string;
  }>
>(({ className, style, testId }, ref) => (
  <IconHandle
    ref={ref as Ref<HTMLSpanElement>}
    data-disable-node-drag
    className={classnames(
      'cursor-move text-[var(--semi-color-text-3)]',
      className,
    )}
    style={style}
    data-testid={testId}
  />
));
