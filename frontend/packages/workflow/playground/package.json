{"name": "@coze-workflow/playground", "version": "0.0.1", "description": "workflow 画布页面", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.tsx", "./workflow-playground": "./src/workflow-playground.tsx", "./services/*": "./src/services/*", "./typing": "./src/typing/index.ts"}, "main": "src/index.tsx", "typesVersions": {"*": {"typing": ["./src/typing/index.ts"], "workflow-playground": ["./src/workflow-playground.tsx"], "services/*": ["./src/services/*"]}}, "scripts": {"build": "exit 0", "create:node": "plop --plopfile ./scripts/create-node/plopfile.js", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@codemirror/commands": "^6.3.3", "@codemirror/view": "^6.34.1", "@coze-agent-ide/bot-plugin": "workspace:*", "@coze-agent-ide/bot-plugin-export": "workspace:*", "@coze-agent-ide/bot-plugin-tools": "workspace:*", "@coze-agent-ide/model-manager": "workspace:*", "@coze-agent-ide/plugin-risk-warning": "workspace:*", "@coze-agent-ide/plugin-shared": "workspace:*", "@coze-agent-ide/space-bot": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-http": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/bot-monaco-editor": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/hooks": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/idl": "workspace:*", "@coze-arch/load-remote-worker": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-arch/report-tti": "workspace:*", "@coze-arch/responsive-kit": "workspace:*", "@coze-arch/web-context": "workspace:*", "@coze-common/biz-tooltip-ui": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-common/editor-plugins": "workspace:*", "@coze-common/json-viewer": "workspace:*", "@coze-common/loading-button": "workspace:*", "@coze-common/mouse-pad-selector": "workspace:*", "@coze-common/prompt-kit": "workspace:*", "@coze-common/prompt-kit-adapter": "workspace:*", "@coze-common/prompt-kit-base": "workspace:*", "@coze-common/resource-tree": "workspace:*", "@coze-data/database": "workspace:*", "@coze-data/database-v2": "workspace:*", "@coze-data/knowledge-ide-adapter": "workspace:*", "@coze-data/knowledge-ide-base": "workspace:*", "@coze-data/knowledge-modal-adapter": "workspace:*", "@coze-data/knowledge-modal-base": "workspace:*", "@coze-data/knowledge-resource-processor-base": "workspace:*", "@coze-devops/mockset-manage": "workspace:*", "@coze-devops/testset-manage": "workspace:*", "@coze-editor/editor": "0.1.0-alpha.d92d50", "@coze-editor/extension-json-empty-string-value-completion": "0.1.0-alpha.d92d50", "@coze-editor/extension-json-hover": "0.1.0-alpha.d92d50", "@coze-editor/extension-json-unnecessary-properties": "0.1.0-alpha.d92d50", "@coze-editor/extension-regexp-decorator": "0.1.0-alpha.d92d50", "@coze-foundation/layout": "workspace:*", "@coze-foundation/local-storage": "workspace:*", "@coze-project-ide/framework": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/components": "workspace:*", "@coze-studio/open-chat": "workspace:*", "@coze-studio/premium-components-adapter": "workspace:*", "@coze-studio/user-store": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/base-adapter": "workspace:*", "@coze-workflow/code-editor-adapter": "workspace:*", "@coze-workflow/components": "workspace:*", "@coze-workflow/fabric-canvas": "workspace:*", "@coze-workflow/feature-encapsulate": "workspace:*", "@coze-workflow/history": "workspace:*", "@coze-workflow/nodes": "workspace:*", "@coze-workflow/nodes-adapter": "workspace:*", "@coze-workflow/render": "workspace:*", "@coze-workflow/resources-adapter": "workspace:*", "@coze-workflow/setters": "workspace:*", "@coze-workflow/test-run": "workspace:*", "@coze-workflow/test-run-next": "workspace:*", "@coze-workflow/variable": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@douyinfe/semi-illustrations": "^2.36.0", "@douyinfe/semi-ui": "~2.72.3", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "@radix-ui/react-scroll-area": "^1.1.0", "@tanstack/react-query": "~5.13.4", "@types/parse-multipart": "~1.0.2", "ahooks": "^3.7.8", "ajv": "~8.12.0", "axios": "^1.4.0", "classnames": "^2.3.2", "colorthief": "~2.4.0", "copy-to-clipboard": "^3.3.3", "cron-string-generator": "^1.0.11", "cropperjs": "^1.5.12", "dayjs": "^1.11.7", "dnd-core": "~16.0.1", "eventemitter3": "^5.0.1", "immer": "^10.0.3", "immutability-helper": "^3.1.1", "inversify": "^6.0.1", "io-ts": "2.2.20", "lodash-es": "^4.17.21", "md5": "^2.3.0", "mime-types": "2.1.35", "nanoid": "^4.0.2", "parse-multipart": "^1.0.4", "re-resizable": "~6.9.11", "react-beautiful-dnd": "13.1.1", "react-click-away-listener": "^2.2.3", "react-cropper": "^2.3.3", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-error-boundary": "^4.0.9", "react-intersection-observer": "~9.5.3", "reflect-metadata": "^0.1.13", "semver": "^7.3.7", "slate": "0.101.5", "slate-history": "0.100.0", "slate-react": "0.101.5", "use-context-selector": "^1.4.1", "yargs-parser": "~21.1.1", "zod": "3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@codemirror/language": "^6.10.1", "@codemirror/state": "^6.4.1", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/tailwind-config": "workspace:*", "@coze-arch/tea": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@lezer/common": "^1.2.2", "@monaco-editor/react": "^4.5.2", "@rspack/core": "0.6.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/json-schema": "~7.0.15", "@types/lodash-es": "^4.17.10", "@types/md5": "^2.3.2", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-helmet": "^6.1.11", "@types/semver": "^7.3.4", "@vitest/coverage-v8": "~3.0.5", "debug": "^4.3.4", "fp-ts": "^2.5.0", "i18next": ">= 19.0.0", "less": "^3.13.1", "monaco-editor": "^0.45.0", "plop": "~4.0.1", "prop-types": "^15.5.7", "react": "~18.2.0", "react-dom": "~18.2.0", "react-helmet": "^6.1.0", "react-is": ">= 16.8.0", "react-router-dom": "^6.22.0", "scheduler": ">=0.19.0", "styled-components": ">=4", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "typescript": "~5.8.2", "utility-types": "^3.10.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0", "utility-types": "^3.10.0"}, "// deps": "debug@^4.3.4 为脚本自动补齐，请勿改动"}