{"name": "@coze-workflow/variable", "version": "0.0.1", "description": "workflow 变量包", "license": "Apache-2.0", "author": "<EMAIL>", "sideEffects": ["**/*.css", "**/*.less", "**/*.sass", "**/*.scss"], "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache --quiet", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-workflow/base": "workspace:*", "@douyinfe/semi-ui": "~2.72.3", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "ajv": "~8.12.0", "inversify": "^6.0.1", "lodash-es": "^4.17.21", "react": "~18.2.0", "react-dom": "~18.2.0", "zod": "3.22.4"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@rsbuild/core": "1.1.13", "@types/lodash-es": "^4.17.10", "@types/node": "18.18.9", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "acorn": "^8.12.1", "less": "^3.13.1", "react-is": ">= 16.8.0", "reflect-metadata": "^0.1.13", "scheduler": ">=0.19.0", "styled-components": ">=4", "typescript": "~5.8.2", "vitest": "~3.0.5", "webpack": "~5.91.0"}}