# @coze-workflow/variable

workflow 变量包

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes component, hook, adapter and more.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/variable": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/variable';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component
- Hook
- Adapter
- Service
- Editor
- Plugin

## API Reference

### Exports

- `FlowNodeVariableData`
- `*`
- `*`
- `*`
- `*`
- `*`
- `*`
- `*`
- `*`
- `*`

*And more...*

For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- React

- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
