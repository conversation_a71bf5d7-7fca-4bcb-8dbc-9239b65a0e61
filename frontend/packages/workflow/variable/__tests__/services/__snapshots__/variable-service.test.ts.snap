// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`test variable service > test get variable 1`] = `
[
  [
    {
      "children": undefined,
      "description": "test_description",
      "key": "String",
      "name": "String",
      "required": true,
      "type": 1,
    },
    1,
    "String",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": "test_description",
      "name": "String",
      "readonly": undefined,
      "required": true,
      "schema": undefined,
      "type": "string",
    },
    {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "start",
          "name": "String",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "String",
    [
      "start.outputs",
    ],
    [],
    {
      "keyPath": [
        "start",
        "String",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "start",
      "label": "",
    },
    "start",
  ],
  [
    {
      "children": [
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "String",
          "name": "String",
          "required": true,
          "type": 1,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Integer",
          "name": "Integer",
          "required": true,
          "type": 2,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Boolean",
          "name": "Boolean",
          "required": true,
          "type": 3,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Number",
          "name": "Number",
          "required": true,
          "type": 4,
        },
        {
          "children": [],
          "description": "test_child_description",
          "key": "Object",
          "name": "Object",
          "required": true,
          "type": 6,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Image",
          "name": "Image",
          "required": true,
          "type": 7,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "File",
          "name": "File",
          "required": true,
          "type": 8,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Doc",
          "name": "Doc",
          "required": true,
          "type": 9,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Code",
          "name": "Code",
          "required": true,
          "type": 10,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "PPT",
          "name": "PPT",
          "required": true,
          "type": 11,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Txt",
          "name": "Txt",
          "required": true,
          "type": 12,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Excel",
          "name": "Excel",
          "required": true,
          "type": 13,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Audio",
          "name": "Audio",
          "required": true,
          "type": 14,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Zip",
          "name": "Zip",
          "required": true,
          "type": 15,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Video",
          "name": "Video",
          "required": true,
          "type": 16,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Svg",
          "name": "Svg",
          "required": true,
          "type": 17,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Voice",
          "name": "Voice",
          "required": true,
          "type": 18,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Time",
          "name": "Time",
          "required": true,
          "type": 19,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<String>",
          "name": "Array<String>",
          "required": true,
          "type": 99,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Integer>",
          "name": "Array<Integer>",
          "required": true,
          "type": 100,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Boolean>",
          "name": "Array<Boolean>",
          "required": true,
          "type": 101,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Number>",
          "name": "Array<Number>",
          "required": true,
          "type": 102,
        },
        {
          "children": [],
          "description": "test_child_description",
          "key": "Array<Object>",
          "name": "Array<Object>",
          "required": true,
          "type": 103,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Image>",
          "name": "Array<Image>",
          "required": true,
          "type": 104,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<File>",
          "name": "Array<File>",
          "required": true,
          "type": 105,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Doc>",
          "name": "Array<Doc>",
          "required": true,
          "type": 106,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Code>",
          "name": "Array<Code>",
          "required": true,
          "type": 107,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<PPT>",
          "name": "Array<PPT>",
          "required": true,
          "type": 108,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Txt>",
          "name": "Array<Txt>",
          "required": true,
          "type": 109,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Excel>",
          "name": "Array<Excel>",
          "required": true,
          "type": 110,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Audio>",
          "name": "Array<Audio>",
          "required": true,
          "type": 111,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Zip>",
          "name": "Array<Zip>",
          "required": true,
          "type": 112,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Video>",
          "name": "Array<Video>",
          "required": true,
          "type": 113,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Svg>",
          "name": "Array<Svg>",
          "required": true,
          "type": 114,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Voice>",
          "name": "Array<Voice>",
          "required": true,
          "type": 115,
        },
        {
          "children": undefined,
          "description": "test_child_description",
          "key": "Array<Time>",
          "name": "Array<Time>",
          "required": true,
          "type": 116,
        },
      ],
      "description": "test_description",
      "key": "Object",
      "name": "Object",
      "required": true,
      "type": 6,
    },
    6,
    "Object",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": "test_description",
      "name": "Object",
      "readonly": undefined,
      "required": true,
      "schema": [
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "String",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Integer",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "integer",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Boolean",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "boolean",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Number",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "float",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Object",
          "readonly": undefined,
          "required": true,
          "schema": [],
          "type": "object",
        },
        {
          "assistType": 2,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Image",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 1,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "File",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 3,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Doc",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 4,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Code",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 5,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "PPT",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 6,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Txt",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 7,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Excel",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 8,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Audio",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 9,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Zip",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Video",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 11,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Svg",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 12,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Voice",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10000,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Time",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<String>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Integer>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "integer",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Boolean>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "boolean",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Number>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "float",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Object>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": [],
            "type": "object",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Image>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 2,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<File>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 1,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Doc>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 3,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Code>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 4,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<PPT>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 5,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Txt>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 6,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Excel>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 7,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Audio>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 8,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Zip>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 9,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Video>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Svg>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 11,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Voice>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 12,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Time>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10000,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
      ],
      "type": "object",
    },
    {
      "assistType": undefined,
      "schema": [
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "String",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Integer",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "integer",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Boolean",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "boolean",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Number",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "float",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Object",
          "readonly": undefined,
          "required": true,
          "schema": [],
          "type": "object",
        },
        {
          "assistType": 2,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Image",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 1,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "File",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 3,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Doc",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 4,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Code",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 5,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "PPT",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 6,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Txt",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 7,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Excel",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 8,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Audio",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 9,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Zip",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Video",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 11,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Svg",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 12,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Voice",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10000,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Time",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<String>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Integer>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "integer",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Boolean>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "boolean",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Number>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "float",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Object>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": [],
            "type": "object",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Image>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 2,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<File>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 1,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Doc>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 3,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Code>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 4,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<PPT>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 5,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Txt>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 6,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Excel>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 7,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Audio>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 8,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Zip>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 9,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Video>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Svg>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 11,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Voice>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 12,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Time>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10000,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
      ],
      "type": "object",
      "value": {
        "content": {
          "blockID": "llm_0",
          "name": "Object",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "Object",
    [
      "llm_0.outputs",
    ],
    [
      "String",
      "Integer",
      "Boolean",
      "Number",
      "Object",
      "Image",
      "File",
      "Doc",
      "Code",
      "PPT",
      "Txt",
      "Excel",
      "Audio",
      "Zip",
      "Video",
      "Svg",
      "Voice",
      "Time",
      "Array<String>",
      "Array<Integer>",
      "Array<Boolean>",
      "Array<Number>",
      "Array<Object>",
      "Array<Image>",
      "Array<File>",
      "Array<Doc>",
      "Array<Code>",
      "Array<PPT>",
      "Array<Txt>",
      "Array<Excel>",
      "Array<Audio>",
      "Array<Zip>",
      "Array<Video>",
      "Array<Svg>",
      "Array<Voice>",
      "Array<Time>",
    ],
    {
      "keyPath": [
        "llm_0",
        "Object",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "llm_0",
      "label": "",
    },
    "llm_0",
  ],
  [
    {
      "children": undefined,
      "description": "test_child_description",
      "key": "Image",
      "name": "Image",
      "required": true,
      "type": 7,
    },
    7,
    "Image",
    {
      "assistType": 2,
      "defaultValue": undefined,
      "description": "test_child_description",
      "name": "Image",
      "readonly": undefined,
      "required": true,
      "schema": undefined,
      "type": "string",
    },
    {
      "assistType": 2,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "start",
          "name": "Object.Image",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "Image",
    [
      "start.outputs",
      "Object",
    ],
    [],
    {
      "keyPath": [
        "start",
        "Object",
        "Image",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "start",
      "label": "",
    },
    "start",
  ],
  [
    {
      "children": undefined,
      "description": "test_child_description",
      "key": "Number",
      "name": "Number",
      "required": true,
      "type": 4,
    },
    4,
    "Number",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": "test_child_description",
      "name": "Number",
      "readonly": undefined,
      "required": true,
      "schema": undefined,
      "type": "float",
    },
    {
      "assistType": undefined,
      "schema": undefined,
      "type": "float",
      "value": {
        "content": {
          "blockID": "llm_0",
          "name": "Object.Number",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "Number",
    [
      "llm_0.outputs",
      "Object",
    ],
    [],
    {
      "keyPath": [
        "llm_0",
        "Object",
        "Number",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "llm_0",
      "label": "",
    },
    "llm_0",
  ],
  [
    {
      "children": undefined,
      "description": "test_description",
      "key": "Array<Code>",
      "name": "Array<Code>",
      "required": true,
      "type": 107,
    },
    107,
    "Array<Code>",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": "test_description",
      "name": "Array<Code>",
      "readonly": undefined,
      "required": true,
      "schema": {
        "assistType": 4,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
    },
    {
      "assistType": undefined,
      "schema": {
        "assistType": 4,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "start",
          "name": "Array<Code>",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "Array<Code>",
    [
      "start.outputs",
    ],
    [],
    {
      "keyPath": [
        "start",
        "Array<Code>",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "start",
      "label": "",
    },
    "start",
  ],
  [
    {
      "children": undefined,
      "description": "test_description",
      "key": "Array<String>",
      "name": "Array<String>",
      "required": true,
      "type": 99,
    },
    99,
    "Array<String>",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": "test_description",
      "name": "Array<String>",
      "readonly": undefined,
      "required": true,
      "schema": {
        "assistType": undefined,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
    },
    {
      "assistType": undefined,
      "schema": {
        "assistType": undefined,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "llm_0",
          "name": "Array<String>",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "Array<String>",
    [
      "llm_0.outputs",
    ],
    [],
    {
      "keyPath": [
        "llm_0",
        "Array<String>",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "llm_0",
      "label": "",
    },
    "llm_0",
  ],
  [
    {
      "children": undefined,
      "description": "test_child_description",
      "key": "Number",
      "name": "Number",
      "required": true,
      "type": 4,
    },
    4,
    "Number",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": "test_child_description",
      "name": "Number",
      "readonly": undefined,
      "required": true,
      "schema": undefined,
      "type": "float",
    },
    {
      "assistType": undefined,
      "schema": undefined,
      "type": "float",
      "value": {
        "content": {
          "blockID": "start",
          "name": "Array<Object>.Number",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "Number",
    [
      "start.outputs",
      "Array<Object>",
    ],
    [],
    {
      "keyPath": [
        "start",
        "Array<Object>",
        "Number",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "start",
      "label": "",
    },
    "start",
  ],
  [
    null,
  ],
  [
    null,
  ],
  [
    null,
  ],
  [
    null,
    undefined,
    "Unknown",
    undefined,
    {
      "type": "string",
      "value": {
        "content": {
          "blockID": "",
          "name": "",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "test_group_1",
    [
      "merge_0.outputs",
    ],
    [],
    {
      "keyPath": [
        "merge_0",
        "test_group_1",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "merge_0",
      "label": "",
    },
    "merge_0",
  ],
  [
    null,
    undefined,
    "Unknown",
    undefined,
    {
      "type": "string",
      "value": {
        "content": {
          "blockID": "",
          "name": "",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "test_group_2",
    [
      "merge_0.outputs",
    ],
    [],
    {
      "keyPath": [
        "merge_0",
        "test_group_2",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "merge_0",
      "label": "",
    },
    "merge_0",
  ],
  [
    null,
  ],
  [
    null,
  ],
  [
    null,
  ],
  [
    null,
  ],
  [
    null,
    undefined,
    "Unknown",
    undefined,
    {
      "type": "string",
      "value": {
        "content": {
          "blockID": "",
          "name": "",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "loop_output_var_string",
    [
      "loop_0.outputs",
    ],
    [],
    {
      "keyPath": [
        "loop_0",
        "loop_output_var_string",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "loop_0",
      "label": "",
    },
    "loop_0",
  ],
  [
    {
      "children": undefined,
      "key": "loop_output_string",
      "name": "loop_output_string",
      "type": 99,
    },
    99,
    "Array<String>",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": undefined,
      "name": "loop_output_string",
      "readonly": undefined,
      "required": undefined,
      "schema": {
        "assistType": undefined,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
    },
    {
      "assistType": undefined,
      "schema": {
        "assistType": undefined,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "loop_0",
          "name": "loop_output_string",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "loop_output_string",
    [
      "loop_0.outputs",
    ],
    [],
    {
      "keyPath": [
        "loop_0",
        "loop_output_string",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "loop_0",
      "label": "",
    },
    "loop_0",
  ],
  [
    {
      "children": undefined,
      "key": "loop_output_image",
      "name": "loop_output_image",
      "type": 104,
    },
    104,
    "Array<Image>",
    {
      "assistType": undefined,
      "defaultValue": undefined,
      "description": undefined,
      "name": "loop_output_image",
      "readonly": undefined,
      "required": undefined,
      "schema": {
        "assistType": 2,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
    },
    {
      "assistType": undefined,
      "schema": {
        "assistType": 2,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "loop_0",
          "name": "loop_output_image",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "loop_output_image",
    [
      "loop_0.outputs",
    ],
    [],
    {
      "keyPath": [
        "loop_0",
        "loop_output_image",
      ],
      "source": "block-output_",
    },
    {
      "icon": "",
      "key": "loop_0",
      "label": "",
    },
    "loop_0",
  ],
]
`;
