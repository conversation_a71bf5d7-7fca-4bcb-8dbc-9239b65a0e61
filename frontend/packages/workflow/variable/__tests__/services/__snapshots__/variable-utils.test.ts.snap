// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`test variable utils > Ref VO DTO convert 1`] = `
[
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "start",
          "name": "String",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "start__String",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": [
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "String",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Integer",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "integer",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Boolean",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "boolean",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Number",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "float",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Object",
          "readonly": undefined,
          "required": true,
          "schema": [],
          "type": "object",
        },
        {
          "assistType": 2,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Image",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 1,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "File",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 3,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Doc",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 4,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Code",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 5,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "PPT",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 6,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Txt",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 7,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Excel",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 8,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Audio",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 9,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Zip",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Video",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 11,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Svg",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 12,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Voice",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10000,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Time",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<String>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Integer>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "integer",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Boolean>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "boolean",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Number>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "float",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Object>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": [],
            "type": "object",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Image>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 2,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<File>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 1,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Doc>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 3,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Code>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 4,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<PPT>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 5,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Txt>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 6,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Excel>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 7,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Audio>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 8,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Zip>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 9,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Video>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Svg>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 11,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Voice>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 12,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Time>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10000,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
      ],
      "type": "object",
      "value": {
        "content": {
          "blockID": "llm_0",
          "name": "Object",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "llm_0__Object",
  },
  {
    "input": {
      "assistType": 2,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "start",
          "name": "Object.Image",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "start__Object__Image",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "float",
      "value": {
        "content": {
          "blockID": "llm_0",
          "name": "Object.Number",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "llm_0__Object__Number",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": {
        "assistType": 4,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "start",
          "name": "Array<Code>",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "start__Array<Code>",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": {
        "assistType": undefined,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "llm_0",
          "name": "Array<String>",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "llm_0__Array<String>",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "float",
      "value": {
        "content": {
          "blockID": "start",
          "name": "Array<Object>.Number",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "start__Array<Object>__Number",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "llm_0",
          "name": "Array<String>.Number",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "llm_0__Array<String>__Number",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "start",
          "name": "Object.Undefined",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "start__Object__Undefined",
  },
  undefined,
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "merge_0",
          "name": "test_group_1",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "merge_0__test_group_1",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "merge_0",
          "name": "test_group_2",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "merge_0__test_group_2",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
      "value": {
        "content": {
          "blockID": "loop_0",
          "name": "loop_output_var_string",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "loop_0__loop_output_var_string",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": {
        "assistType": undefined,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "loop_0",
          "name": "loop_output_string",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "loop_0__loop_output_string",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": {
        "assistType": 2,
        "schema": undefined,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": {
          "blockID": "loop_0",
          "name": "loop_output_image",
          "source": "block-output",
        },
        "type": "ref",
      },
    },
    "name": "loop_0__loop_output_image",
  },
  {
    "input": {
      "type": "string",
      "value": {
        "content": "test_constant",
        "rawMeta": undefined,
        "type": "literal",
      },
    },
    "name": "test_constant_no_raw_meta",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": {
        "assistType": 2,
        "type": "string",
      },
      "type": "list",
      "value": {
        "content": [
          "image_url",
        ],
        "rawMeta": {
          "type": 104,
        },
        "type": "literal",
      },
    },
    "name": "test_constant_image_url",
  },
  {
    "input": {
      "assistType": undefined,
      "schema": {
        "assistType": undefined,
        "schema": [],
        "type": "object",
      },
      "type": "list",
      "value": {
        "content": [
          {
            "count": 0,
          },
        ],
        "rawMeta": {
          "children": [
            {
              "key": "count",
              "name": "count",
              "type": 4,
            },
          ],
          "type": 103,
        },
        "type": "literal",
      },
    },
    "name": "test_constant_array_object",
  },
]
`;

exports[`test variable utils > Variable VO DTO convert 1`] = `
[
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "String",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Integer",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "integer",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Boolean",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "boolean",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Number",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "float",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Object",
    "readonly": undefined,
    "required": true,
    "schema": [
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "String",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Integer",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "integer",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Boolean",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "boolean",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Number",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "float",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Object",
        "readonly": undefined,
        "required": true,
        "schema": [],
        "type": "object",
      },
      {
        "assistType": 2,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Image",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 1,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "File",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 3,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Doc",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 4,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Code",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 5,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "PPT",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 6,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Txt",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 7,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Excel",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 8,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Audio",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 9,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Zip",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 10,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Video",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 11,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Svg",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 12,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Voice",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": 10000,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Time",
        "readonly": undefined,
        "required": true,
        "schema": undefined,
        "type": "string",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<String>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": undefined,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Integer>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": undefined,
          "schema": undefined,
          "type": "integer",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Boolean>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": undefined,
          "schema": undefined,
          "type": "boolean",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Number>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": undefined,
          "schema": undefined,
          "type": "float",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Object>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": undefined,
          "schema": [],
          "type": "object",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Image>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 2,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<File>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 1,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Doc>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 3,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Code>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 4,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<PPT>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 5,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Txt>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 6,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Excel>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 7,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Audio>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 8,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Zip>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 9,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Video>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 10,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Svg>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 11,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Voice>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 12,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
      {
        "assistType": undefined,
        "defaultValue": undefined,
        "description": "test_child_description",
        "name": "Array<Time>",
        "readonly": undefined,
        "required": true,
        "schema": {
          "assistType": 10000,
          "schema": undefined,
          "type": "string",
        },
        "type": "list",
      },
    ],
    "type": "object",
  },
  {
    "assistType": 2,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Image",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 1,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "File",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 3,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Doc",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 4,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Code",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 5,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "PPT",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 6,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Txt",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 7,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Excel",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 8,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Audio",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 9,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Zip",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 10,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Video",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 11,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Svg",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 12,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Voice",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": 10000,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Time",
    "readonly": undefined,
    "required": true,
    "schema": [],
    "type": "string",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<String>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": undefined,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Integer>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": undefined,
      "schema": undefined,
      "type": "integer",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Boolean>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": undefined,
      "schema": undefined,
      "type": "boolean",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Number>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": undefined,
      "schema": undefined,
      "type": "float",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Object>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": undefined,
      "schema": [
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "String",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Integer",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "integer",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Boolean",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "boolean",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Number",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "float",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Object",
          "readonly": undefined,
          "required": true,
          "schema": [],
          "type": "object",
        },
        {
          "assistType": 2,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Image",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 1,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "File",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 3,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Doc",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 4,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Code",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 5,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "PPT",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 6,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Txt",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 7,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Excel",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 8,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Audio",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 9,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Zip",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Video",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 11,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Svg",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 12,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Voice",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": 10000,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Time",
          "readonly": undefined,
          "required": true,
          "schema": undefined,
          "type": "string",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<String>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Integer>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "integer",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Boolean>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "boolean",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Number>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": undefined,
            "type": "float",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Object>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": undefined,
            "schema": [],
            "type": "object",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Image>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 2,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<File>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 1,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Doc>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 3,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Code>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 4,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<PPT>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 5,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Txt>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 6,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Excel>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 7,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Audio>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 8,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Zip>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 9,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Video>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Svg>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 11,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Voice>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 12,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
        {
          "assistType": undefined,
          "defaultValue": undefined,
          "description": "test_child_description",
          "name": "Array<Time>",
          "readonly": undefined,
          "required": true,
          "schema": {
            "assistType": 10000,
            "schema": undefined,
            "type": "string",
          },
          "type": "list",
        },
      ],
      "type": "object",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Image>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 2,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<File>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 1,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Doc>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 3,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Code>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 4,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<PPT>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 5,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Txt>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 6,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Excel>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 7,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Audio>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 8,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Zip>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 9,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Video>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 10,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Svg>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 11,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Voice>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 12,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
  {
    "assistType": undefined,
    "defaultValue": undefined,
    "description": "test_description",
    "name": "Array<Time>",
    "readonly": undefined,
    "required": true,
    "schema": {
      "assistType": 10000,
      "schema": undefined,
      "type": "string",
    },
    "type": "list",
  },
]
`;
