/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useCallback } from 'react';

import { Rect, type Canvas } from 'fabric';

import { type FabricSchema } from '../typings';

export const useCanvasClip = ({
  canvas,
  schema,
}: {
  canvas?: Canvas;
  schema: FabricSchema;
}) => {
  const addClip = useCallback(() => {
    if (!canvas) {
      return;
    }
    canvas.clipPath = new Rect({
      absolutePositioned: true,
      top: 0,
      left: 0,
      width: schema.width,
      height: schema.height,
    });
    canvas.requestRenderAll();
  }, [canvas, schema]);

  const removeClip = useCallback(() => {
    if (!canvas) {
      return;
    }
    canvas.clipPath = undefined;
    canvas.requestRenderAll();
  }, [canvas]);

  return {
    addClip,
    removeClip,
  };
};
