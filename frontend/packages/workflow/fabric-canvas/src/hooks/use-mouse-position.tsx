/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect, useState } from 'react';

import { type Canvas } from 'fabric';

export const useMousePosition = ({ canvas }: { canvas?: Canvas }) => {
  const [position, setPosition] = useState<{ left: number; top: number }>({
    left: 0,
    top: 0,
  });
  useEffect(() => {
    if (!canvas) {
      return;
    }

    const dispose = canvas.on('mouse:move', event => {
      const pointer = canvas.getScenePoint(event.e);
      setPosition({
        left: pointer.x,
        top: pointer.y,
      });
    });
    return dispose;
  }, [canvas]);

  return {
    mousePosition: position,
  };
};
