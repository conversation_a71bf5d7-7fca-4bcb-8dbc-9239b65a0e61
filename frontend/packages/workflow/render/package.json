{"name": "@coze-workflow/render", "version": "0.1.0", "author": "<EMAIL>", "sideEffects": ["**/*.css", "**/*.less", "**/*.sass", "**/*.scss"], "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "exit 0", "build:watch": "edenx build --watch", "dev": "edenx dev", "lint": "eslint ./", "new": "edenx new", "upgrade": "edenx upgrade"}, "dependencies": {"@coze-arch/bot-flags": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/report-tti": "workspace:*", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "@use-gesture/vanilla": "^10.0.0", "classnames": "^2.3.2", "inversify": "^6.0.1", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@babel/core": "^7.26.0", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@modern-js/plugin-router-v5": "^2.38.0", "@modern-js/runtime": "^2.38.0", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "less": "^3.13.1", "react": "~18.2.0", "react-dom": "~18.2.0", "require-from-string": "^2.0.2", "scheduler": ">=0.19.0", "styled-components": ">=4", "typescript": "~5.8.2", "webpack": "~5.91.0"}}