.base-panel {
  display: flex;
  flex-direction: column;

  border: 1px solid var(--coz-stroke-primary);
  border-radius: 8px;
  background-color: var(--coz-bg-plus);
  position: relative;
  min-width: 540px;
}

.dragging {
  cursor: row-resize;
  user-select: none;
  pointer-events: none;
}

.panel-header {
  height: 48px;
  border-bottom: 1px solid var(--coz-stroke-primary);

  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0 12px;
  flex-shrink: 0;
  column-gap: 8px;
}
.panel-content {
  height: 100%;
  flex-shrink: 1;
  overflow-y: auto;
}
.panel-footer {
  border-top: 1px solid var(--coz-stroke-primary);;
}

.resize-bar {
  width: 100%;
  height: 5px;

  position: absolute;
  top: 0;
  left: 0;

  cursor: row-resize;
}
