# @coze-workflow/test-run-shared

Workflow TestRun 公共包

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes component, editor.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/test-run-shared": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/test-run-shared';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component
- Editor

## API Reference

### Exports

- `JsonEditor`
- `safeFormatJsonString, safeJsonParse, gotoDebugFlow`
- `BottomPanel`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
