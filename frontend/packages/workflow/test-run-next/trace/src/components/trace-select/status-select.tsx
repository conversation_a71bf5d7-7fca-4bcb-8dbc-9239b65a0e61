/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useCallback } from 'react';

import { I18n } from '@coze-arch/i18n';
import { IconCozFilter } from '@coze-arch/coze-design/icons';
import { Select, IconButton } from '@coze-arch/coze-design';
import { SpanStatus } from '@coze-arch/bot-api/workflow_api';

interface StatusSelectProps {
  value: SpanStatus;
  onChange: (v: SpanStatus) => void;
}

export const StatusSelect: React.FC<StatusSelectProps> = ({
  value,
  onChange,
}) => {
  const triggerRender = useCallback(
    () => (
      <IconButton icon={<IconCozFilter />} color="secondary" size="small" />
    ),
    [],
  );

  return (
    <Select
      value={value}
      triggerRender={triggerRender}
      optionList={[
        {
          value: SpanStatus.Unknown,
          label: I18n.t('query_status_all'),
        },
        {
          value: SpanStatus.Fail,
          label: I18n.t('query_status_failed'),
        },
        {
          value: SpanStatus.Success,
          label: I18n.t('query_status_completed'),
        },
      ]}
      onChange={onChange as any}
    />
  );
};
