@scrollbar-size: 11px;
@scrollbar-padding: 2px;
@transition-timing-function-standard: cubic-bezier(0.34, 0.69, 0.1, 1);

.webkit-scrollbar_mixin() {
  &::-webkit-scrollbar-thumb {
    background-color: rgba(125, 125, 125, 30%);
    background-clip: padding-box;
    border: @scrollbar-padding solid transparent;
    border-radius: 9999px;

    transition: background .2s @transition-timing-function-standard;
  }

  &::-webkit-scrollbar {
    width: @scrollbar-size;
    height: @scrollbar-size;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb:hover {
    /* stylelint-disable-next-line declaration-no-important */
    background-color: rgba(125, 125, 125, 60%) !important;
  }

  &::-webkit-scrollbar:hover {
    width: @scrollbar-size;
    height: @scrollbar-size;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}
