@import '../../variables.less';

.message-title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  height: 20px;
  margin-bottom: 8px;

  .message-title-text {
    font-size: 14px;
    font-weight: 600;
    color: @text-gray-0;

    :global(.semi-typography-action-copied) {
      vertical-align: middle
    }

    svg {
      transform: translateY(-2px);
    }
  }

  .node-detail-title-description {
    font-size: 12px;
    color: @text-gray-3;
  }

}


.common-text-content {
  max-width: 400px;
}

.copy-icon {
  color: @icon-color;
}
