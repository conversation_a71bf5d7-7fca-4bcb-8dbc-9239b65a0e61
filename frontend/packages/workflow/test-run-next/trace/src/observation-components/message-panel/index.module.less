@import '../variables.less';
@import '../scroll-bar.less';

.message-panel-content {
  .webkit-scrollbar_mixin();

  overflow: auto;

  max-height: 340px;
  padding: 8px 4px;
  margin-bottom: 12px;


  border: 1px solid @border-color;
  border-radius: 8px;

  .message-panel-content-detail-text {
    font-size: 12px;
    color: @text-gray-0;
    word-break: break-word;
  }

  .message-panel-content-json-container {
    :global(.string-value) {
      word-break: break-word;
    }
  }
}

.message-panel-content_error {
  border: 1px solid @error-color;
}

.encryption {
  overflow: auto;
  display: flex;
  flex-direction: column;
  row-gap: 0.5rem;

  max-height: 600px;
  padding: 8px 4px;

  font-size: 12px;

  border: 1px solid #1d1c2314;
  border-radius: 8px;

}

.encryption-tip {
  margin-top: 8px;
  font-weight: 600;
  color: rgb(239,68,68);
}
