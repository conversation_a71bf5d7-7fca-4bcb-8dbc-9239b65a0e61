/* stylelint-disable max-nesting-depth */
@import '../scroll-bar.less';
@import '../variables.less';


.trace-tree {
  overflow: visible;

}

.trace-trees-wrapper {
  overflow: auto;
  display: flex;
  flex:1;
  flex-direction: column;
  gap: 8px;
  justify-content: flex-start;

  min-height: 0;
  padding-top: 6px;
  padding-bottom: 6px;
  .webkit-scrollbar_mixin();
}

.trace-tree-layout {
  display: flex;
  flex:1;
  flex-direction: column;
  gap:8px;
  height:100%;
}

.tree-custom-node {
  display: inline-flex;
  gap: 4px;
  align-items: center;

  .tree-custom-node-icon {
    display: inline-flex;
    gap: 3px;
    align-items: center;
    justify-content: center;

    .tree-custom-node-icon-default {
      display: inline-flex;
      align-items: center;
      justify-content: center;

      width: 16px;
      height: 16px;

      font-size: 10px;
      font-weight: 600;
      color: @white;

      border-radius: 3px;
    }
  }


  .tree-custom-node-content {
    cursor: pointer;
    display: flex;
    align-items: stretch;

    .title {
      overflow: hidden;
      align-items: center;

      // max-width: 268px;
      padding: 1px 8px;

      font-size: 12px;
      color: @text-gray-0;
      text-overflow: ellipsis;
      white-space: nowrap;

      border-radius: 4px;
    }

    &.selected {
      .title {
        background: #F1F2FD;
      }
    }

    &.hover {
      .title {
        background: #F1F2FD;
      }
    }

    &.error {
      .title {
        color: @error-color;
      }

      &.selected {
        .title {
          background: #FFE0D2;
        }
      }

      &.hover {
        .title {
          background: #FFF3EE;
        }
      }
    }

    &.disabled {
      cursor: default;
      background-color: transparent;
    }
  }
}

.tree-data-declare {
  font-size: 12px;
}

.node-tag {
  max-width: 100%;
  height: 16px;
  padding-right:4px;
  padding-left:4px;

  font-size: 10px;
}

.is-open {
  transform: rotate(-90deg);
}
