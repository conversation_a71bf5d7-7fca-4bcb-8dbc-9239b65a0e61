# @coze-workflow/test-run-trace

Workflow TestRun Form

## Overview

This package is part of the Coze Studio monorepo and provides workflow functionality. It includes component.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-workflow/test-run-trace": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-workflow/test-run-trace';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component

## API Reference

### Exports

- `TraceListPanel`
- `TraceDetailPanel`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
