/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { SchemaContext, type FormSchema } from '../shared';
import { useComponents } from '../hooks';

interface ObjectFieldProps {
  schema: FormSchema;
}

export const ObjectField: React.FC<
  React.PropsWithChildren<ObjectFieldProps>
> = ({ schema, children }) => {
  const components = useComponents();
  const renderDecorator = () => {
    if (!schema.decoratorType || !components[schema.decoratorType]) {
      return <>{children}</>;
    }
    return React.createElement(
      components[schema.decoratorType],
      schema.decoratorProps,
      children,
    );
  };

  return (
    <SchemaContext.Provider value={schema}>
      {renderDecorator()}
    </SchemaContext.Provider>
  );
};
