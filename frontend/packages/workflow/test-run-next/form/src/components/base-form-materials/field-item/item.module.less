.field-item {
  margin-bottom: 12px;
}

.item-title {
  position: relative;
  margin-bottom: 4px;

  font-size: 12px;
  line-height: 16px;
  overflow-wrap: break-word;

  .item-label {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
  }

  .item-tag {
    flex-shrink: 0;
    margin-left: 4px;
  }
  .tooltip-icon {
    margin-left: 4px;
    color: var(--coz-fg-dim);
  }
}

.title-text {
  color: var(--coz-fg-primary);
}

.title-required {
  color: var(--coz-fg-hglt-red);
}
.item-description {
  color: var(--coz-fg-secondary);
}

.item-feedback {
  margin-top: 2px;
  font-size: 12px;
  line-height: 16px;
  color: var(--coz-fg-hglt-red);
  overflow-wrap: break-word;
}
