/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { clsx } from 'clsx';
import { DatePicker } from '@coze-arch/coze-design';

import css from './time.module.less';

export interface InputTimeProps {
  className?: string;
  value?: string;
  onChange?: (v?: string) => void;
}

export const InputTime: React.FC<InputTimeProps> = ({
  className,
  value,
  onChange,
  ...props
}) => (
  <DatePicker
    className={clsx(css['input-time'], className)}
    type="dateTime"
    size="small"
    showClear={false}
    showSuffix={false}
    value={value}
    onChange={(_date, dateString) => {
      if (typeof dateString === 'string' || dateString === undefined) {
        onChange?.(dateString);
      }
    }}
    {...props}
  />
);
