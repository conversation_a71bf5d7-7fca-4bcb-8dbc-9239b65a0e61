{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "stories", "vitest.config.ts", "tailwind.config.ts"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDir": "./", "outDir": "./dist", "paths": {"@/*": ["./src/*"]}, "types": ["vitest/globals"], "strictNullChecks": true, "noImplicitAny": true, "moduleResolution": "bundler", "module": "ESNext"}}