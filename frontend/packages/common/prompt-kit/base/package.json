{"name": "@coze-common/prompt-kit-base", "version": "0.0.1", "description": "@coze-common/prompt-kit-base", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {"./create-prompt": "./src/create-prompt/index.tsx", "./shared": "./src/shared/index.tsx", "./shared/types": "./src/shared/types/index.ts", "./shared/css": "./src/shared/css/index.css", "./editor": "./src/editor/index.tsx"}, "main": "src/index.tsx", "typesVersions": {"*": {"create-prompt": ["./src/create-prompt/index.tsx"], "shared": ["./src/shared/index.tsx"], "editor": ["./src/editor/index.tsx"], "shared/css": ["./src/shared/css/index.css"], "shared/types": ["./src/shared/types/index.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@codemirror/view": "^6.34.1", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-common/editor-plugins": "workspace:*", "@coze-editor/editor": "0.1.0-alpha.d92d50", "@coze-foundation/local-storage": "workspace:*", "ahooks": "^3.7.8", "classnames": "^2.3.2", "immer": "^10.0.3", "lodash-es": "^4.17.21", "react-markdown": "^8.0.3", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}