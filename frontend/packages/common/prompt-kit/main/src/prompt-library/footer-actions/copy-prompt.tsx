/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import copy from 'copy-to-clipboard';
import { type EditorAPI } from '@coze-editor/editor/preset-prompt';
import { I18n } from '@coze-arch/i18n';
import { Button, Toast } from '@coze-arch/coze-design';

export const CopyPrompt = (props: {
  editor: EditorAPI;
  onCopyPrompt: () => void;
}) => {
  const { editor, onCopyPrompt } = props;
  return (
    <Button
      color="primary"
      onClick={() => {
        const text = editor?.$view.state.doc.toString();
        const result = copy(text, { format: 'text/plain' });
        result &&
          Toast.success(I18n.t('prompt_library_prompt_copied_successfully'));
        onCopyPrompt?.();
      }}
    >
      {I18n.t('prompt_detail_copy_prompt')}
    </Button>
  );
};
