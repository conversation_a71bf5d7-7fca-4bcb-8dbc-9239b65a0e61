.footer-container {
  padding: 12px 0 28px;
  text-align: center;

  * {
    vertical-align: middle;
  }

  .loading,
  .error-retry {
    margin-left: 10px;
    line-height: 20px;
    color: var(--semi-color-text-3, rgba(29, 28, 35, 35%));
  }

  .error-retry {
    cursor: pointer;
    color: var(--semi-color-focus-border, #4D53E8);
  }

  :global {
    .semi-spin-middle>.semi-spin-wrapper {
      height: 16px;

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .load-more-btn {
    font-weight: 600;
    background: #FFF;
    border-radius: 40px;

    span {
      color: #1D1C23;
    }

    &:hover {
      background: #FFF;
      border: none;
    }
  }

  &.responseive-foot-container {
    padding: 0 0 16px;

    .load-more-btn {
      height: 40px;
      padding: 16px 24px;
    }
  }
}

.height-whole-100 {
  overflow: visible;
  height: 100%;



  .spin {
    display: block;
    width: 100%;
    height: 100%;

    :global {
      .semi-spin-wrapper {
        position: absolute;
        display: flex;
        justify-content: center;
        margin-top: 16px;

        svg {
          width: 24px;
          height: 24px;
        }
      }



      .semi-tabs-content {
        padding: 0;
      }

      .semi-spin-children {
        height: 100%;
      }
    }

    .loading-text {
      margin-left: 8px;

      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      color: var(--semi-color-text-3, rgba(29, 28, 35, 35%));
    }
  }

  :global {
    .semi-list-item {
      padding: 0
    }

    .semi-list-footer {
      padding: 0;
    }
  }
}
