{"name": "@coze-common/prompt-kit", "version": "0.0.1", "description": "prompt编辑器，基于@coze-editor/editor", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./*": "./src/", "./editor": "./src/editor/index.ts", "./prompt-recommend": "./src/prompt-recommend/index.ts", "./create-prompt": "./src/create-prompt/index.ts", "./nl-prompt": "./src/nl-prompt/index.ts"}, "main": "src/index.ts", "typesVersions": {"*": {"editor": ["./src/editor/index.ts"], "prompt-recommend": ["./src/prompt-recommend/index.ts"], "create-prompt": ["./src/create-prompt/index.ts"], "nl-prompt": ["./src/nl-prompt/index.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@codemirror/state": "^6.4.1", "@codemirror/view": "^6.34.1", "@coze-agent-ide/bot-editor-context-store": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-http": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/idl": "workspace:*", "@coze-common/chat-answer-action": "workspace:*", "@coze-common/chat-area-utils": "workspace:*", "@coze-common/chat-core": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-common/editor-plugins": "workspace:*", "@coze-common/prompt-kit-adapter": "workspace:*", "@coze-common/prompt-kit-base": "workspace:*", "@coze-common/scroll-view": "workspace:*", "@coze-editor/editor": "0.1.0-alpha.d92d50", "@coze-foundation/local-storage": "workspace:*", "@lezer/common": "^1.2.2", "ahooks": "^3.7.8", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "immer": "^10.0.3", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "nanoid": "^4.0.2", "rc-textarea": "^1.6.3", "react-markdown": "^8.0.3", "zustand": "^4.4.7"}, "devDependencies": {"@codemirror/language": "^6.10.1", "@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}