/* stylelint-disable selector-class-pattern */
.library-variable-insert-tree {
  :global {
    .semi-tree-option-empty {
      background: transparent;
    }

    .semi-tree-option-highlight {
      color: var(--coz-fg-hglt-yellow, #FF7300);
    }
  }
}

.tab-bar-wrapper {
  width: 100%;
  padding: 8px 8px 0;

  :global {
    .semi-radio-buttonRadioGroup {
      padding: 4px 5px;
    }

    .semi-radio-addon-buttonRadio {
      width: 158px;
    }
  }
}

.library-item {
  background: transparent;
}

.library-suggestion-keyboard-selected {
  background: var(--semi-color-fill-0);
}