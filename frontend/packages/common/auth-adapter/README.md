# @coze-common/auth-adapter

统一的权限控制逻辑

## Overview

This package is part of the Coze Studio monorepo and provides utilities functionality. It serves as a core component in the Coze ecosystem.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-common/auth-adapter": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-common/auth-adapter';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Core functionality for Coze Studio
- TypeScript support
- Modern ES modules

## API Reference

### Exports

- `useInitSpaceRole`
- `useInitProjectRole`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- React
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
