/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/* eslint-disable @typescript-eslint/naming-convention */
// 类型选择控件基础宽度
export const OperatorTypeBaseWidth = 155;

// 61 = 删除按钮 + 添加按钮 的上层容器宽度
export const OperatorLargeSize = 61;
// 31 = 删除按钮 的上层容器宽度
export const OperatorSmallSize = 31;
// 8 = 删除按钮与变量类型中间的 margin 距离
export const SpacingSize = 8;
