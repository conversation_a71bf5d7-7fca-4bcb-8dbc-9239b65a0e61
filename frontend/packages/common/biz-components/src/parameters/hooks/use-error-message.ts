/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import resolvePath from '../utils/resolve-path';
import useNode from './use-node';
import useParametersConfig from './use-config';

export default function useErrorMessage(key: string): string {
  const { errors = [] } = useParametersConfig();
  const { field = '' } = useNode();
  const pathSearched = resolvePath(field, key);

  const error = errors.find(({ path }) => pathSearched === path);

  return error?.message || '';
}
