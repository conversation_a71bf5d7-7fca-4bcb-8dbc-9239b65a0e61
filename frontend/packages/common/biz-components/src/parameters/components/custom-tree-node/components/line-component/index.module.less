/** 基本组件样式 */
.dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: gray;
}

.empty-block {
  flex: 1;
}

.help-line-block {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 100%;
    background: gray;
  }
}

/** 业务组件样式 */
.half-top-root {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 0;
    left: 0;
    border-left: 1px solid gray;
    border-bottom: 1px solid gray;
    border-radius: 0 0 0 4px;
    width: 80%;
    height: 22px;
  }

  .dot {
    display: none;
  }

  &.children {
    .dot {
      display: block;
      position: absolute;
      top: 22px;
      left: 70%;
      transform: translate3d(0, -60%, 0);
    }
  }
}

.half-bottom-root {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 22px;
    left: 0;
    border-left: 1px solid gray;
    border-top: 1px solid gray;
    border-radius: 4px 0 0;
    width: 80%;
    height: calc(100% - 12px);
  }

  .dot {
    display: none;
  }

  &.children {
    .dot {
      display: block;
      position: absolute;
      top: 22px;
      left: 70%;
      transform: translate3d(0, -40%, 0);
    }
  }
}

.full-root {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;

  .top-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 80%;
    height: 22px;
    border-left: 1px solid gray;
    border-bottom: 1px solid gray;
    border-radius: 0 0 0 4px;
  }

  .bottom-line {
    position: absolute;
    top: 19px;
    left: 0;
    width: 1px;
    height: calc(100% - 19px);
    background: gray;
  }

  .dot {
    display: none;
  }

  &.children {
    .dot {
      display: block;
      position: absolute;
      top: 22px;
      left: 70%;
      transform: translate3d(0, -60%, 0);
    }
  }
}

.half-top-child {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;

  .line {
    position: absolute;
    top: 0;
    left: 0;
    border-left: 1px solid gray;
    border-bottom: 1px solid gray;
    border-radius: 0 0 0 4px;
    width: 80%;
    height: 22px;
  }

  .dot {
    display: none;
  }

  &.children {
    .dot {
      display: block;
      position: absolute;
      top: 22px;
      left: 70%;
      transform: translate3d(0, -60%, 0);
    }
  }
}

.full-child {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;

  .top-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 80%;
    height: 22px;
    border-left: 1px solid gray;
    border-bottom: 1px solid gray;
    border-radius: 0 0 0 4px;
  }

  .bottom-line {
    position: absolute;
    top: 19px;
    left: 0;
    width: 1px;
    height: calc(100% - 19px);
    background: gray;
  }

  .dot {
    display: none;
  }

  &.children {
    .dot {
      display: block;
      position: absolute;
      top: 22px;
      left: 70%;
      transform: translate3d(0, -60%, 0);
    }
  }
}

.multiline {
  .line {
    position: absolute;
    top: -51px;
    height: 73px;
  }

  .top-line {
    position: absolute;
    top: -51px;
    height: 73px;
  }

  &.with-name-error {
    .line {
      position: absolute;
      top: -7px;
      height: 29px;
    }

    .top-line {
      position: absolute;
      top: -7px;
      height: 29px;
    }
  }
}
