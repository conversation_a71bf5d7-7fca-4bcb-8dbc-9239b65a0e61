.container {
  display: flex;
  // align-items: center;
  position: relative;
  align-self: stretch;
  flex-direction: column;
  width: 155px;
  margin-left: 8px;



  .pop-container {
    align-self: self-start;
    width: 100%;
  }

  .param-type {
    width: 100%;
    height: 24px;
    padding: 0 1px;
  }

  // 已经无用，可以删除，添加子项按钮已经放在单独组件中了
  .param-operator {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-start;
    align-self: stretch;
    width: 86px;
    height: 24px;
    margin-left: 8px;

    .icon-no {
      display: flex;
      align-items: center;

      .icon {
        width: 20px;
        height: 20px;
        cursor: pointer;
        color: #888D92;

        &.disabled {
          cursor: not-allowed;
        }

        &>svg {
          width: 20px;
          height: 20px;
        }
      }
    }

    .add {
      margin-left: 8px;
      color: #4D53E8;
      cursor: pointer;
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 16px;
    }
  }

}
