.input-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;

  >* {
    width: 100%;
  }

  span {
    width: 100%;
  }
}

.error-content {
  height: 20px;
}

.error-float {
  width: 100%;
  position: absolute;
}

.error-text {
  font-size: 14px;
  font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  line-height: 22px;
  color: var(--semi-color-danger)
}

.input {
  input {
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.limit-count {
  padding-left: 8px;
  padding-right: 12px;
  overflow: hidden;
  color: var(--light-usage-text-color-text-3, rgba(28, 31, 35, 0.35));
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}
