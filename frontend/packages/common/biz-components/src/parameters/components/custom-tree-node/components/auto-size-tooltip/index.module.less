.popup_container {
  display: flex;
  flex-direction: column;
  position: relative;
  .nano {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    align-self: stretch;
    pointer-events: none;
    :global {
      .semi-portal-inner {
        left: 50% !important;
      }
    }
  }
}
.tooltip {
  &.top-level {
    word-break: break-word;
    border-radius: 6px;
    background: var(--light-color-grey-grey-7, #41414C);
    max-width: 400px;
    color: var(--light-usage-bg-color-bg-0, #FFF);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  & > svg > path {
    fill: var(--light-color-grey-grey-7, #41414C);
  }
}
