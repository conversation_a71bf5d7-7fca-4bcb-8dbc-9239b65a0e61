.header {
  display: flex;
  align-items: center;

  .name {
    flex: 1;
  }

  .type {
    margin-left: 8px;
    width: 155px;
    flex-grow: 0;
    flex-shrink: 0;
  }

  .description {
    margin-left: 8px;
    width: 312px;
  }

  &.withDescription {
    .name {
      flex: auto;
      width: 181px;
      flex-grow: 0;
      flex-shrink: 0;
    }
  }
}

.text {
  color: var(--light-usage-text-color-text-3, rgb(28 31 35 / 35%));
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}
