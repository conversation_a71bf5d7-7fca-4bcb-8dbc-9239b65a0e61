.upload {
  overflow: hidden;
  width: fit-content;
  height: 64px;
  margin: auto;

  .circle {
    :global {
      .semi-upload-picture-file-card-uploading::before {
        border-radius: 50%;
      }

      img {
        border-radius: 50%;
      }
    }
  }

  .avatar {
    width: 64px;
    height: 64px;
    border-radius: 14px;

    >img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    :global {
      .semi-skeleton-image {
        background-color: #fff;
        border: 1px solid rgb(29 28 35 / 8%);
        border-radius: 14px;
      }
    }
  }
}

.upload-button-wrap {
  display: flex;
  justify-content: center;
  width: 100%;
}

.upload-button {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.avatar-wrap {
  position: relative;
  width: 64px;
  height: 64px;

  .mask {
    cursor: pointer;

    &.full-center {
      position: absolute;
      top: 0;
      left: 0;

      display: flex;
      align-items: center;
      justify-content: center;

      width: 100%;
      height: 100%;

      color: rgba(255, 255, 255, 0%);

      visibility: hidden;
      background-color: rgba(22, 22, 26, 0%);
      border-radius: 14px;

      transition: all 0.1s;
    }

    &.right-bottom {
      position: absolute;
      right: 0;
      bottom: 0;

      display: flex;
      align-items: center;
      justify-content: center;

      width: 28px;
      height: 28px;
      padding: 4px 0 0 4px;

      color: #fff;
    }
  }

  &:hover {
    .mask {
      &.full-center {
        color: #fff;
        visibility: visible;
        background-color: var(--coz-mg-mask);
      }
    }
  }
}

.upload-with-auto-generate {
  display: flex;
  align-items: flex-end;

  .upload {
    height: 64px;
    margin: 0;

    .avatar {
      width: 64px;
      height: 64px;
    }
  }

  .avatar-wrap {
    width: 64px;
    height: 64px;
  }
}
