{"extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"baseUrl": "./", "paths": {}, "types": [], "jsx": "react", "isolatedModules": true, "preserveSymlinks": false, "strictPropertyInitialization": false, "module": "ESNext", "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}], "$schema": "https://json.schemastore.org/tsconfig"}