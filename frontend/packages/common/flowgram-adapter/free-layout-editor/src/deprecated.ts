/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/**
 * @deprecated
 * @param val
 */
export function markParsed(val: any) {
  console.error('[flowgram.ai] "markParsed" is deprecated.');
  return val;
}

/**
 * @deprecated
 * @param ctx
 */
export function useFormItemValidate(ctx: any) {
  // D Nothing
  console.error('[flowgram.ai] "useFormItemValidate" is deprecated.');
}

/**
 * @deprecated
 * @param ctx
 */
export function useFormItemContext(ctx: any) {
  // DO Nothing
  console.error('[flowgram.ai] "useFormItemContext" is deprecated.');
  return {};
}
