# @flowgram-adapter/fixed-layout-editor

对 flowgram 的封装

## Overview

This package is part of the Coze Studio monorepo and provides utilities functionality. It includes service, manager, editor and more.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@flowgram-adapter/fixed-layout-editor": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@flowgram-adapter/fixed-layout-editor';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Service
- Manager
- Editor
- Plugin

## API Reference

### Exports

- `type FlowNodeEntity,
  type CollapseProps,
  type IPoint,
  type EntityOpts,
  type PluginsProvider,
  type Plugin,
  type ClipboardService,
  type EditorPluginContext,
  type FlowDocumentJSON,
  type FlowLayoutDefault,
  type FlowOperationService,
  type SelectionService,
  type FixedHistoryPluginOptions,
  type HistoryService,
  type FlowRendererRegistry,
  type <PERSON>deFormProps,
  type FixedLayoutProps,
  type FlowNodeRegistry,
  type FlowNodeRegistry as FlowNodeRegister,
  type PluginCreator,
  type PositionSchema,
  type FlowNodeJSON,
  type FlowNodeMeta,
  useConfigEntity,
  useService,
  useBaseColor,
  EntityManager,
  useRefresh,
  Rectangle,
  usePlayground,
  EditorState,
  usePlaygroundTools,
  ConfigEntity,
  FlowDocument,
  createPluginContextDefault,
  PlaygroundReactProvider,
  bindContributions,
  FlowDocumentContribution,
  FlowRendererContribution,
  createOperationPlugin,
  createDefaultPreset,
  createPlaygroundPlugin,
  FlowDocumentOptionsDefault,
  FlowDocumentOptions,
  FlowNodesContentLayer,
  FlowNodesTransformLayer,
  FlowScrollBarLayer,
  FlowScrollLimitLayer,
  EditorProps,
  FlowNodeRenderData,
  FlowNodeTransformData,
  FlowNodeTransitionData,
  PlaygroundLayer,
  FixedLayoutRegistries,
  ConstantKeys,
  PlaygroundEntityContext,
  getNodeForm,
  FlowRendererKey,
  EditorRenderer,
  FlowDocumentTransformerEntity,
  Layer,
  observeEntity,
  domUtils,
  FlowNodeBaseType,
  bindConfigEntity,
  definePluginCreator,
  Emitter,
  Point,
  createPlaygroundReactPreset,`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
