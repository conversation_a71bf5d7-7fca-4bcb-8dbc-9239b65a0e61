# @coze-arch/uploader-interface

uploader interface

## Overview

This package is part of the Coze Studio monorepo and provides utilities functionality. It includes store, service.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/uploader-interface": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/uploader-interface';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Store
- Service

## API Reference

### Exports

- `type ProgressEventInfo = BaseEventInfo;`
- `type StreamProgressEventInfo = BaseEventInfo;`
- `type ErrorEventInfo = BaseEventInfo;`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
