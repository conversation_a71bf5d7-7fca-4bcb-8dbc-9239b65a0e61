/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import {
  PluginMode,
  PluginName,
  WriteableChatAreaPlugin,
} from '@coze-common/chat-area';

import { ResumeRenderLifeCycleService } from './life-cycle-service/render-life-cycle-service';
import { ResumeMessageLifeCycleService } from './life-cycle-service/message-life-cycle-service';

export class ResumePlugin extends WriteableChatAreaPlugin<unknown> {
  public pluginMode: PluginMode = PluginMode.Writeable;
  public pluginName: PluginName = PluginName.Resume;

  public lifeCycleServices = {
    messageLifeCycleService: new ResumeMessageLifeCycleService(this),
    renderLifeCycleService: new ResumeRenderLifeCycleService(this),
  };
}
