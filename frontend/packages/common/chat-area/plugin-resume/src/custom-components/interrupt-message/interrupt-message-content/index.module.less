.interrupt-message-box {
  user-select: none;

  overflow: hidden;
  display: flex;
  flex-direction: column;

  box-sizing: border-box;
  width: 100%;

  font-weight: 400;
  line-height: 16px;
  word-break: break-word;

  // 用户点击授权后文案样式，无背景
  .interrupt-message-action {
    padding: 6px;
    color: var(--fg-coze-fg-secondary, rgba(6, 7, 9, 50%));
  }
  // 中断消息内容
  .interrupt-message-content {
    padding: 12px;
    color: var(--fg-coze-fg-primary, rgba(6, 7, 9, 80%));
    background-color: var(--bg-coze-bg-max, #FFF);

    .interrupt-message-content-btns {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  }


}
