/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { AxiosError, type AxiosResponse } from 'axios';

export class ApiError extends AxiosError {
  public raw?: unknown;
  type: string;

  logId: string;

  constructor(
    public code: string,
    public msg: string | undefined,
    response: AxiosResponse,
  ) {
    super(msg, code, response.config, response.request, response);
    this.name = 'ApiError';
    this.type = 'Api Response Error';
    this.raw = response.data;
    this.logId = response.headers?.['x-tt-logid'];
  }
}

export const isApiError = (error: unknown): error is ApiError =>
  error instanceof ApiError;
