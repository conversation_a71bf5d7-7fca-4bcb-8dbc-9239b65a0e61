{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "vitest.config.ts", "__mocks__", ".eslintrc.js"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDir": "./", "outDir": "./dist", "types": ["vitest/globals"], "moduleResolution": "Node10", "module": "CommonJS", "lib": ["ES2022", "DOM"], "paths": {"@/*": ["./src/*"]}, "strictNullChecks": true, "noImplicitAny": true, "skipLibCheck": true}}