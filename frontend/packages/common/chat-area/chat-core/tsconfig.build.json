{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "moduleResolution": "Node10", "module": "CommonJS", "lib": ["ES2022", "DOM"], "paths": {"@/*": ["./src/*"]}, "strictNullChecks": true, "noImplicitAny": true, "skipLibCheck": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/fetch-stream/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../../../arch/slardar-adapter/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../../studio/common/file-kit/tsconfig.build.json"}, {"path": "../../uploader-adapter/tsconfig.build.json"}, {"path": "../utils/tsconfig.build.json"}]}