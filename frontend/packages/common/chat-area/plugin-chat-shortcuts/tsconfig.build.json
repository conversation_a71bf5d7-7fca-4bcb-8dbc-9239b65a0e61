{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "strictNullChecks": true, "noUncheckedIndexedAccess": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../agent-ide/tool-config/tsconfig.build.json"}, {"path": "../../../agent-ide/tool/tsconfig.build.json"}, {"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../../arch/bot-store/tsconfig.build.json"}, {"path": "../../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../chat-area/tsconfig.build.json"}, {"path": "../chat-core/tsconfig.build.json"}, {"path": "../chat-uikit-shared/tsconfig.build.json"}, {"path": "../chat-uikit/tsconfig.build.json"}, {"path": "../../../components/bot-icons/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../../studio/components/tsconfig.build.json"}, {"path": "../../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../utils/tsconfig.build.json"}, {"path": "../../websocket-manager-adapter/tsconfig.build.json"}, {"path": "../../../workflow/sdk/tsconfig.build.json"}]}