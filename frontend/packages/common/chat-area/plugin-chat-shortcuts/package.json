{"name": "@coze-common/chat-area-plugins-chat-shortcuts", "version": "0.0.1", "description": "作为chat-input的插件slot默认实现，维护快捷键相关功能", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.tsx", "./shortcut-tool": "./src/shortcut-tool/index.tsx"}, "main": "src/index.tsx", "typesVersions": {"*": {"shortcut-tool": ["./src/shortcut-tool/index.tsx"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-agent-ide/tool": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-common/chat-area": "workspace:*", "@coze-common/chat-area-utils": "workspace:*", "@coze-common/chat-core": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-common/chat-uikit-shared": "workspace:*", "@coze-common/websocket-manager-adapter": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/components": "workspace:*", "@coze-workflow/sdk": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@douyinfe/semi-ui": "~2.72.3", "ahooks": "^3.7.8", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "immer": "^10.0.3", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@coze-agent-ide/tool-config": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "utility-types": "^3.10.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}