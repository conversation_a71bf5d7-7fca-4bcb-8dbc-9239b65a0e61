/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type FC } from 'react';

import { Form, Tooltip, Typography } from '@coze-arch/bot-semi';
import { IconInfo } from '@coze-arch/bot-icons';

import style from './index.module.less';

export const LabelWithDescription: FC<{
  name: string;
  description?: string;
  required?: boolean;
}> = ({ name, description, required = true }) => (
  <div className="w-full flex items-center px-2 mb-[2px]">
    <Form.Label
      text={
        <Typography.Text
          ellipsis={{ showTooltip: true }}
          className={style.text}
        >
          {name}
        </Typography.Text>
      }
      required={required}
      className={style.label}
    />
    {!!description && (
      <Tooltip content={description}>
        <IconInfo className={style.icon} />
      </Tooltip>
    )}
  </div>
);
