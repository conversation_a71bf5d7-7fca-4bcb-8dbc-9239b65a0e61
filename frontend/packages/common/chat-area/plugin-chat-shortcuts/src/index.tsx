/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// !Notice 禁止直接导出 shortcut-tool，会导致下游依赖不需要的 knowledge-upload
// export { ShortcutToolConfig } from './shortcut-tool';
export { ShortcutBar } from './shortcut-bar';

export { ComponentsTable } from './shortcut-tool/shortcut-edit/components-table';

export {
  ShortCutCommand,
  getStrictShortcuts,
} from '@coze-agent-ide/tool-config';

export type {
  OnBeforeSendTemplateShortcutParams,
  OnBeforeSendQueryShortcutParams,
} from './shortcut-bar/types';

export { getUIModeByBizScene } from './utils/get-ui-mode-by-biz-scene';
