/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import mitt from 'mitt';
import { type PluginRegistryEntry } from '@coze-common/chat-area';

import {
  type BackgroundPluginBizContext,
  type ChatBackgroundEvent,
} from './types/biz-context';
import { createBackgroundImageStore } from './store';
import { BizPlugin } from './plugin';

export const chatBackgroundEvent = mitt<ChatBackgroundEvent>();
export {
  ChatBackgroundEvent,
  ChatBackgroundEventName,
} from './types/biz-context';

export const createChatBackgroundPlugin = () => {
  const useChatBackgroundContext = createBackgroundImageStore('chatBackground');

  // eslint-disable-next-line @typescript-eslint/naming-convention -- 插件命名大写开头符合预期
  const ChatBackgroundPlugin: PluginRegistryEntry<BackgroundPluginBizContext> =
    {
      createPluginBizContext() {
        return {
          storeSet: {
            useChatBackgroundContext,
          },
          chatBackgroundEvent,
        };
      },
      Plugin: BizPlugin,
    };
  return {
    ChatBackgroundPlugin,
  };
};
