/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { messageSource, taskType } from '@coze-common/chat-core';
import { type Message, type MessageMeta } from '@coze-common/chat-area';

import { getIsLastGroup } from './get-is-last-group';

export const getShowBotTriggerButton = ({
  // eslint-disable-next-line @typescript-eslint/naming-convention -- .
  message: { source, extra_info },
  meta,
  latestSectionId,
}: {
  message: Pick<Message, 'source' | 'extra_info'>;
  meta: Pick<MessageMeta, 'isFromLatestGroup' | 'sectionId'>;
  latestSectionId: string;
}) =>
  source === messageSource.TaskManualTrigger &&
  extra_info.task_type === taskType.PresetTask &&
  getIsLastGroup({ meta, latestSectionId });
