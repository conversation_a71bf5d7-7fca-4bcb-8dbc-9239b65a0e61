/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { isObject } from 'lodash-es';

import { type InputWorkflowNodeContent, type WorkflowNode } from './type';

export const isWorkflowNodeData = (value: unknown): value is WorkflowNode =>
  isObject(value) && 'content' in value && 'content_type' in value;

export const isInputWorkflowNodeContent = (
  value: unknown,
): value is InputWorkflowNodeContent =>
  isObject(value) && 'type' in value && 'name' in value;

export const isInputWorkflowNodeContentLikelyArray = (
  value: unknown,
): value is unknown[] => Array.isArray(value);
