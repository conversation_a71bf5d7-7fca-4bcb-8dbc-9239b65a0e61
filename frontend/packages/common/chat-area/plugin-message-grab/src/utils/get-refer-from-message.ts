/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { ContentType, type Message } from '@coze-common/chat-area';

export const getReferFromMessage = (message: Message<ContentType>) => {
  if (message.content_type === ContentType.Mix) {
    const { refer_items } = JSON.parse(message.content) ?? {};

    if (refer_items) {
      const firstItem = refer_items?.[0];

      if (firstItem?.type === 'text') {
        return {
          type: ContentType.Text,
          text: firstItem?.text,
        };
      } else if (firstItem?.type === 'image') {
        return {
          type: ContentType.Image,
          uri: firstItem?.image?.key,
          url: firstItem?.image?.image_thumb?.url,
        };
      }
    }
  }
  return undefined;
};
