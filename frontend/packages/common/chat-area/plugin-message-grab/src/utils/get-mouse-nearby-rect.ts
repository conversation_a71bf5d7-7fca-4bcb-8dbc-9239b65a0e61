/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type GrabPosition } from '@coze-common/text-grab';

const BUFFER_SIZE = 5;

export const getMouseNearbyRect = (
  rects: DOMRect[],
  mouseInfo: GrabPosition,
) => {
  let nearbyRect = rects.at(0);
  for (const rect of rects) {
    if (
      mouseInfo.x >= rect.left - BUFFER_SIZE &&
      mouseInfo.x <= rect.right + BUFFER_SIZE &&
      mouseInfo.y >= rect.top - BUFFER_SIZE &&
      mouseInfo.y <= rect.bottom + BUFFER_SIZE
    ) {
      nearbyRect = rect;
    }
  }

  return nearbyRect;
};
