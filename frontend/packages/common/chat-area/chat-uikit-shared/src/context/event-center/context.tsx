/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type PropsWithChildren, createContext } from 'react';

import mitt from 'mitt';
import { useCreation } from 'ahooks';

import {
  type UIKitEventMap,
  type UIKitEventCenter,
  type UIKitEventProviderProps,
} from './type';
import { useObserveChatContainer } from './hooks';

export const UIKitEventContext = createContext<UIKitEventCenter | null>(null);

export const UIKitEventProvider: React.FC<
  PropsWithChildren<UIKitEventProviderProps>
> = ({ chatContainerRef, children }) => {
  const eventCenter = useCreation(() => mitt<UIKitEventMap>(), []);

  useObserveChatContainer({ eventCenter, chatContainerRef });

  return (
    <UIKitEventContext.Provider value={eventCenter}>
      {children}
    </UIKitEventContext.Provider>
  );
};
