/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type PropsWithChildren } from 'react';

import classNames from 'classnames';

import { typeSafeAudioStaticToastVariants } from './variant';

export interface AudioStaticToastProps {
  theme?: 'danger' | 'primary' | 'background';
  className?: string;
  color?: 'primary' | 'danger';
}

export const AudioStaticToast: React.FC<
  PropsWithChildren<AudioStaticToastProps>
> = ({ children, theme = 'primary', color = 'primary', className }) => {
  const cvaClassNames = typeSafeAudioStaticToastVariants({ theme, color });
  return <div className={classNames(cvaClassNames, className)}>{children}</div>;
};
