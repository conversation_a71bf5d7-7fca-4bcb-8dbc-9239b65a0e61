/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type FC } from 'react';

import classNames from 'classnames';
import { IconCozSendFill } from '@coze-arch/coze-design/icons';
import { IconButton } from '@coze-arch/coze-design';
import { Layout, type SendButtonProps } from '@coze-common/chat-uikit-shared';

import { UIKitTooltip } from '../../../../common/tooltips';

const SendButton: FC<SendButtonProps> = props => {
  const { isDisabled, tooltipContent, onClick, layout } = props;
  return (
    <UIKitTooltip
      content={tooltipContent}
      hideToolTip={layout === Layout.MOBILE}
    >
      <IconButton
        className={classNames('!rounded-full', !isDisabled && '!coz-fg-hglt')}
        disabled={isDisabled}
        data-testid="bot-home-chart-send-button"
        size="default"
        color="secondary"
        icon={<IconCozSendFill className="text-18px" />}
        onClick={onClick}
      />
    </UIKitTooltip>
  );
};

export default SendButton;
