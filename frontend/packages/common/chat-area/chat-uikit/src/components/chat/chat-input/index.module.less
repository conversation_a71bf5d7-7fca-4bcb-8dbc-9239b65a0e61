.input-wrap {
  position: relative;

  display: flex;
  flex-direction: row;
  align-items: center;

  width: 100%;
}

@keyframes move-left-fade-out {
  0% {
      transform: translateX(0);
      opacity: 1;
  }

  100% {
      transform: translateX(-100%);
      opacity: 0;
  }
}

@keyframes move-right-fade-in {
  0% {
      transform: translateX(-100%);
      opacity: 0;
  }

  100% {
      transform: translateX(0);
      opacity: 1;
  }
}
@fade-easing-time: 300ms;

@fade-easing-function: cubic-bezier(0.65, 0, 0.35, 1);

.animate-left {
  animation: move-left-fade-out forwards @fade-easing-time @fade-easing-function;
}

.animate-left-revert {
  animation: move-right-fade-in forwards @fade-easing-time @fade-easing-function;
}


.input-container {
  display: flex;
  flex-direction: column;
}

.input-tooltip-anchor {
  position: absolute;
  top: -10%;
}

.left-actions-container {
  align-self: flex-end;
  width: fit-content;
}

.textarea-with-top-rows {
  display: flex;
  flex-direction: column;

  border-style: solid;
  border-width: 1px;
  border-radius: 24px;

  transition: width @fade-easing-time;
}

.background-theme {
  textarea {
    // 背景图模式覆盖样式
    /* stylelint-disable-next-line declaration-no-important */
    background: transparent !important;
  }
}

.textarea-with-actions-container {
  position: relative;
  flex: 1;
  align-items: center;
  min-height: 48px;
}

.input-focus {
  border-style: solid;
  border-width: 1px;
}

/* stylelint-disable-next-line selector-class-pattern */
.textarea-with-actions-container__col {
  flex-direction: column;
}

/* stylelint-disable-next-line selector-class-pattern */
.textarea-with-actions-container__row {
  display: flex;
  flex-direction: row;
}

.textarea-actions-container {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  align-self: flex-end;
}

.textarea-actions-container-transition {
  transition: opacity @fade-easing-time @fade-easing-function
}

.textarea-actions-left {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-right: 4px;
}

.textarea-actions-right {
  margin-left: 4px;
}

.textarea {
  display: flex;

  width: 100%;
  height: 20px;
  min-height: 20px;
  margin: 0;

  font-family: inherit;
  font-weight: 500;
  line-height: 20px;
  overflow-wrap: break-word;
  white-space: pre-wrap;

  border: none;
  border-radius: 0;
  outline: none;

  &::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #e6e6e7;
    border-radius: 4px;
  }
}

.textarea::placeholder {
  font-weight: 500;
}

.textarea:disabled {
  background: #fff;
}

.bottom-tips {
  width: 100%;
  padding: 8px 24px;

  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  text-align: center;
}

.danger-mask-transition {
  transition: opacity 500ms cubic-bezier(0.33, 1, 0.68, 1);
}

.mobile-audio-bg {
  /* stylelint-disable-next-line declaration-no-important */
  background: linear-gradient(117deg, #5448FF 4.74%, #B026F1 131.42%) !important;
}

.mobile-audio-bg-danger {
  @apply !coz-mg-hglt-plus-red;
}
