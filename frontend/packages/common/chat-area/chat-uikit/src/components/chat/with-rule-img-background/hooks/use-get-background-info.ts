/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useRef } from 'react';

import { useSize } from 'ahooks';

import { getStandardRatio } from '../utils';
import { type BackgroundImageInfo } from '../types';
import { MODE_CONFIG } from '../const';

export const useGetResponsiveBackgroundInfo = ({
  backgroundInfo,
}: {
  backgroundInfo?: BackgroundImageInfo;
}) => {
  const targetRef = useRef(null);

  const size = useSize(targetRef);
  const { width = 0, height = 0 } = size ?? {};

  const isMobileMode = width / height <= getStandardRatio('mobile');

  const mobileBackgroundInfo = backgroundInfo?.mobile_background_image;
  const pcBackgroundInfo = backgroundInfo?.web_background_image;

  const currentBackgroundInfo = isMobileMode
    ? mobileBackgroundInfo
    : pcBackgroundInfo;

  const { theme_color } = currentBackgroundInfo ?? {};
  const { size: cropperSize } = MODE_CONFIG[isMobileMode ? 'mobile' : 'pc'];

  return {
    targetRef,
    currentBackgroundInfo,
    targetWidth: width,
    targetHeight: height,
    currentThemeColor: theme_color,
    cropperSize,
  };
};
