/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export interface StoreStruct {
  coze_home_mention_tip_showed: boolean;
  coze_home_favorite_list_display: boolean;
  coze_home_favorite_list_filter: 'all' | 'byMe';
}

export type LocalCacheKey = keyof StoreStruct;

type StoreStructRange = Record<keyof StoreStruct, boolean | string | number>;

const storeStructRangeCheck = (range: StoreStructRange) => 0;
declare const voidStruct: StoreStruct;
// eslint-disable-next-line @typescript-eslint/no-unused-vars -- 类型测试
const dryRun = () => storeStructRangeCheck(voidStruct);
