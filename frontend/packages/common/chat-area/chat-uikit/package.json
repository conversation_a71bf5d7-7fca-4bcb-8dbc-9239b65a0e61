{"name": "@coze-common/chat-uikit", "version": "0.0.1", "description": "@coze-common/chat-uikit", "license": "Apache-2.0", "author": "liushu<PERSON><EMAIL>", "maintainers": [], "main": "src/index.ts", "types": "./src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "exit 0", "dev": "NODE_ENV=development rspack serve", "dev:storybook": "storybook dev -p 6006", "lint": "eslint ./ --cache", "lint:type": "tsc -p tsconfig.json --noEmit", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test --coverage", "test:watch": "vitest --passWithNoTests"}, "dependencies": {"@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-common/chat-area-utils": "workspace:*", "@coze-common/chat-core": "workspace:*", "@coze-common/chat-hooks": "workspace:*", "@coze-common/chat-uikit-shared": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@douyinfe/semi-illustrations": "^2.36.0", "@douyinfe/semi-ui": "~2.72.3", "bowser": "2.11.0", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "dayjs": "^1.11.7", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "rc-textarea": "^1.6.3", "react": "~18.2.0", "react-dom": "~18.2.0", "react-error-boundary": "^4.0.9", "stylelint": "^15.11.0"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-json": "~6.0.0", "@rollup/plugin-node-resolve": "~15.0.1", "@rollup/plugin-replace": "^4.0.0", "@rspack/cli": "0.4.0", "@rspack/core": "0.4.0", "@rspack/plugin-react-refresh": "0.4.0", "@storybook/addon-essentials": "^7.6.7", "@storybook/addon-interactions": "^7.6.7", "@storybook/addon-links": "^7.6.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.7", "@storybook/react": "^7.6.7", "@storybook/react-vite": "^7.6.7", "@storybook/test": "^7.6.7", "@svgr/webpack": "^8.1.0", "@swc/core": "^1.3.35", "@swc/helpers": "^0.4.12", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "ahooks": "^3.7.8", "autoprefixer": "^10.4.16", "file-loader": "^6.2.0", "less-loader": "~11.1.3", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "react-router-dom": "^6.11.1", "rollup": "^4.9.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-node-externals": "^6.1.2", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-ts": "^3.1.1", "storybook": "^7.6.7", "storybook-builder-rspack": "~7.0.0-rc.25", "storybook-react-rspack": "~7.0.0-rc.25", "tailwindcss": "~3.3.3", "vitest": "~3.0.5"}}