/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { computedStyleToNumber } from '../../../src/utils/dom/computed-style-to-number';

describe('computedStyleToNumber', () => {
  it('correctly number', () => {
    const res1 = computedStyleToNumber('123123124px');
    const res2 = computedStyleToNumber('1231.4124123px');

    expect(res1).toBe(123123124);
    expect(res2).toBe(1231.4124123);
  });
});
