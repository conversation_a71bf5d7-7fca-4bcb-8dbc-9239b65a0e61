/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { isFileCountExceedsLimit } from '../../src/utils/is-file-count-exceeds-limit';

describe('is-file-count-exceeds-limit', () => {
  test('expect to be true', () => {
    const res = isFileCountExceedsLimit({
      fileCount: 5,
      fileLimit: 6,
      existingFileCount: 3,
    });
    expect(res).toBeTruthy();
  });
  test('expect to be false', () => {
    const res = isFileCountExceedsLimit({
      fileCount: 5,
      fileLimit: 6,
      existingFileCount: 1,
    });
    expect(res).toBeFalsy();
  });
});
