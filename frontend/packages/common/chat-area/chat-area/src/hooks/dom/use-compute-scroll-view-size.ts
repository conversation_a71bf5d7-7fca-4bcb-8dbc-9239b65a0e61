/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useSize } from 'ahooks';

import { computedStyleToNumber } from '../../utils/dom/computed-style-to-number';
import { usePreference } from '../../context/preference';

export const useComputeScrollViewSize = ({
  scrollViewWrapper,
}: {
  scrollViewWrapper: HTMLDivElement | null | undefined;
}) => {
  const { isOnboardingCentered, enableImageAutoSize } = usePreference();
  const sizeTarget =
    isOnboardingCentered || enableImageAutoSize ? scrollViewWrapper : null;
  const scrollViewSize = useSize(sizeTarget);
  if (!sizeTarget || !scrollViewSize) {
    return;
  }
  const computedStyle = getComputedStyle(sizeTarget);

  return {
    ...scrollViewSize,
    paddingLeft: computedStyleToNumber(
      computedStyle.getPropertyValue('padding-left'),
    ),
    paddingRight: computedStyleToNumber(
      computedStyle.getPropertyValue('padding-right'),
    ),
  };
};
