/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useShallow } from 'zustand/react/shallow';

import { useChatAreaStoreSet } from '../context/use-chat-area-context';

export const useBotInfo = () => {
  const { useSenderInfoStore } = useChatAreaStoreSet();

  return useSenderInfoStore(
    useShallow(state => ({
      getBotInfo: state.getBotInfo,
      updateBotInfo: state.updateBotInfo,
      setSenderInfoBatch: state.setSenderInfoBatch,
    })),
  );
};

export const useBotInfoWithSenderId = (senderId?: string) => {
  const { useSenderInfoStore } = useChatAreaStoreSet();
  const { botInfo } = useSenderInfoStore(
    useShallow(state => ({
      botInfo: senderId ? state.getBotInfo(senderId) : undefined,
    })),
  );

  return botInfo;
};

/**
 * 返回 action，稳定引用
 */
export const useSetBotInfoBatch = () => {
  const { useSenderInfoStore } = useChatAreaStoreSet();

  return useSenderInfoStore(state => state.setSenderInfoBatch);
};
