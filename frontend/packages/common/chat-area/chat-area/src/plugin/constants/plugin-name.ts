/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export const enum PluginName {
  Demo = 'demo',
  MemoryComponents = 'memoryComponents',
  StoreHistoryMessageList = 'storeHistoryMessageList',
  ShareMessageComponents = 'shareMessageComponents',
  KnowledgeComponents = 'knowledgeComponents',
  Resume = 'resume',
  MessageGrab = 'messageGrab',
  ChatBackground = 'ChatBackground',
  Tts = 'Tts',
  DebugCommon = 'DebugCommon',
  ChatPlayground = 'ChatPlayground',
  PremiumPlans = 'PremiumPlans',
  TemplateMessageSelector = 'TemplateMessageSelector',
  WebsdkChatCozeSdkPlugin = 'WebsdkChatCozeSdkPlugin', // WebSdk的走openApi的plugin
  WebsdkDefaultChatPlugin = 'WebsdkDefaultChatPlugin', // WebSdk的默认plugin
  WebsdkChatCommonPlugin = 'WebsdkChatCommonPlugin', // WebSdk的公共plugin
  UIBuilderChatUIPlugin = 'UIBuilderChatUIPlugin', // UIBuilderChatPlugin
  UIBuilderEventcallbackPlugin = 'UIBuilderEventcallbackPlugin', // UIBuilderEventcallbackPlugin
  AgentTemplateSubScene = 'AgentTemplateSubScene',
  Reasoning = 'Reasoning',
  CozeDev = 'CozeDev',
}
