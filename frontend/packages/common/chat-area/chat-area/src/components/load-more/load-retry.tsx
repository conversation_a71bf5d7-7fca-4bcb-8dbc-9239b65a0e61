/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type FC } from 'react';

import { I18n } from '@coze-arch/i18n';
import { IconRefreshOutlinedNormalized } from '@coze-arch/bot-icons';

import styles from './load-retry.module.less';

export const LoadRetry: FC<{ onClick: () => void }> = ({ onClick }) => (
  <div className={styles.retry} onClick={onClick}>
    <IconRefreshOutlinedNormalized className={styles.icon} />
    <span className={styles.text}>{I18n.t('Coze_token_reload')}</span>
  </div>
);

LoadRetry.displayName = 'LoadRetry';
