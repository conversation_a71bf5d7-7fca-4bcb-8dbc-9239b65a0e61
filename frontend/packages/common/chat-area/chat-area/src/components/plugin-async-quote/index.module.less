.quote {
  display: inline-flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;

  max-width: 100%;
  margin-bottom: 8px;
}

.left {
  width: 2px;
  height: 14px;
  background: var(--Light-color-grey---grey-2, #C6C6CD);
  border-radius: 3px;
}

.content {
  overflow: hidden;

  font-size: 12px;
  font-weight: 500;
  font-style: normal;
  line-height: 16px;
  color: var(--Light-color-grey---grey-3, #A7A7B0);
  text-overflow: ellipsis;
  text-wrap: nowrap;
}
