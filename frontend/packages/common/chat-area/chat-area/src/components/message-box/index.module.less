.wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .box {
    padding: 16px 20px;

    font-weight: 500;

    background: white;
    border-radius: 16px;
    box-shadow: 0 32px 50px -12px rgb(10 17 61 / 6%), 0 0 0 1px rgb(10 17 61 / 6%);
  }

  .user {
    font-weight: 700;
    background: linear-gradient(269deg, #A171FF -3.63%, #5D66FF 100.38%);
  }

  .icon-sending {
    margin-left: 10px;
    color: #4D53E8;
  }

  .icon-fail {
    cursor: pointer;
    margin-left: 10px;
    color: #FF441E;

    &>svg {
      width: 16px;
      height: 16px;
    }
  }
}

.footer-slot-style {
  display: flex;
  gap: 12px;
}
