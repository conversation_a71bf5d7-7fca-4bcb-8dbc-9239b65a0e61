/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type WaitingState, WaitingPhase } from '../../store/waiting';

export const getIsGroupChatActive = ({
  waiting,
  sending,
  groupId,
}: Pick<WaitingState, 'waiting' | 'sending'> & { groupId: string }) => {
  const isFormalWaiting =
    waiting?.replyId === groupId && waiting.phase === WaitingPhase.Formal;

  if (!sending) {
    return isFormalWaiting;
  }

  const isSending =
    sending.message_id === groupId ||
    sending?.extra_info.local_message_id === groupId;

  return isFormalWaiting || isSending;
};
