/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import type ChatCore from '@coze-common/chat-core';
import { safeAsyncThrow } from '@coze-common/chat-area-utils';

const fakeChatCoreMark = Symbol('fake-chat-core');

export const getFakeChatCore = () => {
  const fakeCore = {} as unknown as ChatCore;

  return new Proxy(fakeCore, {
    get(_, key) {
      if (key === fakeChatCoreMark) {
        return true;
      }

      const callTip = `This error is caused when calling: ${String(key)}`;
      safeAsyncThrow(
        `!!!chatCore not found, make sure to call chatArea hooks inside chatAreaProvider!!! ${callTip}`,
      );

      // 已经最大化兼容了，我感觉
      return () => Object.create(null);
    },
  });
};

export const getIsFakeChatCore = (core: ChatCore) =>
  (core as unknown as { [fakeChatCoreMark]: boolean })[fakeChatCoreMark];
