{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"rootDir": "./", "outDir": "./dist", "types": ["vitest/globals"], "jsx": "react-jsx", "strictNullChecks": true, "noImplicitAny": true, "noUncheckedIndexedAccess": true}, "include": ["src", "__tests__", "stories", "vitest.config.ts"], "references": [{"path": "../chat-core/tsconfig.json"}]}