{"$schema": "https://json.schemastore.org/tsconfig", "extends": "./tsconfig.json", "compilerOptions": {"rootDir": "./src", "outDir": "./dist", "types": [], "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-http/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../chat-core/tsconfig.build.json"}, {"path": "../chat-uikit-shared/tsconfig.build.json"}, {"path": "../chat-uikit/tsconfig.build.json"}, {"path": "../../../components/bot-icons/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../components/json-viewer/tsconfig.build.json"}, {"path": "../../../components/scroll-view/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../../data/memory/llm-plugins/tsconfig.build.json"}, {"path": "../hooks/tsconfig.build.json"}, {"path": "../../uploader-adapter/tsconfig.build.json"}, {"path": "../utils/tsconfig.build.json"}, {"path": "../../websocket-manager-adapter/tsconfig.build.json"}]}