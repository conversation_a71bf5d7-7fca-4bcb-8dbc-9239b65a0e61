/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export const sleep = (t = 0) => new Promise(resolve => setTimeout(resolve, t));

export class Deferred<T = void> {
  promise: Promise<T>;
  resolve!: (value: T) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- .
  reject!: (reason?: any) => void;
  then: Promise<T>['then'];
  constructor() {
    this.promise = new Promise<T>((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
    this.then = this.promise.then.bind(this.promise);
  }
}
