{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"strictNullChecks": true, "types": [], "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../arch/bot-env/tsconfig.build.json"}, {"path": "../../../arch/bot-md-box-adapter/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}]}