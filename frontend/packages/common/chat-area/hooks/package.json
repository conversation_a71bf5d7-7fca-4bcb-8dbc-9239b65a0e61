{"name": "@coze-common/chat-hooks", "version": "0.0.1", "description": "Simple pure helpful hooks.", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "lint:type": "tsc -p tsconfig.json --noEmit", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "lodash-es": "^4.17.21", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vitest": "~3.0.5"}, "peerDependencies": {"lodash-es": "^4.17.21", "react": ">=18.2.0", "react-dom": ">=18.2.0"}}