# @coze-common/chat-hooks

Simple pure helpful hooks.

## Overview

This package is part of the Coze Studio monorepo and provides utilities functionality. It includes hook.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-common/chat-hooks": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-common/chat-hooks';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook

## API Reference

### Exports

- `useImperativeLayoutEffect`
- `useSearch`
- `useEventCallback`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
