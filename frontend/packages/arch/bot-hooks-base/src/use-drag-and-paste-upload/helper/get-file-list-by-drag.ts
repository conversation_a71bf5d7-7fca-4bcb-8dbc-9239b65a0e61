/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export const getFileListByDragOrPaste = (
  e: HTMLElementEventMap['drop'] | HTMLElementEventMap['paste'],
): File[] => {
  let fileList: FileList | undefined;
  if ('dataTransfer' in e) {
    fileList = e.dataTransfer?.files;
  } else {
    fileList = e.clipboardData?.files;
  }
  if (!fileList) {
    return [];
  }
  return formatTypeFileListToTypeArray(fileList);
};

export const formatTypeFileListToTypeArray = (fileList: FileList) => {
  const fileLength = fileList.length;
  const fileArray: (File | null)[] = [];
  for (let i = 0; i < fileLength; i++) {
    fileArray.push(fileList.item(i));
  }
  return fileArray.filter((file): file is File => Boolean(file));
};
