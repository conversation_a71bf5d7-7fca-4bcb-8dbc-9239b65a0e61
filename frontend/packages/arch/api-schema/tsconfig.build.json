{"extends": "@coze-arch/ts-config/tsconfig.node.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "module": "CommonJS", "target": "ES2020", "moduleResolution": "node", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../bot-http/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../../infra/idl/idl2ts-cli/tsconfig.build.json"}, {"path": "../../../infra/idl/idl2ts-runtime/tsconfig.build.json"}]}