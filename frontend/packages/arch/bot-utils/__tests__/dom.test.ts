/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { openNewWindow } from '../src/dom';

it('openNewWindow', async () => {
  const testUrl = 'test_url';
  const testOrigin = 'test_origin';

  const newWindow = {
    close: vi.fn(),
    location: '',
  };
  vi.stubGlobal('window', {
    open: vi.fn(() => newWindow),
  });
  vi.stubGlobal('location', {
    origin: testOrigin,
  });

  const cb = vi.fn(() => Promise.resolve(testUrl));
  const cbWithError = vi.fn(() => Promise.reject(new Error()));
  await openNewWindow(cb);
  expect(newWindow.close).not.toHaveBeenCalled();
  expect(newWindow.location).equal(testUrl);

  await openNewWindow(cbWithError);
  expect(newWindow.close).toHaveBeenCalled();
  expect(newWindow.location).equal(`${testOrigin}/404`);

  vi.clearAllMocks();
});
