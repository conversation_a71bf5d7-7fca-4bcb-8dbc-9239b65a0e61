# @coze-studio/slardar-adapter

provide report adapter

## Overview

This package is part of the Coze Studio monorepo and provides architecture functionality. It includes plugin.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-studio/slardar-adapter": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-studio/slardar-adapter';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Plugin

## API Reference

### Exports

- `const jsErrorPlugin = () => ();`
- `const customPlugin = () => ();`
- `const createMinimalBrowserClient: () => any = () => slardarInstance;`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
