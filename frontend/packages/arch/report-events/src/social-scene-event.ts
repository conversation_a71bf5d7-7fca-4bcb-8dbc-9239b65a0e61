/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export enum SocialSceneEvents {
  ConfirmSceneModalError = 'confirm_scene_modal',
  DeleteSceneMetaError = 'delete_scene_meta_error',
  DuplicateSceneMetaError = 'duplicate_scene_meta_error',
  SceneMetaListSearchError = 'scene_meta_list_search_error',
  SceneMetaListLoadMoreError = 'scene_meta_list_load_more_error',
  SceneModalCallbackIdEmptyError = 'scene_modal_callback_id_empty_error',
}
