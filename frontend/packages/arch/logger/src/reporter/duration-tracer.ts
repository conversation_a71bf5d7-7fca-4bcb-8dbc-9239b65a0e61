/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export type TracePointName = 'success' | 'fail' | string;

export interface TraceDuration {
  points: TracePointName[];
  interval: {
    [key: TracePointName]: number;
  };
}

export function genDurationTracer() {
  const duration: TraceDuration = {
    points: [],
    interval: {},
  };

  const tracer = (pointName: TracePointName) => {
    if (!pointName) {
      return duration;
    }
    if (duration.points.indexOf(pointName) === -1) {
      duration.points.push(pointName);
    }
    performance.mark(pointName);
    if (duration.points.length > 1) {
      const curIdx = duration.points.length - 1;
      const measure = performance.measure(
        'measure',
        duration.points[curIdx - 1],
        duration.points[curIdx],
      );
      duration.interval[pointName] = measure?.duration ?? 0;
    }

    return duration;
  };

  return {
    tracer,
  };
}
