{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"noEmit": false, "types": ["vitest/globals"], "strictNullChecks": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["./src", "./src/**/*.json"], "references": [{"path": "../bot-env/tsconfig.build.json"}, {"path": "../bot-typings/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../slardar-interface/tsconfig.build.json"}]}