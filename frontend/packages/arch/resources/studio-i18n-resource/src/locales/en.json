{"700012014": "Unusual traffic detected from your network. Coze services are unavailable.", "About_Plugins_tip": "About Plugins", "Actions": "Action", "AddFailedToast": "Failed to add workflow due to plugin request failure.", "AddSuccessToast": "\"{name}\" has been added", "Add_1": "Add", "Add_2": "Add", "Added": "Added", "All": "All", "Cancel": "Cancel", "Complete": "Complete", "Confirm": "Confirm", "Content": "Content", "Copy": "Copy", "Copy_link": "Copy link", "Copy_name": "Copy name", "Coze_token_reload": "Reload", "Coze_token_title": "Coze token", "Create_failed": "Create failed", "Create_newtool_s1_dercribe": "Tool description", "Create_newtool_s1_dercribe_empty": "Please enter the tool description", "Create_newtool_s1_dercribe_error": "Please describe the primary functions and scenarios of the tool to help users/LLMs better understand it. Ensure that the content is clear and complies with platform guidelines.", "Create_newtool_s1_method": "Request method", "Create_newtool_s1_method_delete": "DELETE", "Create_newtool_s1_method_get": "GET", "Create_newtool_s1_method_post": "POST", "Create_newtool_s1_method_put": "PUT", "Create_newtool_s1_name": "Tool name", "Create_newtool_s1_title": "Enter basic information", "Create_newtool_s1_title_empty": "Please enter a clear and compliant name for the tool", "Create_newtool_s1_title_error1": "Only letters, numbers, and underscores are valid", "Create_newtool_s1_url": "Tool path", "Create_newtool_s1_url_empty": "Enter the specific path of the tool, starting with /. Example: \"/search\".", "Create_newtool_s1_url_error1": "Path needs to start with /", "Create_newtool_s1_url_error2": "Please enter the specific path of the tool", "Create_newtool_s2": "Configure input parameters", "Create_newtool_s2_table_des": "Parameter description", "Create_newtool_s2_table_des_tooltip": "Please describe the functions of the parameter to help users/LLMs better understand it.", "Create_newtool_s2_table_method": "Input method", "Create_newtool_s2_table_name_empty": "Please enter a clear and compliant name for the parameter", "Create_newtool_s2_table_name_error1": "Please enter a parameter name", "Create_newtool_s2_table_name_error2": "Only letters, numbers and underscores are valid", "Create_newtool_s2_table_name_tooltip": "The parameter name can only contain letters, digits, or underscores.", "Create_newtool_s2_table_required": "Required?", "Create_newtool_s2_title": "Input parameters", "Create_newtool_s3_Outputparameters": "Configure output parameters", "Create_newtool_s3_button_auto": "Auto parse", "Create_newtool_s3_table_des_empty": "Please enter the parameter description", "Create_newtool_s3_table_des_tooltip": "Please describe the function of parameters to help users / LLM better understanding.", "Create_newtool_s3_table_name": "Parameter name", "Create_newtool_s3_table_name_tooltip": "Output parameter name: only letters, numbers or underscores are valid", "Create_newtool_s3_table_new": "Add parameter", "Create_newtool_s3_table_type": "Parameter type", "Create_newtool_s4_debug": "Debug & Verify", "Create_newtool_s4_done": "Done", "Create_newtool_s4_name": "Parameter name", "Create_newtool_s4_result": "Debugging result", "Create_newtool_s4_run": "Run", "Create_newtool_s4_title": "Input parameter", "Create_newtool_s4_type": "Parameter type", "Create_newtool_s4_value": "Parameter value", "Create_success": "Created successfully", "Create_time": "Creation time", "Create_tool_s1_method_patch_name": "PATCH", "Create_tool_s1_method_patch_tooltip_desp": "Update existing resources but not create new resources. Example:", "Create_tool_s1_method_patch_tooltip_explain": "Updates data about the user with ID 123", "Create_tool_s1_method_patch_tooltip_title": "PATCH", "Create_tool_s1_method_patch_tooltip_url": "PATCH /Users/<USER>", "Creator": "Creator", "Datasets": "Knowledge", "Delete": "Delete", "Delete_failed": "Delete failed", "Delete_success": "Deleted", "Description": "Description", "Display format": "Display format", "Duplicate_success": "<PERSON>pied", "Edit": "Edit", "Edit_success": "Edit successfully", "Edit_time_2": "Edit time", "Imageflow_generate_standard": "Generation quality", "Imageflow_mode_choose": "Please select a mode", "Imageflow_model": "Model", "Imageflow_model_deploy": "Model settings", "Imageflow_negative": "Negative prompt", "Imageflow_negative_placeholder": "Negative prompt for image generation. It describes the content you do not wish to include in your image. You can use {{variable}} to reference variables within the input parameters.", "Imageflow_not_support": "This model does not currently support this image reference mode", "Imageflow_positive": "Positive prompt", "Imageflow_positive_placeholder": "Positive prompt used to generate images. You can use {{variable name}} to reference the variables in the input parameters.", "Imageflow_prompt": "Prompt", "Imageflow_ratio": "<PERSON><PERSON>", "Imageflow_reference_image": "Reference image", "Imageflow_reference_info1": "Model", "Imageflow_reference_info2": "Reference image", "Imageflow_reference_info3": "Reference level", "Imageflow_size_range": "Supported width and height range: [512,1536]", "Input": "Input", "Lark_00001": "Lark", "Lark_00002": "Lark Data", "Loading": "Loading", "Manual_crawling_040": "column name cannot be duplicated", "Me": "Me", "More": "More", "Network_error": "Network error", "Next_1": "Next", "Next_2": "Next", "No_more": "No more", "No_recall_001": "Reply", "No_recall_002": "No replies recalled", "No_recall_003": "<PERSON><PERSON><PERSON>", "No_recall_004": "Customization", "No_recall_005": "Reply script used when no raw data segments from the knowledge are recalled", "No_recall_006": "Your question is not within the scope of knowledge and it cannot be answered at this stage", "No_recall_007": "After enabling the custom prompt, refer to the following example in your reply if no raw data segments are hit", "Output": "Output", "Plugin_button_code_tooltip": "Edit tool with code", "Plugin_button_publish_tooltip": "It can be published after all tools pass trial runs.", "Plugin_delisted": "The plugin has been removed", "Plugin_list_table_owner": "Owner", "Plugin_new_toast_success": "Plugin created successfully", "Plugin_publish_update_toast_success": "New version published successfully", "Plugin_update_info_text": "You have modified {number} tools, {array}. Once this new version is published, the related Agents will update the referenced tools simultaneously.", "Plugin_update_info_title": "Publish New Version", "Plugin_update_success": "Updated successfully", "Plugin_update_toast_success": "Update completed", "Plugins": "Plugins", "Popular": "Popular", "Previous_1": "Previous", "Previous_2": "Previous", "Publish": "Publish", "PublishSuccessConfirm": "Workflow published! Add it to the current agent?", "Published_1": "Published", "Remove": "Remove", "Reset": "Reset", "Responding": "Responding", "Retry": "Retry", "Save": "Save", "Save_success": "Saved", "Search": "Search", "Searched": "Searched", "Searching": "Searching", "Sort": "Sort", "Starling_filebox_api_list": "List", "Starling_filebox_name": "Filebox", "Success": "Success", "Tools": "Tools", "Type": "Type", "Unpublished_1": "Unpublished", "Update": "Update", "Update_failed": "Update failed", "Update_success": "Update successfully", "Update_time": "Edit time", "Upload_failed": "Upload failed", "Use_template": "Use template", "Used": "Used", "Using": "Using", "View": "View", "Visited": "Visited", "Visiting": "Visiting", "Workflow": "Workflow", "Workflows": "Workflows", "about_privacy_policy": "Privacy Policy", "account_update_hint": "Account status change detected, please refresh the page.", "actions": "Actions", "add": "Add", "add_api_token_1": "Click to add a personal access token", "add_image": "Insert image", "add_link": "Insert link", "add_mock_data": "Add mock data", "add_new_pat_1": "Add personal access token", "add_new_token_button_1": "Add token", "add_nickname": "Insert user nickname", "add_resource_modal_copy_to_project": "Copy to App", "add_resource_modal_sidebar_library_tools": "Library tools", "add_resource_modal_sidebar_project_tools": "App tools", "agent_creat_tips": "Prompt & model comparison debugging available.", "agent_ide_default_input_option": "Default input", "agent_ide_default_input_option_text": "Type", "agent_ide_default_input_option_voice": "Speak", "agent_prompt_editor_insert_placeholder": " or enter {keymap} to insert configured skills", "agentflow_addbot_select_empty_no_bot": "Agent not found", "agentflow_jump_running_process_backtrack": "Back track to", "agentflow_jump_running_process_jump": "Jumped to", "agentflow_jump_running_process_jump_time": "Switch node", "agentflow_jump_running_process_trigger_condition": "Trigger condition:", "agentflow_transfer_ conversation_settings_backtrack_previous": "Go back to the previous conversing node to try to solve user's problem", "agentflow_transfer_ conversation_settings_backtrack_start": "Go back to the start node to try to solve user's problem", "agentflow_transfer_ conversation_settings_mode_node_title": "Recognized during the operation of the current node", "agentflow_transfer_ conversation_settings_title": "Node switching settings", "ai_plugin_(fill_in_json)_*": "ai_plugin (input json)*", "analytic_query_agenttype": "Agent Type", "analytic_query_blank_context": "Data is empty", "analytic_query_calculating": "Calculating", "analytic_query_calltype": "Call Type", "analytic_query_channel": "Channel", "analytic_query_clear": "Clear", "analytic_query_clear_tips": "Clear All Filiters\n", "analytic_query_detail_copy_failed": "copy failed", "analytic_query_detail_copy_success": "copy success", "analytic_query_detail_key_latency": "Latency", "analytic_query_detail_left_panel_flamethread": "Flamethread", "analytic_query_detail_left_panel_runtree": "Run Tree", "analytic_query_detail_returnerror": "There are some issues with <PERSON><PERSON>. Please refresh the page and try again.", "analytic_query_detail_right_panel_title_nodedetail": "Node Details", "analytic_query_detail_subtype_value_userinput": "UserInput", "analytic_query_detail_tips_for_outputmode": "Return variable, generated by {outputMode}", "analytic_query_detail_title_channel": "Channel", "analytic_query_detail_title_endtime": "End Time", "analytic_query_detail_title_success": "Success", "analytic_query_detail_topology": "Topology", "analytic_query_detail_topology_tooltip": "{errorCount} errors / {callCount} calls", "analytic_query_diagloground": "Dialog Round", "analytic_query_endtime": "End Time", "analytic_query_env": "Env", "analytic_query_env_value_botmakerdebug": "Agent Maker Debug", "analytic_query_env_value_realuser": "Real User", "analytic_query_export": "Export to Excel", "analytic_query_export_content": "The maximum number of items that can be exported at one time is {maxExportCount}, currently {selectedExportCount} items are selected. Are you sure you want to export the first {maxExportCount} entries if you continue?", "analytic_query_export_title": "Exceeds the maximum number that can be exported at one time", "analytic_query_filters_key_botversion": "Agent <PERSON>.", "analytic_query_filters_key_input": "Input", "analytic_query_filters_key_output": "Output", "analytic_query_filters_key_status": "Status", "analytic_query_firstrestime": "First Response Time", "analytic_query_gpt": "Model", "analytic_query_hook_resp_code": "Hook Resp Code", "analytic_query_input": "Input", "analytic_query_inputtokens": "InputTokens", "analytic_query_inputtype": "Input Type", "analytic_query_latency": "Latency", "analytic_query_latencyfirst": "LatencyFirs<PERSON>", "analytic_query_logid": "<PERSON><PERSON><PERSON>", "analytic_query_model": "Model", "analytic_query_name": "Name", "analytic_query_network_tips": "Network timeout, please ", "analytic_query_network_tips_retry": "try again.", "analytic_query_noright_tips": "Sorry, you do not have permission to view this data at this time. Please contact the space owner to become a collaborator before you can view it.", "analytic_query_os": "OS", "analytic_query_output": "Output", "analytic_query_outputtokens": "OutputTokens", "analytic_query_resmaxlen": "Res<PERSON>.", "analytic_query_security_verify_check": "Verify", "analytic_query_security_verify_context": "Our system has detected unusually rapid access to user data. In consideration of the protection of user privacy, please proceed with security verification. Review the {agreementName} for further details.", "analytic_query_security_verify_context_agreementname": "Coze User Privacy Agreement", "analytic_query_security_verify_select": "I am not a robot", "analytic_query_security_verify_title": "Security Verification", "analytic_query_starttime": "StartTime", "analytic_query_status": "Status", "analytic_query_status_broken": "Broken", "analytic_query_status_error": "Error", "analytic_query_status_success": "Success", "analytic_query_status_unknown": "Unknown", "analytic_query_subtype": "SubType", "analytic_query_subtype_value_card": "Card", "analytic_query_subtype_value_code": "Code", "analytic_query_subtype_value_codebatch": "CodeBatch", "analytic_query_subtype_value_condition": "Condition", "analytic_query_subtype_value_invokeagent": "InvokeAgent", "analytic_query_subtype_value_knowledge": "Knowledge", "analytic_query_subtype_value_llmbatchcall": "LLMBatchCall", "analytic_query_subtype_value_llmcall": "LLMCall", "analytic_query_subtype_value_opendialog": "OpenDialog", "analytic_query_subtype_value_plugintool": "PluginTool", "analytic_query_subtype_value_plugintoolbatch": "PluginToolBatch", "analytic_query_subtype_value_restartagent": "RestartAgent", "analytic_query_subtype_value_scheduledtasks": "ScheduledTasks", "analytic_query_subtype_value_switchagent": "SwitchAgent", "analytic_query_subtype_value_thirdparty": "ThirdParty", "analytic_query_subtype_value_unknown": "Unknown", "analytic_query_subtype_value_userinput": "UserInput", "analytic_query_subtype_value_workflow": "Workflow", "analytic_query_subtype_value_workflow_message": "Message", "analytic_query_subtype_value_workflowend": "WorkflowEnd", "analytic_query_subtype_value_workflowstart": "WorkflowStart", "analytic_query_summary_errorrate": "Error Rate", "analytic_query_summary_queriescount": "Queries Count", "analytic_query_summary_title": "Summary", "analytic_query_table_title_channel": "Channel", "analytic_query_table_title_starttime": "Start Time", "analytic_query_table_title_tokens": "Tokens", "analytic_query_temperature": "Temperature", "analytic_query_token_0_tips": "Processing data. Refresh the page when reports are complete", "analytic_query_tokens": "Tokens", "analytic_query_type": "Type", "analytic_query_type_cid": "Message ID", "analytic_query_type_input_number_tips": "Please fill in the correct value", "analytic_query_type_input_tips": "Supports multiple inputs, enter to confirm", "analytic_query_type_sid": "Conversation ID", "analytic_query_type_uid": "User ID", "analytic_query_type_value_agent": "Agent", "analytic_query_type_value_card": "Card", "analytic_query_type_value_code": "Code", "analytic_query_type_value_condition": "Condition", "analytic_query_type_value_knowledge": "Knowledge", "analytic_query_type_value_llmcall": "Call LLM", "analytic_query_type_value_message": "Message", "analytic_query_type_value_plugin": "Plugin", "analytic_query_type_value_start": "Start", "analytic_query_type_value_unknown": "Unknown", "analytic_query_type_value_workflow": "Workflow list", "analytic_query_type_value_workflowend": "Workflow ends", "analytic_query_type_value_workflowstart": "Workflow starts", "analytic_query_workflowvers": "Workflow Version", "analytic_streaming_output_status_close": "Close", "analytic_streaming_output_status_open": "Open", "analytics_page_title": "Analysis", "analytics_query_aigc_detail": "Detail", "analytics_query_aigc_errorpanel_context": "Load failed", "analytics_query_aigc_errorpanel_ok": "OK", "analytics_query_aigc_infopanel_cancel": "Cancel", "analytics_query_aigc_infopanel_context": "This type does not support viewing details, you can download and view it.", "analytics_query_aigc_infopanel_download": "Download", "analytics_query_aigc_infopanel_title": "Unable to view", "analytics_query_aigc_inforpanel_title_file": "Unable to view File", "analytics_query_invoke": "Invoke {name}", "analytics_query_type_variable": "Variable", "analytics_timepicker_customize": "Customize", "analytics_timepicker_last_1_day": "Last 1 day", "analytics_timepicker_last_days": "Last {count} days", "api": "API", "api_analytics_null": "No Data", "api_analytics_refresh": "Refresh", "api_permissionkey_notification_content": "Permission keys have been upgraded! If you wish to call Conversation, Message, and File APIs, please review and select the corresponding permissions before confirming and saving. For more details, please visit the API Docs.", "api_permissionkey_notification_title": "Permission keys upgraded!", "api_sdk_published": "API (Charged based on {coze_token} usage)", "api_status_1": "Status", "api_status_active_1": "Active", "api_status_expired_1": "Expired", "api_status_permanent_1": "Permanent", "api_token_reminder_1": "Each account is granted 100 free API call credits. Once the free quota is used up, you can purchase tokens on the Coze Token page.", "app_ide_publish_modal_publish_button": "Publish", "app_ide_publish_modal_publish_management": "Publish management", "app_ide_publish_modal_recent_publication": "Recently published", "app_ide_viewing_archive": "Viewing archive", "app_publish_connector_mcp": "MCP Services", "app_publish_connector_space_mcp_config_dialog_cancel": "Cancel", "app_publish_connector_space_mcp_config_dialog_choose_wf": "Choose workflows", "app_publish_connector_space_mcp_config_dialog_confirm": "Confirm", "app_publish_connector_space_mcp_config_dialog_desc": "Publish one or more workflows to your CozeSpace Extensions.", "app_publish_connector_space_mcp_config_dialog_desc2": "Certain types of workflows are not supported", "app_publish_connector_space_mcp_config_dialog_filter_all": "All", "app_publish_connector_space_mcp_config_dialog_hover_wf_constraints": "Unsupported workflow types\n1. Used local plugin nodes\n2. The type is chatflow\n3. Used conversation management nodes\n4. Used question nodes\n\nIn addition, if output nodes are used, the intermediate outputs will be ignored", "app_publish_connector_space_mcp_config_dialog_no_results_found": "No results found", "app_publish_connector_space_mcp_config_dialog_search_placeholder": "Search workflows", "app_publish_connector_space_mcp_config_dialog_title": "Extension setup", "app_publish_sdk_confirm": "Confirm", "app_publish_sdk_step_1": "Authorize access tokens to your users by adding your OAuth token into auth key:\n{doc_link}", "app_publish_sdk_step_1_doc": "OAuth Authentication overview", "app_publish_sdk_step_2": "Copy this code and paste it into the\n<body> tag on your website", "app_publish_sdk_step_3": "Refresh your website, your agent\nshould appear in the bottom corner\nof a page.", "app_publish_sdk_title": "Web SDK Installation", "ask_quote": "Quote", "asyn_task_reply_need": "Reply content is required", "asyn_task_reply_toolong": "Reply content is too long", "asyn_task_setting_desc": "When a task enters asynchronous operation, it returns a reply by default. You can continue the conversation while the task runs in the background. You will be notified once completed.", "asyn_task_setting_response_content": "You can set the message reply here. It will be automatically sent when the task is running. For example: \"The task is in progress. I will report the results to you as soon as it's completed. Is there anything else I can assist you with?\"", "asyn_task_setting_response_title": "Reply content", "asyn_task_setting_title": "Asynchronous operation", "atasets_createpdf_over250": "PDF page count exceeds 500 pages", "attach_user_data_to_create_a_new_user_": "Enter user data to create a user.", "audit_unsuccess_general_type": "The content does not match {link}, please modify it and try again.", "audit_unsuccess_general_type_url": "\"Button Platform Release Specifications\"", "auth_tab_auth": "Authorization", "auth_tab_pat": "Personal Access Tokens", "auto_generate": "Auto generate", "back": "Back", "background_confirm": "Confirm", "basic_log_out": "Log out", "basic_setting": "Settings", "bgi_adjust_tooltip_content": "Drag to adjust the position or scroll to zoom", "bgi_adjust_tooltip_title": "Adjust Background Image", "bgi_already_set": "Background image already set", "bgi_desc": "Add a background image for your agent in the Coze Agent Store for a more immersive chat experience", "bgi_remove_popup_content": "Once removed, you must republish the agent for the changes to take effect.", "bgi_remove_popup_title": "Remove the background image?", "bgi_reupload": "Re-upload background image", "bgi_title": "Background image", "bgi_upload_image_format_requirement": "Please upload a background image with a minimum height of 640px.", "bgi_upload_image_format_requirement_title": "Background Image Size", "binding_add_card": "Create", "binding_card_list": "Choose Card Style", "binding_card_preview": "Preview", "binding_card_update": "Update", "binding_duplicate_card": "Copy", "binding_edit_card": "Edit", "binding_view_card": "View", "bmv_enter_version_description": "Enter version description here", "bmv_load_to_draft": "Load to draft", "bmv_official_version": "Official version", "bmv_offline_time": "Offline time", "bmv_please_release_the_official_version_before_releasing_it_": "Please release the official version before releasing it ", "bmv_ppe_lane": "PPE lane", "bmv_pre_release_to_lane": "Pre-release to lane", "bmv_submit_id": "Submit ID", "bmv_view_version": "View Version", "bot_autosave_saving": "Saving…", "bot_build_title": "Arrangement", "bot_copy_id_error": "Missing bot_id", "bot_copy_info_error": "Missing agent information", "bot_copy_success": "The agent is duplicated", "bot_create_desciption": "Agent function description", "bot_create_description_placeholder": "It introduces the agent functions and is displayed to the agent users", "bot_create_name": "Agent name", "bot_create_name_placeholder": "Give the agent a unique name", "bot_created_toast": "The agent is created", "bot_database": "Database", "bot_database_add_field": "Add up to {number} fields at most", "bot_database_ai_create": "Generate with AI", "bot_database_ai_create_tip": "Describe the purpose of the table (e.g., record reading notes)", "bot_database_ai_generate": "Generate", "bot_database_ai_replace": "Replace current table", "bot_database_ai_replace_detailed": "The regenerated content replaces the current table.", "bot_database_ai_waiting": "AI is working on it...", "bot_datamemory_remove_field": "Delete variable", "bot_debug_question_wait": "Waiting for a reply", "bot_delete_confirm_title": "Delete this agent", "bot_deleted_toast": "The agent is deleted", "bot_dev_privacy_setting_channel": "Only avaliable for the following channels:", "bot_dev_privacy_setting_conversation": "Conversation", "bot_dev_privacy_setting_desc": "Once enabled, you can view the conversation history between the user and the Agent. The user will also see the following message at the beginning of the conversation: The developer of this Agent has enabled chat history access to iteratively improve the Agent's conversational abilities. Please do not share sensitive information in the conversation.", "bot_dev_privacy_setting_developer_collect1": "Content you collect", "bot_dev_privacy_setting_developer_collect2": "Contact info", "bot_dev_privacy_setting_developer_collect3": "input developer's name", "bot_dev_privacy_setting_developer_collect4": "Content you collect", "bot_dev_privacy_setting_developer_collect5": "Your contact info", "bot_dev_privacy_setting_developer_collect7": "Only conversation is avaliable now", "bot_dev_privacy_setting_developer_name": "Developer name", "bot_dev_privacy_setting_generate_link1": "Generate", "bot_dev_privacy_setting_generate_link2": "Generating", "bot_dev_privacy_setting_invalid_link": "Please input a valid url", "bot_dev_privacy_setting_link1": "Privacy URL", "bot_dev_privacy_setting_link2": "Your Agent's privacy link will be sent to the end user in the conversation.", "bot_dev_privacy_setting_privacy_template": "Use template", "bot_dev_privacy_setting_privacy_template_1": "Privacy template", "bot_dev_privacy_setting_privacy_template_2": "Please enter the following information, and a link will be generated for you based on the {privacy_template}.", "bot_dev_privacy_setting_privacy_template_3": "Privacy Policy Template", "bot_dev_privacy_setting_title": "View conversation", "bot_dev_privacy_title": "Privacy settings", "bot_duplicateded_toast": "The agent is duplicated", "bot_edit_auto_suggestion_customize_description": "After agent response, provide at most three suggested questions based on the context and prompt.", "bot_edit_auto_suggestion_customize_failed_to_generate": "Failed to generate suggestions", "bot_edit_auto_suggestion_customize_modal_prompt_placeholder": "Enter prompt here to tell the bot how to generate suggestion questions", "bot_edit_auto_suggestion_customize_user_checkbox": "Custom prompt", "bot_edit_auto_suggestion_default_description": "After the agent responds, it automatically provides 3 suggestions for your question based on the chat", "bot_edit_auto_suggestion_off_description": "No user question suggestions will be provided after each agent response.", "bot_edit_auto_suggestion_status_off": "Off", "bot_edit_database_add_tooltip": "Add table", "bot_edit_dataset_add_tooltip": "Add knowledge", "bot_edit_dataset_added_toast": "Dataset {dataset_name} added", "bot_edit_dataset_explain": "After uploading a file or website URL as a knowledge, the agent can reference the content of the knowledge to answer user questions.", "bot_edit_dataset_on_demand_prompt1": "Please prompt the agent in the \"Personas & Prompt\" area to call the", "bot_edit_dataset_on_demand_prompt2": "method. It allows the agent to reply to you based on the content in specific or all knowledge.", "bot_edit_dataset_removed_toast": "Knowledge {dataset_name} is removed", "bot_edit_datasetsSettings_MaxTip": "The maximum number of paragraphs returned to the LLM from the knowledge. A larger number indicates more content being returned.", "bot_edit_datasetsSettings_MinTip": "Selects paragraphs based on the set matching degree and returns them to the LLM. Content with a matching degree lower than the set threshold is not recalled.", "bot_edit_datasets_copyName": "Copy", "bot_edit_memory_title_action": "Actions", "bot_edit_memory_title_default": "Default value", "bot_edit_memory_title_description": "Description", "bot_edit_memory_title_filed": "Name", "bot_edit_opening_question_title": "Preset opening questions", "bot_edit_opening_questions_tooltip": "Three opening questions will be randomly displayed to user if you provide more than three questions.", "bot_edit_opening_text_title": "Opening text", "bot_edit_opening_tooltip": "Auto-generate", "bot_edit_page_plugin_copy_tool_name_tip": "Copy", "bot_edit_page_plugin_list_plugin_has_n_tools": "Tools: {n}", "bot_edit_page_plugin_list_plugin_n_bots_using": "Used by: {n} agents", "bot_edit_page_plugin_tool_param_not_required": "optional", "bot_edit_page_plugin_tool_param_required": "Required", "bot_edit_plugin_add_tooltip": "Add plugin", "bot_edit_plugin_delete_tooltip": "Delete question", "bot_edit_plugin_explain": "Plugins allow the agent to call external APIs that enable it to search for information, browse web pages, generate images, and more, expanding the agent's capabilities and applications.", "bot_edit_plugin_select_title": "Add plugin", "bot_edit_profile_pircture": "Icon", "bot_edit_profile_pircture_autogen_quota_tooltip": "You can generate a maximum of 25 avatars per day. Please try again tomorrow.", "bot_edit_profile_pircture_autogen_tooltip": "Enter the name and description, and use DALL·E-3 to generate a picture", "bot_edit_profile_pircture_autogen_tooltip_cn": "Enter the name and description, and use AI to generate a picture.", "bot_edit_remove_workflow": "Remove workflow", "bot_edit_store": "Store", "bot_edit_suggestion": "Auto-suggestion", "bot_edit_title": "Edit Agent", "bot_edit_tool_added_toast": "API {api_name} added", "bot_edit_tool_added_toast_error": "Failed to add API {api_name}", "bot_edit_tool_removed_toast": "API {api_name} is removed", "bot_edit_type_character": "Roles", "bot_edit_type_dialog": "Chat experience", "bot_edit_type_knowledge": "Knowledge", "bot_edit_type_memory": "Memory", "bot_edit_type_skills": "Skills", "bot_edit_variable_add_tooltip": "Add variable", "bot_edit_variable_add_tooltip_edit": "Edit variable", "bot_edit_variable_default_value_placeholder": "Default value", "bot_edit_variable_description_placeholder": "Field description", "bot_edit_variable_field_occupied_error": "This field is already in use", "bot_edit_variable_field_required_error": "Field name is required", "bot_edit_voices_modal_add_language": "Languages & Voices", "bot_edit_voices_modal_description": "You need to select voices for different languages. If no language and voice are specified, {platform} applies the default voice.", "bot_edit_voices_modal_language": "Language", "bot_edit_voices_modal_title": "Select voice", "bot_edit_voices_title": "Voices", "bot_edit_workflow_add_tooltip": "Add workflow", "bot_edit_workflow_explain": "Workflow supports the combination of plugins, LLMs, code blocks, and other features through a visual interface, enabling the arrangement of complex and stable business processes, such as travel planning and report analysis.", "bot_element_unset": "Unset", "bot_execute_by_autosave": "The data is being saved. Please try again later", "bot_has_changes_tip": "Unpublished changes", "bot_ide_browser_not_support_geolocation_toast": "Geolocation is not supported by this browser", "bot_ide_geolocation_not_usable_toast": "Geolocation is not usable", "bot_ide_geolocation_request_timeout_toast": "Timeout when requesting user's geolocation", "bot_ide_geolocation_request_unknown_error_toast": "Unknown error when requesting user's geolocation", "bot_ide_knowledge_confirm_cancel": "Do not add", "bot_ide_knowledge_confirm_content": "Is it added to the Agent?", "bot_ide_knowledge_confirm_ok": "Add", "bot_ide_knowledge_confirm_title": "prompt", "bot_ide_knowledge_table_desc": "Table supports matching appropriate rows according to a certain column of the table. It also supports querying and calculating the database based on natural language.", "bot_ide_knowledge_text_desc": "After documents, URLs, and third-party data sources are uploaded into text knowledge, the agent can reference its content to answer your questions.", "bot_ide_plugin_setting_modal_default_value_select_mode_input": "Input", "bot_ide_plugin_setting_modal_default_value_select_mode_input_placeholder": "Input value", "bot_ide_plugin_setting_modal_default_value_select_mode_reference": "Reference", "bot_ide_plugin_setting_modal_default_value_select_mode_reference_placeholder": "Select value", "bot_ide_plugin_setting_modal_mockset_tab": "<PERSON><PERSON><PERSON>", "bot_ide_plugin_setting_modal_parameter_tab": "Parameters", "bot_ide_shortcut": "Shortcuts", "bot_ide_shortcut_add_button": "Add shortcut", "bot_ide_shortcut_intro": "Shortcuts are buttons above the chat input box. Once configured, you can quickly initiate a preset chat.", "bot_ide_shortcut_item_edit": "Edit", "bot_ide_shortcut_item_trash": "Trash", "bot_ide_shortcut_max_limit": "Max 10 shortcuts", "bot_ide_shortcut_removal_confirm": "Remove this shortcut?", "bot_ide_user_declines_geolocation_auth_toast": "The user refused to authenticate access for geolocation. Please manually turn it on in the browser's setting\n", "bot_list_create": "Create agent", "bot_list_delete_bot": "This action will only delete the agent in <PERSON>ze, and won't affect agents in Cici, Discord and other platforms if published. You can delete them in corresponding platforms.", "bot_list_mine": "Created by me", "bot_list_open_button": "Chat now", "bot_list_rank_tag_edited": "Edited", "bot_list_rank_tag_published": "Published", "bot_list_team": "All", "bot_opening_remarks_replace_cancel_button": "Cancel", "bot_opening_remarks_replace_confirm_button": "Confirm", "bot_persona_and_prompt": "Persona & Prompt", "bot_preview_attach_0319": "File format: image, PDF, DOCX, excel, CSV, or audio. Please {placeholder} to process the file.", "bot_preview_attach_select": "select tools", "bot_preview_debug_title": "Preview & Debug", "bot_preview_file_cancel": "Cancel", "bot_preview_file_copyURL": "Copy URL", "bot_preview_file_retry": "Retry", "bot_preview_hide_running_process": "Hide running process", "bot_preview_opening_remarks": "Opening questions", "bot_preview_run_completed": "Run completed", "bot_preview_run_terminated": "Run terminated", "bot_preview_searched_dataset": "Knowledge searched", "bot_preview_task": "Triggers", "bot_prompt_bracket_error": "{{Double curly brackets}} are preserved symbols. Please try removing them or using other symbols.", "bot_publish_ republish_btn": "Republish", "bot_publish_action_configure": "Configure", "bot_publish_bind_error": "The {key_name} has been bound to  {bot_name}, please unbind it first.", "bot_publish_button": "Publish", "bot_publish_changelog": "Changelog", "bot_publish_columns_action_authorize": "Authorize", "bot_publish_columns_action_revoke_authorize": "Revoke authorization", "bot_publish_columns_platform": "Publishing platform", "bot_publish_columns_result": "Publish results", "bot_publish_columns_status_authorized": "Authorized", "bot_publish_columns_status_configured": "Configured", "bot_publish_columns_status_disconnected": "Disconnected", "bot_publish_columns_status_failed": "Failed", "bot_publish_columns_status_in_review": "Under review", "bot_publish_columns_status_not_configured": "Not configured", "bot_publish_columns_status_offline": "Offline", "bot_publish_columns_status_unauthorized": "Unauthorized", "bot_publish_disable_check_tip": "Please complete authorization or configuration first", "bot_publish_disconnect": "Disconnect {platform}", "bot_publish_disconnect_desc": "After unbinding, the online {platform} robot will not be able to receive messages from this Agent.", "bot_publish_disconnect_success": "Disconnected!", "bot_publish_disconnect_title": "Are you sure to unbind the {platform} Agent?", "bot_publish_field_placeholder": "Please enter {field}", "bot_publish_in_review_disable_check_tip": "The publishing channel is reviewing permissions. Please try again later.", "bot_publish_in_review_notice": "Your bot is under review by the publishing platform. Hang tight!", "bot_publish_offline_notice_no_certain_time": "There's no user interaction with your {platform} agent for a rather long period, so the agent got offline. Republish your agent to {platform} to restart.", "bot_publish_result_copy_bot_link": "Copy agent link", "bot_publish_select_desc_compliance_new": "By publishing your agent on the following platforms, you fully understand and agree to abide by {publish_terms_title} (including, but not limited to, any privacy policy, community guidelines, data processing agreement, etc.).", "bot_publish_select_title": "Publish to", "bot_publish_success_back": "Finish", "bot_publish_token_expired_notice": "Failed to verify {platform} credentials and the agent got offline. Please configure the platform and republish the agent.", "bot_pulish_offline_modal_title1": "{platform} Offline", "bot_pulish_offline_modal_title2": "Agent Offline in {platform_number} Platform(s)", "bot_share_more_platforms": "More", "bot_share_not_supported_opening": "If the agent is published on other platforms, please go to the corresponding platform to use and share it.", "bot_status_published": "Published", "bot_suggestion_customize_default_gpt": "Considering the AI's character settings, the user's previous chat history with the AI assistant, think about the user's scenario, intention, background in their last inquiry, and generate the questions that the user is most likely to ask the AI assistant (you) next.\n1. Do not generate questions that the user may already know the answer, or unrelated to the current topics.\n2. Always generate very brief and clear questions (less than 15 words) that the user may ask the AI assistant (you), NOT questions that the AI assistant (you) asks the user.\n3. DO NOT generate the same or similar questions.\n\nAdditional requirements:\n1. Generate 3 questions each time.\n2. If the latest user's question involves creative tasks (like coming up with a title), give at least one of the questions that directly asks about enhancing the creativity or attractiveness of the AI assistant's previous answer.\n3. If the AI assistant did not or refused to answer the user's question, generate suggestions based on what the assistant can answer to guide the topic in a more productive direction, unrelated to the current topic.\n4. Ensure the questions are different from the chat history.", "bot_suggestion_customize_default_seed": "- Questions should be closely related to your last response and can provoke further discussion.\n- Do not repeat questions or content that have already been asked or answered above.\n- Each sentence should contain only one question, but it can also be an instruction instead of a question.\n- Suggest questions that you are capable of answering.", "bot_suggestion_switch_on_title": "On", "bot_task": "Scheduled Tasks", "bot_task_preset_day_of_month": "{day, plural, =1{#st} =2{#nd} =3{#rd} other{#th}}", "bot_task_preset_day_of_week": "{day, plural, =0{Sunday} =1{Monday} =2{Tuesday} =3{Wednesday} =4{Thursday} =5{Friday} =6{Saturday} other{Monday}}", "bot_task_preset_everyday_task": "Everyday at {time}", "bot_task_preset_everyweek_task": "At {time} every {day}", "bot_task_preset_interval_task": "At {time} every {day} days", "bot_task_preset_monthly_task": "At {time} on the {day, plural, =1{#st} =2{#nd} =3{#rd} other{#th}} of every month", "bot_task_preset_triggered_everyday": "Triggerd everyday", "bot_task_preset_triggered_everyweek": "Triggerd everyweek", "bot_task_preset_triggered_interval": "Triggerd interval", "bot_task_preset_triggered_monthly": "Triggerd monthly", "bot_userProfile_add": "Add", "browser_upgrade": "The browser version you are using is outdated. Please upgrade to the latest version of Chrome, Safari, Edge, or Firefox", "browser_upgrade_button": "Click to upgrade", "builder_canvas_tools_pc": "Pc", "builder_canvas_tools_phone": "Phone", "builder_publish_changelog_label": "Changelog", "builder_publish_changelog_placeholder": "Please enter the changelog", "builder_publish_version_label": "Version", "bwc_no_version_record": "No version record", "bwc_version_description_exceeds_word_limit": "Version description exceeds word limit, please revise and submit", "bwc_view_multiple_environments": "View multiple environments", "bz_coop_upgrade_for_more": "You can currently add up to {max_coop} people, upgrade to unlock more slots", "bz_reache_max": "Up to {max_cnt} collaboration {type_name} can be enabled simultaneously. Upgrade to unlock more", "bz_upgrade_button": "Upgrade", "bz_upgrade_detail": "Upgrade to unlock more", "cancel": "Cancel", "cancel_template": "Cancel", "cannot_enable_mock_set_due_empty_return": "The current plugin tool has not configured return values or is not yet released, so the mock set cannot be enabled", "cannot_enable_mock_set_due_to_no_configured_return_value": "The current plugin has no configured return value or the return value type does not meet the requirements, unable to enable the mock set", "card_builder_api_http_delete_error": "Save failed", "card_builder_api_http_params_columns_type": "Parameter type", "card_builder_builder_publish_changelog_label": "Changelog", "card_builder_builtinLogic_confirm_message": "Confirm details", "card_builder_check_title": "Check list", "card_builder_dataEditor_get_errormsg_please_enter": "Please enter", "card_builder_hover_align_horizontal": "Center horizontally", "card_builder_hover_align_left": "<PERSON><PERSON> left", "card_builder_hover_align_right": "Align right", "card_builder_image": "Image", "card_builder_move_to_bottom": "Move to bottom", "card_builder_move_to_top": "Move to top", "card_builder_redoUndo_redo": "Redo", "card_builder_redoUndo_undo": "Undo", "card_builder_releaseBtn_releaseApp_copyTip": "Copy successfully", "card_builder_releaseBtn_release_btn": "Publish", "card_builder_userVar_list_search_empty": "No corresponding variable found", "card_builder_varpanel_var_empty": "No variables yet", "card_not_support_display_content": "Please check the response result is correct", "card_not_support_display_title": "Not supported for display here", "chat-area-knowledge-crawl-data-source": "Manual Data", "chat-area-knowledge-custom-data-source": "Custom Data", "chat-area-knowledge-feishu-data-source": "<PERSON><PERSON><PERSON>", "chat-area-knowledge-google-data-source": "Google Data", "chat-area-knowledge-local-data-source": "Local Data", "chat-area-knowledge-notion-data-source": "Notion Data", "chat-area-knowledge-online-data-source": "Online Data", "chat_GenAI_tips": "The content is generated by AI and is used for reference only, as it may be untrue and inaccurate.", "chat_geolocation_auth_allow_tip": "You allowed {plugin}'s request for your geolocation", "chat_geolocation_auth_decline_tip": "You rejected {plugin}'s request for your geolocation", "chat_geolocation_auth_request_message": "{plugin_name} needs to access your current location information to provide services", "chat_geolocation_auth_request_message_allow_button": "Allow once", "chat_geolocation_auth_request_message_decline_button": "Decline", "chat_input_hover_tip_keyboard_input_button": "Keyboard input", "chat_input_hover_tip_voice_input_button": "Voice input", "chat_setting_user_input_default_mode": "Default input mode", "chat_tooltips_resend": "Resend", "chat_voice_input_tip_speaking_cancel_and_send": "Swipe up to cancel speaking, and release to send.", "chat_voice_input_tip_speaking_cancel_and_send_when_hold_down_space": "Swipe up to cancel speaking, and release to send.", "chat_voice_input_tip_speaking_record_and_send_after_x_seconds": "The recording will automatically end and be sent after <PERSON>.", "chat_voice_input_toast_no_content_recognized": "No content recognized\n", "chatflow_agent_menu_rename": "<PERSON><PERSON>", "chatflow_agent_skill_name": "Skill", "chatflow_develop_tooltip_hide": "<PERSON><PERSON> Develop", "chatflow_develop_tooltip_show": "Show Develop", "chatflow_error_create_failed": "Failed to create agent", "chatflow_error_delete_failed": "Failed to delete agent", "chatflow_error_miss_start": "Missing the start node", "chatflow_error_miss_start_agent": "The start node must be connected to an agent", "chatflow_preview_tooltip_hide": "Hide Preview and Debug", "chatflow_preview_tooltip_show": "Show Preview and Debug", "chatflow_switch_mode_title": "Switch mode", "click_button_to_create_mockset": "Click button to create a mockset", "click_upload_or_drag_files": "Click to upload or drag files here", "code": "Code", "code_mode": "Code mode", "code_node_help_doc": "Help center", "code_node_language": "language", "code_node_more_info": "For more information, please refer to", "code_node_switch_language": "Switch language", "code_node_switch_language_description": "Switching languages clears the current code and replaces it with a template for the target language", "code_node_test_code": "test code", "code_snippet": "Code Snippet", "codedev_hook_hook_type": "Hook Type", "codedev_hook_invoked_failed": "Failed to call the Hook", "codedev_hook_run_log_invoked": "Invoked", "collapse": "Collapse", "collapse-chat-knowledge-source-header": "Collapse", "community_Group_Title_content": "Main Text", "community_Image_uploading": "Image uploading... ({upload_num}/{total_num})", "community_Please_enter_please_enter_your_post": "Enter main text", "community_This_is_a_toast_Machine_review_failed": "The content contains sensitive information. Please modify it before posting.", "community_time_date": "{mm}/{dd}/{yyyy}", "community_time_day": "{n}d ago", "community_time_hour": "{n}h ago", "community_time_just_now": "Just now", "community_time_min": "{n}min ago", "compare_guide_description_model": "Added comparison debugging mode for different model configurations.", "compare_guide_description_prompt": "Added comparison debugging mode for different system prompts.", "compare_guide_title_model": "Model Comparison", "compare_guide_title_prompt": "Prompt Comparison", "compare_model_compare_model": "Model comparison debugging", "compare_prompt_compare_debug": "Prompt comparison debugging", "compare_tooltips_submit_to_the_prompt": "Submit to prompt library", "confirm": "Confirm", "confirm_plugin_information": "Confirm Plugin Information", "confirm_switch_model": "Confirm switch model?", "confirm_switch_to_on_demand_call": "Confirm switch to {call_method} call?", "content_view_001": "Content view", "content_view_002": "Segment view", "content_view_003": "Segmentation is being processed", "context_clear_finish": "Context cleared", "copied": "<PERSON>pied", "copy": "Copy", "copy_failed": "Co<PERSON> failed", "copy_session_id": "Copy ID", "copy_success": "Copy successfully", "coze_api_instru": "API instructions", "coze_api_list1": "Name", "coze_api_list3": "Creation time", "coze_api_list4": "Last used", "coze_api_list5": "Actions", "coze_bot_diff_btn_view_diff": "View diff", "coze_bot_diff_diffdetail_latestversion": "Latest version", "coze_bot_diff_diffdetail_mydraft": "My draft", "coze_bot_diff_diffdetail_onlineversion": "Online version", "coze_bot_diff_diffdetail_pagetitle": "<PERSON><PERSON> Details", "coze_bot_diff_diffdetail_tobereleasedversion": "To-be-Released version", "coze_copy_to_tips_1": "Create a copy in the current workspace", "coze_custom_publish_platform_10": "Create time", "coze_custom_publish_platform_11": "Actions", "coze_custom_publish_platform_12": "Private", "coze_custom_publish_platform_13": "Public", "coze_custom_publish_platform_14": "Not configured", "coze_custom_publish_platform_15": "Configured", "coze_custom_publish_platform_16": "Platform name", "coze_custom_publish_platform_17": "Description", "coze_custom_publish_platform_18": "Describe the platform", "coze_custom_publish_platform_19": "Binding Oauth app", "coze_custom_publish_platform_2": "My platform management", "coze_custom_publish_platform_20": "Please select Oauth app", "coze_custom_publish_platform_21": "Create new app", "coze_custom_publish_platform_23": "Platform callback URL", "coze_custom_publish_platform_24": "Used to obtain the release and update notifications of the Agents", "coze_custom_publish_platform_26": "Please select workspace", "coze_custom_publish_platform_27": "Cancel", "coze_custom_publish_platform_28": "Confirm", "coze_custom_publish_platform_29": "Platform name is required.", "coze_custom_publish_platform_3": "Add platform", "coze_custom_publish_platform_30": "Description is required.", "coze_custom_publish_platform_31": "Binding Oauth app is required.", "coze_custom_publish_platform_32": "Please enter a valid URL that uses the https protocol.", "coze_custom_publish_platform_34": "Please choose a workspace", "coze_custom_publish_platform_37": "Edit", "coze_custom_publish_platform_38": "Delete", "coze_custom_publish_platform_39": "Delete this platform?", "coze_custom_publish_platform_4": "Create and manage custom agent publishing platforms for your workspaces. If you wish to become a Coze public platform, please fill out the ", "coze_custom_publish_platform_41": "Configure", "coze_custom_publish_platform_42": "<PERSON><PERSON><PERSON> Configuration", "coze_custom_publish_platform_43": "Authorize Coze users to access account information through the channel to complete inter-platform account binding authorization.", "coze_custom_publish_platform_5": "application form", "coze_custom_publish_platform_55": "Callback <PERSON>", "coze_custom_publish_platform_56": "This operation will not be reversed.", "coze_custom_publish_platform_57": "callback_token", "coze_custom_publish_platform_58": "Copy", "coze_custom_publish_platform_6": "Platform", "coze_custom_publish_platform_63": "Workspace", "coze_custom_publish_platform_64": "Only supports numbers, letters, Chinese characters, and underscores.", "coze_custom_publish_platform_7": "ID", "coze_custom_publish_platform_8": "Public status", "coze_custom_publish_platform_9": "<PERSON><PERSON><PERSON>", "coze_free_credits_insufficient": "Sorry, your account free credits are insufficient. Please wait for the quota to refresh or upgrade to the paid version.", "coze_home_delete_btn": "Delete chat history", "coze_home_delete_modal_btn_cancel": "Cancel", "coze_home_page_fail_btn_retry": "Try again", "coze_home_page_fail_text": "Initialization failed,", "coze_home_stop_btn": "Stop responding", "coze_premium_credits_cycle_1": "Every day", "coze_premium_credits_cycle_2": "Every week", "coze_premium_credits_cycle_3": "Every month", "coze_premium_credits_cycle_4": "Never reset", "coze_premium_credits_cycle_5": "Credit Reset Cycle", "coze_premium_credits_cycle_tip1": "Credits will reset to the specified number of sponsored messages at each set interval.", "coze_premium_credits_cycle_tip2": "Excessive trial use increases credit costs, use wisely.", "coze_premium_credits_cycle_tip3": "Free trials allow more users to discover the bot.", "coze_premium_credits_cycle_tip4": "Increase sponsored messages to enable the credit refresh cycle.", "coze_premium_credits_cycle_tip6": "12:00 AM daily at UTC+0", "coze_premium_credits_cycle_tip7": "12:00 AM every Monday at UTC+0", "coze_premium_credits_cycle_tip8": "12:00 AM on the first day of every month at UTC+0", "coze_pro_payment_overdue": "Sorry, your account has an overdue payment, please recharge on volcengine before continuing to use it.", "cozedev_collaborator_btn_confirm": "Confirm", "creat_new_prompt": "Create prompt", "creat_new_prompt_des_placeholder": "Please enter a brief introduction to the prompt", "creat_new_prompt_edit_block": "Edit block", "creat_new_prompt_import_link": "Import current prompt", "creat_new_prompt_name_placeholder": "Please enter a prompt name", "creat_new_prompt_prompt": "Prompt", "creat_new_prompt_prompt_description": "Prompt description", "creat_new_prompt_prompt_name": "Prompt name", "creat_popup_profilepicture_generategif": "Generate GIF", "creat_popup_profilepicture_generateimage": "Generate image", "creat_popup_profilepicture_upload": "Upload", "creat_project_agent_describe": "To build a conversational agent", "creat_project_creat_agent": "Create agent", "creat_project_creat_new_project": "New app", "creat_project_creat_project": "Create app", "creat_project_describe": "Suitable for constructing complete apps", "creat_project_describe_open": "A complete app for building multi-workflow collaboration", "creat_project_project_describe": "Description", "creat_project_project_name": "App name", "creat_project_templates": "Templates", "creat_project_templates_load_failed": "Failed to load the template", "creat_project_title": "Create app", "creat_project_toast_success": "Created successfully", "creat_project_use_template": "Create an app using template", "creat_project_use_template_preview": "Preview", "creat_project_use_template_use": "Use", "creat_prompt_button_comfirm_and_compare": "Confirm and compare debugging", "creat_tooltip_create": "Create", "create-dataset-import-type": "Import type", "create-knowledge-table-type": "Table format", "create-knowledge-text-type": "Text format", "create_local_plugin_basic_tool_function": "Tool function", "create_local_plugin_basic_tool_function_input_placeholder": "Please enter tool function name", "create_local_plugin_basic_warning_no_tool_function_entered": "Please enter the tool function name", "create_mockset": "Create mockset", "create_plugin_modal_Authorization_no": "No authorization required", "create_plugin_modal_Authorization_oauth": "OAuth", "create_plugin_modal_Authorization_service": "Service", "create_plugin_modal_Parameter": "Parameter name", "create_plugin_modal_Parameter_empty": "Please enter a parameter name", "create_plugin_modal_Parameter_error": "Please enter a parameter name", "create_plugin_modal_Parameter_info1": "Parameter name", "create_plugin_modal_Parameter_info2": "This is the parameter name for transmitting the service token. It equals a \"key\", and the service token corresponds to the \"value\". It allows the API service to identify the authorization information you provided in the parameter. Example: Enter the token in abc.com/getInfo?token=xxx.", "create_plugin_modal_Servicetoken": "Service token/API key", "create_plugin_modal_Servicetoken_empty": "Please enter the service token/API key", "create_plugin_modal_Servicetoken_error": "Please enter the service token/API key", "create_plugin_modal_Servicetoken_info1": "Service token/API key", "create_plugin_modal_Servicetoken_info2": "This is a unique API key that indicates your identity or granted permissions. The API service verifies this token to ensure that you are authorized to perform the operations. Example: Enter xxx in abc.com/getInfo?token=xxx.", "create_plugin_modal_URLerror": "Please enter a valid URL", "create_plugin_modal_auth1": "Authorization method", "create_plugin_modal_authorization_content_type_empty": "Please enter authorization_content_type", "create_plugin_modal_authorization_content_type_info1": "The content type used to send data to the OAuth provider. The default value is the most common content type. Example:", "create_plugin_modal_authorization_content_type_info2": "Application/JSON", "create_plugin_modal_authorization_url_empty": "Please enter the authorization_url", "create_plugin_modal_authorization_url_info1": "Authorization_url: The URL of the OAuth provider where you are redirected to authorize the application. URL authentication is required. Example:", "create_plugin_modal_authorization_url_info2": "https://authprovider.com/authorize", "create_plugin_modal_button_cancel": "Cancel", "create_plugin_modal_button_confirm": "Confirm", "create_plugin_modal_client_id2": "Please enter the client_id", "create_plugin_modal_client_id4": "The unique identifier obtained when the application registers with the OAuth provider. Example:", "create_plugin_modal_client_id5": "abc123xyz", "create_plugin_modal_client_secret2": "Please enter the client_secret", "create_plugin_modal_client_secret4": "Client_secret: The key paired with the client_id. It helps you authenticate the application and obtain the token.", "create_plugin_modal_client_url_empty": "Please enter the client_url", "create_plugin_modal_client_url_info1": "Client_url: The callback URL of the application, where the authorization code is sent. This URL must be valid. Example:", "create_plugin_modal_client_url_info2": "https://yourapp.com/callback", "create_plugin_modal_descrip1": "Plugin description", "create_plugin_modal_descrip1_error": "Please enter the plugin description", "create_plugin_modal_descrip2": "Please describe the primary functions and scenarios of the plugin to help users/LLMs better understand it. Ensure that the content complies with platform guidelines.", "create_plugin_modal_descrip_error": "Only ASCII characters such as English letters, numbers, and common symbols are valid", "create_plugin_modal_header": "Header", "create_plugin_modal_header_list": "Header list", "create_plugin_modal_header_list1": "HTTP header list is a list of strings sent and received by the client applications and server in each HTTP request and response. These headers are invisible to end users and are only processed or logged by the server and client applications.", "create_plugin_modal_info_None1": "None", "create_plugin_modal_info_None2": "No authorization required.", "create_plugin_modal_info_Oauth1": "OAuth", "create_plugin_modal_info_Oauth2": "OAuth is an open standard commonly used for user agent authentication. It allows third-party applications to access specific resources of a user's account without sharing the user's password. For example, if a developer wants to use the API to publish Twitter but does not want to reveal the password, he or she can use the OAuth method.", "create_plugin_modal_info_Service1": "Service", "create_plugin_modal_info_Service2": "Service authentication refers to a simplified method where the server requires a specific key or token to verify the validity of API calls. This key is passed through query parameters or request headers, ensuring that only users or systems with the key can access the API. For example, to access the functions of some public APIs, you must register to obtain an API key.", "create_plugin_modal_info_all": "Select the authorization or verification method for the plugin. The following three types of methods are supported:", "create_plugin_modal_location": "Location", "create_plugin_modal_name1": "Plugin name", "create_plugin_modal_name1_error": "Please enter a plugin name", "create_plugin_modal_name2": "Please enter a clear and compliant name for the plugin", "create_plugin_modal_nameerror": "Only letters, numbers, underscores or spaces are valid", "create_plugin_modal_nameerror_cn": "Only Chinese characters, letters, numbers, underscores, or spaces are valid", "create_plugin_modal_query": "Query", "create_plugin_modal_scope_empty": "Please enter the scope", "create_plugin_modal_scope_info1": "The scope or level of resources your application wants to access. Example:", "create_plugin_modal_scope_info2": "read_profile, write_post", "create_plugin_modal_title1": "Create plugin", "create_plugin_modal_url1": "Plugin URL", "create_plugin_modal_url1_error": "Please enter plugin URL", "create_plugin_modal_url2": "Please provide the plugin URL or related resource URLs", "create_plugin_modal_url_error_https": "Please enter a valid HTTPS URL.", "create_time": "Create time", "create_title": "Create", "create_tool": "Create tool", "created_mockset_please_add_mock_data": "Created a mockset. Please add mock data to the mockset.", "created_mockset_please_add_mock_data_llm_generation": "Created a mockset, mock data is being generated, please wait.", "created_mockset_please_add_mock_data_random_generation": "Created a mockset and automatically generated mock data.", "creators": "Creators", "curl": "cURL", "customize_key_1": "Custom", "data_error_msg": "Please try again later or contact coze team.", "data_error_title": "Failed to view the {module}", "data_filter_values": "filter", "database_240227_01": "Reset data", "database_240304_01": "Supports up to {TableNumber} tables.", "database_240520_01": "Unique identification of data (primary key).", "database_240520_03": "The table is accessible in Prompts by default. If unchecked, it will not support access in Prompts (and can only be accessed within the Workflow).", "database_240522_01": "Copy", "database_240522_02": "Copy successful.", "database_240618_01": "Supports calling in the Prompt", "database_learnmore": "Learn more", "database_memory_menu": "Memory", "database_optimize_100": "Please select the import channel.", "database_optimize_200": "Online data refers to the data generated when an application is actually running. Please modify it with caution.", "dataide001": "Data", "dataide002": "Variables", "dataset-name-empty-tooltip": "Knowledge name cannot be empty", "dataset-name-has-wrong-word-tooltip": "The name cannot contain special characters", "dataset-setting_recall_title": "Recall", "dataset_automatic_call": "Auto-call", "dataset_bot_count_tag": "{num} Data", "dataset_bot_create_time_knowledge": "Creation time {time}", "dataset_bot_update_time_knowledge": "Update {time}", "dataset_call_method": "Call method", "dataset_create_knowledge_generate_avatar_tips": "Enter name and description, and use AI to generate a picture.", "dataset_create_knowledge_generate_content_tips": "Please check that name and description do not contain inappropriate content.", "dataset_data_processing_tag": "{num} data processing", "dataset_detail_source_custom": "Custom", "dataset_detail_source_local": "Local", "dataset_detail_source_online": "Online", "dataset_detail_tableTitle_actions": "Actions", "dataset_detail_tableTitle_enable": "Enable", "dataset_detail_table_deleteModel_description": "After deletion, references in related agents will become invalid.", "dataset_detail_type_table": "Table", "dataset_detail_type_text": "Text", "dataset_max_recall": "Maximum recalls", "dataset_max_recall_default": "<PERSON><PERSON><PERSON>", "dataset_max_recall_desc": "A large number may cause recalled paragraphs to exceed the token limit, where the low-match paragraphs beyond this limit are not transmitted to the LLM.", "dataset_min_degree": "Minimum matching degree", "dataset_min_degree_default": "<PERSON><PERSON><PERSON>", "dataset_on_demand_call": "On-demand call", "dataset_process_fail": "Data processing failed", "dataset_recall_copy_label": "RecallKnowledge", "dataset_recall_copy_value": "recallKnowledge", "dataset_segment_content": "Segment Content：", "dataset_segment_empty_desc": "No Segment yet", "dataset_set_title": "Select knowledge", "dataset_settings_title": "Knowledge settings", "dataset_upload_image_warning": "Please upload an image less than 2MB", "datasets_Custom_maxLength": "Maximum segment length", "datasets_Custom_rule": "Text preprocessing rules", "datasets_Custom_rule_delete": "Delete all URLs and email addresses", "datasets_Custom_rule_replace": "Replace consecutive spaces, line breaks, and tabs", "datasets_Custom_segmentID": "Segment ID", "datasets_Custom_segmentID_2linebreak": "2 line breaks", "datasets_Custom_segmentID_cn_exclamation": "Chinese exclamation mark", "datasets_Custom_segmentID_cn_question": "Chinese question mark", "datasets_Custom_segmentID_cnperiod": "Chinese period", "datasets_Custom_segmentID_custom": "Custom", "datasets_Custom_segmentID_en_exclamation": "English exclamation mark", "datasets_Custom_segmentID_en_question": "English question mark", "datasets_Custom_segmentID_enperiod": "English period", "datasets_Custom_segmentID_linebreak": "Line break", "datasets_ID_miss": "Missing knowledge ID", "datasets_createFileModel_CancelBtn": "Cancel", "datasets_createFileModel_NextBtn": "Next", "datasets_createFileModel_previousBtn": "Previous", "datasets_createFileModel_step1_CustomDescription": "Custom content. You can create and edit the content.", "datasets_createFileModel_step1_CustomTitle": "Custom", "datasets_createFileModel_step1_LocalDescription": "Upload local files in the PDF, TXT, MD, DOC, or DOCX format", "datasets_createFileModel_step1_LocalTitle": "Local documents", "datasets_createFileModel_step1_TabCustomDescription": "Customize content, support creation & editing", "datasets_createFileModel_step1_TabCustomTitle": "Customization", "datasets_createFileModel_step1_TabLocalDescription": "Upload documents in Excel or CSV format", "datasets_createFileModel_step1_TabLocalTitle": "Local documents", "datasets_createFileModel_step1_apiTitle": "API", "datasets_createFileModel_step1_urlTitle": "Online data", "datasets_createFileModel_step2": "Upload", "datasets_createFileModel_step2_UploadDoc": "Click to upload or drag and drop files here", "datasets_createFileModel_step2_UploadDoc_description": "Up to {maxDocNum} files in {fileFormat} format, with a maximum file size of {filesize} MB. PDF files can only contain {pdfPageNum} pages.", "datasets_createFileModel_step3": "Set segmentation", "datasets_createFileModel_step3_auto": "Automatic segmentation & Cleaning", "datasets_createFileModel_step3_autoDescription": "Automatic segmentation & Preprocessing rules", "datasets_createFileModel_step3_custom": "Custom", "datasets_createFileModel_step3_customDescription": "Custom segmentation rules & Segment length & Preprocessing rules", "datasets_createFileModel_step4": "Process data", "datasets_createFileModel_step4_Finish": "Server processing completed", "datasets_createFileModel_step4_failed": "Server processing failed", "datasets_createFileModel_step4_processing": "Server processing…", "datasets_createFileModel_tab_DataSheet": "Data sheet", "datasets_createFileModel_tab_dataStarRow": "Data starting row", "datasets_createFileModel_tab_dataStarRow_value": "Line {LineNumber}", "datasets_createFileModel_tab_header": "Header", "datasets_createFileModel_tab_step2": "Table Configuration", "datasets_createFileModel_tab_step3": "Preview", "datasets_create_btn": "Create knowledge", "datasets_custom_segmentID_error": "Segment identifier can't be empty", "datasets_custom_segmentID_placeholder": "Please enter segment identifier", "datasets_editProfile_title": "Edit Knowledge", "datasets_empty_description": "Please create first and then add", "datasets_empty_title": "No knowledge found", "datasets_frequencyModal_frequency": "Update frequency", "datasets_frequencyModal_frequency_day": "{num, plural, one {Every day } other {Every # days}}", "datasets_frequencyModal_frequency_noUpdate": "Do not update automatically", "datasets_frequencyModal_whenUpdate": "When updating", "datasets_frequencyModal_whenUpdate_overwrite": "Overwrite original data", "datasets_frequencyModal_whenUpdate_overwrite_keep": "Keep original data and apend new", "datasets_model_create_avatar": "Icon", "datasets_model_create_description": "Description", "datasets_model_create_description_placeholder": "Enter the content of the dataset", "datasets_model_create_name": "Name", "datasets_model_create_name_placeholder": "Enter the knowledge name", "datasets_model_create_title": "Create knowledge", "datasets_placeholder_search": "Search", "datasets_processing_notice": "Processing following files. Agent can reference them after completion.", "datasets_segment_Update": "Update frequency", "datasets_segment_card_bit": "{num, plural, =0{Char} =1{Char} other{Chars}}", "datasets_segment_card_hit": "{num, plural, one {# hit } other {# hits}}", "datasets_segment_detailModel_save": "Save", "datasets_segment_detailModel_title": "{num, plural, other {'#' #}}", "datasets_segment_edit": "Edit", "datasets_segment_resegment": "Re-segment", "datasets_segment_tableStructure_add_field": "Add Field", "datasets_segment_tableStructure_delTips": "The index matching field can not be deleted.", "datasets_segment_tableStructure_field_errEmpty": "Field name can not be empty.", "datasets_segment_tableStructure_field_name": "Field name", "datasets_segment_tableStructure_field_type_errEmpty": "Please select the data type", "datasets_segment_tableStructure_field_value": "Field value", "datasets_segment_tableStructure_semantic_name": "Index", "datasets_segment_tableStructure_semantic_no": "No", "datasets_segment_tableStructure_semantic_yes": "Yes", "datasets_segment_tableStructure_title": "Table Structure", "datasets_segment_tag_auto": "Auto-segment", "datasets_segment_tag_custom": "Custom segmentation", "datasets_segment_tag_overwrite": "Overwrite original data", "datasets_segment_tag_overwriteNo": "Keep original data and append new", "datasets_segment_tag_processing": "Processing…", "datasets_segment_tag_segments": "{num} segments", "datasets_segment_tag_updateFrequency": "{num, plural, =1{Updated everyday} other{Updated every # days}}", "datasets_segment_tag_updateNo": "Do not update", "datasets_table_title_actions_delete": "Delete", "datasets_title": "Knowledge", "datasets_unit_config_title1": "Update content", "datasets_unit_exception_name_empty": "Please enter a name", "datasets_unit_process_success": "Processed completed", "datasets_unit_tableformat_tips1": "A total of {TotalRows} records, the current preview only displays the first {ShowRows} records.", "datasets_unit_update_exception_tips3": "Upload one document in Excel or CSV format. The file size should be less than 20MB.", "datasets_unit_update_retry": "Re-upload", "datasets_unit_upload_fail": "Upload failed", "datasets_unit_upload_field_action": "Actions", "datasets_unit_upload_field_size": "File size", "datasets_unit_upload_field_update_frequency": "Update frequency", "datasets_unit_upload_state": "Uploading…", "datasets_unit_upload_success": "Uploaded", "datasets_update_type": "Knowledge update type", "datasets_url_empty": "Can not be empty", "datasets_url_saveSuccess": "Saved successfully", "db2_003": "Previous step", "db2_004": "Confirm", "db2_005": "The data table name is a required field", "db2_008": "Required fields need to be filled in", "db2_009": "Creator", "db2_010": "Everyone", "db2_011": "Sorting method", "db2_012": "Creation time", "db2_013": "Edit time", "db2_014": "Search", "db2_015": "Custom data table", "db2_016": "Create based on template", "db2_017": "Example", "db2_018": "Reading notes recording template", "db2_019": "This template can be used for saving reading notes. After developers use this template to develop a Bot, the book titles, chapters, and corresponding notes recorded by users will be stored in a table structure within the Bot.Users can search for relevant information while using the Bot", "db2_020": "Examples of users storing corresponding data in the Bot", "db2_021": "user", "db2_022": "Save 'The Art of Possibility', Chapter 3 - 'In this world, what's more terrifying than misery is the unknown misery'", "db2_023": "bot", "db2_024": "Alright, note saved", "db2_025": "Choose Database", "db2_027": "Data not saved prompt", "db2_028": "Stay on current page", "db2_029": "The data you submitted has not been saved. Are you sure you want to leave the page?", "db2_030": "Add to Agent", "db2_031": "Remove from Agent", "db_add_table_cust": "Custom table", "db_add_table_desc": "Table description", "db_add_table_desc_tips": "Please introduce the primary functions of the table to help the LLM better understand it", "db_add_table_field_desc": "Description", "db_add_table_field_desc_tips": "Additional information for the storage field. It can be a description in natural language, example data, or format instructions. For example, the book title format is \"XXX\".", "db_add_table_field_name": "Storage field name", "db_add_table_field_name_tips": "Defines the \"header\" for the storage table. Once developers have defined the storage fields, you can store relevant data of the fields. The following example shows the storage fields for reading notes:", "db_add_table_field_name_tips1": "Format: abc, abc123, or abc_123", "db_add_table_field_name_tips2": "can only contain lowercase letters, numbers, or underscores", "db_add_table_field_name_tips3": "must start with an English letter", "db_add_table_field_name_tips4": "and have a maximum length of 64 characters", "db_add_table_field_necessary": "Required?", "db_add_table_field_necessary_tips1": "Required field: You must provide the field information to successfully save a row of data", "db_add_table_field_necessary_tips2": "Optional field: You can save a row of data even when the field information is missing", "db_add_table_field_type": "Data type", "db_add_table_field_type_bool": "Boolean", "db_add_table_field_type_int": "Integer", "db_add_table_field_type_number": "Number", "db_add_table_field_type_time": "Time", "db_add_table_field_type_tips": "Select the data type for the storage field. The agent processes and saves your input content according to the data type selected by the developers.", "db_add_table_field_type_txt": "String", "db_add_table_name": "Table name", "db_add_table_name_tips": "Please enter a table name", "db_add_table_temp_desc": "for saving reading notes", "db_add_table_temp_field_desc1": "Record book titles", "db_add_table_temp_field_desc2": "Record book chapters", "db_add_table_temp_field_desc3": "Record reading insights", "db_add_table_temp_preview": "Preview", "db_add_table_temp_preview_tips": "Examples of users storing corresponding data in the Agent:", "db_add_table_temp_tips": "You can use this template to save reading notes. After developers develop an agent based on this template, the book titles, chapters, and insights you recorded are stored in a table structure in the agent. You can search for relevant information while using it.", "db_add_table_temp_title": "Reading notes recording template", "db_add_table_temp_use": "Use template", "db_add_table_title": "Create table", "db_del_field_confirm_info": "Delete the table field will cause the corresponding user data to be cleared. Please proceed with caution.", "db_del_field_confirm_no": "Cancel", "db_del_field_confirm_title": "Delete table field?", "db_del_field_confirm_yes": "Confirm", "db_del_table_confirm_info": "Once deleted, it cannot be recovered. Are you sure you want to delete the table?", "db_del_table_confirm_title": "Delete table?", "db_del_table_title": "Delete table", "db_edit_save": "Save", "db_edit_table_title": "Edit table", "db_edit_tips1": "Deleting existing fields will result in the clearing of the user's original data. Please proceed with caution.", "db_edit_tips2": "no more prompts", "db_edit_title": "Edit table", "db_memory_entry_tips": "Table storage allows agent developers to define table structures and store user data in tables, making functions such as favorites, to-do lists, book management, and financial management available.", "db_new_0001": "Table structure", "db_new_0002": "Online data", "db_new_0003": "Edit table", "db_new_0004": "The database name should only contain lowercase letters, numbers, and underscores, and it should start with a lowercase letter.", "db_notable_tips": "Organizes data in a table structure to access functions such as bookmarks and book management.", "db_optimize_002": "Data generation or usage channel", "db_optimize_003": "Data Insertion Time", "db_optimize_009": "Test data", "db_optimize_010": "Test data is mainly used to assist in debugging \"business logic\" and \"user interface\", and is isolated from online data", "db_optimize_011": "Clear", "db_optimize_012": "Refresh", "db_optimize_013": "Batch Import", "db_optimize_014": "Upload", "db_optimize_015": "Table Structure Configuration", "db_optimize_016": "Preview", "db_optimize_017": "Data Processing", "db_optimize_018": "You can download the template before uploading the file, and edit it in the template according to the guide", "db_optimize_019": "Download Template", "db_optimize_020": "Previous Step", "db_optimize_021": "Next Step", "db_optimize_022": "Add Row", "db_optimize_023": "Edit Row", "db_optimize_024": "Cancel", "db_optimize_025": "Insert", "db_optimize_026": "Are you sure to delete it?", "db_optimize_027": "Deleted data cannot be recovered", "db_optimize_028": "Delete", "db_optimize_029": "Cancel", "db_optimize_030": "<PERSON><PERSON> Delete", "db_optimize_031": "{n} data items have been selected", "db_optimize_032": "A total of {n} rows of data", "db_optimize_033": "Variable Settings", "db_optimize_034": "Prompt Word Invocation Settings", "db_optimize_035": "When enabled, the data table is supported to be accessed in the prompt word, otherwise it is only supported to be accessed in the workflow.", "db_optimize_036": "Prompt Word Invocation", "db_optimize_037": "Yes", "db_optimize_038": "No", "db_table_0126_001": "Cancel", "db_table_0126_003": "Next step", "db_table_0126_004": "Previous", "db_table_0126_005": "Complete", "db_table_0126_010": "Import Excel/CSV", "db_table_0126_011": "Create new table by Excel/CSV", "db_table_0126_012": "Upload", "db_table_0126_013": "Table Configuration", "db_table_0126_014": "Preview", "db_table_0126_015": "Data processing", "db_table_0126_016": "Click to upload or drag files here", "db_table_0126_017": "Upload one document in XLSX or CSV format. The file size should be less than 20MB.", "db_table_0126_018": "File name", "db_table_0126_019": "State", "db_table_0126_020": "Size", "db_table_0126_021": "Actions", "db_table_0126_027": "The maximum is {ColumNum} columns, please remove any unnecessary columns.", "db_table_0126_028": "A total of {TotalRows} records, the current preview only displays the first {ShowRows} records.", "db_table_0126_029": "Importing data", "db_table_0126_031": "failed", "db_table_0126_032": "The file format is not supported", "db_table_0129_001": "Table query mode", "db_table_0129_002": "Single-user mode", "db_table_0129_003": "Read-only mode", "db_table_0129_004": "Multi-user mode", "db_table_0129_005": "Single-user mode: Develo<PERSON> and users can add records but can only read/modify/delete data they created themselves from the same channel.", "db_table_0129_006": "Read-only mode: developers can read/write/modify/delete, and users only read.", "db_table_0129_007": "Multi-user mode: Develo<PERSON> and users can read/write/modify/delete any data from the same channel in the table. The read-write permissions are controlled by business logic. Note that it only takes effect in the workflow node", "db_table_data_entry": "Database", "db_table_entry": "Database", "db_table_save_exception_fieldname": "Duplicate storage field names detected", "db_table_save_exception_fieldtype": "Please select the data type", "db_table_save_exception_nofield": "Please define at least one storage field", "db_table_save_exception_nofieldname": "Please enter the field name", "debug_area_time_label_dataset": "Knowledge", "debug_area_time_label_llm": "LLM", "debug_area_time_label_model": "Model", "debug_area_time_label_plugin": "Plugin", "debug_area_time_label_tool": "Tool", "debug_asyn_task_notask": "No data", "debug_asyn_task_task_status": "Status", "debug_asyn_task_task_status_failed": "Failed", "debug_asyn_task_task_status_success": "Success", "debug_btn": "Debug", "debug_copy_report": "<PERSON><PERSON><PERSON>", "debug_copy_success": "The information has been copied to the clipboard", "debug_copy_suggestion": "Suggest screenshotting the agent configuration information.", "debug_detail_tab": "Debug Detail", "debug_skills": "Skills", "default_test_set_default_test_set": "Default test set", "default_test_set_default_test_set_release_tips": "Can be used as a test set by other users after publishing", "default_test_set_select_test_set": "Select test set", "delete": "Delete", "delete_desc": "This operation cannot be undone", "delete_mock_data": "Delete the mock data?", "delete_the_mockset": "Delete the mockset？", "delete_title": "Confirm to delete", "delete_tool": "Delete tool", "describe_use_scenarios_of_mockset": "Describe the use scenarios of <PERSON><PERSON><PERSON>.", "develop_list_card_copy_fail": "Co<PERSON> failed", "develop_list_card_tag_agent": "Agent", "develop_list_card_tag_project": "App", "develop_list_rank_tag_opened": "Recently opened", "develop_team_team": "Team", "devops_publish_changelog_generate_stop": "Stop", "devops_publish_multibranch_BotInfo.BackgroundImageInfoList": "Background image", "devops_publish_multibranch_BotInfo.OnboardingInfo": "Opening dialog", "devops_publish_multibranch_Current": "Current", "devops_publish_multibranch_ModelInfo.ModelResponseFormat.JSON": "JSON", "devops_publish_multibranch_ModelInfo.ModelResponseFormat.Markdown": "<PERSON><PERSON>", "devops_publish_multibranch_ModelInfo.ModelResponseFormat.Text": "Text", "devops_publish_multibranch_ModelInfo.ResponseFormat": "Output format", "devops_publish_multibranch_NetworkError": "Network error. Please refresh this page to try again.", "devops_publish_multibranch_PersionDrafts": "Personal drafts", "devops_publish_multibranch_PersionDraftsInfo": "Each collaborator has an independent draft for editing and modifications.", "devops_publish_multibranch_RecentSubmit": "Recent commit", "devops_publish_multibranch_RetrieveAndMerge": "Retrieve and merge", "devops_publish_multibranch_RetrieveAndMergeInfo": "When multiple users are editing simultaneously, support pulling versions submitted by others into one's own draft.", "devops_publish_multibranch_Save": "Save", "devops_publish_multibranch_VersionControl": "Version control", "devops_publish_multibranch_VersionControlInfo": "Versions need to be submitted before being published.", "devops_publish_multibranch_auto_saved": "Auto-saved at {time}", "devops_publish_multibranch_changes": "Changes", "devops_publish_multibranch_changeset_add": "Add", "devops_publish_multibranch_changeset_delete": "Delete", "devops_publish_multibranch_changeset_modify": "Modify", "devops_publish_multibranch_changeset_remove": "Remove", "devops_publish_multibranch_changetype": "Change type", "devops_publish_multibranch_diffwithin": "Difference within {connectorName}", "devops_publish_multibranch_done": "Done", "devops_publish_multibranch_i_know": "Ok.", "devops_publish_multibranch_nodiff": "No difference", "devops_publish_multibranch_property": "Property", "devops_publish_multibranch_publish_disabled_tooltip": "Multiple editors are collaborating on the agent. Please submit it to the workspace before publishing it.", "devops_publish_multibranch_viewdiff": "View difference", "dialog_240305_01": "Clear the data?", "dialog_240305_02": "Data cannot be recovered once cleared.", "dialog_240305_03": "Confirm", "dialog_240305_04": "Cancel", "dislike": "Dislike", "dislike_feedback_placeholder": "(Optional) Please specify the reason for your dissatisfaction or describe your expectations", "dislike_feedback_tag_harm": "The information is harmful", "dislike_feedback_tag_mislead": "The information is inaccurate", "dislike_feedback_tag_unfollow_instruction": "Instructions are not followed", "dislike_feedback_tag_unfollow_others": "Other", "dislike_feedback_title": "Tell us why you are dissatisfied to help us improve", "display_on_vertical_screen": "Display on vertical screen", "display_on_widescreen": "Display on widescreen", "do_not_remind_again": "Do not remind again", "drill_down_placeholer_select": "Select", "duplicate": "Duplicate", "duplicate_rename_copy": "Copy", "duplicate_select_workspace": "Workspace", "duplicate_tools_within_plugin": "There are duplicate tools within the plugin", "edit_block_api_disable_tooltips": "This resource is unavailable. Please switch to another accessible resource.", "edit_block_api_empty": "No configured skills available", "edit_block_api_imageflow": "Imageflow", "edit_block_api_knowledge_image": "Image", "edit_block_api_knowledge_table": "Table", "edit_block_api_knowledge_text": "Text", "edit_block_api_plugin": "Plugin", "edit_block_api_rename": "<PERSON><PERSON>", "edit_block_api_workflow": "Workflow", "edit_block_default_guidance_text": "Please enter the prompt here", "edit_block_guidance_text_placeholder": "Please enter the prompt text when the edit block content is empty", "edit_block_guidance_text_when_empty": "Guide for blank state", "edit_block_guild_describe": "Using edit blocks to emphasize areas in the prompt that users are suggested to modify can provide clearer usage guidance.", "edit_block_guild_title": "Edit block", "edit_block_prefilled_text": "Preset text", "edit_block_set_as_edit_block": "Set as edit block", "edit_mock_data": "<PERSON> <PERSON><PERSON>", "edit_mockset": "<PERSON>", "edit_pat_1": "Edit Personal Access Token", "edit_prompt": "Edit prompt", "edit_time": "Edit Time", "edit_variables_modal_cancel_text": "Cancel", "edit_variables_modal_ok_text": "Save", "edit_variables_modal_title": "Edit variable", "editor_toolbar_image": "Image", "enter_raw_content_or_url": "Enter the raw content of cURL, command Swagger, openAPI, or the URL pointing to them", "error": "Sorry, some error occured, please try again later.", "error_id_copy_success": "Error ID copied successfully.", "errorpage_bot_btn": "Back to Coze", "errorpage_bot_title": "Failed to view the agent", "errorpage_subtitle": "Please check your URL or retry after joining the corresponding workspace.", "eval_status_referenced": "Referenced", "exit": "Exit", "expand": "Expand", "expire_time_1": "Expiration time", "expired_time_days_1": "{num, plural, one {# day ({date})} other {# days ({date})}}", "expired_time_forbidden_1": "Please select the expiration time carefully, as the personal access token cannot be modified once generated", "explore_bot_category_all": "All", "explore_tools": "Explore tools", "failed": "Sorry, some error occurred...", "failed_to_import_tool": "{num, plural, =1 {1 tool} other {# tools}} failed to import ", "feedback_submit": "Submit", "file_format_not_supported": "The file format is not supported", "file_name_cannot_be_empty": "File name cannot be empty", "file_name_exist": "The file name already exists", "file_too_large": "The file size should be less than {max_size}", "file_upload_success": "Upload Successful", "filebox_0002": "Photos", "filebox_0003": "Documents", "filebox_0007": "Copy name", "filebox_0008": "Photo name copied to clipboard", "filebox_0010": "<PERSON><PERSON>", "filebox_0013": "Delete this photo？", "filebox_0016": "Photo deleted", "filebox_0017": "No image yet", "filebox_0018": "Name", "filebox_0020": "Upload time", "filebox_0022": "Delete this document？", "filebox_0023": "The document name has been copied to the clipboard", "filebox_0024": "Document deleted", "filebox_0025": "No documentation yet", "filebox_0040": "Delete All", "filebox_0042": "Delete Folder", "filebox_0047": "unnamed file", "filebox_010": "No relevant images found", "filebox_011": "No relevant documents found", "files_exceeds_limit": "The number of files exceeds the limit", "filter_all": "All", "filter_develop_agent": "Agent", "filter_develop_all_creators": "All", "filter_develop_all_types": "All", "filter_develop_created_by_me": "My Works", "filter_develop_project": "App", "filter_develop_recent_opened": "Recent", "flowcanvas_shortcuts_backspace": "Backspace", "flowcanvas_shortcuts_click": "Click", "flowcanvas_shortcuts_copy": "Copy", "flowcanvas_shortcuts_delete": "Delete", "flowcanvas_shortcuts_drag": "Drag", "flowcanvas_shortcuts_duplicate": "Create copy", "flowcanvas_shortcuts_move_canvas": "Move canvas", "flowcanvas_shortcuts_multiple_deselect": "Inverse", "flowcanvas_shortcuts_multiple_select": "Multi-select", "flowcanvas_shortcuts_or": "Or", "flowcanvas_shortcuts_paste": "Paste", "flowcanvas_shortcuts_scroll": "<PERSON><PERSON>", "flowcanvas_shortcuts_shortcuts": "Shortcuts", "flowcanvas_shortcuts_space": "Space bar", "flowcanvas_shortcuts_zoom_in": "Zoom in", "flowcanvas_shortcuts_zoom_out": "Zoom out", "form_mode": "Form mode", "free_chat_allowance": "Sponsored Messages", "free_chat_allowance_tips": "Offers up to 100 free messages. Credit costs vary depending on the models used by the bot, sponsored by the bot creator.", "generate": "Generate", "generate_bot_icon_content_filter": "Please check if the name and description contain inappropriate content or are unclear in meaning.", "generate_failed": "Failed to generate table, please modify the description and try again.", "generate_randomly_based_on_data_type_name": "Generate randomly based on data type and name.", "generating": "Generating", "go": "go", "good_mockset_name_descriptive_concise": "A good mockset name would be descriptive,and concise.", "google": "google", "got_it": "Got it", "guidance_got_it": "Got it", "home_favor_desc1": "No favorites yet", "home_favor_desc2": "Click the ⭐️ button to add content here", "http_node_auth_notopen": "Authentication not enabled", "image_download_not_supported": "Sorry, this image is not available for download.", "imageflow_add": "Add image stream", "imageflow_add_toast_error": "Failed to obtain image stream information", "imageflow_canvas_a41": "A4 (Landscape)", "imageflow_canvas_a42": "A4 (Portrait)", "imageflow_canvas_align1": "<PERSON><PERSON> left", "imageflow_canvas_align2": "Center horizontally", "imageflow_canvas_align3": "Align right", "imageflow_canvas_align4": "Align top", "imageflow_canvas_align5": "Center vertically", "imageflow_canvas_align6": "Align bottom", "imageflow_canvas_align7": "Evenly space horizontally", "imageflow_canvas_align8": "Evenly space vertically", "imageflow_canvas_cancel_change": "Cancel upload", "imageflow_canvas_change_img": "Replace image", "imageflow_canvas_change_text": "Click to edit the text in the preview", "imageflow_canvas_circle": "Circle", "imageflow_canvas_color": "Color", "imageflow_canvas_copy": "Copy", "imageflow_canvas_desc": "You can add texts and images to customize the layout", "imageflow_canvas_double_click": "Double-click to edit", "imageflow_canvas_down_1": "Send backward", "imageflow_canvas_draw": "Draw mode", "imageflow_canvas_edit": "Editor", "imageflow_canvas_element_desc": "Value", "imageflow_canvas_element_name": "Name", "imageflow_canvas_element_set": "Element settings", "imageflow_canvas_elment_tooltip": "You can reference the outputs from previous nodes on the canvas", "imageflow_canvas_fill": "Fill", "imageflow_canvas_fill1": "Auto-fit", "imageflow_canvas_fill2": "Fill", "imageflow_canvas_fill3": "Stretch and fill", "imageflow_canvas_fill_image": "Content", "imageflow_canvas_fill_mode": "Fill mode", "imageflow_canvas_fill_preview": "Preview", "imageflow_canvas_frame": "<PERSON>ame", "imageflow_canvas_height": "Height", "imageflow_canvas_line": "Straight line", "imageflow_canvas_line_style": "Line style", "imageflow_canvas_paste": "Paste", "imageflow_canvas_rect": "Rectangle", "imageflow_canvas_reference": "Reference", "imageflow_canvas_restart": "Reset view", "imageflow_canvas_select_var": "Select variable", "imageflow_canvas_setting": "Settings", "imageflow_canvas_stroke": "Stroke", "imageflow_canvas_stroke_width": "Stroke width", "imageflow_canvas_style_tooltip": "Style", "imageflow_canvas_text1": "Single-line text", "imageflow_canvas_text2": "Text block", "imageflow_canvas_text_default": "Text", "imageflow_canvas_text_tooltip1": "Font size", "imageflow_canvas_text_tooltip2": "Row height", "imageflow_canvas_to_back": "Move to bottom", "imageflow_canvas_to_front": "Move to top", "imageflow_canvas_top_1": "Bring forward", "imageflow_canvas_transparency": "Transparency", "imageflow_canvas_trian": "Triangle", "imageflow_canvas_var_add": "Add variables to the canvas", "imageflow_canvas_var_delete": "Variable deleted", "imageflow_canvas_var_no": "No variables", "imageflow_canvas_var_reference": "{n} time(s) referenced", "imageflow_canvas_width": "<PERSON><PERSON><PERSON>", "imageflow_create": "Create image stream", "imageflow_create_description": "image stream description", "imageflow_create_description_placeholder": "Please enter a description so that the large model understands when this image stream should be called", "imageflow_create_name": "image stream name", "imageflow_create_name_placeholder": "Please enter an image stream name", "imageflow_create_name_wrong_format": "Image stream names allow only letters, numbers, and underscores, and begin with a letter", "imageflow_create_toast_success": "Image stream created successfully", "imageflow_detail_no_search_result": "Image stream not found", "imageflow_detail_toast_createcopy_succeed": "Image stream copy created", "imageflow_edit": "Edit image stream", "imageflow_explore": "Explore Image Stream", "imageflow_generation_desc1": "The default is 25, the range is [1, 40], the larger the value, the finer the picture, and the longer the generation time.", "imageflow_generation_desc4": "Edit prompts for image models to generate content", "imageflow_input_upload": "Upload", "imageflow_input_upload_placeholder": "Upload image", "imageflow_output_display": "Output image preview", "imageflow_output_display_desc1": "After running, preview image can be displayed", "imageflow_output_display_desc2": "The image is being generated...", "imageflow_output_display_desc3": "Image generation failed, please try again", "imageflow_output_display_save": "Save Picture", "imageflow_title": "image stream", "imageflow_title_description": "创建图像流", "imageflow_upload_action": "Click or drag the image to upload.", "imageflow_upload_action_common": "Click or drag to upload files", "imageflow_upload_error": "Image upload failed, please try again.", "imageflow_upload_error1": "The image aspect ratio does not meet the requirements.", "imageflow_upload_error2": "Image height cannot be less than {value}", "imageflow_upload_error3": "Image width cannot be less than {value}", "imageflow_upload_error4": "Image height cannot exceed {value}", "imageflow_upload_error5": "Image width cannot exceed {value}", "imageflow_upload_error6": "Image loading exception", "imageflow_upload_error7": "Image upload timed out", "imageflow_upload_error_type": "The image format needs to be {type}", "imageflow_upload_exceed": "Upload file size must not exceed {size}", "imageflow_upload_type": "Support {type} format", "imageflow_workspace2": "Team Image Stream", "import": "Import", "import_plugin": "Import Plugin", "import_plugin_tool": "Import Tool", "inappropriate_contents": "Please check if it contains any inappropriate content.", "inifinit_list_empty_title": "No Content", "inifinit_list_load_fail": "Loading Failed", "inifinit_list_retry": "Retry", "inifinit_search_not_found": "No Found", "insert": "Insert", "intelligently_generated_by_large_language_model": "Intelligent generation by language model based on Mockset info may contain inaccuracies.", "invalid_database": "Invalid database", "jinja_invalid": "Persona & Prompt contain unsupported syntax, such as illegal variable naming within double brackets. Please delete or modify according to the Coze OpenAPI doc and try again.", "keep": "Yes", "kl2_002": "Supports up to 10,000 segments in Knowledge base", "kl2_003": "Up to 300 documents can be added under Knowledge base", "kl2_004": "All added documents are expected to be segmented, which may take a longer period of time. Are you sure you want to re-segment them?", "kl2_006": "Delete document", "kl2_007": "Are you sure you want to delete it", "kl2_009": "{num, plural, one {# document } other {# documents}}", "kl2_010": "All", "kl2_011": "Document", "kl2_012": "Table", "kl2_013": "Add to agent", "kl2_014": "Remove from agent", "kl_write_003": "Please add the knowledge base to this node. Only one document-type knowledge base is supported", "kl_write_004": "Quick Parsing", "kl_write_005": "Will not extract elements such as images and tables from the document, suitable for pure text", "kl_write_006": "Precision Parsing", "kl_write_007": "Extract elements such as images and tables from the document, which takes longer time", "kl_write_008": "Image Elements", "kl_write_009": "Scanned copy (OCR)", "kl_write_010": "Table Elements", "kl_write_011": "Segmentation Strategy", "kl_write_012": "Automatic Segmentation", "kl_write_013": "Automated segmentation based on content paragraphs", "kl_write_014": "Segmentation Overlap Degree %", "kl_write_015": "The proportion of overlap between adjacent slices. Overlapping slices can ensure that important information is not lost at the slice boundaries, providing more context for the model.", "kl_write_017": "Vector Index", "kl_write_018": "Store each slice and its corresponding vector in a vector database, suitable for semantic-based fuzzy queries.", "kl_write_019": "Vector Model", "kl_write_020": "Keyword Index", "kl_write_021": "Build a mapping from each keyword to the corresponding slice, suitable for keyword-based precise queries.", "kl_write_022": "Table SQL Query", "kl_write_023": "Synchronously convert the queried natural language into SQL to query; the SQL execution results and the RAG recalled paragraphs are input into the model together", "kl_write_024": "Query Rewriting", "kl_write_025": "Optimize or reconfigure the user's input query, capture the user's intention more accurately and improve the efficiency", "kl_write_026": "Result Reranking", "kl_write_027": "Rerank the retrieved document slices based on relevance or quality to improve the accuracy of the generated answers", "kl_write_028": "Only Personal Documents", "kl_write_029": "When enabled, users can only search for documents uploaded by themselves and developers, documents uploaded by others are not visible", "kl_write_030": "Split the text according to the selected identifier.", "kl_write_031": "The maximum allowed length of a single slice after splitting", "kl_write_032": "Document parsing strategy", "kl_write_033": "Data writing settings", "kl_write_034": "Sample Reference", "kl_write_035": "Assume that the user previously mentioned in a conversation system:", "kl_write_036": "\"I have recently been learning Python programming.\"", "kl_write_037": "Then the user continues to ask:", "kl_write_038": "\"How should I start?\"", "kl_write_039": "In this context, the system can rewrite the query as:", "kl_write_040": "\"From which Python learning resources or projects should I start?\"", "kl_write_041": "Slice {index}:", "kl_write_042": "Introduce the history of pasta.", "kl_write_043": "Discussed different types of pasta and their pairings.", "kl_write_044": "Described in detail the steps of making pasta, including required materials and cooking techniques.", "kl_write_045": "Provided some pasta recipes.", "kl_write_046": "Suppose we have a user query: “How to make pasta?” The following document segments will be retrieved from the knowledge base, which are listed in ABCD order:", "kl_write_047": "In the process of rearracking, by analyzing the intention of user inquiries, the sequence of sorting the sequence of sorting is made, so that the most relevant content is ranked ahead. The final order may become:", "kl_write_100": "Extracted Content", "kl_write_102": "Content filtering", "kl_write_103": "Set filtering content", "kl_write_104": "No filtering content set", "kl_write_105": "Document list", "kl_write_106": "Filter this page", "kl_write_107": "Creation settings", "kl_write_108": "Complete creation", "kl_write_109": "Create and import", "knowledg_table_increment_tips": "Note: The schema of the newly uploaded file needs to be aligned with the schema of the existing table, otherwise the upload will fail.", "knowledg_table_segments_content": "Segment Content", "knowledg_table_structure_err_msg": "The table header is inconsistent with the current table structure, and the column names and order must be consistent.", "knowledg_table_structure_tips": "The header need to be consistent with the existing table structure.", "knowledg_unit_add_segments": "Add content", "knowledge-3rd-party-feishu": "<PERSON><PERSON><PERSON>", "knowledge-3rd-party-google-drive": "Google Drive", "knowledge-3rd-party-notion": "Notion", "knowledge-dataset-type-all": "All", "knowledge-dataset-type-table": "Table", "knowledge-dataset-type-text": "Text", "knowledge_1218_001": "Up to {MaxDocs} docs", "knowledge_1221_02": "Data not retrieved", "knowledge_1221_03": "Loading data", "knowledge_1222_01": "Table format unit, the maximum is 50 columns, please remove any unnecessary columns.", "knowledge_1226_001": "Index", "knowledge_add_btn_by_workflow": "Add to workflow", "knowledge_add_content_processing_tips": "Please add new content after the service processing is completed.", "knowledge_add_unit_process_notice": "Clicking \"Confirm\" does not affect data processing. The documents can be referenced once they are processed.", "knowledge_bot_update_databse_tnserr_msg": "Please check that the database's name and description do not contain inappropriate content", "knowledge_call_method_tooltip": "Select whether to automatically recall each round of chat or recall from specific knowledge as needed", "knowledge_content_illegal_error_msg": "The content does not comply with platform specifications.", "knowledge_custom_add_content": "Add value", "knowledge_detail_doc_rename": "<PERSON><PERSON>", "knowledge_document_view": "Failed to view the document. Please try again later. ", "knowledge_edit_unit_name_title": "Edit name", "knowledge_es_001": "Cloud Search Service", "knowledge_es_024": "Knowledge Base Storage Invalid. Please go to Knowledge Base Details to modify storage configuration and retry", "knowledge_feishu_10": "Authorize", "knowledge_full_text_search_title": "Full-text", "knowledge_full_text_search_tooltip": "The keyword-based full-text search. It is recommended to use it in scenarios when searching for specific names, acronyms, phrases, or IDs.", "knowledge_hierarchies_categories": "Operation categories that support hierarchies.", "knowledge_hierarchies_categories_01": "Cannot be adjusted to this position", "knowledge_hierarchies_categories_02": "The merging master node cannot be found", "knowledge_hierarchies_categories_03": "The title node cannot be merged", "knowledge_hierarchies_categories_04": "The node to be merged cannot be found.", "knowledge_hybird_search_title": "Hybrid", "knowledge_hybird_search_tooltip": "Leverages the advantages of full-text search and semantics search to comprehensively rank the results", "knowledge_insert_img_001": "Content preview", "knowledge_insert_img_002": "Insert image", "knowledge_insert_img_003": "Source: {url}", "knowledge_insert_img_004": "View", "knowledge_insert_img_005": "Image URL", "knowledge_insert_img_006": "Click here to upload or drag files", "knowledge_insert_img_007": "Re-upload and replace the current image", "knowledge_insert_img_009": "Uploading", "knowledge_insert_img_010": "Image", "knowledge_insert_img_011": "Columns with images cannot be set as indexes", "knowledge_insert_img_013": "Maximum supported upload size is 20M", "knowledge_level_001": "Segmented by hierarchy", "knowledge_level_004": "Segment hierarchy", "knowledge_level_005": "Retrieve slice retention hierarchy information", "knowledge_level_010": "Original document preview", "knowledge_level_011": "Segmented preview", "knowledge_level_012": "Document list", "knowledge_level_016": "Hierarchical segmentation", "knowledge_level_028": "Delete", "knowledge_level_029": "Merged into one segment", "knowledge_level_030": "Preview the original document", "knowledge_level_110": "Image content", "knowledge_level_adjust": "Segmented Hierarchy", "knowledge_limit_20": "Table format unit, the maximum is 50 columns, please remove any unnecessary columns.", "knowledge_multi_index": "1、The user's query will be compared with the content of the index fields, and the matching will be carried out according to the similarity.\n2、It supports setting multiple indexes for matching to obtain information from more fields.\n3、Note: An excessive number of indexes may lead to a decrease in accuracy.", "knowledge_multi_index_noti": "Please configure at least one index to match the user query.", "knowledge_new_001": "View or update settings", "knowledge_new_002": "Filtered content exists", "knowledge_no_result": "No Result", "knowledge_optimize_0010": "Add a new row", "knowledge_optimize_005": "Are you sure you want to load the latest content?", "knowledge_optimize_006": "When loading the latest content, the current content will be overwritten", "knowledge_optimize_007": "Confirm", "knowledge_optimize_008": "Load latest content in batches", "knowledge_optimize_009": "After loading the latest content in batches, the imported content from all web pages or documents from third-party data sources will be overwritten.", "knowledge_optimize_010": "Documentation of all web pages or third-party data sources", "knowledge_optimize_014": "Set update frequency in batches", "knowledge_optimize_015": "After setting the update frequency in batches, all web pages or documents from third-party data sources will be updated at the same frequency.", "knowledge_optimize_016": "Add segments below", "knowledge_optimize_017": "Add section above", "knowledge_optimize_019": "{n} Agents are referencing", "knowledge_optimize_020": "please try again later", "knowledge_optimize_099": "unauthorized", "knowledge_photo_001": "Image format", "knowledge_photo_002": "Local image", "knowledge_photo_003": "Upload images in the JPG, JPEG, or PNG format", "knowledge_photo_004": "Click to upload or drag and drop the image here", "knowledge_photo_005": "You can upload files in the JPG, JPEG, or PNG format, with a maximum file size of 20 MB", "knowledge_photo_006": "Upload image", "knowledge_photo_007": "Labeling settings", "knowledge_photo_008": "Smart labeling", "knowledge_photo_009": "Auto-generate detailed information by analyzing images", "knowledge_photo_010": "Manual labeling", "knowledge_photo_011": "No processing is required. Manually add the description after the images are imported.", "knowledge_photo_013": "Labeled", "knowledge_photo_014": "Unlabeled", "knowledge_photo_015": "{num} photos", "knowledge_photo_016": "Unlabeled data will not be retrieved and recalled", "knowledge_photo_019": "Edit description", "knowledge_photo_020": "Auto-generate", "knowledge_photo_021": "Confirm to auto-generate", "knowledge_photo_022": "All content will be replaced after the description is automatically generated. Are you sure you want to proceed?", "knowledge_photo_025": "Images", "knowledge_photo_026": "Please describe the image", "knowledge_photo_027": "After uploading the image, you can choose to automatically or manually add the semantic description. Then, the agent can match the most appropriate image based on its description.", "knowledge_photo_illegal_error_msg": "The picture is suspected of violating the ban on access.", "knowledge_remove_btn_by_workflow": "Remove from workflow", "knowledge_search_strategy_title": "Search strategy", "knowledge_search_strategy_tooltip": "The method for retrieving data from the knowledge. You can use different retrieval strategies to effectively locate information, ensuring that the generated answers are more accurate and practical.", "knowledge_segment_config_table": "Modify table structure", "knowledge_semantic_search_title": "Semantics", "knowledge_semantic_search_tooltip": "The vector-based search for text relevance. It is recommended to use it in scenarios requiring an understanding of semantics relevance and cross-language searches.", "knowledge_source_card_0001": "Text content", "knowledge_source_card_0002": "Card", "knowledge_source_card_0003": "The source of the citation is displayed as text content at the bottom of the answer and is visible on all applications.", "knowledge_source_card_0004": "The reference source is displayed independently through the card, which can show that the scope of the application is aligned with the scope of the card.", "knowledge_source_display_status": "Show source", "knowledge_source_display_title": "Source", "knowledge_source_display_tooltip_content": "In Coze, \"knowledge\" refers to a powerful function that allows your agent to interact with data. It provides an easy-to-use knowledge capability, enabling the agent to process and manage extensive datasets, such as PDFs with thousands of words or real-time data from websites. [1]", "knowledge_source_display_tooltip_link": "Coze Docs: Knowledge Overview", "knowledge_source_display_tooltip_title": "Shows the raw data segments from the recalled knowledge to end users and allows them to view the source files.", "knowledge_tableStructure_errSystemField": "System fields are not allowed", "knowledge_tableStructure_field_errLegally": "Cannot contain single quotes, double quotes, backtick marks, or escape characters", "knowledge_table_content_empty": "Content cannot be empty", "knowledge_table_content_limt": "Content cannot exceed {number} characters", "knowledge_table_custom_submit_tips": "Please ensure that all index columns have been correctly filled in; the values cannot be empty.", "knowledge_table_nl2sql_tooltip": "The table knowledge supports matching based on index columns (tables are divided by rows), and also supports queries and calculations based on NL2SQL.", "knowledge_table_structure_column_name": "Column Name", "knowledge_table_structure_column_tooltip": "Currently, only the first 3 sheets and the first 50 rows can be selected to configure the table header. The data of the columns set as indexes will be converted to string type.", "knowledge_table_structure_data_type": "Data Type", "knowledge_table_structure_desc": "Desc", "knowledge_table_structure_semantic": "Index", "knowledge_tableview_01": "View and edit rows", "knowledge_tableview_02": "Delete", "knowledge_tableview_03": "New segment added successfully.", "knowledge_upload_create_title": "Create new knowledge base", "knowledge_upload_format_error": "The file format is not supported", "knowledge_upload_remaining_time_text": "Remaining time {minutes} minutes {seconds} seconds", "knowledge_upload_text_custom_add_title": "Text filled", "knowledge_upload_text_custom_doc_content": "Document content", "knowledge_upload_text_custom_doc_content_tips": "Please enter the document content.", "knowledge_upload_text_custom_doc_name": "Document name", "knowledge_upload_text_custom_doc_name_tips": "Please enter a document name", "knowledge_variable_description_placeholder": "Description", "knowledge_weixin_001": "WeChat Official Account", "knowledge_weixin_015": "Do not automatically append", "knowledge_weixin_016": "Daily", "knowledge_weixin_017": "Every three days", "knowledge_weixin_018": "Every seven days", "knowledge_write_tips_doc": "Documents to be written into the knowledge base need to meet the File-Doc type. ", "knowledge_writing_101": "All uploaded documents will be stored in the selected knowledge base.", "knowlege_qqq_001": "Segmented preview", "knowlege_qqq_002": "Processing", "knowlege_qqq_003": "Process failed", "landing_mobile_popup_button": "I Got it", "landing_mobile_popup_context": "This page has not been adapted to the current display size, it is recommended to use on a PC and adjust to the appropriate size", "landing_mobile_popup_title": "Tips", "landingpage_description": "Coze is a next-generation AI application and chatbot developing platform for everyone. Regardless of your programming experience, <PERSON><PERSON> enables you to effortlessly create a variety of chatbots and deploy them across different social platforms and messaging apps.", "language": "Language", "latest_version": "Latest version", "level_997": "Support the deletion of slices", "level_998": "Merge into slices according to the hierarchy", "level_999": "Drag to adjust the hierarchical structure", "library_actions": "Action", "library_actions_enable": "Enable", "library_delete_desc": "Once deleted, the resources referenced by the previous agents or workflows will be canceled. This operation cannot be undone.", "library_edited_time": "Edit Time", "library_empty_clear_filters": "Clear Filters", "library_empty_no_results_found_under": "No results found under the current filters", "library_filter_tags_all_creators": "All", "library_filter_tags_all_status": "All", "library_filter_tags_all_types": "All Types", "library_filter_tags_collaboration": "Collaboration enabled", "library_filter_tags_created_by_me": "My Works", "library_filter_tags_disabled": "Disabled", "library_filter_tags_image": "Image", "library_filter_tags_processing": "Processing…", "library_filter_tags_published": "Published", "library_filter_tags_table": "Table", "library_filter_tags_text": "Text", "library_filter_tags_unpublished": "Not Published", "library_name": "Resources", "library_resource": "Resources", "library_resource_detail_back": "Back", "library_resource_type_knowledge": "Knowledge", "library_resource_type_plugin": "Plugin", "library_resource_type_prompt": "Prompt", "library_resource_type_workflow": "Workflow", "library_resource_type_mcp": "MCP", "library_type": "Type", "library_workflow_header_reference_graph_entry_hover_no_reference": "No reference yet", "library_workflow_header_reference_graph_entry_hover_view_graph": "View reference graph", "like": "Like", "llm": "LLM", "llm_mode": "LLM mode", "loading": "Loading...", "local_file": "Local file", "local_plugin_label": "Local Plugin", "log_out_desc": "Are you sure you want to log out?", "login_button_text": "Log in", "login_failed": "<PERSON><PERSON> failed", "ltm_240227_01": "Recall from Long-term Memory", "ltm_240617_02": "The Long-term Memory switch status of this Agent:", "ltm_240826_01": "Enter the query that might call Long-term Memory.", "ltm_240826_02": "The output list is the information that best match the input parameters, called from Long-term Memory of related Bot.", "manage_mockset": "Manage mockset", "markdown_bold": "Bold", "markdown_bold_syntax": "**{text}** {space}", "markdown_bulletedlist": "Unordered list", "markdown_bulletedlist_syntax": "- {space}", "markdown_code": "Code", "markdown_code_syntax": "`{code}` {space}", "markdown_codeblock": "Code block", "markdown_codeblock_syntax": "``` {space} or ``` code language {space}", "markdown_heading1": "Heading 1", "markdown_heading1_syntax": "# {space}", "markdown_heading2": "Heading 2", "markdown_heading2_syntax": "## {space}", "markdown_heading3": "Heading 3", "markdown_heading3_syntax": "### {space}", "markdown_intro": "Here, you can use Markdown syntax to format your text. For example, enter \"**text**\" to make the text bold. Refer to the table below for detailed Markdown syntax.", "markdown_is_supported": "Support Markdown", "markdown_italic": "Italic", "markdown_italic_syntax": "*{text}* {space}", "markdown_numberedlist": "Ordered list", "markdown_numberedlist_syntax": "1. {space}", "markdown_quote": "Reference", "markdown_quote_syntax": "> {space}", "markdown_strickthrough": "Strikethrough", "markdown_strickthrough_syntax": "~~{text}~~ {space}", "menu_bots": "Agents", "menu_datasets": "Knowledge", "menu_documents": "Documents", "menu_plugins": "Plugins", "menu_profile_account": "Account", "menu_title_personal_space": "Personal space", "menu_title_store": "Explore", "menu_widgets": "Cards", "menu_workflows": "Workflows", "merge_duplicate_tools": "Merge duplicate tools", "message_content_error": "This message format is incorrect and is temporarily not supported for display.", "message_tool_regenerate": "Regenerate", "mkl_plugin_created": "Created", "mkl_plugin_publish": "Published on", "mkl_plugin_to_plugin_gallery": "Explore plugins", "mkl_plugin_updated": "Updated", "mkpl_bot_duplicate_tips": "The workflow currently used by the agent does not support synchronous replication. Please stay tuned!", "mkpl_bots_category": "Category", "mkpl_bots_private_configuration": "Private configuration", "mkpl_bots_private_configuration_description": "By configuring your agent as \"Private\", other Coze users will not be able to see the system prompts of your agent. However, they can still chat with your agent and view the third party platforms where your agent is published and its public resources, including any integrated plugins.", "mkpl_bots_public_configuration": "Public configuration", "mkpl_bots_public_configuration_description": "By configuring your agent as \"Public\", you agree to grant other developers and users in Coze the right to create agents using your agent and your content in accordance with our terms of service.", "mkpl_bots_visibility": "Visibility", "mkpl_favorite": "Favorites", "mkpl_load_btn": "Load more", "mkpl_num_favorites": "Favorites", "mkpl_plugin_delisted_tips": "This plugin has been unlisted and is unavailable for use.", "mkpl_plugin_detail": "Plugin details", "mkpl_plugin_disable_delete": "Please remove your plugin from the store before deleting.", "mkpl_plugin_tool_parameter_description": "Parameter description", "mkpl_plugin_tooltip_official": "Coze official plugin", "mkpl_published": "Published", "mkpl_send_tooltips": "Send", "mobile": "Mobile", "mock_data_counts": "Mock data Counts", "mock_data_quantity": "Mock data quantity", "mock_enable_switch": "Enabling Mo<PERSON><PERSON> will not send real requests to the Plugin but will return the mock data you selected below.", "mockdata_field_empty": "Required field \"{fieldName}\" cannot be empty", "mockset": "MockSet", "mockset_data": "Mockset data", "mockset_description": "Mockset description", "mockset_field_is_required": "{field} is required", "mockset_invaild_tip": "{MockSetName} is no longer valid", "mockset_is_empty_add_data_before_use": "The Mockset is empty, please add data before use", "mockset_label_tip": "MockSet: {MockSetName}", "mockset_name": "Mockset name", "mockset_of_toolname": "Mockset of {toolName}", "mockset_save": "Save", "mockset_save_description": "After the project/agent is published, the real data is automatically used", "mockset_tip_data_will_lose": "Entered content will be lost", "mockset_toast_data_size_limit": "The size of the data input exceeds the limit (100k), please modify", "model_config_generate_advance": "Advance", "model_config_generate_balance": "Balance", "model_config_generate_creative": "Creative", "model_config_generate_customize": "Custom", "model_config_generate_explain": "**Precise Mode**:\n\n- Strictly follows instructions to generate content\n- Suitable for scenarios requiring accuracy, such as formal documents and code\n\n**Balanced Mode**:\n\n- Seeks a balance between innovation and precision\n- Suitable for most daily applications, generating content that is interesting yet rigorous\n\n**Creative Mode**:\n\n- Encourages creativity and provides unique ideas\n- Suitable for scenarios requiring inspiration and unique perspectives, such as brainstorming and creative writing\n\n**Custom Mode**:\n\n- Allows users to customize the generation method through advanced settings\n- Enables fine-tuning based on specific needs, achieving personalized optimization", "model_config_generate_precise": "Precise", "model_config_history_json": "JSON", "model_config_history_markdown": "<PERSON><PERSON>", "model_config_history_round": "Number of context rounds included", "model_config_history_round_explain": "You can set the number of chat history rounds to include in the model's context. More rounds increase relevance in multi-round chats but consume more tokens.", "model_config_history_text": "Text", "model_config_model": "Model", "model_config_response_format": "Resposne format", "model_config_response_format_explain": "**Output Format**:\n\n- **Text**: Replies in plain text format\n- **Markdown**: Uses Markdown format for replies\n- **JSON**: Uses JSON format for replies", "model_config_title": "Model settings", "model_family": "Model family", "model_form_fail_retry": "Try again", "model_form_fail_text": "Network error,", "model_info": "Model Info", "model_list_ensure_service_quality": "To ensure service quality, newly released models will be prioritized for Pro users", "model_list_free": "Limited Usage", "model_list_model_company": "{company}", "model_list_model_deprecation_date": "Will be deprecated on {date}", "model_list_model_deprecation_notice": "The currently selected model will be deprecated", "model_list_model_setting": "{model} Setting", "model_list_model_switch_announcement": "The currently selected \"{model_deprecated}\" will be deprecated on {date}, and will automatically switch to \"{model_up}\"", "model_list_upgrade_button": "Upgrade to Pro Version", "model_list_upgrade_to_pro_advanced_tips": "Exclusive to Pro users: Be the first to experience the latest AI breakthroughs", "model_list_upgrade_to_pro_version": "Upgrade to Pro Version, Experience New Models First\n ", "model_list_upgrade_to_pro_version_advancedModel": "Upgrade to Pro Version, Experience Advanced Models First\n ", "model_list_willDeprecated": "EOL", "model_max_token_alert": "If the agent response is abnormally interrupted, please click on agent model settings, increase the max response length, and republish the agent (typically needs to be more than 1024)", "model_name": "Model Name", "model_not_supported": "The following features are not supported by the current model", "model_selection": "Model selection", "model_setting_alert": "Note: Values less than 0 may cause timeouts when invoking plugin knowledge bases or other functions!", "model_setting_alert_2": "Note: Setting the value too small may result in function call request timeouts!", "model_support_poor": "The following features have poor performance with the current model", "model_support_poor_warning": "Please note that {modelName} has poor support for the following features", "modules_menu_guide": "🚀 On demand selection of functional modules for display, making agent development more efficient!", "modules_menu_guide_gotit": "Got it", "modules_menu_guide_warning": "Unable to hide modules with added content", "monetization": "User message billing", "monetization_des": "When enabled, the user covers the credit costs; when disabled, the bot creator covers the credit costs.", "monetization_off": "User message billing (off)", "monetization_off_des": "User message billing is disabled. The bot creator covers the credit costs. When enabled, the user covers the credit costs.", "monetization_on": "User message billing (on)", "monetization_on_des": "User message billing is enabled. The user covers the credit costs, and a bill will be sent when the user's balance is depleted. When disabled, the bot creator covers the credit costs. ", "monetization_on_viewbill": "Bill preview", "monetization_publish_fail": "User message billing failed", "monetization_publish_off": "Off", "monetization_publish_on": "On", "monetization_support": "Support platforms", "monetization_support_tips": "Supported platforms", "move_desc1": "Note: Agents using the professional version of the Ark model need to manually switch the model after migration, otherwise it cannot be used normally", "move_failed": "Migration failed", "move_failed_btn_cancel": "Cancel migration", "move_failed_btn_force": "Force migrate agent", "move_failed_cancel_confirm_content": "Even if the migration is canceled, the resources that have already been migrated will remain in the new team space, but the agent and any resources that failed to migrate will stay in the original team space.", "move_failed_cancel_confirm_title": "Are you sure you want to cancel the agent migration?", "move_failed_desc": "The agent has not been migrated to the new space due to the migration failure of the following resources. Please click the button below to retry. If you click \"Force migrate agent\", the resources that failed to migrate will remain in the original space, but the agent will be migrated to the new space.", "move_failed_force_confirm_content": "After direct migration, resources that fail to migrate will remain in the original space, while the agent will be moved to the new team space.", "move_failed_force_confirm_title": "Are you sure you want to directly migrate the agent to the new team space?", "move_failed_toast": "Agent migration failed. Please click on the agent card for more details.", "move_not_allowed_contain_bot_nodes": "Sorry, the current agent includes sub-agents and cannot be migrated. To migrate the agent, please remove the sub-agents from it, or migrate the sub-agents to the same target team space first.", "multiagent_node_scenarios_context_default": "Used for a \"FUNCTION\". It helps you solve problems of a \"SCENARIO\".", "multiagent_shortcut_modal_specify_node": "Specify a node to answer", "multiagent_shortcut_modal_specify_node_option_do_not_specify": "Do not specify", "multimodal_upload_file": "Upload failure", "name_already_taken": "The name has already been taken.", "navi_bar_account_settings": "Account", "navigation_store": "Store", "navigation_workspace": "Workspace", "navigation_workspace_develop": "Development", "navigation_workspace_favourites": "Favorites", "navigation_workspace_favourites_cancle": "Remove from favorites", "navigation_workspace_library": "Library", "navigation_workspace_task": "Task Management", "neutral_age_gate_confirm": "Confirm", "new_db_001": "Database", "new_db_002": "Add database successfully", "new_db_003": "Remove database successfully", "new_pat_1": "New Personal Access Token", "new_pat_reminder_1": "This token is displayed only once. Please save this key somewhere safe and accessible. Do not share or expose it in browsers or other client codes.", "next": "Next", "no_api_token_1": "No personal access token yet", "no_mock_yet": "No Mock yet", "no_mockset_yet": "No mockset yet", "node_http_api": "API", "node_http_api_tooltips": "API URL", "node_http_auth": "Authentication", "node_http_auth_basic": "Basic Auth", "node_http_auth_bearer": "<PERSON><PERSON>", "node_http_auth_custom": "Custom", "node_http_auth_desc": "Verify the identity and permissions of the requester", "node_http_body": "Request body", "node_http_body_binary": "binary", "node_http_body_desc": "The request body, which sets the details to be sent", "node_http_body_form_data": "form-data", "node_http_body_form_urlencoded": "x-www-form-urlencoded", "node_http_body_json": "JSON", "node_http_body_none": "none", "node_http_body_raw_text": "raw text", "node_http_headers": "Request header", "node_http_headers_desc": "Configure HTTP request headers to pass key parameters such as authentication information and content type", "node_http_import_curl": "Import cURL", "node_http_import_curl_placeholder": "Please enter the cURL", "node_http_json_collapse": "<PERSON>de", "node_http_json_data_input": "Please enter JSON data. You can use \"{{\" to reference variables.", "node_http_json_format": "Format", "node_http_json_input": "Please enter JSON", "node_http_json_invalid_var": "Invalid variables found in the JSON", "node_http_json_required": "JSON cannot be empty", "node_http_name_rule": "The name can contain only letters, digits, hyphens (-), or underscores (_), and start with a letter or underscore (_)", "node_http_raw_text_input": "Please enter raw text. You can use \"{{\" to reference variables.", "node_http_raw_text_invalid_var": "Invalid variables found in the raw text", "node_http_request_params": "Request parameter", "node_http_request_params_desc": "Configure the details of the specified parameter when calling the API", "node_http_response_data": "API response", "node_http_retry_count": "Number of retries", "node_http_timeout_setting": "Timeout settings (in seconds)", "node_http_url_input": "Please enter the API URL. You can use \"{{\" to reference variables.", "node_http_url_invalid_var": "Invalid variables found in the URL", "node_http_url_length_limit": "The URL cannot exceed 10,000 characters in length", "node_http_url_required": "URL cannot be empty", "node_http_var_infer_delete": "Invalid variable", "not_show_again": "Don't Show Again", "not_support_edit_1": "Not support edit expired token", "not_supported": "Unsupported", "not_supported_explain": "{modelName} doesn't support this feature. Disable it or switch models.", "not_supported_explain_toolName": "{modelName} doesn't support {toolName}. Disable it or switch models.", "ocean_deploy_list_pkg_version": "Version", "open_source_login_placeholder_email": "Please enter your email address", "open_source_login_placeholder_password": "Please enter your password", "open_source_login_welcome": "Welcome to Coze - Community Edition", "open_source_terms_linkname": "open source license", "openapi_(fill_in_yaml)_*": "openapi (input yaml)*", "opening_question_placeholder": "Enter the opening question", "opening_showall": "Show All", "opening_showall_explain": "When enabled, all preset questions in the opening dialog will be displayed in sequence; otherwise, three random preset questions will be displayed vertically.", "opening_showall_explain_demo_off": "Disabled example", "opening_showall_explain_demo_on": "Enabled example", "operation_cannot_be_reversed": "This operation will not be reversed", "or": "or", "parameters": "Parameters", "pat_reminder_1": "Personal access tokens for other applications and platforms. For details, see:", "pat_reminder_2": "For the security of your account, do not share your personal access token or expose it in browsers or other client codes, as it may be automatically disabled.", "path_has_duplicates": "{path} has{num, plural, =1{one duplicate} other {# duplicates}}", "pdf_encrypted": "PDF file is encrypted and upload failed", "people_using_mockset_delete": "{num, plural, =1 {One person is} other {# people are}} currently using this Mockset. Delete the mockset？", "performance_knowledge": "Knowledge", "permission_manage_modal_cancel_auth_confirm_modal": "Confirm to cancel authorization", "permission_manage_modal_empty_list": "No authorized OAuth plugins", "permission_manage_modal_reauth_hint": "To re-authorize, please trigger the authorization again in chat", "permission_manage_modal_tab_name": "Permission", "permission_manage_modal_table_action": "Action", "permission_manage_modal_table_name": "Name", "permission_manage_modal_title": "Manage O<PERSON><PERSON>", "permission_manage_modal_title_hover_tip": "OAuth plugins allows third-party applications to access specific resources of a user's account without sharing the user's password. ", "photo-size-limit": "{fileName} size over 20MB", "pic_not_supported": "Currently, picture parsing of DOC type documents is not supported.", "platform_name": "<PERSON><PERSON>", "platfrom_trigger_creat_name": "Name", "platfrom_trigger_dialog_trigge_icon": "Triggers", "platfrom_triggers_title": "Triggers", "please_enter_mockset_name": "Please enter the mockset name.", "please_select_an_authorization_method": "Please select authorization method", "plugin_Parametename_error": "Please enter valid characters. Only ASCII characters such as English letters, numbers, and common symbols are valid.", "plugin_Parameter_des": "Please enter the parameter description", "plugin_Parameter_name_error": "Parameter name is already taken", "plugin_Parameter_type": "Please select parameter type", "plugin_Update": "Update plugin", "plugin_api_list_table_Create_time": "Creation time", "plugin_api_list_table_Parameter": "Input parameter", "plugin_api_list_table_action": "Actions", "plugin_api_list_table_botUsing": "Agents using", "plugin_api_list_table_debugicon_tooltip": "Debug", "plugin_api_list_table_name": "Tool list", "plugin_api_list_table_status": "Debugging status", "plugin_api_list_table_toolname": "Tool name", "plugin_api_type_fail": "Failed", "plugin_api_type_pass": "Pass", "plugin_auth_method_service_api_key": "Service token / API key", "plugin_auth_method_service_zti": "Bytedance Zero Trust Identity", "plugin_bot_ide_output_param_enable_tip": "When disabled, the output parameter will not be visible when returned", "plugin_bot_ide_plugin_setting_icon_tip": "Edit parameters", "plugin_bot_ide_plugin_setting_modal_input_param_title": "Input parameters", "plugin_bot_ide_plugin_setting_modal_item_enable_tip": "When a parameter is disabled, the LLM will not be able to see this parameter. If the parameter has a default value and is disabled, the agent will only use this value when the plugin is called.", "plugin_bot_ide_plugin_setting_modal_output_param_title": "Output parameters", "plugin_category_auto_suggestion": "Smart suggestion", "plugin_create": "Create plugin", "plugin_create_action_btn": "Actions", "plugin_create_draft_desc": "When registering for this plugin, please comply with regulations related to privacy, copyright, and data security.", "plugin_create_guide_link": "Related guidelines", "plugin_create_header_list_title": "Header list", "plugin_create_modal_safe_error": "Please check that the plugin's name, description and icon do not contain inappropriate content.", "plugin_creation_create_tool_in_ide": "Create tool in IDE", "plugin_creation_method": "Plugin creation method", "plugin_creation_method_cloud_plugin_use_existing_services": "Cloud Plugin - Create based on existing services", "plugin_creation_method_hover_tip_using_existing_services_desc": "Configure self-developed/published API as plugin", "plugin_creation_method_hover_tip_using_existing_services_title": "Create tools with external APIs:", "plugin_creation_select_creation_method_warning": "Please select a method to create a plugin.", "plugin_detail_edit_modal_title": "Edit Plugin Configuration", "plugin_detail_view_modal_title": "Plugin Configuration", "plugin_edit_tool_default_value_array_edit_button": "Edit", "plugin_edit_tool_default_value_array_edit_modal_title": "Default value config", "plugin_edit_tool_default_value_config_item_default_value": "Default value", "plugin_edit_tool_default_value_config_item_enable": "Enable", "plugin_edit_tool_default_value_config_item_enable_disable_tip": "This parameter is required. This button will be usable when a default value is set", "plugin_edit_tool_default_value_config_item_enable_tip": "When a parameter is set to invisible, agenth the plugin user and the LLM cannot see this parameter. If the parameter has a default value and is also set to invisible, the agent automatically uses this value when calling the plugin.", "plugin_edit_tool_default_value_input_placeholder": "Please fill in the default value", "plugin_edit_tool_edit_example": "Edit Example", "plugin_edit_tool_oauth_enabled_status_auth_disabled": "Authentication is deactivated", "plugin_edit_tool_oauth_enabled_status_auth_optional": "Authentication is optional", "plugin_edit_tool_oauth_enabled_status_auth_required": "Authentication is required", "plugin_edit_tool_oauth_enabled_title": "Oauth-standard settings", "plugin_edit_tool_oauth_enabled_title_hover_tip": "By deactivating Oauth, agent users will bypass authentication whenever an agent calls with this tool.", "plugin_edit_tool_output_param_enable_tip": "When disabled, the output parameter will not be visible when returned", "plugin_edit_tool_test_run_cancel_example": "The current example will be removed when you uncheck the box and republish", "plugin_edit_tool_test_run_debugging_example": "Debugging results", "plugin_edit_tool_test_run_example_tip": "Run example", "plugin_edit_tool_test_run_save_results_as_example": "Save the debugging result as an example", "plugin_edit_tool_title": "Edit tool", "plugin_edit_tool_view_example": "View Example", "plugin_empty": "Please enter the content", "plugin_empty_desc": "No tools found", "plugin_empty_description": "Create a tool with your own API and use it in the agent", "plugin_exception": "An exception occurred in the service", "plugin_feedback_entry_button": "Submit feedback", "plugin_feedback_entry_tip": "Couldn't find the plugin you're looking for?", "plugin_feedback_error_tip_empty_content": "Feedback content is empty", "plugin_feedback_error_tip_no_official_plugin_choosen": "No official plugin is choosen", "plugin_feedback_modal_choose_official_plugin": "Select official plugin", "plugin_feedback_modal_feedback_content": "Feedback content", "plugin_feedback_modal_feedback_content_placeholder": "Please enter the feedback content", "plugin_feedback_modal_request_type": "Request type", "plugin_feedback_modal_request_type_feedback_to_existing_plugin": "Feedback on functionality of existing official plugins", "plugin_feedback_modal_request_type_official_plugins_not_found": "Unable to find the plugin", "plugin_feedback_modal_tip_submission_success": "Submitted successfully. Thanks for your feedback!", "plugin_feedback_modal_title": "Plugin feedback", "plugin_file_max": "You can upload up to 20 files", "plugin_file_unknown": "Unknown type", "plugin_file_upload": "Upload files", "plugin_file_upload_image": "Upload images", "plugin_file_upload_mention": "Please upload files", "plugin_file_upload_mention_image": "Please upload images", "plugin_file_uploading": "Uploading…", "plugin_form_add_child_tooltip": "Add child", "plugin_form_no_result_desc": "No result", "plugin_imported_successfully": "Plugin imported successfully", "plugin_location_header": "Header", "plugin_location_header1": "It indicates that the key is in the header of the HTTP request. Example: In the request header.", "plugin_location_header2": "Authorization: Bearer your_token_here", "plugin_location_info_all": "It controls the path where the key is transmitted to the server", "plugin_location_info_query": "Query", "plugin_location_info_query1": "It indicates that the key is a part of the URL. Example:", "plugin_location_info_query2": "https://api.example.com/data?key=your_key_here", "plugin_mark_created_by_existing_services": "Third party services", "plugin_mark_created_by_ide": "<PERSON><PERSON> hosting", "plugin_metric_average_time": "Average time", "plugin_metric_bots_using": "Agents referenced", "plugin_metric_success_rate": "Success rate", "plugin_metric_usage_count": "Called plugins", "plugin_name_conflict_error": "Duplicate plugin name", "plugin_parameter_create_modal_safe_error": "Please check that the parameter's name, description do not contain inappropriate content.", "plugin_publish_form_version": "Version", "plugin_publish_form_version_desc": "Version description", "plugin_quote_tip_1": "Plugins may be provided by third parties. You can view the plugin's publisher on the Plugin detail page.", "plugin_quote_tip_2": "After activation, your Agent may send end-user information, such as conversations, to the activated Plugins. ", "plugin_quote_tip_3": "Your Agent will automatically trigger the Plugins as needed during conversations with end users.", "plugin_s3_Parse": "Parse output parameters", "plugin_s3_Parsing": "Parsing", "plugin_s3_failed": "Output parameter parsing failed, please try again", "plugin_s3_running": "Running…", "plugin_s3_success": "Output parameter parsing completed", "plugin_s4_debug_detail": "Reasons", "plugin_s4_debug_empty": "The debugging result is displayed here. After debugging is passed, you can proceed to the next step.", "plugin_s4_debug_failed": "Debugging failed", "plugin_s4_debug_pass": "Debugging passed", "plugin_service_status": "Service status", "plugin_service_status_offline": "Offline", "plugin_service_status_online": "Online", "plugin_store_authorized": "Authorized", "plugin_store_contact_deployer": "Please contact the service deployer to configure authorization", "plugin_store_unauthorized": "unauthorized", "plugin_team_edit_tip_another_user_is_editing": "Another user is currently editing. You may continue your operations until the editing is done.", "plugin_team_edit_tip_unable_to_edit": "Currently unable to edit", "plugin_tool_config_auth_modal_auth_required": "This tool requires your authorization", "plugin_tool_config_auth_modal_auth_required_desc": "Redirect to third party pages to complete authorization", "plugin_tool_config_auth_modal_cancel_confirmation": "Confirm cancelling your authorization\n", "plugin_tool_config_auth_modal_cancel_confirmation_desc": "Your authorization will be cancelled and deleted", "plugin_tool_config_status_authorized": "Authorized", "plugin_tool_config_status_unauthorized": "Unauthorized\n", "plugin_tool_create_modal_safe_error": "Please check that the tool's name, description do not contain inappropriate content.", "plugin_tool_exists_tips": "{num, plural, =1{one tool} other{# tools}} with the same path already exists", "plugin_tool_import_succes": "Tool imported successfully", "plugin_tool_replace_success": "Tools replaced successfully", "plugin_tooltip_url": "Requests specific resources via URL. It retrieves data. Example:", "plugin_type_app": "APP", "plugin_type_func": "FUNC", "plugin_type_plugin": "PLUGIN", "plugin_type_workflow": "WORKFLOW", "plugin_update_tip": "The plugin has been changed, but until it is published, the agent will continue to use the plugin from the previous published version.", "plugin_usage_limits_modal_got_it_button": "Got it", "plugin_usage_limits_modal_table_header_plugin": "Plugin", "plugin_usage_limits_modal_table_header_price": "Price", "plugin_usage_limits_modal_title": "Plugin usage limits", "plugin_usage_limits_modal_view_details": "View details", "plugins_with_limited_calls_added_tip": "Plugins with limited calls are added.", "poorly_supported_explain_toolName": "{modelName} doesn't support {toolName}. Disable it or switch models.", "pop_edit_save_confirm": "Changes you made may not be saved if you leave this page.", "pop_up_button_refresh": "Refresh", "pop_up_description_data_conflict": "The content you've edited has been modified by another user. Please refresh the page to get the latest content.", "pop_up_title_data_conflict": "Refresh and try again", "premium_monetization_config": "User message billing settings", "privacy_link_placeholder": "www.example.com/privacy", "professional_plan_n_paid_plugins_included_in_bot": "There are {count} paid plugins used in the agent. Please see Coze API for more details.", "professional_plan_n_paid_plugins_included_in_workflow": "There are {count} paid plugins used in the workflow. Please see Coze API for more details.", "profile_entrance": "Profile", "profile_memory_sample_description_address": "Save user's address in the format of \"Street Address - City - State\"", "profile_memory_sample_description_height": "Save user's height in centimeters (cm)", "profile_memory_sample_description_mobile": "Save user's phone number", "profile_memory_sample_description_name": "Save user's name", "profile_memory_sample_description_weight": "Save user's weight in kilograms (kg)", "profile_settings": "Settings", "profilepicture_hover_failed": "Generate failed", "profilepicture_hover_generated": "Generated", "profilepicture_hover_generating": "Generating", "profilepicture_popup_async": "If you don’t want to wait here, you can click <PERSON><PERSON> to close the pop-up window. You will see a prompt when completed.", "profilepicture_popup_cancel": "Cancel", "profilepicture_popup_generate": "Generate", "profilepicture_popup_generategif_default": "Use picture as the first frame and the prompt to generate GIF.Please use complete sentences. For example, a kawaii squirrel is surfing and dancing on the board.", "profilepicture_popup_toast_daymax_gif": "You have reached the limit of 10 GIFs generated per day.", "profilepicture_popup_toast_daymax_image": "You have reached the limit of 20 images generated per day.", "profilepicture_popup_toast_picturemax": "Please remove unnecessary images or gifs and then continue.", "profilepicture_toast_failed": "Generation failed", "profilepicture_toast_generated": "Generation succeed", "project_241115": "Modify the description", "project_conversation_list_batch_delete_btn": "Delete the selected {len} session(s)", "project_conversation_list_batch_delete_tooltip": "Batch delete sessions", "project_conversation_list_batch_delete_tooltip_context": "This will delete {len} dynamic session(s). Messages within the sessions will also be deleted. This action cannot be undone. Are you sure you want to delete?", "project_conversation_list_delete_all_tooltip_context": "Delete all dynamic sessions created from session nodes. This action cannot be undone.", "project_conversation_list_dynamic_title": "Session node creation", "project_conversation_list_operate_batch_tooltip": "Batch operations", "project_conversation_list_static_title": "Conversation list", "project_delete_permission_tooltips": "Only the project creator can delete.", "project_ide_cancel": "Cancel", "project_ide_create_duplicate": "Create copy", "project_ide_delete": "Delete", "project_ide_delete_confirm": "Are you sure you want to delete the project?", "project_ide_delete_confirm_describe": "Deletion is irreversible. Please proceed with caution. If you are sure to delete, please enter the project name below.", "project_ide_delete_project": "Delete app", "project_ide_duplicate": "Create copy", "project_ide_duplicate_loading": "Creating copy…", "project_ide_edit_project": "Edit app", "project_ide_frame_plugin": "Plugin", "project_ide_frame_publish": "Publish", "project_ide_info_created_on": "Created at {time}", "project_ide_maximize": "Full screen", "project_ide_project_name": "Project name", "project_ide_quit": "Exit", "project_ide_restore": "<PERSON>de", "project_ide_tab_title": "{project_name} - App - <PERSON>ze", "project_ide_tabs_close": "Close", "project_ide_tabs_close_all": "Close all", "project_ide_tabs_close_other_tabs": "Close other tabs", "project_ide_tabs_open_on_left": "Open in left split screen", "project_ide_tabs_open_on_right": "Open in right split screen", "project_ide_toast_delete_success": "Project deleted", "project_ide_toast_duplicate_fail": "Failed to create the copy", "project_ide_toast_duplicate_fail_retry": "Retry", "project_ide_toast_duplicate_success": "Copy created", "project_ide_toast_duplicate_view": "View", "project_ide_toast_edit_success": "Updated successfully", "project_ide_unsaved_changes": "Unsaved changes", "project_ide_unsaved_describe": "Some changes are not saved. Are you sure you want to exit?", "project_ide_view_document": "View document", "project_ide_welcome_db_describ": "Preset knowledge (RAG) and database to integrate private data in your AI applications", "project_ide_welcome_db_title": "Configure database and knowledge", "project_ide_welcome_describe": "Start with <PERSON><PERSON> and develop your AI application now.", "project_ide_welcome_title": "Hi, Welcome to Coze App IDE", "project_ide_welcome_ui_builder_describe": "Drag-and-drop components for easy interactive UI building", "project_ide_welcome_ui_builder_title": "UI Builder (Coming Soon)", "project_ide_welcome_workflow_describe": "Offer nodes like LLMs, APIs, and flow control for flexible development via workflows", "project_ide_welcome_workflow_title": "Build backend with workflows", "project_plugin_delete_modal_description": "Please operate with caution. Once deleted, it cannot be recovered.", "project_plugin_delete_modal_title": "Confirm deleting {pluginName}?\n", "project_plugin_delete_success_toast": "Plugin deleted successfully", "project_plugin_setup_metadata_cancel": "Cancel", "project_plugin_setup_metadata_edit": "Edit", "project_plugin_setup_metadata_more_info": "More info", "project_plugin_setup_metadata_save": "Save", "project_plugin_testrun": "Test Run", "project_publish_select_desc_compliance_new": "By publishing your app on the following platforms, you fully understand and agree to abide by {publish_terms_title} (including, but not limited to, any privacy policy, community guidelines, data processing agreement, etc.).", "project_release_Please_select": "Please select chatflow", "project_release_already_released": "Published successfully", "project_release_already_released_desc": "You can close this page during publishing. For status, go to the app details page.", "project_release_api1": "API or SDK", "project_release_api_sdk_desc": "Publish workflows as APIs or chatflows as SDKs to embed in your apps.", "project_release_cancel": "Unpublish", "project_release_cancel1": "Unpublish?", "project_release_cancel1_desc": "The current published app will be invalid", "project_release_channel": "Channel review and publishing", "project_release_chatflow2": "Message processing chatflow", "project_release_chatflow3": "The chatflow has been deleted. Please select again.", "project_release_chatflow4": "Please select a message processing chatflow first", "project_release_chatflow_choose": "Message processing chatflow", "project_release_coze1": "<PERSON><PERSON>", "project_release_coze_audit": "Coze review", "project_release_display_label": "Display as", "project_release_download_code": "Code-based", "project_release_example": "Sample version: V0.0.1", "project_release_example1": "Current version: {version}", "project_release_example2": "Please enter a version number", "project_release_example3": "The version number already exists", "project_release_failed": "Failed", "project_release_finish": "Completed", "project_release_guide": "Installation Guide", "project_release_h5_desc": "You can publish your project to WeChat or Douyin mini programs, or by downloading a code package.", "project_release_in_progress": "In progress", "project_release_miniprogram1": "Mini Program", "project_release_not_pass": "Fail", "project_release_notify": "We recommend that you perform a complete test run for your app to ensure the expected results.", "project_release_open_in_store": "Use Now", "project_release_pack_fail_reason": "Failed to package the following resources. Please check and try again.", "project_release_package": "Packaging", "project_release_package_failed": "Packaging failed", "project_release_pass": "Pass", "project_release_select_chatflow": "Please select workflow", "project_release_set_desc": "Please configure/authorize first", "project_release_social1": "Social platform", "project_release_social_desc1": "Publish the chatflow to the social platform.\nYou need to select the chatflow that receives user messages. When the user sends a message on the social platform, the chatflow will be called to receive the user message. All output nodes in the app will be replied to the user.", "project_release_stage": "Status", "project_release_success": "Published", "project_release_template_info": "Configure template", "project_release_template_info_category": "Template category", "project_release_template_info_desc": "Template description", "project_release_template_info_display": "Display mode", "project_release_template_info_info": "Template introduction", "project_release_template_info_name": "Template name", "project_release_template_info_not": "Please enter {template_info_type}", "project_release_template_info_poster": "Template cover", "project_release_ts_desc": "Publish your app as a template or to the store.", "project_release_version_info": "Version", "project_releasing": "Publishing…", "project_resource_modal_copy_to_project": "Copy to App", "project_resource_modal_library_resources": "Library {resource}", "project_resource_modal_project_resources": "App {resource}", "project_resource_sidebar_confirm_batch_delete": "Confirm to delete the following {count} files? Please operate with caution. Once deleted, they cannot be recovered.", "project_resource_sidebar_confirm_delete": "Confirm to delete {resourceName}? Please operate with caution. Once deleted, it cannot be recovered.", "project_resource_sidebar_copy": "Copy", "project_resource_sidebar_copy_to_library": "Copy to Resource Library", "project_resource_sidebar_create_new_folder": "New Folder\n", "project_resource_sidebar_create_new_resource": "New {resource}", "project_resource_sidebar_data_section": "Data", "project_resource_sidebar_delete": "Delete", "project_resource_sidebar_disable_resource": "Disable {resource}", "project_resource_sidebar_enable_resource": "Enable {resource}", "project_resource_sidebar_import_from_library": "Import from Resource Library", "project_resource_sidebar_move_to_library": "Move to Resource Library", "project_resource_sidebar_please_enter": "Please enter", "project_resource_sidebar_rename": "<PERSON><PERSON>", "project_resource_sidebar_resource_not_added": "{resource} has not been added yet.", "project_resource_sidebar_title": "Resources", "project_resource_sidebar_warning_empty_key": "Empty key", "project_resource_sidebar_warning_label_exists": "{label} already exists", "project_resource_sidebar_warning_length_exceeds": "Length exceeds\n", "project_store_search": "App", "project_toast_copy_failed": "project_toast_copy_failed", "project_toast_copy_successful": "Copy successful", "project_toast_copying_resource": "Copying {resourceName}", "project_toast_only_published_resources_can_be_imported": "Only published resources can be imported to App", "project_toast_successfully_imported_from_library": "project_toast_successfully_imported_from_library", "prompt_detail_copy_prompt": "Copy prompt", "prompt_detail_prompt_detail": "Prompt details", "prompt_generate_instruction_validate": "Statement is required", "prompt_generate_statement_validate": "Statement is required", "prompt_generate_stop": "Stop enriching", "prompt_generate_stop_responding": "Stop Responding", "prompt_library_edit": "Edit", "prompt_library_empty_describe": "Click the button below to create prompt resources", "prompt_library_empty_title": "There are no available prompt resources in this space", "prompt_library_new_prompt": "New prompt", "prompt_library_prompt_copied_successfully": "Prompt copied", "prompt_library_prompt_creat_successfully": "Prompt created", "prompt_library_prompt_empty": "Empty prompt", "prompt_library_prompt_library": "Prompt library", "prompt_library_unselected": "Please select the prompt you want to preview in the list on the left", "prompt_optimization_button": "Optimize", "prompt_optimization_button_hover_tooltip": "Auto-optimize prompt", "prompt_resource_delete_describ": "Once deleted, this operation cannot be undone. However, it does not affect the agents or workflows that have used this prompt.", "prompt_resource_insert_prompt": "Insert prompt", "prompt_resource_personal": "Personal", "prompt_resource_recommended": "Recommended", "prompt_resource_team": "Team", "prompt_resource_view_all": "All", "prompt_submit": "Submit", "prompt_version_submit": "The version will take effect immediately after submit. Are you sure you want to submit it?", "publish_audit_pop7": "The content does not comply with [\"Coze platform publishing guidelines\"](https://www.coze.com/docs/guides/coze_community_guidelines?_lang=en), please modify it and try again.", "publish_base_configFields": "Configure Base Input Form", "publish_base_configFields_ unfinished_toast": "Please complete all required information", "publish_base_configFields_complete_Information_describe": "Please enter the required information to publish it to the Base Shortcut Center", "publish_base_configFields_complete_Information_edit": "Edit", "publish_base_configFields_complete_Information_fill_out": "Enter", "publish_base_configFields_complete_Information_title": "Complete the information to publish the shortcut", "publish_base_configFields_component": "Control", "publish_base_configFields_component_placeholder": "Please select a control", "publish_base_configFields_dataType_placeholder": "Select a data type", "publish_base_configFields_field": "Field", "publish_base_configFields_invalid": "Invalid", "publish_base_configFields_key": "Key", "publish_base_configFields_key_placeholder": "Enter the key in the object", "publish_base_configFields_placeholder": "Placeholder", "publish_base_configFields_placeholder_placeholder": "Enter placeholder", "publish_base_configFields_requiredWarn": "Required", "publish_base_configFields_status_completed": "Completed", "publish_base_configFields_title": "Title", "publish_base_configFields_title_placeholder": "Please enter a form title", "publish_base_configStruct_dataType": "Data Type", "publish_base_configStruct_id": "ID", "publish_base_configStruct_primary": "Primary Attribute", "publish_base_config_configBaseInfo": "Configure Basic Information for Base Shortcuts", "publish_base_config_configFeishuBase": "Configure Base", "publish_base_config_configOutputType": "Shortcut output data type", "publish_base_config_needReconfigure": "Some variables have changed. Please adjust the configuration.", "publish_base_config_structOutputConfig": "Object output field configuration", "publish_base_inputFieldConfig_fieldSelector": "Field selector", "publish_base_inputFieldConfig_maxChars": "Max. characters", "publish_base_inputFieldConfig_multiSelect": "Multiple-select", "publish_base_inputFieldConfig_options": "Option", "publish_base_inputFieldConfig_singleSelect": "Single-select", "publish_base_inputFieldConfig_supports": "Supported data types", "publish_base_inputFieldConfig_textInput": "Text input box", "publish_douyin_config_ing": "Authorizing", "publish_list_header_status": "Status", "publish_result_all_failed": "Publish failed", "publish_success": "Publication submitted!", "publish_terms_title": "Terms of service for each publishing channel", "publish_tooltip_select_category": "Please select a category in the Agent Store", "publish_tooltip_select_platform": "Please select a publishing channel", "publish_wechat_old_disconnect": "According to WeChat requirements, please bind the Agent and publish it to the currently selected new version of WeChat official account (service number) ASAP. The new version uses WeChat third-party platform access, which is simpler to configure and more secure and reliable to authorize. Before operation, please untie the \"[to be offline] WeChat official account (service number) \" channel at the button.", "publish_wechat_old_disconnect_title": "Please untie the original \"[to be offline] WeChat official account (service number) \" release channel first.", "publisher_market_public_disabled": "Agents which contain private resources cannot currently be set as public. Private resources include knowledge or workflows that were created in team spaces, or plugins that have not been submitted to the store.", "python": "Python", "query_analytics_bar_chart": "Bar chart", "query_analytics_intent": "Intent", "query_analytics_intent_rank": "Intent Ranking", "query_analytics_level_one": "First level classification", "query_analytics_level_two": "Second level classification", "query_analytics_pie_chart": "Pie chart", "query_analytics_skill_rank": "Triggered Skills Ranking", "query_card_id": "CardID", "query_data_empty": "No data found", "query_detail_tip_copy": "Copy", "query_detail_title_input": "Input", "query_detail_title_output": "Output", "query_flamethread": "Flamethread", "query_latency": "Latency {duration}", "query_list_loadmore": "Load More", "query_node_details": "Node Details", "query_query_analytics": "Query analytics", "query_run_tree": "Run Tree", "query_select_batch": "Select which batch result to view", "query_status_all": "All", "query_status_broken": "Broken", "query_status_completed": "Completed", "query_status_error": "Error", "query_status_failed": "Run failed", "query_status_success": "Success", "query_status_unknown": "Unknown", "query_stream_output": "Stream Res", "query_tokens_number": "{number} Tokens", "quote_ask_in_chat": "Quote", "randomlymode": "Randomly mode", "read_file_failed_please_retry": "File read failed, please upload again", "real_data": "Real Data", "recall_knowledge_no_related_slices": "No recall related slices", "recommended_failed": "No recommended  table, please add table manually.", "reference_graph_entry_button": "Reference graph", "reference_graph_modal_subtitle_view_relationship_given_workflow": "View reference of a given workflow", "reference_graph_modal_title": "Resource reference", "reference_graph_modal_title_info_hover_tip": "View all references in the workflow", "reference_graph_modal_title_info_hover_tip_explain": "A pointing to B represents A references B", "reference_graph_node_loop_tip": "The current node encounters a loop. The recursive content will not be displayed", "reference_graph_node_open_in_new_tab": "Open in new tab", "reference_graph_tag_different_version_of_same_resource": "Diff version", "reference_graph_tag_different_version_same_resource": "Same resource", "reference_graph_tip_current_workflow_has_no_reference": "The current workflow has no references", "reference_graph_tip_fail_to_load": "Fail to load", "reference_graph_tip_fail_to_load_retry_needed": "Retry", "refresh_project_tags": "Refresh", "regenerate": "Regenerate", "register": "Register", "register_success": "Register successfully", "release_analysis_by_day": "Daily", "release_analysis_by_month": "Monthly", "release_analysis_by_quater": "Quarterly", "release_analysis_by_week": "By week", "release_analysis_change_time": "Yesterday's data was updated at 10:00", "release_analysis_change_time_desc": "Please modify the statistical range of the data at the top of the page to view it.", "release_analysis_channel": "Channel analysis", "release_analysis_channel_name": "Channel", "release_analysis_chat": "Number of conversations", "release_analysis_days_compare": "Compared to {days} days", "release_analysis_lj_chat": "Cumulative number of conversations", "release_analysis_lj_desc": "* Cumulative metrics are not affected by time filtering", "release_analysis_lj_run": "Cumulative run", "release_analysis_lj_token": "Cumulative Token Consumption", "release_analysis_lj_user": "Cumulative user", "release_analysis_lj_zhibiao": "Cumulative indicator", "release_analysis_new_user": "Number of new users", "release_analysis_overview": "Overview", "release_analysis_performance": "Performance", "release_analysis_performance_average": "Average response duration", "release_analysis_performance_average_desc": "Average runtime of interface requests", "release_analysis_performance_error_code": "Error code statistics", "release_analysis_performance_error_code_all": "View all", "release_analysis_performance_error_code_count": "Error count", "release_analysis_performance_error_code_desc": "The error code, error code, and error message for the interface request are consistent with [Error Code Specification for Coze Open API] (coze.com/open/docs/developer_guides/coze_error_codes).", "release_analysis_performance_error_code_info": "Error message", "release_analysis_performance_error_code_name": "Error code", "release_analysis_performance_success_rate": "Response success rate", "release_analysis_performance_success_rate_desc": "The success rate of interface requests can be viewed in \"Error Code Statistics\" for real errors.", "release_analysis_run": "Number of runs", "release_analysis_store/template": "Store / Template", "release_analysis_time": "Last {days} days", "release_analysis_token": "Token consumption", "release_analysis_use_msg_average": "Conversations per user", "release_analysis_use_new_msg_average": "Conversations per new user", "release_analysis_user": "Number of users", "release_analysis_user_active": "Active user", "release_analysis_user_active_cnt": "Active users", "release_analysis_user_active_new_cnt": "Active new users", "release_analysis_user_from": "User source", "release_analysis_user_new_msg_cnt": "Number of new user conversations", "release_analysis_user_new_run_average": "Number of runs per new user", "release_analysis_user_new_run_cnt": "New user runs", "release_analysis_user_performance": "Customer engagement", "release_analysis_user_retention": "User retention", "release_analysis_user_retention_7_day": "7-Day user retention", "release_analysis_user_retention_7_day_DAU": "DAU - 7-day user retention", "release_analysis_user_retention_7_day_DNU": "DNU - 7-day user retention", "release_analysis_user_retention_month": "Monthly user retention", "release_analysis_user_retention_month_MAU": "MAU - monthly user retention", "release_analysis_user_retention_month_MNU": "MNU - monthly new user retention", "release_analysis_user_retention_next_day": "Next day user retention", "release_analysis_user_retention_next_day_DAU": "DAU - Next Day User Retention Rate", "release_analysis_user_retention_next_day_DNU": "DNU - next day new user retention", "release_analysis_user_run_average": "Number of runs per user", "release_management": "Publish Management", "release_management_1": "Manage", "release_management_cannot_removed": "Cannot be removed", "release_management_connector_released": "Published platforms", "release_management_detail1": "You can view details in {button} after it is published for the first time.", "release_management_generating": "Generating pages… Please check it later.", "release_management_no_permision": "You do not have permission to access the information", "release_management_no_project": "No published projects in the workspace", "release_management_only_owner": "Project owner only", "release_management_openin": "Open in channel", "release_management_project": "Project", "release_management_recent": "Last published", "release_management_removed1": "Remove", "release_management_removed2": "Remove the project from the selected {count} channel(s)", "release_management_removed_sucess": "Project removed", "release_management_removed_sucess1": "Project removed from the channel selected", "release_management_removed_sucess2": "Removed", "release_management_search1": "Search for workflows", "release_management_search_app": "Search for apps", "release_management_search_bot": "Search for agents", "release_management_search_no": "No results found for the current filters", "release_management_token": "Token consumption", "release_management_trace": "Logs", "release_management_trace_ascend": "Ascending", "release_management_trace_default_rank": "Restore default sorting", "release_management_trace_descend": "Descending", "release_management_trace_excecute_id": "Run ID", "release_management_trace_excecute_token": "Run <PERSON>", "release_management_trace_from": "Source", "release_management_trace_trigger_id": "Trigger ID", "release_management_trace_version": "Version", "release_management_trigger": "Trigger management", "release_management_trigger1": "<PERSON><PERSON>", "release_management_trigger_has": "<PERSON><PERSON> configured", "remove_dataset": "Remove knowledge", "remove_token_1": "Deleting it will affect all applications that are using the API Personal Access Token.", "remove_token_reminder_1": "Delete Personal Access Token", "replace": "Replace", "requests_the_server_to_delete_the_specified_resource__example_": "Request the server to delete specified resources. Example:", "required": "Required", "resource_copy_move_notify": "For resources that have not been testrun, the copy/move result may not meet expectations. It is recommended to operate after a complete testrun", "resource_move": "Migrate", "resource_move_bot_success_toast": "Agent migrated", "resource_move_confirm_content": "Each agent currently supports only one migration.", "resource_move_confirm_title": "Are you sure you want to migrate the agent and all resources to the team space?", "resource_move_no_team_joined": "You haven't joined any team spaces yet, so there's no need to migrate the agent.", "resource_move_notice": "You can only migrate agents from personal spaces to team spaces now.", "resource_move_target_team": "Select target team space", "resource_move_title": "Migrate agent to Team Space - {bot_name}", "resource_move_together": "Resources to migrate together", "resource_move_together_desc": "To ensure the agent runs properly, the following resources used by the agent will also be migrated to the new space. If other agents in the original space are also using the listed resources, it is recommended to create copies of the resources before migrating. In addition, after the agent migration, \"Publish to API\" will be disabled. You will need to republish the agent following the migration.", "resource_process_modal_cancel_button": "Cancel", "resource_process_modal_retry_button": "Retry", "resource_process_modal_text_copying_process_interrupt_warning": "Closing the pop-up window or refreshing the web page will interrupt the copying process", "resource_process_modal_text_copying_resource_to_library": "Copying {resourceName} to the Resource Library...", "resource_process_modal_text_copying_resource_to_project": "Copying {resourceName} to the App...", "resource_process_modal_text_moving_process_interrupt_warning": "Closing the pop-up window or refreshing the web page will interrupt the moving process", "resource_process_modal_text_moving_resource_to_library": "Moving {resourceName} to the Resource Library...", "resource_process_modal_title_copy_resource_to_library": "Copy to the Resource Library", "resource_process_modal_title_import_resource_from_library": "Import from Resource Library", "resource_process_modal_title_move_resource_to_library": "Move to the Resource Library\n", "resource_toast_copy_to_library_fail": "Failed to copy. Please retry", "resource_toast_copy_to_library_success": "Successfully copied to the Resource Library", "resource_toast_copy_to_project_fail": "Failed to import. Please retry", "resource_toast_move_to_library_fail": "Move failed, please retry", "resource_toast_move_to_library_success": "Successfully moved to the Resource Library", "resource_toast_view_resource": "View", "resource_type_database": "database", "resource_type_knowledge": "knowledge", "response": "Response", "retry": "Retry", "review_agent_suggestreplyinfo": "auto-suggestion", "review_bot_Onboarding_suggested_questions": "Opening dialog preset problem", "review_bot_database": "Database", "role_info.description": "Role Description", "scene_beta_sign": "Beta", "scene_edit_roles_create_name": "Role name", "scene_edit_roles_list_nickname_empty_seat": "Empty seat", "scene_mkpl_search_title": "<PERSON><PERSON><PERSON>", "scene_resource_name": "Social scenarios", "scene_workflow_chat_message_content_placeholder": "Please edit the message", "scene_workflow_chat_message_error_content_empty": "Message to be sent cannot be empty", "scene_workflow_chat_node_conversation_batch_empty": "You must add a list of speakers", "scene_workflow_chat_node_conversation_content_speaker": "Speaker", "scene_workflow_chat_node_conversation_content_speaker_fixed": "Fixed content", "scene_workflow_chat_node_conversation_content_speaker_fixed_content": "Content", "scene_workflow_chat_node_conversation_content_speaker_fixed_placeholder": "You can use the {{variable name}} method to introduce variables from input parameters.", "scene_workflow_chat_node_conversation_content_speaker_generate": "Generate by agent", "scene_workflow_chat_node_conversation_content_speaker_message_type": "Message type", "scene_workflow_chat_node_conversation_content_speaker_placeholder": "Please select a speaker", "scene_workflow_chat_node_conversation_visibility_all": "All members in the scenario", "scene_workflow_chat_node_conversation_visibility_custom": "Custom", "scene_workflow_chat_node_conversation_visibility_custom_roles": "Role", "scene_workflow_chat_node_conversation_visibility_custom_variable": "Nickname Variables", "scene_workflow_chat_node_conversation_visibility_speaker": "All members who will speak in this round", "scene_workflow_chat_node_name": "Chat Arrangement", "scene_workflow_chat_node_test_run_button": "Continue running", "scene_workflow_chat_node_test_run_running": "Running…", "scene_workflow_chat_node_test_run_title": "Test Role Scheduling Node", "scene_workflow_delete_workflow_button": "Remove from {source}", "scene_workflow_delete_workflow_popup_subtitle": "When removed, references in the associated {source} will be invalidated", "scene_workflow_delete_workflow_popup_title": "Are you sure you want to remove it from {source}?", "scene_workflow_invalid": "Expired", "scene_workflow_popup_add_forbidden": "You can only add it to the current scenario after publishing", "scene_workflow_popup_delete_confirm_subtitle": "This operation cannot be undone", "scene_workflow_popup_delete_confirm_title": "Are you sure you want to delete this workflow?", "scene_workflow_popup_list_empty": "No workflows found", "scene_workflow_popup_search_empty": "No results found", "scene_workflow_popup_title": "Workflows Created in This Scenario", "scene_workflow_start_roles": "All role info", "scene_workflow_start_roles_introduce": "Member introduction", "scene_workflow_start_roles_name": "Role name", "scene_workflow_start_roles_nickname": "Member nickname", "scene_workflow_testrun_nickname_error": "The nickname can only contain English letters, digits, and underscores, and must be unique", "scene_workflow_testrun_nickname_nickname": "Nickname", "scene_workflow_testrun_nickname_placeholder": "Please enter a nickname", "scene_workflow_testrun_title": "Complete the information for vacant roles in the scenario", "scene_workflow_testrun_title_tooltip": "Some roles in the scenario are vacant. Please preset a nickname for each. Nicknames can only contain letters, digits, and underscores, and must be unique.", "scope_all": "All", "scope_self": "Self", "search_not_found": "No results found", "see_more": "See more", "select_agent_no_result": "No agents available", "select_agent_title": "Select agents for comparison debugging", "select_category": "Select category", "select_expired_time_1": "Select expiration time", "select_later": "Select later", "select_team": "Select team space", "setting_name_placeholder": "Input you name", "setting_name_save": "Save", "setting_username_empty": "Please set a username", "settings_api_authorization": "API Authorization", "settings_language_en": "English", "settings_language_zh": "Chinese", "share_conversation": "Share chat", "shortcut_Illegal_file_format": "Unable to upload file format not allowed", "shortcut_component_type_selector": "Selector", "shortcut_component_type_text": "Text", "shortcut_component_upload_component_placeholder": "Click or drop", "shortcut_modal_add_plugin_wf_complex_input_error": "Tools/Workflow with complex input parameters (object or array) is currently not supported", "shortcut_modal_add_plugin_wf_no_input_error": "Please select plugin or workflow with parameters", "shortcut_modal_button_name": "Button name", "shortcut_modal_button_name_conflict_error": "The button name is taken", "shortcut_modal_button_name_input_placeholder": "The name show on the button", "shortcut_modal_button_name_is_required": "Button name is required", "shortcut_modal_cancel": "Cancel", "shortcut_modal_component_name": "Component name", "shortcut_modal_component_plugin_wf_parameter": "Parameter", "shortcut_modal_component_type": "Component type", "shortcut_modal_components": "Components", "shortcut_modal_components_hover_tip": "If components are used, insert them into the query message. For instance, with \"topic\" and \"language\" components, the query could be \"Check out news on the topic {var1} in {var2} language\". Enter the value of \"topic\" and \"language\" when using the shortcut", "shortcut_modal_components_modal_component_type": "Component type", "shortcut_modal_components_modal_upload_component": "Upload", "shortcut_modal_confirm": "Confirm", "shortcut_modal_fail_to_add_shortcut_error": "Fail to add shortcut", "shortcut_modal_fail_to_delete_shortcut_error": "Fail to delete shortcut", "shortcut_modal_fail_to_update_shortcut_error": "Fail to update shortcut", "shortcut_modal_form_to_be_filled_up_tip": "Users needs to fill up the form to send shortcut when using components", "shortcut_modal_illegal_keyword_detected_error": "Review unsuccessful", "shortcut_modal_max_component_tip": "Up to {maxCount} components can be added at most", "shortcut_modal_please_select_file_formats_for_upload_component_tip": "Please select at least one file format for the upload component", "shortcut_modal_query_content": "Query content", "shortcut_modal_query_content_input_placeholder": "Enter the message content to be sent", "shortcut_modal_query_content_is_required": "Query is required", "shortcut_modal_query_insert_component_tip": "Insert component", "shortcut_modal_query_message_hover_tip_component_mode": "If components are used, insert them into the query message. For instance, with \"topic\" and \"language\" components, the query could be \"Check out news on the topic {var1} in {var2} language\". Enter the value of \"topic\" and \"language\" when using the shortcut", "shortcut_modal_query_message_hover_tip_component_mode_var1": "topic", "shortcut_modal_query_message_hover_tip_component_mode_var2": "language", "shortcut_modal_query_message_hover_tip_how_to_insert_components": "How to insert a component: click the top-right insert button or type { in the input", "shortcut_modal_query_message_hover_tip_send_query_mode": "If no components are used, then the query message will be sent directly", "shortcut_modal_query_message_hover_tip_title": "Query sent to your Agent when using a shortcut", "shortcut_modal_query_message_insert_component_button": "Insert component", "shortcut_modal_query_message_max_length_reached_error": "The max length of a query message cannot exceed 3000 characters", "shortcut_modal_query_message_placeholder": "Enter the message content to be sent including all of the component names with the format of {{name}}. You can also click the top-right button to insert\n", "shortcut_modal_remove_plugin_wf_button": "Remove", "shortcut_modal_remove_plugin_wf_double_confirm": "Remove this plugin?", "shortcut_modal_remove_plugin_wf_double_tip": "The components added below will also be removed", "shortcut_modal_save_shortcut_with_components_unused_modal_desc": "There are components ({unUsedComponentsNames}) not referenced in the Query, which may affect your usage of the shortcut", "shortcut_modal_save_shortcut_with_components_unused_modal_title": "Save the shortcut?", "shortcut_modal_selector_component_default_text": "Please select", "shortcut_modal_selector_component_no_options_error": "There is a selector component with no options added/selected yet.", "shortcut_modal_selector_component_options": "Options", "shortcut_modal_shortcut_action_use_plugin_wf": "Use plugin or workflow directly", "shortcut_modal_shortcut_description": "Description", "shortcut_modal_shortcut_description_input_placeholder": "Enter the description of the shortcut", "shortcut_modal_shortcut_name": "Shortcut name", "shortcut_modal_shortcut_name_conflict_error": "The shortcut name is taken", "shortcut_modal_shortcut_name_input_placeholder": "Available on Discord, etc. Please use letters and underscores like get_news", "shortcut_modal_shortcut_name_is_required": "Shortcut name is required", "shortcut_modal_skill": "Skill ", "shortcut_modal_skill_has_no_param_tip": "The skill selected has no parameter and does not support components. The shortcut will be sent directly.", "shortcut_modal_skill_select_button": "Please select skill above", "shortcut_modal_title": "Create Shortcut", "shortcut_modal_title_edit_shortcut": "Edit shortcut", "shortcut_modal_upload_component_file_format_audio": "Audio", "shortcut_modal_upload_component_file_format_code": "Code", "shortcut_modal_upload_component_file_format_doc": "Doc", "shortcut_modal_upload_component_file_format_img": "Image", "shortcut_modal_upload_component_file_format_ppt": "PPT", "shortcut_modal_upload_component_file_format_table": "Table", "shortcut_modal_upload_component_file_format_txt": "TXT", "shortcut_modal_upload_component_file_format_video": "Video", "shortcut_modal_upload_component_file_format_zip": "Zip", "shortcut_modal_upload_component_supported_file_formats": "Supported file formats", "shortcut_modal_use_at_least_one_letter_error": "At least one letter should be used, e.g. img_2_text", "shortcut_modal_use_tool_parameter_default_value": "Default value", "shortcut_modal_use_tool_parameter_default_value_placeholder": "Default value", "shortcut_modal_use_tool_select_button": "Select", "singleagent_LLM_mode": "Single Agent (LLM Mode)", "singleagent_LLM_mode_desc": "The agent in single-agent mode applies to simple logic chats in the LLM.", "singleagent_workflow_mode": "Single Agent (Chatflow Mode)", "singleagent_workflow_mode_desc": "The agent in single-agent mode applies to simple logic chats in the chatflow.", "skill_role_information": "Role information", "skillset_241115_01": "Select response mode", "skillset_241115_02": "Return the variable value directly without model summarization", "skillset_241115_03": "Return the variable, output summarized by the model", "space": "Space", "stop_generating": "Stop generating", "stop_receiving": "Stop receiving", "store_add_connector_tootip": "Install the corresponding channels in Team - Publish Management - Channel Management", "store_bot_detail_title_mobile": "Details", "store_chat_placeholder_blank": "Sending message...", "store_chat_placeholder_continue": "Continue chat...", "store_search_rank_default": "Relevance", "store_search_recommend_result2": "Plugins", "store_search_recommend_result3": "Workflows", "store_service_plugin": "Client plugins", "store_service_plugin_connector": "Supported channels", "store_service_plugin_connector_only": "This plugin is only active in the {connector_names} channel", "submit_data_to_a_specified_resource__often_used_to_submit_forms_or_upload_files_": "Submits data to specified resources. It submits forms or uploads files. Example:", "support_poor": "Suboptimal support", "support_poor_explain": "{modelName} doesn't support this feature. Disable it or switch models.", "supports_uploading_json_or_yaml_files": "Supports uploading of JSON or YAML files with OpenAPI, Swagger, or Postman collection protocols.", "switch_to_on_demand_call_warning_notsupported": "After switching to {call_method}, the currently selected model {modelName} will not support the {toolName} module", "switch_to_on_demand_call_warning_supportpoor": "After switching to {callMethod}, the currently selected model {modelName} will have poor support for the {toolName} feature", "tab_bot_detail": "{bot_name} - Agents", "tab_dataset_detail": "{dataset_name} - Knowledge", "tab_dataset_list": "Knowledge", "tab_explore_bot_detail": "{bot_name} - Explore", "tab_plugin_detail": "{plugin_name} - Plugins", "table_view_002": "{n} rows selected", "task_preset_timezone": "Timezone", "task_preset_trigger_time": "Trigger time", "team_column_role": "Characters", "team_management_role_admin": "Admin", "team_management_role_member": "Member", "team_management_role_owner": "Owner", "template_agent": "Agent", "template_buy_paid_agreement_action": "I have read and agree.", "template_buy_paid_agreement_detail": "\"Template Payment Service Agreement\"", "template_buy_paid_agreement_toast": "Please read and agree to the agreement first.", "template_name": "Template", "template_workflow": "Workflow", "terms_of_service": "Terms of Service", "text": "Text", "timecapsule_0108_003": "No record.", "timecapsule_0124_001": "On", "timecapsule_0124_002": "Off", "timecapsule_1228_001": "Long-term memory", "token_copied_1": "Token copied to clipboard", "token_key_1": "Tokens", "tool_load_error": "Loading Error", "tool_new_S1_URL_error": "Please enter a valid URL path. Only ASCII characters such as English letters and numbers are valid", "tool_new_S2_feedback_failed": "Parameter verification failed, please check and try again", "tool_para_required": "Required", "tool_updated_check_mockset_compatibility": "The tool has been updated, please check if the Mockset is compatible.", "toolname_used_mockset_mocksetname": "{toolName} has used Mockset: {mockSetName}", "tools_imported_successfully": "{num, plural, =1 {1 tool} other {# tools}} tools imported successfully", "type": "Type", "unable_to_access_input_url": "The entered URL cannot be accessed correctly", "under_review": "Under review", "unreleased_plugins_tool_cannot_create_mockset": "The Plugin's Tool, which has not yet been released, cannot create a MockSet.", "update_required": "Update Required", "upgrade_guide_got_it": "Got It", "upgrade_guide_next": "Next", "upload_avatar_success": "upload avatar success", "upload_data_or_resources_to_a_specified_location__often_used_to_update_existing_": "Uploads data or resources to a specified location. It updates existing resources or creates new resources. Example:", "upload_empty_file": "The uploaded file is empty, please check the file content and try again", "upload_image": "Upload Image", "upload_image_format_requirement": "Supports uploading images in PNG, JPG, and JPEG formats", "upload_image_guide": "Click to upload or drag and drop the image here", "upload_image_size_limit": "Maximum image size: {max_size}", "upload_success_failed_count": "Successfully uploaded {successNum} files, {failedNum} file upload failed", "uploading_filename": "Uploading {filename}", "url_add_008": "Delete All", "url_raw_data": "URL & Raw data", "use_in_bot": "Use in Agent", "use_in_workflow": "Use in Workflow", "use_template_confirm_ cancel_text": "Cancel", "use_template_confirm_info": "The current template data will overwrite the existing variable data. Are you sure you want to use the template and overwrite it?", "use_template_confirm_ok_text": "Confirm", "use_template_confirm_title": "Using templates", "used_to_delete_the_user_with_id_123_": "Deletes the user with ID 123.", "used_to_obtain_user_information_with_id_123": "To obtain information about the user with ID 123", "used_to_update_user_information_with_id_123_": "Updates information about the user with ID 123.", "user_connections_desc": "After authorizing the following platforms, you can publish the agent on them", "user_info_custom_name": "<PERSON><PERSON>", "user_info_email": "Email", "user_info_password": "Password", "user_info_username": "Username", "user_profile": "Variables", "user_profile_intro": "After setting the memory, the agent will recall those memories during chats, which enables the agent to provide personalized responses.", "user_revoke_authorization_title": "Revoke authorization?", "username_invalid_letter": "Only English alphabets (A-Z, a-z), numbers and underscore (_) are valid.", "username_placeholder": "Enter your username", "username_too_short": "Required 4-16 characters.", "variable_240407_01": "Preset variables are used to obtain user data such as sys_uuid, sys_longitude, and sys_lark_open_id. They are read-only and cannot be modified.", "variable_240416_01": "Preset variables are read-only and do not support modifications. The system will handle the updates of preset variables.", "variable_240520_03": "The variable is accessible in Prompts by default. If unchecked, it will not support access in Prompts (and can only be accessed within the Workflow).", "variable_Button_reset_variable": "Reset data", "variable_Table_Title_edit_time": "Last modified", "variable_Table_Title_name": "Name", "variable_Table_Title_support_channels": "Channel", "variable_Table_Title_type": "Type", "variable_Table_Title_value": "Value", "variable_Tabname_test_data": "Test data", "variable_app_name": "App variable", "variable_assignment_node_select_empty": "Select a variable to assign a value", "variable_assignment_node_select_placeholder": "Select", "variable_binding_continue": "Continue", "variable_binding_please_bind_an_agent_or_app_first": "Please bind an agent or app first", "variable_binding_please_select_a_variable": "Please select a variable", "variable_binding_search_project": "Search for agents/apps", "variable_binding_there_are_no_variables_in_this_project": "No variables defined in this project", "variable_button_input_json": "Enter in JSON format", "variable_config_change_banner": "Once you modify the variable settings, the workflow using this variable may be affected. Please update the corresponding variable logic of the workflow.", "variable_config_toast_return_button": "Save", "variable_config_toast_savetips": "You have unsaved variable settings", "variable_edit_not_pass": "Please check that the variable's name and description do not contain inappropriate content.", "variable_edit_time": "Edit time", "variable_field_name": "Field name", "variable_field_value": "Value", "variable_name": "Variables", "variable_name_placeholder": "Name (Required)", "variable_node_offline_toast": "This node is discontinued and cannot be edited", "variable_reset": "Reset data", "variable_reset_confirm": "Reset data?", "variable_reset_fail_tips": "Reset failed", "variable_reset_no": "Cancel", "variable_reset_succ_tips": "Reset successfully", "variable_reset_tips": "All data will be reset to default value.", "variable_reset_yes": "Confirm", "variable_select_empty_appide_tips": "No variables found. Please go to Global Settings > Variables to configure a variable.", "variable_select_empty_library_tips": "No variables found. Please link an agent or app with variables in the test run first.", "variable_select_empty_library_tips_02": "No variable is available", "variable_system_describtion": "Displays the data that you enabled as needed, which can be used to identify users via IDs or handle channel-specific features. The data is automatically generated and is read-only.", "variable_system_name": "System variable", "variable_template_demo_desc": "Developers can also define other variables that the agent needs to save in the \"fields\".", "variable_template_demo_text": "Template <PERSON><PERSON>", "variable_template_title": "Example of variable usage", "variable_user_description": "Persistently stores and reads project date for users, such as the preferred language and custom settings.", "variable_user_name": "User variable", "variables_app_name_limit": "It can only contain letters, digits, and underscores, and must start with a letter or underscore", "variables_json_input_error": "Invalid JSON format", "variables_json_input_limit": "Characters in the JSON file exceeds the maximum length", "variables_json_input_readonly_button": "View JSON", "variables_json_input_readonly_title": "JSON data", "variables_user_data_empty": "No content found", "version": "Publish History", "view-all-chat-knowledge-source-header": "View all", "view_detailed_information": "View detailed information", "view_workflow_details": "View workflow details", "voice_select_library_null": "There is currently no voice for the corresponding language", "wf_20241206_001": "Neces.", "wf_chatflow_01": "For handling dialogue-type requests, including special input parameters USER_INPUT and CONVERSATION_NAME. This execution will bind to that conversation, automatically write messages to that conversation, and read conversation history from it.", "wf_chatflow_02": "For handling function-type requests, such as: generating an article, retrieving a list of records", "wf_chatflow_03": "Create within the App", "wf_chatflow_101": "Conversation management", "wf_chatflow_102": "Dialogue-type requests often require conversation capabilities to save message records and use them as context for the model. Conversation management provides the ability to manage multiple conversations, including both static and dynamic conversations.", "wf_chatflow_103": "Static conversations", "wf_chatflow_104": "Conversations created in the conversation management are static conversations, which are created before the function runs.", "wf_chatflow_106": "App", "wf_chatflow_107": "Agent", "wf_chatflow_109": "A conversation with the same name exists, please use a different name", "wf_chatflow_111": "Created successfully", "wf_chatflow_112": "Deleted successfully", "wf_chatflow_114": "Please select", "wf_chatflow_115": "No conversation found", "wf_chatflow_116": "Conversation name is limited to 200 characters", "wf_chatflow_121": "Switch to {flowMode}", "wf_chatflow_122": "Chatflow description cannot be empty", "wf_chatflow_123": "Successfully switched to {Chatflow}", "wf_chatflow_124": "Conversation history", "wf_chatflow_125": "After enabling conversation history, the contextual information of the conversation corresponding to the CONVERSATION_NAME input parameter of the Start node will be automatically sent to the model.", "wf_chatflow_126": "This App has defined the following variables :", "wf_chatflow_127": "This App has not defined any variables yet", "wf_chatflow_13": "Input parameters required for creating a conversation", "wf_chatflow_131": "Output type", "wf_chatflow_132": "Each conversation will call this Chatflow, and the user's \"current round input\" will be passed as the input parameter \"USER_INPUT\" for the Chatflow.", "wf_chatflow_133": "The user's dialogue will invoke the Chatflow to run, and inject the \"USER_INPUT\" system variable parameter on the Start node. Configuration parameters on the Start node other than \"USER_INPUT\" and \"CONVERSATION_NAME\" are invalid.", "wf_chatflow_14": "Conversation name, please note that conversation names cannot be duplicated", "wf_chatflow_141": "Unable to select <PERSON> because the process contains conversation nodes, which are not currently supported by Agents", "wf_chatflow_142": "Unable to select A<PERSON> because Apps do not currently support LTM node", "wf_chatflow_143": "Configuration", "wf_chatflow_15": "Output information for creating a conversation: When successful, isSuccess is true. If the conversation already exists (when the name is duplicated), isExisted is true.", "wf_chatflow_151": "The conversation has existing bindings, deletion failed", "wf_chatflow_154": "The conversation bound to this request will automatically write messages and read conversation history from that conversation. You can manage existing conversations in \"Settings - Conversations\".", "wf_chatflow_155": "No Skills configured yet", "wf_chatflow_23": "Input parameters required to clear conversation history", "wf_chatflow_24": "Conversation Name", "wf_chatflow_25": "Output information when clearing conversation history, isSuccess is true when successful", "wf_chatflow_33": "Input parameters required to query the message list", "wf_chatflow_34": "The amount of data returned per query, the range is 1~50.", "wf_chatflow_35": "View messages before a specified position. Pass in an empty string to indicate no specified position. To browse backward, specify it as the first_id in the returned result.", "wf_chatflow_36": "View messages after a specified position. Pass in an empty string to indicate no specified position. To browse forward, specify it as the last_id in the returned result.", "wf_chatflow_37": "Output information for query message list", "wf_chatflow_41": "No dynamically created conversations at the moment", "wf_chatflow_42": "Display conversations created through \"Create conversation node\"", "wf_chatflow_43": "Dynamic conversations", "wf_chatflow_44": "Display conversations created through the \"Create Conversation Node,\" which are conversations created during the runtime process.", "wf_chatflow_51": "Delete conversation", "wf_chatflow_52": "This will also delete the message history in the conversation. This action cannot be undone. Are you sure you want to delete?", "wf_chatflow_53": "The following Chatflow is linked to the conversation:", "wf_chatflow_54": "You can choose a new conversation to link, by default it will be linked to the \"Default Conversation\"", "wf_chatflow_55": "Delete", "wf_chatflow_56": "Cancel", "wf_chatflow_61": "Conversation not created", "wf_chatflow_62": "Please try again later", "wf_chatflow_71": "Input configuration", "wf_chatflow_72": "Associate an Agent or App", "wf_chatflow_73": "Select the Agent or App you need", "wf_chatflow_74": "Select conversation", "wf_chatflow_75": "Save and start dialogue debugging", "wf_chatflow_76": "Chatflow", "wf_chatflow_81": "Create chatflow", "wf_chatflow_82": "Create a conversation with the same name, which will be used as the default value for the CONVERSATION_NAME input parameter of the chatflow", "wf_chatflow_84": "Edit chatflow", "wf_chatflow_85": "Chatflow name", "wf_chatflow_86": "Chatflow description", "wf_chatflow_87": "Create and bind a conversation with the same name", "wf_chatflow_91": "Please enter the chatflow name", "wf_chatflow_92": "Please enter a description to help the LLM understand when this chatflow should be called", "wf_chatflow_93": "Chatflow name cannot be empty", "wf_chatflow_94": "Chatflow name can only contain letters, numbers, and underscores, and must start with a letter", "wf_chatflow_95": "Chatflow created successfully", "wf_chatflow_96": "Default value", "wf_chatflow_97": "Description", "wf_chatflow_98": "Help the LLM accurately understand the function of the parameter", "wf_chatflow_99": "Parameter default value, when this parameter is not passed, the default value will be used", "wf_history_rounds": "Rounds", "wf_node_add_wf_modal_tip_must_publish_to_add": "Add bot after publishing the workflow", "wf_node_add_wf_modal_toast_wf_added": "{workflowName} is added", "wf_problem_my_tag": "This workflow", "wf_problem_other_tag": "Sub workflow", "wf_role_config_avatar_ai_tooltip": "Use DALL·E-3 to auto-generate based on the name and description", "wf_role_config_avatar_ai_tooltip_cn": "Enter the name and description, and click to generate an avatar.", "wf_role_config_error_toast": "Generation failed: Please check whether the name or description is invalid or unclear", "wf_test_run_form_input_collapse_label": "Input", "wf_testrun_ai_button_complete": "AI Completion", "wf_testrun_ai_button_complete_stop": "Stop Completion", "wf_testrun_ai_button_cover": "AI Overwrite", "wf_testrun_ai_button_cover_stop": "Stop Overwriting", "wf_testrun_ai_button_popover": "Select Completion Mode", "wf_testrun_ai_button_popover_complete": "Blank Field Completion", "wf_testrun_ai_button_popover_complete_extra": "Only complete the unfilled fields", "wf_testrun_ai_button_popover_cover": "Full Field Overwrite", "wf_testrun_ai_button_popover_cover_extra": "Regenerate and overwrite all input parameters each time", "wf_testrun_ai_gen_toast": "Generation timed out; random filling will be applied this time", "wf_testrun_form_json_group_batch": "Batch processing data", "wf_testrun_form_json_group_batch_extra": "Required batch data for test run", "wf_testrun_form_json_group_input": "Input data", "wf_testrun_form_json_group_input_extra": "Required input for test run", "wf_testrun_form_json_group_settings": "Set data", "wf_testrun_form_json_group_settings_extra": "Required batch processing data for test run", "wf_testrun_form_json_key_hover_no": "Undefined field", "wf_testrun_form_mode_text": "JSON mode", "wf_testrun_form_related_title": "Related content", "wf_testrun_log_md_llm_diff_tooltip": "The object structure output by the LLM for the current node is inconsistent with the output structure defined by the node. It is recommended to adjust the LLM's prompt or output parameter description.", "wf_testrun_log_md_llm_diff_tooltip_a": "Documentation", "wf_testrun_problems_loading": "Checking", "wget": "wget", "what_is_coze": "What is <PERSON><PERSON>?", "wmv_collaborate_collabration_explain": "Collaborators can edit, modify, and publish workflow. ", "wmv_diff_latest_draft": "There are differences between the latest version and the current draft. Please choose a version to overwrite the current draft.", "wmv_draft_version": "Draft version", "wmv_latest_version": "Latest version", "wmv_merge_versions": "Merge versions", "wmv_publish_multibranch_IntroTitle": "Enabling collaboration mode to add other members to collaborate:", "wmv_view_latest_version": "View latest version", "workflow_0224_01": "Node", "workflow_0224_02": "From {source}", "workflow_0224_03": "Favorite Plugins", "workflow_0224_04": "Explore", "workflow_0224_05": "Expand", "workflow_0224_06": "Plugin Store", "workflow_240218_02": "Select Agent that contains the variable & database you need", "workflow_240218_04": "There are no databases in this Agent yet.", "workflow_240218_05": "This Agent defines the following databases:", "workflow_240218_07": "Input variables to be added, which can be referenced in SQL statements.", "workflow_240218_08": "Variables output after SQL statements are executed. The variable name must match that defined in the SQL statement, and the variable type must match the field type defined in the table.", "workflow_240218_09": "SQL", "workflow_240218_10": "The SQL statement to be executed, which can be referenced from variables in input parameters. Note that row<PERSON>um outputs the number of returned rows or the number of affected rows. The variable name in outputList must match the field name defined in the SQL statement.", "workflow_240218_11": "Auto-generate", "workflow_240218_12": "You can use {{variable name}}, {{variable name.subvariable name}}, or {{variable name[array index]}} to reference variables in input parameters", "workflow_240218_15": "Query target", "workflow_240218_16": "Please describe your query target in natural language", "workflow_240218_17": "Cancel", "workflow_240218_18": "Use", "workflow_240221_01": "User Unique Identifier, generated by the System", "workflow_240919_01": "Not configured", "workflow_240919_02": "Q&A type", "workflow_240919_03": "Not visible to users", "workflow_241015_01": "Enter or reference parameter values", "workflow_241111_01": "Output content", "workflow_241111_02": "Return variables", "workflow_241111_03": "Return text", "workflow_241119_01": "node", "workflow_241120_01": "The information entered by the user will be stored in the corresponding variable.", "workflow_250117_01": "In Speed Mode, a maximum of 10 intent configurations are supported", "workflow_250117_02": "In Full Mode, a maximum of 50 intent configurations are supported", "workflow_250117_03": "Speed Mode", "workflow_250117_04": "Full Mode", "workflow_250117_05": "Supports up to { maxCount }", "workflow_250213_01": "reasoning_content is a system field, please modify the variable name", "workflow_250217_01": "Reasoning content", "workflow_250217_02": "Reasoning content, specific to models that support outputting chains of thought", "workflow_250305_001": "No relevant results", "workflow_250305_002": "Please check if the keyword is correct, or try searching with a different keyword", "workflow_250306_01": "Search nodes, plugins, workflows", "workflow_250310_02": "Ineffective", "workflow_250310_03": "This model does not support binding skills", "workflow_250310_04": "Visual understanding input", "workflow_250310_05": "The selected model does not support visual understanding", "workflow_250310_06": "Skill invocation", "workflow_250310_09": "Upload", "workflow_250310_10": "Enter URL", "workflow_250310_11": "Input", "workflow_250310_12": "Output", "workflow_250310_13": "Copy", "workflow_250310_14": "Child nodes added, input is no longer supported", "workflow_250310_15": "Configure by adding child nodes", "workflow_250317_01": "Cannot have the same name as the input", "workflow_250317_02": "Cannot have the same name as the visual understanding input", "workflow_250320_01": "The selected model does not support image understanding", "workflow_250320_02": "The selected model does not support video understanding", "workflow_250320_03": "For visual understanding input, provide the URL of an image or video, and reference this input in the Prompt. For example: \"What's in the image {{variable name}}?\"", "workflow_250407_001": "Automatic message writing", "workflow_250407_002": "When enabled, the Chatflow will automatically write messages during execution, including messages sent by the user and messages returned to the user.", "workflow_250407_005": "Conversation name", "workflow_250407_006": "Message role name, limited to 'user' or 'assistant'. 'user' indicates that the message content is sent by the user; 'assistant' indicates that the message content is returned to the user.", "workflow_250407_007": "Message content", "workflow_250407_010": "Conversation name", "workflow_250407_011": "Message ID", "workflow_250407_012": "Modified message content", "workflow_250407_015": "Conversation name", "workflow_250407_016": "Message ID", "workflow_250407_019": "Name of the conversation to be modified", "workflow_250407_020": "New name", "workflow_250407_023": "Conversation name", "workflow_250407_028": "Conversation name", "workflow_250407_029": "The number of conversation history rounds to return, supporting up to 30 rounds.", "workflow_250407_201": "Exception handling", "workflow_250407_202": "Execute exception flow", "workflow_250407_203": "Alternative model", "workflow_250407_204": "The alternative model will be used for retry attempts", "workflow_250407_205": "Timeout duration", "workflow_250407_206": "Number of retry attempts", "workflow_250407_207": "Exception handling method", "workflow_250407_208": "Interrupt process", "workflow_250407_209": "After an exception occurs, the process execution is interrupted. The exception information will be displayed on the node card or returned through the call result.", "workflow_250407_210": "Return set content", "workflow_250407_211": "After an exception occurs, the process will not be interrupted. Exception information will be returned through isSuccess and errorBody. Developers can set the content to be returned.", "workflow_250407_212": "Execute exception flow", "workflow_250407_213": "After an exception occurs, the process will not be interrupted. Exception information will be returned through isSuccess and errorBody, and an exception branch will be added. Developers need to complete the exception handling process before running the process.", "workflow_250407_214": "Need to complete node's exception handling process", "workflow_250416_01": "Exception Handling", "workflow_250416_03": "Once streaming output is enabled, even if an exception occurs, it's not possible to retry or switch to an exception branch after data output has begun.", "workflow_250416_04": "Select alternate model", "workflow_250416_05": "To switch to batch processing mode, you first need to change the node's exception handling method to either \"Interrupt process\" or \"Return set content\".", "workflow_250416_06": "No retry", "workflow_250416_07": "Retry once", "workflow_250416_08": "Custom return content", "workflow_250421_01": "Exception, execute exception flow", "workflow_250421_02": "Exception, return set content", "workflow_250421_03": "Exception handling can be set up, including timeout, retry, and exception handling methods.", "workflow_250421_04": "Timeout Interval", "workflow_250508_01": "Endpoint plugin does not support timeout configuration", "workflow_LLM_node_sp_title": "System Prompt", "workflow_abnormal_connection": "Abnormal connection", "workflow_add_common_loading": "loading", "workflow_add_condition": "Add condition", "workflow_add_create_library": "Create workflow", "workflow_add_created_list_empty_description": "Workflows can build complex functional logic for agents, such as travel planning, report analysis, etc.", "workflow_add_created_list_empty_title": "Workflow", "workflow_add_created_tab_all": "All", "workflow_add_created_tab_mine": "My Creation", "workflow_add_delete": "Delete", "workflow_add_delete_fail": "Delete fail", "workflow_add_delete_success": "Delete success", "workflow_add_example": "Coze example", "workflow_add_imageflow_toast_success": "Image stream {name} added", "workflow_add_input": "Add", "workflow_add_list_add": "Add", "workflow_add_list_added": "Added", "workflow_add_list_added_fail": "Failed to add to Agent", "workflow_add_list_added_id_empty": "Get workflow info fail", "workflow_add_list_added_success": "Already added to Agent", "workflow_add_list_copy": "Duplicate", "workflow_add_list_created": "Created on", "workflow_add_list_empty_title": "No relevant workflow yet", "workflow_add_list_publised": "Published", "workflow_add_list_remove": "Remove", "workflow_add_list_removed_success": "Already removed from Agent", "workflow_add_list_unknown": "unknown", "workflow_add_list_updated": "Edited", "workflow_add_navigation_create": "Create workflow", "workflow_add_navigation_explore": "Explore workflows", "workflow_add_navigation_my": "My workflows", "workflow_add_navigation_team": "Team workflows", "workflow_add_not_allow_before_publish": "Can only be added to Agent after publishing", "workflow_add_output": "Add", "workflow_add_parameter_required": "Required", "workflow_add_remove_confirm_content": "After removal, the references in the relevant Agent will become invalid.", "workflow_add_remove_confirm_title": "Confirm remove from Agent?", "workflow_add_search_placeholder": "Search", "workflow_add_status_published": "Published", "workflow_add_status_unpublished": "Unpublished", "workflow_add_title": "Add workflow", "workflow_add_to_workflow": "add to workflow", "workflow_agent_add": "Click to add chatflow", "workflow_agent_configure": "Chatflow Configuration", "workflow_agent_dialog_set": "Chat settings", "workflow_agent_dialog_set_chathistory": "Input settings", "workflow_agent_dialog_set_desc": "The chat settings only take effect if the \"Chat History\" parameter is enabled in the input module of the \"LLM node\" in the workflow.", "workflow_ans_content_placeholder": "You can use {{variable name}} to reference variables in input parameters", "workflow_area_select": "Area select", "workflow_batch_canvas_title": "Batch body", "workflow_batch_canvas_tooltips": "The logic that arranges the batch body", "workflow_batch_error_items": "Error items", "workflow_batch_error_only": "Error only", "workflow_batch_inputs": "Input", "workflow_batch_inputs_tooltips": "Variables to be used by nodes in the batch body", "workflow_batch_no_failed_entries": "no failed entries", "workflow_batch_outputs": "Output", "workflow_batch_outputs_tooltips": "The output content returned after the batch processing is complete. You can reference the output variables only from the nodes in the batch body.", "workflow_batch_settings": "Batch processing settings", "workflow_batch_tab_batch_radio": "Batch processing", "workflow_batch_tab_single_radio": "Single", "workflow_batch_total_items": "Total items", "workflow_chatflow_testrun_conversation_des": "Chat data generated during chatflow debugging will not affect online data. After the chat data is published, please check them in the corresponding channel.", "workflow_chathistory_testrun_nocontent": "There are no chat history in this agent yet.", "workflow_chathistory_testrun_title": "This agent contains the following conversation history, which comes from the dialog round set in the Agent:", "workflow_code_js_illustrate_all": "// Here, you can use 'params' to access the input variables in the node and use 'ret' to output the result\n// 'params'  have already been properly injected into the environment\n// Below is an example of retrieving the value of the parameter 'input' from the node's input:\n// const input = params.input; \n// Below is an example of outputting a 'ret' object containing multiple data types:\n// const ret = { \"name\": '<PERSON>', \"hobbies\": [\"reading\", \"traveling\"] };", "workflow_code_js_illustrate_output": "// Construct the output object", "workflow_code_js_illustrate_output_arr": "// Output an array", "workflow_code_js_illustrate_output_obj": "// Output an Object", "workflow_code_js_illustrate_output_param": "// Concatenate the value of the two input parameters", "workflow_code_py_illustrate_all": "# Here, you can use 'args' to access the input variables in the node and use 'ret' to output the result\n# 'args'  have already been correctly injected into the environment\n# Below is an example: First, retrieve all input parameters (params) from the node, then get the value of the parameter 'input':\n# params = args.params; \n# input = params.input;\n# Below is an example of outputting a 'ret' object containing multiple data types:\n# ret: Output =  { \"name\": '<PERSON> Ming', \"hobbies\": [\"reading\", \"traveling\"] };", "workflow_code_py_illustrate_output": "# Construct the output object", "workflow_code_py_illustrate_output_arr": "# Output an array", "workflow_code_py_illustrate_output_obj": "# Output an Object", "workflow_code_py_illustrate_output_param": "# Concatenate the value of the two input parameters", "workflow_code_testrun_sync": "Sync output", "workflow_condition_empty": "Query condition is empty", "workflow_condition_left_placeholder": "Please select", "workflow_condition_obj_contain": "Contain the key name", "workflow_condition_obj_not_contain": "Not contain the key name", "workflow_condition_operation_be_false": "FALSE", "workflow_condition_operation_be_true": "TRUE", "workflow_condition_operation_equal": "Equal", "workflow_condition_operation_greater_equal": "Greater equal", "workflow_condition_operation_greater_than": "Greater than", "workflow_condition_operation_in": "In", "workflow_condition_operation_is_not_null": "Is not null", "workflow_condition_operation_is_null": "Is null", "workflow_condition_operation_less_equal": "Less equal", "workflow_condition_operation_less_than": "Less than", "workflow_condition_operation_like": "Like", "workflow_condition_operation_not_equal": "Not equal", "workflow_condition_operation_not_in": "Not in", "workflow_condition_operation_not_like": "Not like", "workflow_confirm_modal_cancel": "Cancel", "workflow_confirm_modal_ok": "Confirm", "workflow_connection_delete": "The connection has been deleted.", "workflow_connection_name": "Connection", "workflow_database_delete_confirm_modal_content": "After removal, all relevant content configured for this node will be deleted and cannot be restored.", "workflow_database_delete_confirm_modal_title": "Confirm to remove this data table?", "workflow_database_no_fields": "There are no fields that can be added.", "workflow_database_node_database_empty": "Please add a database to this node. Only one database is supported for addition.", "workflow_database_node_database_table_title": "Database", "workflow_database_query_limit": "The maximum number of data entries for word query is 1000", "workflow_debug_data_save": "Save data to new testset after run", "workflow_debug_run": "Run", "workflow_debug_testonenode": "Test this node", "workflow_debug_testonenode_group": "{nodeTitle} node", "workflow_debug_testset_placeholder": "testset", "workflow_debug_wrong_json": "Please enter the correct JSON structure", "workflow_delete_conditon_title": "Delete condition", "workflow_derail_node_detail_title_max": "The maximum length cannot exceed {max}", "workflow_detail_api_input_tooltip": "Please enter the parameters for the API. When this node is executed, these parameters will be passed in and used to call the API.", "workflow_detail_batch_item_tooltip": "The item will sequentially refer to each item in {name}", "workflow_detail_code_code": "Code", "workflow_detail_code_code_tooltip": "Write the structure of a function referring to the code example, where you can directly use the variables in the input parameters, and output the processing result by returning an object. This feature does not support writing multiple functions. Even if there is only one output value, make sure to return it as an object", "workflow_detail_code_edit_in_ide": "Edit in IDE", "workflow_detail_code_input_tooltip": "Enter the variable that needs to be added to the code, the code can directly reference the variable added here", "workflow_detail_code_is_running": "The code is running", "workflow_detail_code_is_terminate_execution": "Continuing with operations will terminate the code execution.", "workflow_detail_code_output_tooltip": "The variables output after the code runs must ensure that the variable names and variable types defined here are completely consistent with those in the code's return", "workflow_detail_code_view_in_ide": "View in IDE", "workflow_detail_condition_and": "and", "workflow_detail_condition_comparison": "Compare values", "workflow_detail_condition_condition": "Condition", "workflow_detail_condition_condition_empty": "Condition cannot be empty", "workflow_detail_condition_else": "Else", "workflow_detail_condition_error_enter_comparison": "Please enter comparison", "workflow_detail_condition_error_refer_empty": "The referenced variable cannot be empty", "workflow_detail_condition_or": "or", "workflow_detail_condition_pleaseselect": "Please select", "workflow_detail_condition_reference": "Reference variable", "workflow_detail_condition_select": "Select condition", "workflow_detail_condition_select_contain": "contain", "workflow_detail_condition_select_empty": "is empty", "workflow_detail_condition_select_equal": "equal", "workflow_detail_condition_select_false": "is false", "workflow_detail_condition_select_greater": "greater than", "workflow_detail_condition_select_greater_equal": "greater than or equal", "workflow_detail_condition_select_less": "less than", "workflow_detail_condition_select_less_equal": "less than or equal", "workflow_detail_condition_select_longer": "longer than", "workflow_detail_condition_select_longer_equal": "longer than or equal", "workflow_detail_condition_select_not_contain": "not contain", "workflow_detail_condition_select_not_empty": "is not empty", "workflow_detail_condition_select_not_equal": "not equal", "workflow_detail_condition_select_shorter": "shorter than", "workflow_detail_condition_select_shorter_equal": "shorter than or equal", "workflow_detail_condition_select_true": "is true", "workflow_detail_edit_prompt_button": "Do not remind me again", "workflow_detail_end_answer": "Response", "workflow_detail_end_answer_copy": "Copy answer content", "workflow_detail_end_answer_example": "You can use {{variable name}}, {{variable name.subvariable name}}, or {{variable name[array index]}}  to reference variables in output parameters", "workflow_detail_end_answer_tooltip": "When you edit the response of the agent, the LLM in the agent no longer organizes language after the workflow is completed, but instead replies to you with the original response edited here.\nYou can use {{variable name}} to reference variables in input parameters.", "workflow_detail_end_output": "Output variable", "workflow_detail_end_output_copy": "Copy output variable", "workflow_detail_end_output_entername": "Enter variable name", "workflow_detail_end_output_name": "Variable name", "workflow_detail_end_output_tooltip": "These variables are output after the agent calls the workflow. In \"Return variables\" mode, the agent summarizes these variables before replying to you; in \"Return text\" mode, the agent only replies with the preset \"Response\". However, you can use these variables when configuring the card in agent modes.", "workflow_detail_end_output_value": "Variable value", "workflow_detail_error_interface_initialization": "Interface initialization error", "workflow_detail_error_message": "Error: {msg}", "workflow_detail_error_missing_id": "Missing workflow_id", "workflow_detail_knowledge_error_empty": "Knowledge cannot be empty", "workflow_detail_knowledge_input_tooltip": "Enter the key information to be matched from knowledge", "workflow_detail_knowledge_knowledge": "Knowledge", "workflow_detail_knowledge_knowledge_tooltip": "Select the scope of knowledge to be matched. The information is recalled only from the selected knowledge.", "workflow_detail_knowledge_output_tooltip": "The output list is the information that best match the input parameters, recalled from all selected knowledge", "workflow_detail_knowledge_proceed_with_caution": "Proceed with caution", "workflow_detail_layout_optimization_tooltip": "Optimize layout", "workflow_detail_llm_input_tooltip": "Enter the information that you want to add to the prompt, which can be referenced by the prompt below", "workflow_detail_llm_model": "Model", "workflow_detail_llm_output_decription": "The purpose of the variable", "workflow_detail_llm_output_decription_title": "Description", "workflow_detail_llm_output_tooltip": "The content generated after the completion of the large model run.", "workflow_detail_llm_prompt": "User Prompt", "workflow_detail_llm_prompt_content": "User prompt. You can use {{variable name}}, {{variable name.subvariable name}}, or {{variable name[array index]}}  to reference variables in input parameters.", "workflow_detail_llm_prompt_error_empty": "Prompt cannot be empty", "workflow_detail_llm_prompt_tooltip": "Provide user prompt to the model, such as queries or any questions based on text input.", "workflow_detail_llm_sys_prompt_content": "System prompt. You can use {{variable name}}, {{variable name.subvariable name}}, or {{variable name[array index]}} to reference variables in input parameters.", "workflow_detail_llm_sys_prompt_content_tips": "Provide system-level guidance for the dialogue, such as setting character personas and response logic.", "workflow_detail_node_batch": "Batch processing", "workflow_detail_node_batch_tooltip": "In batch processing mode, the node will run multiple times. In each run, the batch list will assign an item to the batch variable in order, until it reaches the batch limit or the maximum length of the list", "workflow_detail_node_delete": "Delete", "workflow_detail_node_error_empty": "Variable value can not be empty", "workflow_detail_node_error_format": "It can only contain letters, digits, and underscores, and must start with a letter or underscore", "workflow_detail_node_error_name_duplicated": "The current variable name {name} is duplicated", "workflow_detail_node_error_name_empty": "Variable name cannot be empty", "workflow_detail_node_error_variablename_duplicated": "Parameters cannot be duplicated", "workflow_detail_node_input": "Enter", "workflow_detail_node_input_duplicated": "Variable names can not be duplicated", "workflow_detail_node_input_entername": "Enter a parameter name", "workflow_detail_node_input_entervalue": "Select value", "workflow_detail_node_input_selectvalue": "Enter the parameter value", "workflow_detail_node_name_default": "Enter node name", "workflow_detail_node_name_error_empty": "Node name cannot be empty", "workflow_detail_node_nodata": "No data found", "workflow_detail_node_output": "Output", "workflow_detail_node_output_add_subitem": "Add sub-item", "workflow_detail_node_output_parsingfailed": "Parsing failed, please delete this node and add it again", "workflow_detail_node_parameter_input": "Input", "workflow_detail_node_parameter_name": "Variable name", "workflow_detail_node_parameter_reference": "Reference", "workflow_detail_node_parameter_value": "Variable value", "workflow_detail_node_rename": "<PERSON><PERSON>", "workflow_detail_node_save_error": "Save Error", "workflow_detail_node_workflows_max": "最多显示 {number} 条最近修改的工作流", "workflow_detail_node_workflows_referencing": "{number} Workflows Referencing", "workflow_detail_node_workflows_referencing_impact": "Publish impact", "workflow_detail_node_workflows_referencing_invalid": "Related Workflows may be invalid", "workflow_detail_node_workflows_referencing_relationship": "Reference relationship", "workflow_detail_node_workflows_referencing_tip": "A total of {number} workflows have referenced this workflow, and you have permission to view {number} workflows. This list only displays the workflows that you have permission to view.", "workflow_detail_node_workflows_referencing_update": "This workflow contains the following reference relationships. Publishing the update will have the following impacts. Are you sure to publish the update?", "workflow_detail_redo_tooltip": "Redo", "workflow_detail_select_delete_popup_subtitle": "Once deleted, it cannot be recovered.", "workflow_detail_select_delete_popup_title": "Delete the selected nodes?", "workflow_detail_start_input_tooltip": "It defines the input parameters required to start the workflow. The content is read by the LLM during your chat with the agent, allowing the model to start the workflow at the appropriate time and enter the correct information.", "workflow_detail_start_variable_type": "Variable type", "workflow_detail_sub_workflow_change_banner": "Modifying and publishing the changes may affect the running of workflows and agents that reference this workflow.", "workflow_detail_testrun_bot": "Associated agent", "workflow_detail_testrun_error_front": "Error:", "workflow_detail_testrun_panel_batch_naviagte_empty": "Subsequent batch processing not executed", "workflow_detail_testrun_panel_batch_naviagte_stop": "{variable} contains only {len} items. Batch processing will not run after exceeding {len} times.", "workflow_detail_testrun_panel_batch_value": "The variable of this batch process", "workflow_detail_testrun_panel_final_output": "Final output", "workflow_detail_testrun_panel_final_output2": "Node output", "workflow_detail_testrun_panel_raw_output": "Raw Output", "workflow_detail_testrun_panel_raw_output_code": "Code output", "workflow_detail_testrun_panel_raw_output_llm": "LLM output", "workflow_detail_testrun_variable_node_field": "This agent defines the following variables:", "workflow_detail_testrun_variable_node_nofield": "There are no variables in this agent yet.", "workflow_detail_testrun_variable_node_select": "Please select an agent", "workflow_detail_testrun_warning_front": "Warning:", "workflow_detail_title_copy": "Create copy", "workflow_detail_title_cost": "Cost", "workflow_detail_title_lastrun_display": "Show result of last run", "workflow_detail_title_lastrun_hide": "Hide Last Run", "workflow_detail_title_previewing": "Previewing", "workflow_detail_title_publish": "Publish", "workflow_detail_title_publish_toast": "The workflow has been published", "workflow_detail_title_published": "Published", "workflow_detail_title_saved_2": "Saved at {time}", "workflow_detail_title_source": "Source", "workflow_detail_title_testrun": "Test run", "workflow_detail_title_testrun_cancel": "Cancel", "workflow_detail_title_testrun_copy_batch": "Copy the variable of this batch process", "workflow_detail_title_testrun_copyinput": "Copy the input", "workflow_detail_title_testrun_copyoutput": "Copy the output", "workflow_detail_title_testrun_error_input": "Please enter {a}", "workflow_detail_title_testrun_failed_node": "Failed", "workflow_detail_title_testrun_finished": "Finished", "workflow_detail_title_testrun_process": "Execution Result", "workflow_detail_title_testrun_running": "Running", "workflow_detail_title_testrun_submit": "Submit", "workflow_detail_title_testrun_succeed_node": "Succeed", "workflow_detail_title_token": "Token", "workflow_detail_title_total": "Total", "workflow_detail_toast_createcopy_failed": "Creation failed, valid workflow_id", "workflow_detail_toast_createcopy_succeed": "The workflow copy has been created", "workflow_detail_toast_validation_failed": "Nodes with validation failures detected, please reconfigure and run again", "workflow_detail_undo_tooltip": "Undo", "workflow_detail_unknown_variable": "Unknown variable", "workflow_detail_variable_get_title": "Get variable value", "workflow_detail_variable_input_name": "Variable name", "workflow_detail_variable_input_value": "Variable value", "workflow_detail_variable_referenced_error": "Referenced Variable does not exist", "workflow_detail_variable_set_output_tooltip": "Set the result of the variable:\nIf the setting is successful, the system outputs \"True\".\nIf the variable does not exist in the agent or the setting fails for other reasons, the system outputs \"False\".", "workflow_detail_variable_set_title": "Set variable value", "workflow_detail_variable_subtitle": "Reads and writes variables in your agent. The variable name must match that in the agent.", "workflow_encapsulate_button": "Encapsulate workflow", "workflow_encapsulate_button_unable": "Unable to encapsulate workflow", "workflow_encapsulate_button_unable_connected": "There are intermediate nodes connected to nodes outside the selected range, or there are multiple nodes connected to multiple nodes outside the selected range", "workflow_encapsulate_button_unable_continue_or_teiminate": "The selected range contains \"continue loop\" / \"terminate loop\"", "workflow_encapsulate_button_unable_loop_or_batch": "The loop or batch nodes associated with the loop body or batch body are not selected", "workflow_encapsulate_button_unable_start_or_end": "The selected range contains start/end", "workflow_encapsulate_button_unable_uncomplete": "Encapsulation should not include nodes without complete connections", "workflow_encapsulate_decapsulate": "Decapsulate workflow", "workflow_encapsulate_selecet": "Selected {length} nodes", "workflow_encapsulate_toast_batch_or_loop": "The workflow that is decapsulated in loop/batch cannot contain loop or batch nodes", "workflow_error_jump_tip": "There's an unknown error occurred with this workflow. Please try to add it again to your agent.", "workflow_example_succeed": "Used successfully", "workflow_exception_ignore_default_output": "Default output", "workflow_exception_ignore_desc": "Ignore exceptions and use default output as a substitute when an exception occurs.", "workflow_exception_ignore_format": "format", "workflow_exception_ignore_icon_tips": "Exception settings", "workflow_exception_ignore_json_error": "Invalid JSON in exception settings", "workflow_exception_ignore_tag": "ignore", "workflow_exception_ignore_title": "Exception ignore", "workflow_exception_json_error": "The JSON failed to generate automatically due to duplicate variable names.", "workflow_inputs_empty": "Parameter is empty", "workflow_intent_advance_set_placeholder": "Supports additional system prompts, such as providing detailed examples for intent options to enhance the success rate of matching user input with intents.", "workflow_intent_advance_set_tooltips": "The input will be appended to the system prompt", "workflow_intent_input_tooltips": "Enter parameters for intent recognition", "workflow_intent_matchlist_else": "Other intents", "workflow_intent_matchlist_error1": "Option cannot be empty", "workflow_intent_matchlist_error2": "The option can be up to 1,000 characters", "workflow_intent_matchlist_placeholder": "Please enter a description of the intent, such as after-sales issues", "workflow_intent_matchlist_title": "Intent matching", "workflow_intent_matchlist_tooltips": "Intent options for matching user input", "workflow_intent_output_tooltips": "The matching intent options will be referenced as variables by other nodes.", "workflow_interactive_mode": "Interactive", "workflow_interactive_mode_mouse_friendly": "Mouse friendly mode", "workflow_interactive_mode_mouse_friendly_desc": "Move the canvas with the left mouse button and scroll to zoom in and out", "workflow_interactive_mode_pad_friendly": "Pad friendly mode", "workflow_interactive_mode_pad_friendly_desc": "Move the canvas with two fingers and pinch to zoom", "workflow_interactive_mode_popover_title": "Support setting interactive methods for moving and scaling the canvas", "workflow_json_button_node_update": "Sync JSON to node", "workflow_json_import": "Import in JSON", "workflow_json_node_update_tips_content": "The JSON data is expected to overwrite the data on the node", "workflow_json_node_update_tips_title": "Sync JSON to node", "workflow_json_syntax_error": "JSON syntax error", "workflow_json_windows_title": "Please enter JSON data", "workflow_json_windows_title_tips": "To convert JSON data into the data structure on the node, note the following rules:\n1. The key name can be up to 20 characters. If the specified name exceeds this limit, it is automatically truncated.\n2. The value cannot be null. Otherwise, it is automatically ignored.\n3. The nesting level can be up to 3. If the specified level exceeds this limit, it is automatically truncated.", "workflow_knowledeg_un_exit": "Invalid knowledge", "workflow_knowledeg_unexit_error": "Knowledge {id} does not exist", "workflow_knowledge_node_empty": "Please add knowledge to this node", "workflow_list_create_modal_description_label": "Workflow description", "workflow_list_create_modal_description_placeholder": "Please describe the calling scenarios for this workflow to help the LLM better understand it", "workflow_list_create_modal_description_rule_required": "Please describe the calling scenarios for this workflow to help the LLM better understand it", "workflow_list_create_modal_footer_cancel": "Cancel", "workflow_list_create_modal_footer_confirm": "Confirm", "workflow_list_create_modal_name_label": "Workflow name", "workflow_list_create_modal_name_placeholder": "Please enter a workflow name", "workflow_list_create_modal_name_rule_reg": "The workflow name can only contain letters, digits, and underscores, and must start with a letter", "workflow_list_create_modal_name_rule_required": "Please enter a workflow name", "workflow_list_create_modal_title": "Create workflow", "workflow_list_create_modal_workflow_id_empty": "Workflow id is empty", "workflow_list_create_success": "Workflow created", "workflow_list_edit_modal_title": "Edit workflow", "workflow_list_scope_all": "All", "workflow_list_scope_mine": "Me", "workflow_list_sort_create_time": "Create time", "workflow_list_sort_edit_time": "Edit time", "workflow_list_status_all": "All", "workflow_list_status_published": "Published", "workflow_list_status_unpublished": "Unpublished", "workflow_list_update_success": "Update success", "workflow_loop_body_canva": "Loop body", "workflow_loop_body_canva_tips": "For main loop logic orchestration", "workflow_loop_count": "Number of loops", "workflow_loop_count_tooltips": "Enter a number or reference numeric variables", "workflow_loop_loop_times": "Circular array", "workflow_loop_loop_times_tips": "Supports referencing arrays only, with the loop count being the length of the array. If no array is referenced, the loop will run infinitely. To stop the loop, use the \"Stop Loop\" node.", "workflow_loop_loop_variables": "Intermediate variable", "workflow_loop_loop_variables_tips": "Variables can be shared across multiple loops and used to pass variables between loops", "workflow_loop_name_no_index_wrong": "The variable name cannot be an index", "workflow_loop_nest_tips": "Nested loops/batch nodes not supported", "workflow_loop_onlycanva_tips": "It can only be added to the loop body's canvas", "workflow_loop_output": "Output", "workflow_loop_output_tips": "The output after the loop is completed only supports referencing output variables from nodes within the loop body", "workflow_loop_release_tips": "Release the button to place the node on the canvas", "workflow_loop_set_variable_loopvariable": "Intermediate variable", "workflow_loop_set_variable_samewrong": "Variables must be unique", "workflow_loop_set_variable_set": "Settings", "workflow_loop_set_variable_typewrong": "Inconsistent variable types", "workflow_loop_set_variable_variable": "Set value", "workflow_loop_title": "Loop settings", "workflow_loop_type": "Loop type", "workflow_loop_type_array": "Loop through array", "workflow_loop_type_count": "Specify a value", "workflow_loop_type_infinite": "Enable infinite loop", "workflow_loop_type_tooltips": "You can set the number of loops in the following methods: reference an array (where its length indicates the number of loops), specify a value, or select \"Enable infinite loop\". Note that if you select \"Enable infinite loop\", you must complete the loops based on the \"End loop\" node.", "workflow_maximum_parallel_runs": "<PERSON><PERSON><PERSON> runs", "workflow_maximum_parallel_runs_tips": "The number of parallel runs. If you set it to 1, nodes are executed one by one.", "workflow_maximum_run_count": "Upper limit of batch processing", "workflow_maximum_run_count_tips": "The total number of batch runs cannot exceed the upper limit", "workflow_message_anwser_tooltips": "Edit the agent's reply content, that is, during the workflow running process, the agent will directly reply to the conversation with the original content edited here. You can use the {{variable name}} to reference variables in the output parameters.", "workflow_message_streaming_name": "Streaming output", "workflow_message_streaming_tooltips": "When enabled, the generated content from the large language model in the reply will be streamed and output word by word. When disabled, the reply content will be generated and output all at once.", "workflow_message_variable_tooltips": "When the agent calls the workflow, it'll output these variables. It'll respond with just the \"answer content\" you've set. You can use these variables while configuring the card.", "workflow_mouse_friendly": "Mouse-friendly", "workflow_mouse_friendly_desc": "Left-click to drag the canvas and zoom with the scroll wheel", "workflow_multi_choice_copy_failed": "The Start/End node cannot be duplicated", "workflow_multi_choice_copy_partial_success": "The Start/End node cannot be duplicated, other nodes have been copied to the clipboard", "workflow_multi_choice_copy_success": "Nodes have been copied to the clipboard", "workflow_multi_choice_delete_failed": "The Start/End node cannot be deleted", "workflow_no_change_tooltip": "The workflow has not been modified, and no additional release is required", "workflow_node_copy_othercanva": "The current canvas type is inconsistent and cannot be pasted.", "workflow_node_from_store": "Reference from store", "workflow_node_has_delete": "The node has been deleted", "workflow_node_invalid": "The node is invalid", "workflow_node_lose_efficacy": "The plugin {name} is invalid", "workflow_node_lose_efficacy_wf": "The workflow {name} is invalid", "workflow_node_stoprun": "Run terminated", "workflow_node_title_duplicated": "The node name already exists.", "workflow_note_bold": "Bold", "workflow_note_bulleted_list": "Unordered list", "workflow_note_heading": "Title", "workflow_note_hyperlink": "Hyperlink", "workflow_note_italic": "Italic", "workflow_note_main_text": "Body", "workflow_note_numbered_list": "Ordered list", "workflow_note_placeholder": "Enter a comment…", "workflow_note_quote": "Reference", "workflow_note_strikethrough": "Strikethrough", "workflow_note_underline": "Underline", "workflow_order_by_empty": "Sorting method not configured yet", "workflow_order_by_title": "Sorting method", "workflow_pad_friendly": "Touchpad-friendly", "workflow_pad_friendly_desc": "Drag with two fingers in the same direction, and zoom with a pinch or spread gesture", "workflow_prompt_editor_expand": "Show", "workflow_prompt_editor_skill": "Skills", "workflow_prompt_editor_variable": "Variables", "workflow_prompt_editor_view_library": "Prompt library", "workflow_publish_multibranch_PleaseSelect": "Please select", "workflow_publish_multibranch_RetainedResult": "Saved results", "workflow_publish_multibranch_auto_saved": "Draft automatically saved at {time}", "workflow_publish_multibranch_change_picture": "Modify icon", "workflow_publish_multibranch_changes": "Change", "workflow_publish_multibranch_changetype": "Change type", "workflow_publish_multibranch_default_value": "Default value", "workflow_publish_multibranch_diffNodice": "The latest version has some differences from your draft. You should choose a version and merge it into your draft.", "workflow_publish_multibranch_diff_btn": "View differences", "workflow_publish_multibranch_hidediff": "Hide differences", "workflow_publish_multibranch_history": "History", "workflow_publish_multibranch_latest_version": "Latest version", "workflow_publish_multibranch_merge": "<PERSON><PERSON>", "workflow_publish_multibranch_merge_comfirm": "New version available", "workflow_publish_multibranch_merge_comfirm_desc": "Someone has submitted an updated version, and you need to pull and merge it into your draft before submitting it.", "workflow_publish_multibranch_merge_retrieve": "<PERSON><PERSON>", "workflow_publish_multibranch_merge_success": "Merged into draft successfully", "workflow_publish_multibranch_merge_to_draft": "Merge to draft", "workflow_publish_multibranch_modify": "Modify", "workflow_publish_multibranch_modity_flow": "Modify workflow", "workflow_publish_multibranch_my_draft": "My draft", "workflow_publish_multibranch_no_diff": "The latest version does not conflict with the current draft. The latest version can be merged directly into the current draft.", "workflow_publish_multibranch_nodiff": "No difference", "workflow_publish_multibranch_property": "Attributes", "workflow_publish_multibranch_publish_btn": "Publish", "workflow_publish_multibranch_publish_comfirm_title": "Are you sure you want to publish?", "workflow_publish_multibranch_publish_confirm_content": "After publishing, this version will not take effect on other agents/workflows", "workflow_publish_multibranch_publish_disabled_tooltip": "Can be published after submission", "workflow_publish_multibranch_published": "{name} published at {time}", "workflow_publish_multibranch_published_title": "Publish", "workflow_publish_multibranch_revert_confirm_content": "After reverting, the current unsubmitted draft version will be overwritten", "workflow_publish_multibranch_revert_confirm_title": "Revert to this version", "workflow_publish_multibranch_revert_success": "Recovery successful", "workflow_publish_multibranch_submit_btn": "Submit", "workflow_publish_multibranch_submit_comfirm": "Submit to workspace", "workflow_publish_multibranch_submit_comfirm_desc": "After submission, this version can be viewed by other members in the workspace. It will not take effect on other agents/workflows before being published directly.", "workflow_publish_multibranch_submit_disabled_tooltip_nochange": "No changes to submit", "workflow_publish_multibranch_submit_success": "Submitted to workspace successfully", "workflow_publish_multibranch_submitted": "{name} submitted at {time}", "workflow_publish_multibranch_submitted_title": "Submit", "workflow_publish_multibranch_view_conflict": "View conflicts only", "workflow_publish_multibranch_view_lastest_version": "View the latest version", "workflow_publish_multibranch_viewhistory": "View history", "workflow_publish_multibranch_workflow_btn": "Workflow list", "workflow_publish_multibranch_workflow_describe": "Describe", "workflow_publish_multibranch_workflow_flow": "Workflow structure", "workflow_publish_multibranch_workflow_name": "Name", "workflow_publish_multibranch_workflow_picture": "Icon", "workflow_publish_not_testrun_ insist": "Still publish", "workflow_publish_not_testrun_content": "No test run before release. It is recommended to ensure that the workflow is running normally before release.", "workflow_publish_not_testrun_title": "No test run before release.", "workflow_query_condition_title": "Query condition", "workflow_query_fields_empty": "Please add query field", "workflow_query_fields_name": "Field name", "workflow_query_fields_remove_duplicates": "Deduplicate", "workflow_query_fields_title": "Query field", "workflow_query_limit_title": "Query limit", "workflow_ques_ans_testrun_botname": "Bot", "workflow_ques_ans_testrun_dulpicate": "Option content must be unique", "workflow_ques_ans_testrun_message_placeholder": "Send a message", "workflow_ques_ans_testrun_username": "User", "workflow_ques_ans_type": "Please select a response type", "workflow_ques_ans_type_direct": "Reply directly", "workflow_ques_ans_type_direct_checkbox": "Extract fields from response", "workflow_ques_ans_type_direct_checkbox_tooltips": "When enabled, information is extracted from user inputs", "workflow_ques_ans_type_direct_context_setting_tooltips": "The maximum number of responses you are allowed to answer the question. The workflow is terminated if you fail to provide the required key fields multiple times.", "workflow_ques_ans_type_direct_exrtact_context_setting": "Max. responses settings", "workflow_ques_ans_type_direct_exrtact_title": "Set the key fields that need to be extracted from the user's response", "workflow_ques_ans_type_direct_key_decr": "User input for this round of dialog", "workflow_ques_ans_type_option": "Reply based on options", "workflow_ques_ans_type_option_content": "Content", "workflow_ques_ans_type_option_label": "Set option content", "workflow_ques_ans_type_option_other": "Other", "workflow_ques_ans_type_option_other_placeholder": "This option is not visible to you and is activated when you reply with irrelevant content", "workflow_ques_ans_type_option_title": "Option", "workflow_ques_content": "Question content", "workflow_ques_content_placeholder": "You can use {{variable name}} to reference variables in input parameters", "workflow_ques_content_tooltips": "Specific content description for questions to the user", "workflow_ques_input_tooltips": "Enter parameters to be added to the question, which can be referenced by the questions below", "workflow_ques_option_notempty": "The option content cannot be empty", "workflow_ques_output_tooltips": "Responses will be referenced as variables by other nodes.", "workflow_question_ dynamic_content": "Dynamic content", "workflow_question_add_option": "Add option", "workflow_question_az": "A-Z", "workflow_question_dynamic": "dynamicOption", "workflow_question_fixed_content": "Fixed content", "workflow_question_sp": "System prompt", "workflow_question_sp_placeholder": "Supports additional system prompts, such as persona and response logic settings, to enhance the flow of follow-up questions", "workflow_question_sp_setting": "System prompt settings", "workflow_refer_var_type_same": "Please use the same type as the referenced variable ({type}) to ensure successful conversion.", "workflow_remove_to_workflow": "remove from workflow", "workflow_role_config_avatar": "Role avatar", "workflow_role_config_default_input_tooltip": "Input area styles for different default input methods", "workflow_role_config_description_placeheader": "Provide a description for this role", "workflow_role_config_input_title": "User input method", "workflow_role_config_name_tooltip": "Each chatflow indicates a unique role, which is displayed as a nickname in the chat", "workflow_role_config_onboarding": "You can configure the roles here at any time", "workflow_role_config_text_2_voice": "Text-to-speech", "workflow_role_config_title": "Role configurations", "workflow_role_config_title_tooltip": "Role information can be dynamically configured in the UI Builder. When the role information is accessed, the UI Builder's configuration will be applied first.", "workflow_role_config_voices_title": "Agent voice", "workflow_running_results": "Running result", "workflow_running_results_banner": "View test run results", "workflow_running_results_error_code": "Code cannot be empty", "workflow_running_results_error_executeid": "Execution ID", "workflow_running_results_error_node": "Node exception", "workflow_running_results_error_sys": "System error", "workflow_running_results_line_error": "The outgoing line of a concurrent branch is not allowed to enter another concurrent branch.", "workflow_running_results_noresult_content": "Click the \"Test Run\" button and the running result will be displayed here", "workflow_running_results_noresult_title": "No running result", "workflow_running_results_run_failed": "Running failed", "workflow_saved_database": "Debugging data", "workflow_select_and_set_fields_empty": "Please add fields first", "workflow_select_and_set_fields_title": "Select and set fields", "workflow_select_voice_library": "Resource library voice", "workflow_select_voice_official": "Preset voice", "workflow_setting_fields": "Configure field", "workflow_start_no_parameter": "Undefined input parameters", "workflow_start_trigger_cron_ai": "Generate with AI", "workflow_start_trigger_cron_ai_sample": "Example: 0 0 18 * * * means the task will run at 18:00 every day.", "workflow_start_trigger_cron_cancel": "Cancel", "workflow_start_trigger_cron_fillin": "Fill in", "workflow_start_trigger_cron_gen": "Generate with AI", "workflow_start_trigger_cron_gen_stop": "Stop", "workflow_start_trigger_cron_generated": "Generated CRON expression", "workflow_start_trigger_cron_job": "Use CRON expression", "workflow_start_trigger_cron_option": "Select a preset time", "workflow_start_trigger_setting": "Trigger settings", "workflow_start_trigger_setting_tooltips": "Select a method to start the workflow. You can enable a scheduled or custom workflow.", "workflow_start_trigger_triggername": "<PERSON><PERSON>", "workflow_stringprocess_concat_array_desc": "Use the following symbols to automatically concatenate each item in the array", "workflow_stringprocess_concat_array_symbol_title": "Concatenation symbol", "workflow_stringprocess_concat_array_title": "Array concatenation symbol setting", "workflow_stringprocess_concat_symbol_comma": "Comma", "workflow_stringprocess_concat_symbol_custom": "Customise", "workflow_stringprocess_concat_symbol_lineBreak": "Line Break", "workflow_stringprocess_concat_symbol_period": "Period", "workflow_stringprocess_concat_symbol_semicolon": "Semicolon", "workflow_stringprocess_concat_symbol_space": "Space", "workflow_stringprocess_concat_symbol_tab": "Tab Break", "workflow_stringprocess_concat_tips": "You can use {{variable name}}、{{variable naime.subvariable name}}、{{variable name[array index]}} to reference variables in the input parameters", "workflow_stringprocess_delimiter_option": "Select Delimiter", "workflow_stringprocess_delimiter_title": "Delimiter", "workflow_stringprocess_delimiter_tooltips": "The symbol used to separate strings (pay attention to the difference between full-width and half-width characters), after separation, it will automatically turn into an array of strings.", "workflow_stringprocess_dulpicate_hover": "There are duplicate symbols", "workflow_stringprocess_input_tooltips": "Please enter string-type parameters, these parameters can be used to handle specific formats.", "workflow_stringprocess_max_length_item": "Add up to {maxLength} custom symbols", "workflow_stringprocess_node_method": "Select application", "workflow_stringprocess_node_method_concat": "String concatenation", "workflow_stringprocess_node_method_sep": "String separation", "workflow_stringprocess_output_tooltips": "The processed string output result", "workflow_subcanvas_never_remind": "Don't show this again", "workflow_subcanvas_pull_out": "Hold {ctrl} and drag the node to drag it out", "workflow_subcanvas_remove": "Remove from sub-canvas", "workflow_submit_not_testrun_content": "You have not performed a test run before submitting the project. Make sure that the workflow is normal.", "workflow_submit_not_testrun_insist": "Continue to submit", "workflow_submit_not_testrun_title": "Test run not performed", "workflow_subwf_jump_detail": "Workflow detail", "workflow_tab_title": "{name} - Workflow - Agent platform", "workflow_test_nodeerror": "Nodes with validation failures detected, please receonfigure and create again", "workflow_testrun_check list_batchbody_end_unconnect": "The end port of the batch body is not connected", "workflow_testrun_check list_batchbody_start_unconnect": "The start port of the batch body is not connected", "workflow_testrun_check list_loopbody_end_unconnect": "The end port of the loop body is not connected", "workflow_testrun_check list_loopbody_start_unconnect": "The start port of the loop body is not connected", "workflow_testrun_form_vailate_error_toast": "Unable to run. Required fields are not filled in or there are content errors.", "workflow_testrun_hangup_answer": "Answer the questions, then test run.", "workflow_testrun_hangup_input": "Enter the content, then test run.", "workflow_testrun_input_form_empty": "The current test run requires no input", "workflow_testrun_one_node_cancel_run_tooltips": "Cancel", "workflow_testset_aifail": "AI generation failed, Please try again", "workflow_testset_aigenerate": "Generate with AI", "workflow_testset_available": "Available testsets", "workflow_testset_create": "create manually", "workflow_testset_create_btn": "Create testset", "workflow_testset_create_tip": "Click button to create a testset", "workflow_testset_create_title": "Create Testset", "workflow_testset_creation_time": "Created at {xxx}", "workflow_testset_delete_cancel": "Cancel", "workflow_testset_delete_confirm": "Confirm", "workflow_testset_delete_tip": "This operation will not be reversed", "workflow_testset_delete_title": "Delete the testset", "workflow_testset_desc": "Testset description", "workflow_testset_desc_placeholder": "Please enter description", "workflow_testset_edit_confirm": "Save", "workflow_testset_edit_title": "Edit Testset", "workflow_testset_edited": "Edited", "workflow_testset_empty": "No testset yet", "workflow_testset_generating": "Generating", "workflow_testset_hover_tips": "Previously created testsets can be selected to auto-fill test input parameters", "workflow_testset_invaild_tip": "{testset_name} is no longer valid", "workflow_testset_invalid_tip": "Workflow has been updated,please check if the testset 「{testsetName}」 is compatible", "workflow_testset_name": "Testset name", "workflow_testset_name_duplicated": "The testset name is duplicated", "workflow_testset_name_placeholder": "Please enter name", "workflow_testset_node_data": "Node Data", "workflow_testset_paramempty": "The workflow lacks input parameters and does not require creating a test set", "workflow_testset_peedit": "The workflow lacks input parameters and cannot be edited", "workflow_testset_please_select": "Please select", "workflow_testset_required_tip": "{param_name} is required", "workflow_testset_save": "Save current runtime input as a testset or ", "workflow_testset_search_empty": "No search result", "workflow_testset_start_node": "Start Node", "workflow_testset_stopgen": "Stop Generating", "workflow_testset_submit_tooltip_for_expert_mode": "After saving, it will be published directly and visible to all collaborators.", "workflow_testset_testruning": "Test run in progress...", "workflow_testset_tilte": "Testsets", "workflow_testset_upload_clean": "Clear all files", "workflow_testset_upload_content": "The size of a single file cannot exceed {xx} MB", "workflow_testset_upload_release": "Release to start uploading", "workflow_testset_upload_title": "Drag and drop file or click to upload", "workflow_testset_upload_uploaded": "Uploaded files", "workflow_testset_vardatabase_node": "Associated agent", "workflow_testset_vardatabase_placeholder": "Please select an agent", "workflow_testset_vardatabase_tip": "Select an agent", "workflow_testset_view_log": "View logs", "workflow_text_copy": "The text has been copied to the clipboard", "workflow_textprocess_concat_symbol_tips": "The default preset character is an escape character, while the custom character input is a non-escape character.", "workflow_textprocess_custom_shading": "Please enter the concatenation symbol", "workflow_toolbar_add_node": "Add node", "workflow_toolbar_comment_tooltips": "Comment", "workflow_toolbar_minimap_tooltips": "<PERSON><PERSON><PERSON><PERSON>", "workflow_toolbar_zoom_fit": "Auto-fit", "workflow_toolbar_zoom_in": "Zoom in", "workflow_toolbar_zoom_out": "Zoom out", "workflow_toolbar_zoom_to": "Zoom to", "workflow_trigger_bindwf_lib_error": "The resource library workflow is not available. Please copy it to the project before use.", "workflow_trigger_creat_name_placeholder": "Enter a parameter value", "workflow_trigger_cron_gen_prompt_placeholder": "Example: 18:00 every day", "workflow_trigger_cron_gen_sample_placeholder": "You can use natural language in the prompt, such as \"18:00 every day\", and it will generate the corresponding CRON expression. For example, \"0 0 18 * * *\" means the task will run at 18:00 every day.", "workflow_trigger_param_unvalid_cron": "Invalid CRON expression of the parameter", "workflow_trigger_user_create_action": "Action", "workflow_trigger_user_create_advice": "Create with trigger nodes", "workflow_trigger_user_create_bind": "Bind workflow", "workflow_trigger_user_create_bind_tooltips": "Select a workflow. When the trigger is activated, the selected workflow is executed.", "workflow_trigger_user_create_close": "Close", "workflow_trigger_user_create_id": "ID", "workflow_trigger_user_create_id_tooltips": "Trigger unique ID: If this field is left empty, a new trigger will be created by default. If a value is specified, the existing trigger will be updated. Note that the specified ID must already exist in the trigger list.", "workflow_trigger_user_create_list": "Trigger list", "workflow_trigger_user_create_list_read": "View", "workflow_trigger_user_create_name": "Name", "workflow_trigger_user_create_name_tooltips": "The name of the trigger, which can be used to identify the purpose.", "workflow_trigger_user_create_nodata": "No data found", "workflow_trigger_user_create_refresh": "Refresh", "workflow_trigger_user_create_schedula": "Trigger time", "workflow_trigger_user_create_time": "Creation time", "workflow_trigger_user_create_userid": "User ID", "workflow_trigger_user_create_userid_tooltips": "The user to which the trigger belongs. You can use the system variable sys_uuid to uniquely identify the user.", "workflow_unpublish_change": "There are unpublished changes", "workflow_update_condition_title": "Update condition", "workflow_update_fields": "Update field", "workflow_user_trigger_banner": "After completing the settings, you need publish the project to make the scheduled task take effect.", "workflow_user_trigger_list_descr": "View and manage the created trigger tasks", "workflow_var_merge_ strategy_returnnotnull": "Return the first non-empty value in each group", "workflow_var_merge_ strategy_tooltips": "Apply the corresponding aggregation strategy to variables within the same group", "workflow_var_merge_addGroup": "Add a new group", "workflow_var_merge_delete_limit": "At least one group must be retained", "workflow_var_merge_group_tooltips": "The variable type for the group defaults to the type of the first variable", "workflow_var_merge_name_lengthmax": "The name cannot exceed 20 characters in length", "workflow_var_merge_number_max": "The maximum number of groups cannot exceed 50", "workflow_var_merge_output_namedul": "Duplicate name", "workflow_var_merge_output_tooltips": "The result returned after group aggregation", "workflow_var_merge_strategy": "Aggregation strategy", "workflow_var_merge_strategy_hovertips": "Only one aggregation mode is supported", "workflow_var_merge_var_err_noempty": "Please select at least one variable", "workflow_var_merge_var_err_sametype": "The variable type must be consistent with the group variable type", "workflow_var_merge_var_number_max": "The maximum number of variables cannot exceed 50", "workflow_var_merge_var_placeholder": "Please select a variable", "workflow_var_type_same": "This variable must be of the {type} type. Please use the same type to ensure successful conversion.", "workflow_variable_ref_placeholder": "Reference parameter values", "workflow_variable_refer_no_input": "There are no variables in the input parameters", "workflow_variable_refer_no_sub_variable": "The variable has no subVariables", "workflow_variable_select_voice": "Select voice", "workflow_variable_undefined": "Undefined", "workflow_version_add_model_content": "A new resource version from V{oldVersion} to V{newVersion} is available in the workflow. After you add the latest resource to the workflow, all nodes using it will be upgraded.", "workflow_version_number_error1": "Invalid version number format", "workflow_version_number_error2": "The version number cannot be less than or equal to the current version number", "workflow_version_origin_text": "Resource Library", "workflow_version_origin_tooltips": "Reference from the resource library", "workflow_version_update_model_content": "A new resource version from V{myVersion} to V{latestVersion} is available. After you add the latest resource to the workflow, all nodes using it will be upgraded.", "workflow_version_update_model_title": "Upgrade to the new version?", "workflow_version_update_placeholder": "Please describe the content of this version update", "workflow_version_update_tag_tooltips": "A new version is available", "workflow_view_data": "View data", "workflowstore_card_duplicate": "Copy", "workflowstore_category1": "Recommended", "workflowstore_continue_editing": "Continue editing here", "workflowstore_delete_permission": "This workflow has been released in the store. Please contact the workflow creator to remove it and delete it.", "workflowstore_duplicate_and_add": "Duplicate and add", "workflowstore_remove_wf": "Remove workflow", "workflowstore_submit": "Submit and publish", "workflowstore_submit_update": "Submit and update", "workflowstore_the_workflow_has_been": "This workflow has been published in the store. Please remove it and try again.", "workflowstore_unable_to_delete_workflow": "Unable to delete the workflow", "workflowstore_workflow_copy_successful": "Workflow copy created", "worklfow_condition_add_condition": "Add", "worklfow_condition_condition_branch": "Condition branch", "worklfow_condition_else_if": "Else if", "worklfow_condition_if": "If", "worklfow_condition_priority": "Priority", "worklfow_start_basic_setting": "Basic settings", "worklfow_trigger_bind_delete": "The bound workflow is invalid", "worklfow_without_run": "Workflow is not running", "workspace_create": "Create", "workspace_develop": "Development", "workspace_develop_search_project": "Search for projects", "workspace_library_search": "Search resources", "workspace_no_permission_access": "No permission to access this workspace."}