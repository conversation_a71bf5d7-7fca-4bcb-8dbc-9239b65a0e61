{"name": "@coze-studio/studio-i18n-resource-adapter", "version": "0.0.1", "author": "<EMAIL>", "main": "./src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "exit 0", "test:cov": "exit 0"}, "dependencies": {}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18"}, "peerDependencies": {"react": "~18.2.0", "react-dom": "~18.2.0"}}