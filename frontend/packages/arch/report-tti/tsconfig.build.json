{"extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"strictNullChecks": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "../bot-typings/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../logger/tsconfig.build.json"}], "$schema": "https://json.schemastore.org/tsconfig"}