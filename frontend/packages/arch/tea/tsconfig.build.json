{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "strictNullChecks": true, "noImplicitAny": true, "paths": {"byted-tea-sdk": ["./node_modules/byted-tea-sdk"]}, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../bot-typings/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../tea-adapter/tsconfig.build.json"}, {"path": "../tea-interface/tsconfig.build.json"}]}