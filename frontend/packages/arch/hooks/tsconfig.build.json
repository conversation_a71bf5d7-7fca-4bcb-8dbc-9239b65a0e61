{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "module": "CommonJS", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo", "target": "ES2020", "moduleResolution": "node"}, "include": ["src"], "exclude": ["node_modules", "dist", "**/__tests__/*"], "references": [{"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}]}