{"name": "@coze-arch/hooks", "version": "0.0.1", "description": "coze arch hooks", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "sideEffects": false, "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"lodash-es": "^4.17.21", "query-string": "^8.1.0"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "sucrase": "^3.32.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": "~18.2.0", "react-dom": "~18.2.0"}}