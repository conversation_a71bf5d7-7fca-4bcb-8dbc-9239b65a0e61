/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useState, useMemo } from 'react';

export interface ReturnValue {
  state: boolean;
  setTrue: () => void;
  setFalse: () => void;
  toggle: (value?: boolean) => void;
}

export default (initialValue?: boolean): ReturnValue => {
  const [state, setState] = useState(Boolean(initialValue));

  const stateMethods = useMemo(() => {
    const setTrue = () => setState(true);
    const setFalse = () => setState(false);
    const toggle = (val?: boolean) =>
      setState(typeof val === 'boolean' ? val : s => !s);
    return {
      setTrue,
      setFalse,
      toggle,
    };
  }, []);

  return {
    state,
    ...stateMethods,
  };
};
