{"name": "@coze-arch/bot-tea", "version": "0.0.1", "description": "bot tea wrapper", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./utils": "./src/utils.ts"}, "main": "src/index.ts", "typesVersions": {"*": {"utils": ["./src/utils.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/tea": "workspace:*", "query-string": "^8.1.0"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@vitest/coverage-v8": "~3.0.5", "vitest": "~3.0.5"}}