# @coze-arch/bot-tea

bot tea wrapper

## Overview

This package is part of the Coze Studio monorepo and provides architecture functionality. It includes store, plugin, logger.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/bot-tea": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/bot-tea';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Store
- Plugin
- Logger

## API Reference

### Exports

- `EVENT_NAMES,
  AddWorkflowToStoreEntry,
  ExploreBotCardCommonParams,
  ShareRecallPageFrom,
  PluginMockSetCommonParams,
  SideNavClickCommonParams,
  AddPluginToStoreEntry,
  AddBotToStoreEntry,
  PublishAction,
  BotDetailPageAction,
  PluginPrivacyAction,
  PluginMockDataGenerateMode,
  ParamsTypeDefine,
  BotShareConversationClick,
  FlowStoreType,
  FlowResourceFrom,
  FlowDuplicateType,
  /**  product event types */
  ProductEventSource,
  ProductEventFilterTag,
  ProductEventEntityType,
  ProductShowFrontParams,
  DocClickCommonParams,`
- `const LANDING_PAGE_URL_KEY = 'coze_landing_page_url';`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
