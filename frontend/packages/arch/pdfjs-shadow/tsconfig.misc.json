{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "vitest.config.ts", "rspack.config.ts", "package.json"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDir": "./", "resolveJsonModule": true, "types": ["vitest/globals"], "strictNullChecks": true, "noImplicitAny": true, "outDir": "./dist"}}