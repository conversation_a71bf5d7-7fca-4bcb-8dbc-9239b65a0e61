{"extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"jsx": "preserve", "useUnknownInCatchVariables": false, "types": ["vitest/globals"], "strictNullChecks": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["./src"], "references": [{"path": "../bot-typings/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../logger/tsconfig.build.json"}, {"path": "../web-context/tsconfig.build.json"}], "$schema": "https://json.schemastore.org/tsconfig"}