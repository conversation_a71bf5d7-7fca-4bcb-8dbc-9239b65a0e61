{"name": "@coze-arch/bot-http", "version": "0.0.1", "description": "Global Context for whole hot studio web app. You should keep using this package instead of call `window.xxx` directly", "author": "<EMAIL>", "main": "./src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "NODE_OPTIONS='--max_old_space_size=2048' NODE_ENV=test vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/logger": "workspace:*", "axios": "^1.4.0"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-arch/web-context": "workspace:*", "@vitest/coverage-v8": "~3.0.5", "axios-mock-adapter": "^1.22.0", "debug": "^4.3.4", "sucrase": "^3.32.0", "tsconfig-paths": "4.1.0", "vitest": "~3.0.5"}, "// deps": "debug@^4.3.4 为脚本自动补齐，请勿改动"}