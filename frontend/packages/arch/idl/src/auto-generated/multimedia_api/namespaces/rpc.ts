/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ActionKey {
  /** 复制 */
  Copy = 1,
  /** 删除 */
  Delete = 2,
  /** 启用/禁用 */
  EnableSwitch = 3,
  /** 编辑 */
  Edit = 4,
}

export enum ChatEventType {
  /** 会话事件 */
  Unknown = 0,
  ChatCreated = 1,
  ChatInProgress = 2,
  ChatCompleted = 3,
  ChatFailed = 4,
  ChatExpired = 5,
  ChatCancelled = 6,
  ChatRequiresAction = 7,
  /** 消息事件 */
  MessageDelta = 20,
  MessageCompleted = 21,
  /** 错误事件 */
  Error = 98,
  /** 流结束 */
  StreamDone = 99,
  AudioDelta = 200,
}

export enum ChatStatus {
  StatusUnknown = 0,
  Created = 1,
  InProgress = 2,
  Compleated = 3,
  Failed = 4,
  Expired = 5,
  Cancelled = 6,
  RequiresAction = 7,
}

export enum FaasCallbackEventType {
  CreatePreviewAudio = 1,
  /** 同步账户下的音色资源到数据库 */
  SyncVolcanoVoice = 2,
  FixUserVoice = 3,
  /** 同步到资源库 */
  SyncUserVoiceToSourceLibrary = 4,
  /** 语音资源数量上报 */
  VoiceResourceNumberReport = 5,
  /** RTC 用量上报 */
  RTCBillingReport = 6,
  /** 同步火山系统音色到数据库 */
  SyncVolcanoSystemVoice = 7,
  /** 同步 Sami 系统音色到数据库 */
  SyncSamiSystemVoice = 8,
  /** 火山账号音色资源存量校验 */
  VolcanoVoiceQuotaCheck = 9,
  /** 购买音色 */
  PurchaseVolcanoVoice = 10,
  /** 音色资源上报失败任务重试 */
  VolcanoVoiceBillingFailTaskRetry = 11,
  /** 同步用户音色年龄和性别 */
  SyncUserVoiceAgeAndGender = 12,
  /** 购买音色并绑定到用户 */
  PurchaseUserVoiceAndBind = 13,
  /** 定时扫描并发任务 */
  ScanParallelTask = 14,
  /** 定期并发归零 */
  ResetParallelTask = 15,
  /** 多情感音色 */
  SyncMultiEmotionVoice = 16,
}

export interface BotConfig {
  character_name?: string;
  propmt?: string;
}

export interface ChatDetail {
  ID?: Int64;
  ConversationID?: Int64;
  BotID?: Int64;
  CreatedAt?: number;
  CompletedAt?: number;
  FailedAt?: number;
  Metadata?: Record<string, string>;
  Status?: ChatStatus;
  LastError?: ChatErrorInfo;
  Usage?: Usage;
  RequiredAction?: RequiredAction;
}

export interface ChatErrorInfo {
  Code?: number;
  Msg?: string;
}

export interface CustomConfig {
  model_config?: ModelConfig;
  bot_config?: BotConfig;
}

/** 展示用，实现方提供展示信息 */
export interface DisplayResourceInfo {
  /** 资源id */
  ResID?: Int64;
  /** 资源描述 */
  Desc?: string;
  /** 资源Icon，完整url */
  Icon?: string;
  /** 资源状态，各类型资源自身定义 */
  BizResStatus?: number;
  /** 是否开启多人编辑 */
  CollaborationEnable?: boolean;
  /** 业务携带的扩展信息，以res_type区分，每个res_type定义的schema和含义不一样，使用前需要判断res_type */
  BizExtend?: Record<string, string>;
  /** 不同类型的不同操作按钮，由资源实现方和前端约定。返回则展示，要隐藏某个按钮，则不要返回； */
  Actions?: Array<ResourceAction>;
  /** 是否禁止进详情页 */
  DetailDisable?: boolean;
}

export interface EnterMessage {
  /** user / assistant */
  role?: string;
  /** 如果是非 text，需要解析 JSON */
  content?: string;
  meta_data?: Record<string, string>;
  /** text, card, object_string */
  content_type?: string;
  /** function_call, tool_output, knowledge, answer, follow_up, verbose, (普通请求可以不填)
用户输入时可用：function_call，tool_output
不支持用户输入使用：follow_up，knowledge，verbose，answer */
  type?: string;
  name?: string;
}

export interface ErrorDetail {
  Code?: number;
  Msg?: string;
  Extra?: Record<string, string>;
}

export interface InterruptFunction {
  Name?: string;
  Arguments?: string;
}

export interface InterruptPlugin {
  ID?: string;
  /** 1 function, 2 require_info */
  Type?: string;
  Function?: InterruptFunction;
  RequireInfo?: InterruptRequireInfo;
}

export interface InterruptRequireInfo {
  RequireFields?: string;
  Name?: string;
}

export interface MessageDetail {
  ID?: Int64;
  ConversationID?: Int64;
  BotID?: Int64;
  ChatID?: Int64;
  /** "user","assistant" ... */
  Role?: string;
  Type?: string;
  Content?: string;
  /** "text", "card" */
  ContentType?: string;
  Metadata?: Record<string, string>;
}

export interface ModelConfig {
  model_id?: string;
}

export interface RequiredAction {
  /** "submit_tool_outputs" */
  Type?: string;
  SubmitToolOutputs?: SubmitToolOutputs;
}

export interface ResourceAction {
  /** 一个操作对应一个唯一的key，key由资源侧约束 */
  key: ActionKey;
  /** ture=可以操作该Action，false=置灰 */
  enable: boolean;
}

export interface SpeakerIdentifyChoice {
  Score?: number;
  FeatureID?: Int64;
  FeatureName?: string;
  FeatureDesc?: string;
}

export interface SubmitToolOutputs {
  ToolCalls?: Array<InterruptPlugin>;
}

/** 对齐 platform，传递 tools */
export interface Tool {
  plugin_id?: Int64;
  parameters?: string;
  api_name?: string;
}

export interface Usage {
  TokenCount?: number;
  OutputTokens?: number;
  InputTokens?: number;
}

export interface VoicePrintGroup {
  ID?: Int64;
  Name?: string;
  Desc?: string;
  IconURL?: string;
}
/* eslint-enable */
