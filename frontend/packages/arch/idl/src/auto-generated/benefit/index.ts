/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as benefit from './namespaces/benefit';
import * as benefit_common from './namespaces/benefit_common';
import * as bot from './namespaces/bot';
import * as copilot_common from './namespaces/copilot_common';
import * as marketplace_common from './namespaces/marketplace_common';
import * as oapi from './namespaces/oapi';
import * as service from './namespaces/service';
import * as wallet from './namespaces/wallet';

export {
  benefit,
  benefit_common,
  bot,
  copilot_common,
  marketplace_common,
  oapi,
  service,
  wallet,
};
export * from './namespaces/benefit';
export * from './namespaces/benefit_common';
export * from './namespaces/bot';
export * from './namespaces/copilot_common';
export * from './namespaces/marketplace_common';
export * from './namespaces/oapi';
export * from './namespaces/service';
export * from './namespaces/wallet';

export type Int64 = string | number;

export default class BenefitService<T> {
  private request: any = () => {
    throw new Error('BenefitService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * GET /api/marketplace/commerce/bot_monetization/get
   *
   * http
   *
   * 获取 bot 收费配置，bot 所有者和协作者可见
   */
  PublicGetBotMonetizationConfig(
    req?: bot.PublicGetBotMonetizationConfigRequest,
    options?: T,
  ): Promise<bot.PublicGetBotMonetizationConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/commerce/bot_monetization/get',
    );
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      bot_monetization_mode: _req['bot_monetization_mode'],
      is_fallback: _req['is_fallback'],
      entity_id: _req['entity_id'],
      entity_type: _req['entity_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/commerce/bot_monetization/save
   *
   * 保存 bot 收费配置草稿，bot 所有者可调用
   */
  PublicSaveBotDraftMonetizationConfig(
    req?: bot.PublicSaveBotDraftMonetizationConfigRequest,
    options?: T,
  ): Promise<bot.PublicSaveBotDraftMonetizationConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/commerce/bot_monetization/save',
    );
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      is_enable: _req['is_enable'],
      free_chat_allowance_count: _req['free_chat_allowance_count'],
      refresh_period: _req['refresh_period'],
      entity_id: _req['entity_id'],
      entity_type: _req['entity_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/wallet/get
   *
   * http
   */
  PublicGetUserBalance(
    req?: wallet.PublicGetUserBalanceRequest,
    options?: T,
  ): Promise<wallet.PublicGetUserBalanceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/wallet/get');
    const method = 'GET';
    const params = { balance_type_list: _req['balance_type_list'] };
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/marketplace/wallet/get_profit */
  PublicGetUserProfitDetail(
    req?: wallet.PublicGetUserProfitDetailRequest,
    options?: T,
  ): Promise<wallet.PublicGetUserProfitDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/wallet/get_profit');
    const method = 'GET';
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * GET /api/marketplace/commerce/bot_monetization/get_open
   *
   * 获取 bot 商业化公开配置，无权限控制
   */
  PublicGetBotOpenMonetizationConf(
    req?: bot.PublicGetBotOpenMonetizationConfRequest,
    options?: T,
  ): Promise<bot.PublicGetBotOpenMonetizationConfResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/commerce/bot_monetization/get_open',
    );
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      entity_id: _req['entity_id'],
      entity_type: _req['entity_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/wallet/history */
  PublicGetUserWalletHistory(
    req?: wallet.PublicGetUserWalletHistoryRequest,
    options?: T,
  ): Promise<wallet.PublicGetUserWalletHistoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/wallet/history');
    const method = 'GET';
    const params = {
      index: _req['index'],
      count: _req['count'],
      type_list: _req['type_list'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/wallet/detail */
  PublicGetUserWalletDetail(
    req?: wallet.PublicGetUserWalletDetailRequest,
    options?: T,
  ): Promise<wallet.PublicGetUserWalletDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/wallet/detail');
    const method = 'GET';
    const params = { balance_type: _req['balance_type'] };
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/marketplace/commerce/get_user_benefit
   *
   * 获取用户权益通用详情
   */
  PublicGetUserBenefit(
    req?: benefit.PublicGetUserBenefitRequest,
    options?: T,
  ): Promise<benefit.PublicGetUserBenefitResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/commerce/get_user_benefit');
    const method = 'GET';
    const params = {
      benefit_types: _req['benefit_types'],
      coze_account_id: _req['coze_account_id'],
      coze_account_type: _req['coze_account_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /v1/commerce/benefit/bill_tasks
   *
   * --------------------------- OpenAPI ---------------------------
   *
   * 创建流水导出任务
   */
  OapiCreateBillDownloadTask(
    req?: oapi.CreateBillDownloadTaskRequest,
    options?: T,
  ): Promise<oapi.CreateBillDownloadTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/commerce/benefit/bill_tasks');
    const method = 'POST';
    const data = { started_at: _req['started_at'], ended_at: _req['ended_at'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v1/commerce/benefit/bill_tasks
   *
   * 查询流水结果
   */
  OapiListBillDownloadTask(
    req?: oapi.ListBillDownloadTaskRequest,
    options?: T,
  ): Promise<oapi.ListBillDownloadTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/commerce/benefit/bill_tasks');
    const method = 'GET';
    const params = {
      task_ids: _req['task_ids'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /v1/commerce/benefit/limitations/:benefit_id
   *
   * 更新限流配置
   */
  UpdateBenefitLimitation(
    req?: oapi.UpdateBenefitLimitationRequest,
    options?: T,
  ): Promise<oapi.UpdateBenefitLimitationResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/v1/commerce/benefit/limitations/${_req['benefit_id']}`,
    );
    const method = 'PUT';
    const data = {
      active_mode: _req['active_mode'],
      started_at: _req['started_at'],
      ended_at: _req['ended_at'],
      duration: _req['duration'],
      limit: _req['limit'],
      status: _req['status'],
      trigger_unit: _req['trigger_unit'],
      trigger_time: _req['trigger_time'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v1/commerce/benefit/limitations
   *
   * 创建限流配置
   */
  CreateBenefitLimitation(
    req?: oapi.CreateBenefitLimitationRequest,
    options?: T,
  ): Promise<oapi.CreateBenefitLimitationResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/commerce/benefit/limitations');
    const method = 'POST';
    const data = {
      entity_type: _req['entity_type'],
      entity_id: _req['entity_id'],
      benefit_info: _req['benefit_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /v1/commerce/benefit/limitations
   *
   * 查询限流配置
   */
  ListBenefitLimitation(
    req?: oapi.ListBenefitLimitationRequest,
    options?: T,
  ): Promise<oapi.ListBenefitLimitationResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/commerce/benefit/limitations');
    const method = 'GET';
    const params = {
      entity_type: _req['entity_type'],
      entity_id: _req['entity_id'],
      benefit_type: _req['benefit_type'],
      status: _req['status'],
      page_token: _req['page_token'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/marketplace/commerce/benefit/limitations/:benefit_id
   *
   * 更新限流配置
   */
  PublicUpdateBenefitLimitation(
    req?: benefit.PublicUpdateBenefitLimitationRequest,
    options?: T,
  ): Promise<benefit.PublicUpdateBenefitLimitationResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/marketplace/commerce/benefit/limitations/${_req['benefit_id']}`,
    );
    const method = 'PUT';
    const data = {
      limit: _req['limit'],
      status: _req['status'],
      trigger_unit: _req['trigger_unit'],
      trigger_time: _req['trigger_time'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/commerce/benefit/limitations
   *
   * 查询限流配置
   */
  PublicListBenefitLimitation(
    req?: benefit.PublicListBenefitLimitationRequest,
    options?: T,
  ): Promise<benefit.PublicListBenefitLimitationResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/commerce/benefit/limitations',
    );
    const method = 'GET';
    const params = {
      entity_type: _req['entity_type'],
      entity_id: _req['entity_id'],
      benefit_type: _req['benefit_type'],
      status: _req['status'],
      page_token: _req['page_token'],
      page_size: _req['page_size'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/commerce/benefit/limitations
   *
   * 创建限流配置
   */
  PublicCreateBenefitLimitation(
    req?: benefit.PublicCreateBenefitLimitationRequest,
    options?: T,
  ): Promise<benefit.PublicCreateBenefitLimitationResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/commerce/benefit/limitations',
    );
    const method = 'POST';
    const data = {
      entity_type: _req['entity_type'],
      entity_id: _req['entity_id'],
      benefit_info: _req['benefit_info'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/commerce/benefit/update_subscription_renewal_info
   *
   * 修改续费信息
   */
  PublicUpdateSubscriptionRenewalInfo(
    req?: benefit.PublicUpdateSubscriptionRenewalInfoRequest,
    options?: T,
  ): Promise<benefit.PublicUpdateSubscriptionRenewalInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/commerce/benefit/update_subscription_renewal_info',
    );
    const method = 'POST';
    const data = {
      renewal_type: _req['renewal_type'],
      renewal_period_times: _req['renewal_period_times'],
    };
    const params = {
      coze_account_id: _req['coze_account_id'],
      coze_account_type: _req['coze_account_type'],
    };
    return this.request({ url, method, data, params }, options);
  }
}
/* eslint-enable */
