/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as ability_provider from './namespaces/ability_provider';
import * as copilot from './namespaces/copilot';
import * as copilot_common from './namespaces/copilot_common';
import * as model_manage from './namespaces/model_manage';
import * as ocean_cloud_admin_api from './namespaces/ocean_cloud_admin_api';

export {
  ability_provider,
  copilot,
  copilot_common,
  model_manage,
  ocean_cloud_admin_api,
};
export * from './namespaces/ability_provider';
export * from './namespaces/copilot';
export * from './namespaces/copilot_common';
export * from './namespaces/model_manage';
export * from './namespaces/ocean_cloud_admin_api';

export type Int64 = string | number;

export default class PromptAdminApiService<T> {
  private request: any = () => {
    throw new Error('PromptAdminApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/admin/v1/model/model_meta/create
   *
   * 模型创建
   */
  CreateModelMeta(
    req: ocean_cloud_admin_api.CreateModelMetaReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateModelMetaResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model_meta/create');
    const method = 'POST';
    const data = {
      model_name: _req['model_name'],
      family: _req['family'],
      version: _req['version'],
      desc: _req['desc'],
      show_name: _req['show_name'],
      icon: _req['icon'],
      capability: _req['capability'],
      quota: _req['quota'],
      model_config: _req['model_config'],
      prompt_conf: _req['prompt_conf'],
      legacy_fields: _req['legacy_fields'],
      parameters: _req['parameters'],
      display_properties: _req['display_properties'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /admin/redirect
   *
   * 后端用来鉴权 redirect
   */
  CasLogin(
    req?: ocean_cloud_admin_api.CasLoginReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CasLoginResp> {
    const _req = req || {};
    const url = this.genBaseURL('/admin/redirect');
    const method = 'GET';
    const params = { callback: _req['callback'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/admin/v1/model/model_meta/update */
  UpdateModelMeta(
    req?: ocean_cloud_admin_api.UpdateModelMetaReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateModelMetaResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model_meta/update');
    const method = 'POST';
    const data = {
      model_id: _req['model_id'],
      model_name: _req['model_name'],
      family: _req['family'],
      version: _req['version'],
      desc: _req['desc'],
      show_name: _req['show_name'],
      icon: _req['icon'],
      capability: _req['capability'],
      quota: _req['quota'],
      model_config: _req['model_config'],
      prompt_conf: _req['prompt_conf'],
      legacy_fields: _req['legacy_fields'],
      parameters: _req['parameters'],
      display_properties: _req['display_properties'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_meta/delete */
  DeleteModelMeta(
    req?: ocean_cloud_admin_api.DeleteModelMetaReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.DeleteModelMetaResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model_meta/delete');
    const method = 'POST';
    const data = { model_id: _req['model_id'] };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_scene/create */
  CreateModelWithScene(
    req: ocean_cloud_admin_api.CreateModelWithSceneReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateModelWithSceneResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model_scene/create');
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      model_id: _req['model_id'],
      show_name: _req['show_name'],
      ranking: _req['ranking'],
      icon: _req['icon'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_scene/update */
  UpdateModelWithScene(
    req: ocean_cloud_admin_api.UpdateModelWithSceneReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateModelWithSceneResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model_scene/update');
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      model_id: _req['model_id'],
      show_name: _req['show_name'],
      icon: _req['icon'],
      ranking: _req['ranking'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_scene/delete */
  DeleteModelWithScene(
    req: ocean_cloud_admin_api.DeleteModelWithSceneReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.DeleteModelWithSceneResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model_scene/delete');
    const method = 'POST';
    const data = { scene: _req['scene'], model_id: _req['model_id'] };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/admin/v1/model/model_meta/list
   *
   * 因为 AGW 中get 入参只支持逗号分割，但是前端请求是roleIds[]=1&roleIds[]=2，所以这里只能用 post 方法
   */
  GetModelMetaList(
    req?: ocean_cloud_admin_api.GetModelMetaListReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetModelMetaListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model_meta/list');
    const method = 'POST';
    const data = {
      model_id: _req['model_id'],
      status: _req['status'],
      model_name: _req['model_name'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_scene/list */
  GetModelListWithScene(
    req: ocean_cloud_admin_api.GetModelListWithSceneReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetModelListWithSceneResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model_scene/list');
    const method = 'POST';
    const data = {
      scene: _req['scene'],
      has_delete_model: _req['has_delete_model'],
      model_name: _req['model_name'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/admin/v1/model/enum_data_list
   *
   * 一些枚举值的定义，方便前端数据获取
   */
  GetCommonEnumData(
    req?: ocean_cloud_admin_api.GetCommonEnumDataReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetCommonEnumDataResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/enum_data_list');
    const method = 'POST';
    const data = { enum_type: _req['enum_type'] };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_meta/template */
  GetModelMetaTemplate(
    req?: ocean_cloud_admin_api.GetModelMetaTemplateReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GeteModelMetaTemplateResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model_meta/template');
    const method = 'POST';
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, headers }, options);
  }

  /** POST /api/admin/v1/model/model_meta/bind_scene_model_list */
  GetBindSceneModel(
    req?: ocean_cloud_admin_api.GetBindSceneModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetBindSceneModelResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/model/model_meta/bind_scene_model_list',
    );
    const method = 'POST';
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, headers }, options);
  }

  /**
   * POST /api/admin/v1/model/perm/get_role_list
   *
   * GetPermModuleListResp GetPermModuleList(1:GetPermModuleListReq req)(api.post = '/api/admin/v1/perm/module_list', api.category = "权限管理") // 用户有权限的模块
   *
   * 获取模型管理拥有的角色权限以及用户当前有的权限
   */
  GetRoleListForModel(
    req?: ocean_cloud_admin_api.GetRoleListForModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetRoleListForModelResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/perm/get_role_list');
    const method = 'POST';
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, headers }, options);
  }

  /** POST /api/admin/v1/model/model_entity/create */
  CreateModelEntity(
    req: ocean_cloud_admin_api.CreateModelEntityReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateModelEntityResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model_entity/create');
    const method = 'POST';
    const data = {
      model_name: _req['model_name'],
      targets: _req['targets'],
      biz: _req['biz'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_entity/query */
  QueryModelEntity(
    req?: ocean_cloud_admin_api.QueryModelEntityReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.QueryModelEntityResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model_entity/query');
    const method = 'POST';
    const data = {
      model_name: _req['model_name'],
      index: _req['index'],
      page_size: _req['page_size'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/admin/v1/model/model/query
   *
   * 融合后的模型列出和场景列出
   */
  ModelQuery(
    req?: ocean_cloud_admin_api.ModelQueryRequest,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetModelMetaListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model/query');
    const method = 'POST';
    const data = {
      ModelIds: _req['ModelIds'],
      Scene: _req['Scene'],
      Status: _req['Status'],
      ModelName: _req['ModelName'],
      Cursor: _req['Cursor'],
      Size: _req['Size'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_meta/query */
  QueryModelMeta(
    req?: ocean_cloud_admin_api.QueryModelMetaReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.QueryModelMetaResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model_meta/query');
    const method = 'POST';
    const data = {
      model_name: _req['model_name'],
      index: _req['index'],
      page_size: _req['page_size'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model_entity/update */
  UpdateModelEntity(
    req?: ocean_cloud_admin_api.UpdateModelEntityReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateModelEntityResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/model/model_entity/update');
    const method = 'POST';
    const data = {
      model_id: _req['model_id'],
      model_name: _req['model_name'],
      targets: _req['targets'],
      biz: _req['biz'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/model/create */
  CreateModel(
    req: ocean_cloud_admin_api.CreateModelMetaReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateModelMetaResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model/create');
    const method = 'POST';
    const data = {
      model_name: _req['model_name'],
      family: _req['family'],
      version: _req['version'],
      desc: _req['desc'],
      show_name: _req['show_name'],
      icon: _req['icon'],
      capability: _req['capability'],
      quota: _req['quota'],
      model_config: _req['model_config'],
      prompt_conf: _req['prompt_conf'],
      legacy_fields: _req['legacy_fields'],
      parameters: _req['parameters'],
      display_properties: _req['display_properties'],
    };
    const headers = {
      Referer: _req['Referer'],
      'Ocean-Jwt-Token': _req['Ocean-Jwt-Token'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/admin/v1/model/engine_publish/model_deploy */
  CreateModelDeployment(
    req: ocean_cloud_admin_api.CreateModelDeploymentRequest,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateModelDeploymentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/admin/v1/model/engine_publish/model_deploy',
    );
    const method = 'POST';
    const data = {
      Operator: _req['Operator'],
      ModelId: _req['ModelId'],
      ModelName: _req['ModelName'],
      OldModelMetas: _req['OldModelMetas'],
      NewModelMetas: _req['NewModelMetas'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/model/engine_publish/query_deployments */
  QueryDeploymentDetail(
    req: ocean_cloud_admin_api.QueryDeploymentDetailRequest,
    options?: T,
  ): Promise<ocean_cloud_admin_api.QueryDeploymentDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/admin/v1/model/engine_publish/query_deployments',
    );
    const method = 'POST';
    const data = {
      Id: _req['Id'],
      Title: _req['Title'],
      Operator: _req['Operator'],
      Status: _req['Status'],
      PageNum: _req['PageNum'],
      PageSize: _req['PageSize'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/model/engine_publish/chain_deploy */
  CreateChainInfoDeployment(
    req: ocean_cloud_admin_api.CreateChainInfoDeploymentRequest,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateChainInfoDeploymentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/admin/v1/model/engine_publish/chain_deploy',
    );
    const method = 'POST';
    const data = {
      Operator: _req['Operator'],
      TaskId: _req['TaskId'],
      TaskName: _req['TaskName'],
      OldVersion: _req['OldVersion'],
      NewVersion: _req['NewVersion'],
      Cookie: _req['Cookie'],
      FromOversea: _req['FromOversea'],
      AppOwner: _req['AppOwner'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/model/model_meta/query_by_id */
  QueryModelMetaById(
    req: ocean_cloud_admin_api.QueryModelMetaByIdReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.QueryModelMetaByIdResp> {
    const _req = req;
    const url = this.genBaseURL('/api/admin/v1/model/model_meta/query_by_id');
    const method = 'POST';
    const data = { MetaId: _req['MetaId'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/model/engine_publish/rollback_deployment */
  RollBackDeployment(
    req?: ocean_cloud_admin_api.RollBackDeploymentReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.RollBackDeploymentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/model/engine_publish/rollback_deployment',
    );
    const method = 'POST';
    const data = { build_id: _req['build_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/model/engine_publish/update_deployment_status */
  UpdateDeploymentStatus(
    req?: ocean_cloud_admin_api.UpdateDeploymentStatusReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateDeploymentStatusResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/model/engine_publish/update_deployment_status',
    );
    const method = 'POST';
    const data = {
      BizKey: _req['BizKey'],
      BizType: _req['BizType'],
      Status: _req['Status'],
      DeployId: _req['DeployId'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/default_model/create */
  UpdateOpsBotsDefaultModel(
    req?: ocean_cloud_admin_api.UpdateOpsBotsDefaultModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateOpsBotsDefaultModelResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/default_model/create');
    const method = 'POST';
    const data = { modelId: _req['modelId'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model/get_detail */
  GetOpBotsModelDetail(
    req?: ocean_cloud_admin_api.GetOpBotsModelDetailReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetOpBotsModelDetailResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/model/get_detail');
    const method = 'POST';
    const data = { modelId: _req['modelId'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/admin/v1/ops_bots/default_model/get_detail
   *
   * 默认模型管理
   */
  GetOpsBotsDefaultModelDetail(
    req?: ocean_cloud_admin_api.GetOpsBotsDefaultModelDetailReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetOpsBotsDefaultModelDetailResp> {
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/default_model/get_detail',
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/admin/v1/ops_bots/model/update
   *
   * 模型运营平台接口
   *
   * 模型管理
   */
  UpdateOpBotsModel(
    req?: ocean_cloud_admin_api.UpdateOpBotsModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateOpBotsModelResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/model/update');
    const method = 'POST';
    const data = {
      modelId: _req['modelId'],
      icon: _req['icon'],
      description: _req['description'],
      modelParamList: _req['modelParamList'],
      modelName: _req['modelName'],
      paidTags: _req['paidTags'],
      tokenLimit: _req['tokenLimit'],
      modelRuntimeTag: _req['modelRuntimeTag'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model/get_list */
  GetOpBotsModelList(
    req?: ocean_cloud_admin_api.GetOpBotsModelListReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetOpBotsModelListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/model/get_list');
    const method = 'POST';
    const data = { scene: _req['scene'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/hot_model/get_list */
  GetOpsBotsHotModelList(
    req?: ocean_cloud_admin_api.GetOpsBotsHotModelListReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetOpsBotsHotModelListResp> {
    const url = this.genBaseURL('/api/admin/v1/ops_bots/hot_model/get_list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/admin/v1/ops_bots/hot_model/update
   *
   * 热门模型管理
   */
  UpdateOpsBotsHotModel(
    req?: ocean_cloud_admin_api.UpdateOpsBotsHotModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateOpsBotsHotModelResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/hot_model/update');
    const method = 'POST';
    const data = { modelList: _req['modelList'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model_show_family/delete */
  DeleteOpsBotsModelShowFamily(
    req?: ocean_cloud_admin_api.DeleteOpsBotsModelShowFamilyReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.DeleteOpsBotsModelShowFamilyResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model_show_family/delete',
    );
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/admin/v1/ops_bots/model_show_family/create
   *
   * 模型家族管理
   */
  CreateOpsBotsModelShowFamily(
    req?: ocean_cloud_admin_api.CreateOpsBotsModelShowFamilyReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateOpsBotsModelShowFamilyResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model_show_family/create',
    );
    const method = 'POST';
    const data = {
      icon: _req['icon'],
      name: _req['name'],
      ranking: _req['ranking'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model_show_family/update */
  UpdateOpsBotsModelShowFamily(
    req?: ocean_cloud_admin_api.UpdateOpsBotsModelShowFamilyReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateOpsBotsModelShowFamilyResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model_show_family/update',
    );
    const method = 'POST';
    const data = {
      id: _req['id'],
      icon: _req['icon'],
      name: _req['name'],
      ranking: _req['ranking'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model_show_family/get_list */
  GetOpsBotsModelShowFamilyList(
    req?: ocean_cloud_admin_api.GetOpsBotsModelShowFamilyListReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetOpsBotsModelShowFamilyListResp> {
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model_show_family/get_list',
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/admin/v1/ops_bots/model_show_family/create_related_model */
  CreateOpsBotsModelShowFamilyRelatedModel(
    req?: ocean_cloud_admin_api.CreateOpsBotsModelShowFamilyRelatedModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CreateOpsBotsModelShowFamilyRelatedModelResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model_show_family/create_related_model',
    );
    const method = 'POST';
    const data = {
      showFamilyId: _req['showFamilyId'],
      modelId: _req['modelId'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model_show_family/delete_related_model */
  DeleteOpsBotsModelShowFamilyRelatedModel(
    req?: ocean_cloud_admin_api.DeleteOpsBotsModelShowFamilyRelatedModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.DeleteOpsBotsModelShowFamilyRelatedModelResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model_show_family/delete_related_model',
    );
    const method = 'POST';
    const data = {
      showFamilyId: _req['showFamilyId'],
      modelId: _req['modelId'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/admin/v1/ops_bots/model_show_family/get_related_model_list
   *
   * 模型家族和模型关系绑定
   */
  GetOpsBotsModelShowFamilyRelatedModelList(
    req?: ocean_cloud_admin_api.GetOpsBotsModelShowFamilyRelatedModelListReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.GetOpsBotsModelShowFamilyRelatedModelListResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model_show_family/get_related_model_list',
    );
    const method = 'POST';
    const data = { showFamilyId: _req['showFamilyId'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/admin/v1/ops_bots/model/online
   *
   * 上架管理
   */
  OnlineOpBotsModel(
    req?: ocean_cloud_admin_api.OnlineOpBotsModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.OnlineOpBotsModelResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/model/online');
    const method = 'POST';
    const data = {
      modelId: _req['modelId'],
      scene: _req['scene'],
      ranking: _req['ranking'],
      schedule_time: _req['schedule_time'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model/offline */
  OfflineOpBotsModel(
    req?: ocean_cloud_admin_api.OfflineOpBotsModelReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.OfflineOpBotsModelResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/model/offline');
    const method = 'POST';
    const data = {
      modelId: _req['modelId'],
      scene: _req['scene'],
      scheduleTime: _req['scheduleTime'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/admin/v1/ops_bots/model_avatar/upload
   *
   * 模型头像上传
   */
  UploadOpsBotsModelAvatar(
    req?: ocean_cloud_admin_api.UploadOpsBotsModelAvatarReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UploadOpsBotsModelAvatarResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/model_avatar/upload');
    const method = 'POST';
    const data = { filename: _req['filename'], content: _req['content'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model/run_schedule_task */
  RunOpBotsScheduleTask(
    req?: ocean_cloud_admin_api.RunOpBotsScheduleTaskReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.RunOpBotsScheduleTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model/run_schedule_task',
    );
    const method = 'POST';
    const data = { taskId: _req['taskId'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model/cancel_schedule_task */
  CancelOpBotsModelScheduleTask(
    req?: ocean_cloud_admin_api.CancelOpBotsModelScheduleTaskReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.CancelOpBotsModelScheduleTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/admin/v1/ops_bots/model/cancel_schedule_task',
    );
    const method = 'POST';
    const data = { scheduleTaskId: _req['scheduleTaskId'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/admin/v1/ops_bots/model/update_ranking */
  UpdateOpBotsModelRanking(
    req?: ocean_cloud_admin_api.UpdateOpBotsModelRankingReq,
    options?: T,
  ): Promise<ocean_cloud_admin_api.UpdateOpBotsModelRankingResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/admin/v1/ops_bots/model/update_ranking');
    const method = 'POST';
    const data = {
      modelId: _req['modelId'],
      ranking: _req['ranking'],
      scene: _req['scene'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
