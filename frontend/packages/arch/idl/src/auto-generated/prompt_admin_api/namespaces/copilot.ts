/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as copilot_common from './copilot_common';

export type Int64 = string | number;

export enum BotExeType {
  Create = 1,
  Update = 2,
  Delete = 3,
}

export enum BotSpecies {
  /** bot种类 */
  Default = 0,
  Function = 1,
}

export enum BotStatus {
  Using = 0,
  Deleted = 1,
}

export enum BotType {
  /** bot类型 */
  User = 0,
  Coco = 1,
  GuanFang = 2,
}

export enum ModelCacheType {
  PrefixCache = 1,
}

export enum ModelParamType {
  Float = 1,
  Int = 2,
  Boolean = 3,
  String = 4,
}

export enum ModelProvider {
  GptOpenApi = 1,
  GptEngine = 2,
  MaaS = 3,
  QianFan = 4,
  BytedLLMServer = 5,
}

export enum PromptTemplateType {
  GPT_TOOLS = 0,
  GPT_TOOLS_DATASET = 1,
  GPT_DATASET = 2,
  GPT_NOTHING = 3,
  SEED_MUSIC = 4,
}

export enum RecommendStatus {
  /** 个性化推荐状态 */
  Enable = 0,
  Disable = 1,
}

export interface BytedLLMServerConf {
  psm?: string;
  idc?: Array<string>;
  cluster?: string;
  /** 配置值为orca或者ultraman或者genai */
  request_type?: string;
  /** 使用genai时必填 */
  chat_template?: string;
  /** 自定义orca框架的模型出入参 */
  orca_model_param?: OrcaModelParamConf;
}

export interface Capability {
  /** 是否支持 function 调用 */
  function_call?: boolean;
  /** 是否支持头条卡片类型 */
  card?: boolean;
  /** 是否支持视频搜索 */
  media?: boolean;
  /** 执行策略 */
  proxy?: copilot_common.ModelProxy;
  /** 支持工具 */
  tool?: boolean;
  /** seed模型输出为api_name，而非plugin + api_name，若拼接会导致调插件失败。此标识可阻止拼接 */
  fixed_function?: boolean;
  /** for seed_strong_character_with_mem，在 bot_prompt_template_jinja 中引用 */
  profile_memory?: boolean;
  /** 从历史消息中恢复，用于豆包语音链路，作用在 Seed SC 模型上  */
  resume_segment?: boolean;
  /** 是否支持复杂参数调用，例如 */
  complex_function_call?: boolean;
  /** Deprecated: 使用 multimodal_types 取代。是否支持多模态协议，如图片识别 */
  multi_modal?: boolean;
  /** 是否支持 json_mode */
  json_mode?: boolean;
  /** 是否支持消息命名 */
  nameable?: boolean;
  /** 多模态支持的文件类型，遵循 MIME 标准 */
  multimodal_types?: Array<string>;
  /** 是否支持预搜索，aka 续写  */
  pre_query?: boolean;
  /** 是否支持搜索增强，会插入 type=search_enhance 的 tool */
  search_enhance?: boolean;
  /** 是否展示thinking */
  cot_display?: boolean;
  /** 支持的缓存类型 */
  cache?: Array<ModelCacheType>;
  /** 是否支持续写 */
  prefill_resp?: boolean;
  /** 允许展示的如意卡片列表 */
  ala_src_allow_list?: Array<string>;
  /** 是否支持批量调用 */
  batch?: boolean;
}

export interface GptEngineConf {
  /** seed 运行时 */
  runtime?: string;
  /** ab 参数，通过 json 序列化 */
  ab_param?: string;
  /** seed app_id */
  app_id?: string;
  /** cluster */
  cluster?: string;
}

export interface GptOpenApiConf {
  /** 基础请求地址 */
  openai_api_base?: string;
  /** API 协议版本 */
  openai_api_version?: string;
  /** AZURE / OPEN_AI / AZURE_AD */
  openai_api_type?: string;
}

export interface LegacyFields {
  /** 原 model_name 取值 */
  model_name?: Int64;
}

export interface MaasAuthConf {
  ak?: string;
  sk?: string;
  /** 调用方舟的时候不指定大模型名称 而是指定 endpoint */
  endpoint?: string;
  host?: string;
  region?: string;
}

export interface MaasConf {
  auth?: MaasAuthConf;
  /**  */
  plugins?: Array<string>;
  extra?: Record<string, string>;
  using_stream?: boolean;
  api_version?: string;
  /** 火山方舟节点的昵称 */
  endpoint_name?: string;
  /** 火山方舟节点创建时间 */
  endpoint_create_time?: Int64;
}

export interface ModelConf {
  /** 超时时间[ms] */
  timeout?: number;
  /** 重试次数 */
  retry_times?: number;
  /** deprecated 回复输出方式 */
  print_behavior?: copilot_common.PrintBehavior;
  /** Maas 特有的配置 */
  maas?: MaasConf;
  /** 千帆特有的配置 */
  qianfan?: QianFanConf;
  /** gpt openapi 配置 */
  gpt_openapi?: GptOpenApiConf;
  /** gpt engine 配置 */
  gpt_engine?: GptEngineConf;
  /** byted llm server 配置 */
  byted_llm_server?: BytedLLMServerConf;
  /** 提供模型的平台，openai maas 等 */
  provider?: ModelProvider;
}

export interface ModelParameter {
  /** 配置字段，如max_tokens */
  name?: string;
  /** 类型 */
  type?: ModelParamType;
  /** 是否必填 */
  is_required?: boolean;
  /** 数值类型参数，允许设置的最小值 */
  min?: string;
  /** 数值类型参数，允许设置的最大值 */
  max?: string;
  /** float类型参数的精度 */
  precision?: number;
  /** 不同风格的参数默认值 */
  default_value?: Record<string, string>;
  /** 枚举值，如response_format支持text,markdown,json */
  options?: Array<string>;
  /** 是否自动修正该参数到[min, max]范围内， 默认为false */
  auto_fix?: boolean;
}

export interface ModelQuota {
  /** 最大总 token 数量 */
  token_limit?: Int64;
  /** 最终回复最大 token 数量 */
  token_resp?: Int64;
  /** Prompt 系统最大 token 数量 */
  token_system?: Int64;
  /** Prompt 用户输入最大 token 数量 */
  token_user_in?: Int64;
  /** Prompt 工具输入最大 token 数量 */
  token_tools_in?: Int64;
  /** Prompt 工具输出最大 token 数量 */
  token_tools_out?: Int64;
  /** Prompt 数据最大 token 数量 */
  token_data?: Int64;
  /** Prompt 历史最大 token 数量 */
  token_history?: Int64;
  /** Prompt 历史最大 token 数量 */
  token_cut_switch?: boolean;
  /** 输入成本 */
  price_in?: number;
  /** 输出成本 */
  price_out?: number;
  /** Bot 提示词配置 token 数量 */
  token_persona?: number;
  /** 容量，用于监控 */
  request_per_minute?: Int64;
  /** 容量，用于监控 */
  token_per_minute?: Int64;
}

export interface OrcaCustomerParam {
  string_lists?: Record<string, Array<string>>;
  int_lists?: Record<string, Array<Int64>>;
  float_lists?: Record<string, Array<number>>;
  string_arrays?: Record<string, Array<Array<string>>>;
}

export interface OrcaModelParamConf {
  /** openai */
  openai_req_key?: string;
  openai_resp_key?: string;
  /** customer */
  in_customer_param?: OrcaCustomerParam;
}

export interface PromptConf {
  /** 模版配置 tcc: bot_prompt_sequence */
  sequence?: string;
  /** 模版前缀 tcc: bot_prompt_template */
  prefix?: string;
  /** 模版后缀 tcc: bot_prompt_template */
  suffix?: string;
  /** 是否人格化 */
  is_sc?: boolean;
  /** 不开启ReACT(response_format) */
  close_react?: boolean;
  /** user prompt是否必须 */
  is_up_required?: boolean;
}

export interface QianFanAuthConf {
  client_id?: string;
  client_secret?: string;
}

export interface QianFanConf {
  auth?: QianFanAuthConf;
}
/* eslint-enable */
