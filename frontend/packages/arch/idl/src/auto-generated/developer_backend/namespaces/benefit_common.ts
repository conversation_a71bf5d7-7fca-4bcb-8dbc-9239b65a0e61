/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as copilot_common from './copilot_common';

export type Int64 = string | number;

export enum AccountStatus {
  Available = 1,
  /** 账户付费不可用 */
  Unavailable = 2,
}

/** 权益流水状态 */
export enum BenefitCostStatus {
  /** 已撤回 */
  Reverted = 0,
  /** 已成功 */
  Succeed = 1,
}

/** Benefit 所作用的实体类型 */
export enum BenefitEntityType {
  /** 企业下的所有设备 */
  EnterpriseAllDevices = 1,
  /** 企业下的所有终端 */
  EnterpriseAllCustomConsumer = 2,
  /** 单设备 */
  EnterpriseSingleDevice = 11,
  /** 单终端主题，由客户自定义 */
  EnterpriseSingleCustomConsumer = 12,
  /** API */
  API = 13,
  /** Plugin */
  Plugin = 14,
  /** Voice */
  Voice = 15,
  /** Workflow */
  Workflow = 16,
  /** 企业配置类
企业安心用配置 */
  EnterpriseConfConfidenceUsing = 51,
}

/** 权益历史记录类型 */
export enum BenefitHistoryType {
  /** bot 消耗 */
  ChatWithBot = 1,
  TopUpCredit = 2,
  BounsCredit = 3,
  ChargeBack = 4,
  ChargeBackReverse = 5,
  WorkflowConsume = 6,
  /** 智能语音 */
  IntelligentVoice = 11,
  /** 扣子罗盘消耗 */
  Fornax = 12,
  EvaluateConsume = 41,
  EvaluateModelConsume = 42,
  /** 应用消耗 */
  ProjectConsume = 61,
}

/** 权益流水根节点类型 */
export enum BenefitRootHistoryType {
  /** bot 消耗 */
  BotConsume = 1,
  /** workflow 消耗 */
  WorkflowConsume = 2,
  /** 应用消耗 */
  ProjectConsume = 3,
  /** 智能语音 */
  IntelligentVoiceConsume = 4,
  /** 扣子罗盘消耗 */
  FornaxConsume = 5,
  /** 模型评测消耗 */
  EvaluateModelConsume = 6,
}

/** 权益类型
 40 -59 免费次数
 60 - 99 限流
 100-109 资源点
 110-129 Fornax
 130-149 WorkSpace
 150-169 运维
 170-179 知识库
 180-199 语音
 200-219 租户相关
 220-229 发布相关 */
export enum BenefitType {
  /** 海外 */
  MessageCredit = 1,
  UserFreeChat = 2,
  TopUpMessageCredit = 3,
  BonusMessageCredit = 4,
  /** 40 -59 免费次数 */
  Freetimes = 40,
  /** 评测免费次数 */
  EvaluateFree = 41,
  /** Workflow 测试运行免费次数 */
  WorkflowTestRunFree = 42,
  /** App 测试运行免费次数 */
  AppTestRunFree = 43,
  /** Plugin 测试运行免费次数 */
  PluginRunFree = 44,
  /** API 运行免费次数 */
  APIRunFree = 45,
  /** SDK 运行免费次数 */
  SDKRunFree = 46,
  /** 60 - 99 限流
模型 RPM 限流 */
  RateLimitModelRPM = 60,
  /** 模型 Input TPM 限流 */
  RateLimitModelInputTPM = 61,
  /** 模型 Output TPM 限流 */
  RateLimitModelOutputTPM = 62,
  /** 基础模型 Input TPM 限流 */
  RateLimitModelInputTPMBasic = 63,
  /** 基础模型 Output TPM 限流 */
  RateLimitModelOutputTPMBasic = 64,
  /** Plugin 运行 QPS 限流 */
  PluginRunQPS = 65,
  /** Plugin 运行并发度限流 */
  PluginRunParallel = 66,
  /** 图像节点
Workflow 运行 QPS 限流 */
  WorkflowRunQPS = 67,
  /** Workflow 运行并发度限流 */
  WorkflowRunParallel = 68,
  /** API 运行 QPS 限流 */
  APIRunQPS = 70,
  /** 语音 QPS 限流 */
  VoiceQPS = 71,
  /** 语音并发度限流 */
  VoiceParallel = 72,
  /** 100-109 资源点
资源点总量 */
  ResourcePoint = 100,
  /** 免费资源点，废弃 */
  FreeResourcePoint = 101,
  /** 火山购买的资源点 */
  VolcProResourcePoint = 102,
  /** 周期性资源点 */
  PeriodicResourcePoint = 103,
  /** 渠道递减资源点 */
  ChannelResourcePoint = 104,
  /** 试算资源点 */
  CutAndTryResourcePoint = 109,
  /** 110-129 Fornax
Trace 用量 */
  TraceAmount = 111,
  /** Trace 存储时长 */
  TraceStorageDuration = 112,
  /** 130-149 WorkSpace
Space 总量 */
  SpaceAmount = 131,
  /** Space 人数 */
  SpacePeopleNumber = 132,
  /** Space 下协作者人数 */
  SpaceCollaboratorNumber = 133,
  /** 150-169 运维
日志存储时长 */
  LogStorageDuration = 151,
  /** 日志导出 */
  LogExport = 152,
  /** 170-179 知识库
知识库容量 */
  Capacity = 170,
  /** 180-199 语音
音色克隆总数 */
  VoiceCloneNumber = 180,
  /** 音色克隆基础数量 */
  VoiceCloneNumberBasic = 181,
  /** 200-219 租户相关
席位数上限 */
  SeatNumberLimit = 200,
  /** 基础席位数 */
  SeatNumberBasic = 201,
  /** 移除水印 */
  RemoveWatermark = 220,
  /** 240-269 配置
安心用 */
  ConfidenceUsing = 240,
}

/** 权益使用模式 */
export enum BenefitUseMode {
  /** 按额度使用 */
  ByQuota = 1,
  /** 无限使用 */
  Unlimited = 2,
  /** 不可用 */
  UnAvailable = 10,
}

export enum BotMode {
  SingleMode = 0,
  MutiAgent = 1,
  WorkflowMode = 2,
}

export enum ChargeItemType {
  /** 1-99 模型相关 */
  ModelInputTPM = 1,
  ModelOutputTPM = 2,
  /** 100-199 语音相关 */
  VoiceClone = 100,
  VoiceStorage = 101,
  /** 200- */
  PluginRunQPS = 200,
  PluginRunParallel = 201,
}

export enum ChargeResourceType {
  Model = 1,
  Plugin = 2,
}

/** 校验结果。通常结合BenefitType */
export enum CheckResultType {
  Pass = 1,
  /** 超出限额 */
  OutOfLimitation = 2,
  /** 余额/余量不足 */
  InsufficientBalance = 3,
}

/** 权益校验点位 */
export enum CheckType {
  /** 仅校验用于权益余量 */
  CheckCommon = 0,
  /** 对话（含Chatflow）开始。 */
  ChatStart = 1,
  /** 对话（含Chatflow）结束。对话结束后，上报对应对话结果 ErrCode */
  ChatFinish = 2,
  /** 调用模型前（通常为chat_engine/runtime），通常做限流 */
  ModelCallBefore = 6,
  /** 模型执行完成（model_agent/llm_gateway），通常用量上报 */
  ModelExecDone = 7,
  /** workflow执行。通常为非对话接口的workflow的执行前校验，如试用次数 */
  WorkflowRunStart = 11,
  /** workflow执行。通常为非对话接口的workflow执行后 */
  WorkflowRunFinish = 12,
  /** workflow中断重入 */
  WorkflowRunResume = 13,
  /** 调用插件前，通常做限流 */
  PluginCallBefore = 16,
  /** 插件执行完成。通常为插件用量上报 */
  PluginExecFinish = 17,
  /** 评测前（Fornax评测复用） */
  EvaluateBefore = 41,
  /** 评测结果裁判 */
  EvaluateJudge = 42,
  /** 语音消费结束时上报 */
  VoiceUseFinish = 51,
  /** Trace日志落库前，用于限额 */
  FornaxTraceBefore = 61,
}

/** 权益流水对应消耗的资源类型 */
export enum ConsumeResourceType {
  /** 未知 */
  Unknown = 0,
  /** 模型 */
  Model = 1,
  /** 插件 */
  Plugin = 2,
  /** 语音（ASR/TTS） */
  Voice = 3,
  /** RTC */
  RTC = 4,
  /** 知识库（暂不对外暴露该类型） */
  Dateset = 5,
}

/** 权益流水的成本归属用户类型 */
export enum CostUserType {
  /** 未知 */
  Unknown = 0,
  /** 企业（国内为火山账号） */
  Enterprise = 1,
  /** 个人用户 */
  User = 2,
}

export enum CozeAccountType {
  /** 未知 */
  Unknown = 0,
  /** 组织账号 */
  Organization = 1,
  /** 个人账号 */
  Personal = 2,
}

/** 用户权益套餐状态 */
export enum CozeInstanceStaus {
  /** 运行中 */
  Running = 1,
  /** 退订 */
  Unsubs = 2,
  /** 到期 */
  Expired = 3,
  /** 欠费 */
  Overdue = 4,
}

export enum DurationType {
  Day = 1,
  Month = 2,
  Year = 3,
}

export enum EntityBenefitStatus {
  /** 正常使用 */
  Valid = 1,
  /** 冻结使用 */
  Frozen = 3,
  /** 取消 */
  Cancel = 5,
  /** 待生效（此枚举通过计算得出，数据库中并无此项数据） */
  Pending = 6,
}

export enum EntityPeriodType {
  /** 绝对时间 */
  AbsoluteTime = 1,
  /** 相对时间 */
  RelativeTime = 2,
}

export enum ExecutionMode {
  /** 发布态/正式态 */
  Release = 0,
  /** 草稿态/调试态/编辑态。 */
  Draft = 1,
}

export enum InstanceLimitStatus {
  /** 未受限 */
  UnLimited = 1,
  /** 受限中（欠费） */
  Limited = 2,
}

export enum InstanceStatus {
  /** 创建中, 理论上不会返回该状态 */
  InstanceStatusCreating = 0,
  /** 运行中 */
  Running = 1,
  /** 创建失败, 理论上不会返回该状态 */
  InstanceStatusFailed = 2,
  /** 退订回收 */
  UnsubsRecycled = 3,
  /** 到期关停 */
  ExpiredClosed = 4,
  /** 到期回收 */
  ExpiredRecycled = 5,
  /** 欠费关停 */
  InstanceStatusOverdueShutdown = 6,
  /** 欠费回收 */
  InstanceStatusOverdueRecycled = 7,
  /** 退订关停 */
  InstanceStatusTerminatedShutdown = 8,
}

export enum LimitationTriggerUnit {
  Never = 0,
  Minute = 1,
  Hour = 2,
  Day = 3,
  Month = 4,
  Second = 5,
}

export enum MonetizationEntityType {
  Bot = 0,
  Project = 1,
}

/** 权益流水的权益类型（用于对客） */
export enum OpenBenefitType {
  /** 未知 */
  Unknown = 0,
  /** 免费赠送（大类，包括插件试用次数等。对于国内，当前仅个人免费版有该类型） */
  Free = 1,
  /** 资源点 */
  ResourcePoint = 2,
}

export enum OperateType {
  AddBenefit = 1,
  RefundSubscription = 2,
  RefundTopUp = 3,
  SubscriptionChargeBack = 4,
  TopUpChargeBack = 5,
  SubscriptionChargeBackReverse = 6,
  TopUpChargeBackReverse = 7,
}

export enum PluginBillType {
  /** 按次调用计费。适用于大多数插件 */
  ByCallTime = 0,
  /** 按时长计费（单位S）。适用于音乐生成、视频编辑等 */
  ByDuration = 1,
  /** 按token数计费。适用于视频生成 */
  ByTotalTokens = 2,
  /** 插件本身不计费，由下游计费。 */
  NoneButByDownstream = 11,
}

/** 资源归属的实体类型 */
export enum ResBelongsToEntityType {
  /** 未知 */
  Unknown = 0,
  /** bot */
  Bot = 1,
  /** workflow */
  Workflow = 2,
  /** plugin */
  Plugin = 3,
  /** 应用。原Project */
  Application = 4,
  /** 模型 */
  Model = 5,
  /** 语音类（ASR/TTS） */
  Voice = 6,
}

export enum ResourceUsageStrategy {
  /** 无限制 */
  UnLimit = 1,
  /** 限制 */
  Forbidden = 2,
  /** 通过额度校验 */
  ByQuota = 3,
}

/** 场景 */
export enum SceneType {
  /** 对话 */
  Chat = 1,
  /** workflow testrun */
  WorkflowTest = 2,
  /** 评测bot */
  EvaluateBot = 41,
  /** 评测模型 */
  EvaluateModel = 42,
}

export enum UserLevel {
  /** 免费版。 */
  Free = 0,
  /** 海外
PremiumLite */
  PremiumLite = 10,
  /** Premium */
  Premium = 15,
  PremiumPlus = 20,
  /** 国内
V1火山专业版 */
  V1ProInstance = 100,
  /** 个人旗舰版 */
  ProPersonal = 110,
  /** 团队版 */
  Team = 120,
  /** 企业版 */
  Enterprise = 130,
}

export enum VoiceResType {
  /** 音色克隆 */
  VoiceClone = 1,
  /** 复刻语音-文字转语音 */
  TTSCustom = 2,
  /** 系统语音-文字转语音 */
  TTSSystem = 3,
  /** 流式语音识别 - 大模型 */
  ASRStream = 4,
  /** 录音文件语音识别 - 大模型 */
  ASRFile = 5,
  /** 流式语音识别 - 小模型 */
  ASRStreamSmall = 6,
  /** 录音文件语音识别 - 小模型 */
  ASRFileSmall = 7,
  /** 语音通话 音频时长 */
  RTCVoice = 8,
  /** 对话式AI 音频时长 */
  RTCDialogAI = 9,
  /** 视频通话时长-4k */
  RTCVideoCall4K = 10,
  /** 视频通话时长-2k */
  RTCVideoCall2K = 11,
  /** 视频通话时长-1080P */
  RTCVideoCall1080P = 12,
  /** 视频通话时长-720P */
  RTCVideoCall720P = 13,
  /** 视频通话时长-360P */
  RTCVideoCall360P = 14,
  /** TTS 相关计费项 【20-40)
文字转语音，按调用次数收费 - 小模型 */
  TTSSmall = 20,
  /** 语音能力（声纹）计费项
声纹能力 */
  VoicePrint = 60,
}

export enum VolcanoUserType {
  Unknown = 0,
  RootUser = 1,
  BasicUser = 2,
}

export enum VolcInstanceType {
  /** 正常版本 */
  Normal = 1,
  /** 渠道版本 */
  Channel = 2,
}

export enum WorkflowMode {
  Unknown = 0,
  TestRun = 1,
  Released = 2,
}

export interface BenefitRecord {
  ID: string;
  IdempotencyKey: string;
  BizScene: string;
  ConsumeTime: Int64;
  CheckType: Int64;
  RecordRootID: Int64;
  AccountID: Int64;
  CozeUserID: Int64;
  EnterpriseID: string;
  CozeAccountType: Int64;
  ConnectorID: Int64;
  ConnectorUID: string;
  DeviceID: string;
  SpaceID: Int64;
  RootEntityType: Int64;
  RootEntityID: Int64;
  ChangeBalance: string;
  BalanceType: Int64;
  BizDetail?: BizDetail;
  ResourceType: Int64;
  ResourceID: string;
  ResModelContent?: ResModelContent;
  ResPluginContent?: ResPluginContent;
  ResVoiceContent?: ResVoiceContent;
  CostConnectorID: Int64;
  CostConnectorUID: string;
  CostAccountID: Int64;
  CostUserType: Int64;
  CostEnterpriseID: string;
  Status: Int64;
  CreatedAt: Int64;
  UpdatedAt: Int64;
}

export interface BizDetail {
  APIID?: Int64;
  APIOwnerID?: Int64;
  ByteTreeID?: string;
  LogID?: string;
  Caller?: string;
  Ftf?: string;
  Env?: string;
  OriPath?: string;
  OriHost?: string;
  OriConsumeTime?: Int64;
}

export interface CheckAndUpdateReq {
  /** 场景。对应 BizScene，参考  */
  biz_scene: string;
  /** 权益校验点 */
  check_type: CheckType;
  /** 用于串联一次完整请求 */
  record_root_id: Int64;
  /** 执行模式（编辑态/发布态） */
  exec_mode: ExecutionMode;
  /** 使用者connection_id */
  connector_id: Int64;
  /** 使用者connector_uid */
  connector_uid: string;
  /** 资源关联基础信息，用于计算资源消耗的归属 */
  relation_basic?: ResRelationBaiscInfo;
  /** 资源消耗所归属对象 */
  belongs_to_entity?: ResBelongsToEntity;
  /** 使用者火山account_id */
  account_id?: Int64;
  /** 消耗数量（不填默认为1次） */
  cnt?: Int64;
  /** 对话/运行结束的错误码 */
  err_code?: string;
  /** 资源消耗时间戳（单位秒） */
  consume_timestamp?: Int64;
  /** 这里指的是Coze UserID */
  coze_user_id?: Int64;
  /** 以下三个字段至少必填其中一个
这里指的是Coze的AccountID */
  coze_account_id?: Int64;
  /** 这里指的是Coze的AccountType */
  coze_account_type?: CozeAccountType;
  /** 优先取此 SpaceID，若未填充则兜底从 RelationBasic 中取 */
  space_id?: Int64;
  /** 资源消耗明细
资源用量上报时必填。保证幂等（确保资源消耗可重入），应在各资源层唯一 */
  uuid?: string;
  /** 模型资源 */
  model_res_info?: ModeleResInfo;
  /** 插件资源 */
  plugin_res_info?: PluginResInfo;
  /** 语音资源 */
  voice_res_info?: VoiceResInfo;
  /** 扩展字段 */
  extra?: Record<string, string>;
  Base?: base.Base;
}

export interface CheckAndUpdateResp {
  IsPass: boolean;
  /** 用户信息 */
  UserInfo?: UserBasicInfo;
  /** 权益校验结果明细 */
  BenefitResult?: Partial<Record<BenefitType, CheckResultType>>;
  DenyReason?: DenyReason;
  /** 扩展字段 */
  extra?: Record<string, string>;
  BaseResp?: base.BaseResp;
}

export interface CommonCheckAndUpdateReq {
  /** 权益校验点 */
  CheckType: CheckType;
  /** 保证幂等 */
  UUID: string;
  /** 场景 */
  Scene: SceneType;
  ConnectionID: Int64;
  ConnectorUID: string;
  /** 权益数量 */
  Cnt?: Int64;
  SpaceID?: Int64;
  ModelID?: number;
  /** 调用的bot id */
  BotID?: Int64;
  /** 调用的workflow id */
  WorkflowID?: Int64;
  /** 调用的plugin的ID */
  PluginID?: Int64;
  /** 输入token数 */
  InputTokens?: Int64;
  /** 输出token数 */
  OutputTokens?: Int64;
  /** Query结束的错误码 */
  ErrCode?: number;
  Base?: base.Base;
}

export interface CommonCheckAndUpdateResp {
  IsPass: boolean;
  /** 权益校验结果明细 */
  BenefitResult?: Partial<Record<BenefitType, CheckResultType>>;
  /** 用于串联一次完整请求，如果返回值不为""，调用方将该值置于ctx.COZE_RECORD_ROOT_ID中 */
  RootID?: string;
  BaseResp?: base.BaseResp;
}

export interface CommonCounter {
  /** 当 Strategy == ByQuota 时, 表示已使用量, 若权益无相关用量数据则返回 0 */
  used?: number;
  /** 当 Strategy == ByQuota 时, 表示用量上限 */
  total?: number;
  /** 使用策略 */
  strategy?: ResourceUsageStrategy;
  /** 开始时间，单位秒 */
  start_at?: Int64;
  /** 过期时间，单位秒 */
  end_at?: Int64;
}

export interface DenyReason {
  Code: number;
  Message: string;
}

/** 模型资源 */
export interface ModeleResInfo {
  model_id?: Int64;
  /** model family */
  model_family?: copilot_common.ModelFamily;
  /** 输入token数 */
  input_tokens?: Int64;
  /** 输出token数 */
  output_tokens?: Int64;
  /** 模型AK类别 */
  ak_catalog?: string;
  /** 模型AK */
  model_ak?: string;
}

export interface PluginRelationInfo {
  /** 计费类型 */
  bill_type?: PluginBillType;
  /** 插件功能名称。只有当一个插件内有多种计费时，该字段才需要填值 */
  feature_name?: string;
}

export interface PluginResInfo {
  plugin_id?: Int64;
  plugin_api_id?: Int64;
  /** 是否是图像流 */
  is_image_flow?: boolean;
  /** 工具ID */
  tool_id?: Int64;
  /** 工具名称 */
  tool_name?: string;
  /** 插件关联信息 */
  relation_info?: PluginRelationInfo;
  /** 插件执行时间（单位秒）。默认存在是则优先取该值。适用于通过执行时长计费的插件 */
  duration?: Int64;
  /** 输出token数。默认存在是则优先取该值。适用于通过token数计费的插件 */
  total_tokens?: Int64;
}

export interface PublicUserBasicInfo {
  user_level?: UserLevel;
  /** 火山账户信息。CN返回 */
  volc_account_info?: VolcAccountInfo;
}

/** 资源消耗所归属对象。有则填充，避免重复获取 */
export interface ResBelongsToEntity {
  /** 最外层的根bot id */
  root_bot_id?: Int64;
  /** 根bot的创建者Coze UserID */
  bot_creator_id?: Int64;
  /** 根bot的创建者AccountID */
  bot_creator_account_id?: Int64;
  /** 所属应用（原Project）ID */
  application_id?: Int64;
  /** 所属应用（原Project）创建者Coze UserID */
  application_creator_id?: Int64;
  /** 所属应用（原Project）创建者AccountID */
  application_account_id?: Int64;
  /** 执行的workflow id */
  workflow_id?: Int64;
  /** 是否为异步执行 */
  is_async_workflow?: boolean;
  /** Workflow 执行ID */
  workflow_execute_id?: Int64;
  /** 是否是中断重执行Workflow */
  is_reload_workflow?: boolean;
}

export interface ResModelContent {
  ModelID?: Int64;
  ModelFamily?: copilot_common.ModelFamily;
  ModelMetaID?: Int64;
  ModelInputTokens?: Int64;
  ModelOutputTokens?: Int64;
  AKCatalog?: string;
  LLMScene?: string;
  APICatalog?: string;
  ModelAK?: string;
}

/** 资源消耗通用消息体，资源上报上报时消息体需包含该结构。建议优先使用 CheckAndUpdateReq 结构，若自定义结构则加上此结构 */
export interface ResMsgComm {
  /** 场景。对应 BizScene，参考  */
  biz_scene: string;
  /** 权益校验/上报点位 */
  check_type: CheckType;
  /** 用于串联一次完整请求 */
  record_root_id: Int64;
  /** 执行模式（编辑态/发布态） */
  exec_mode: ExecutionMode;
  /** 使用者connection_id */
  connector_id: Int64;
  /** 使用者connector_uid */
  connector_uid: string;
  /** 保证幂等（确保资源消耗可重入），应在各资源层唯一 */
  uuid: string;
  /** 资源消耗所归属对象 */
  belongs_to_entity?: ResBelongsToEntity;
  /** 资源关联基础信息，用于计算资源消耗的归属 */
  relation_basic?: ResRelationBaiscInfo;
  /** 使用者火山account_id */
  account_id?: Int64;
  /** 资源消耗时间戳（单位秒） */
  consume_timestamp?: Int64;
  /** 这里指的是Coze UserID */
  coze_user_id?: Int64;
  /** 以下三个字段至少必填其中一个
这里指的是Coze的AccountID */
  coze_account_id?: Int64;
  /** 这里指的是Coze的AccountType */
  coze_account_type?: CozeAccountType;
  /** 优先取此 SpaceID，若未填充则兜底从 RelationBasic 中取 */
  space_id?: Int64;
  /** 扩展字段 */
  extra?: Record<string, string>;
}

export interface ResPluginContent {
  PluginID?: Int64;
  PluginAPIID?: Int64;
  IsImageFlow?: boolean;
}

/** 资源关联基础信息，用于计算资源消耗的归属。有则填充，避免重复获取 */
export interface ResRelationBaiscInfo {
  /** 所属空间ID */
  space_id?: Int64;
  /** 所属空间创建者Coze UserID */
  space_owner_id?: Int64;
  /** 所属空间创建者AccountID */
  space_owner_account_id?: Int64;
  /** API来源必填。对应APIKey Owner的Coze UserID */
  api_key_owner_id?: Int64;
  /** API来源的APIKey Owner的AccountID */
  api_key_owner_account_id?: Int64;
  /** 调用的bot的创建者Coze UserID */
  bind_coze_uid?: Int64;
  /** 调用的bot的创建者AccountID */
  bind_coze_account_id?: Int64;
  /** 调用的bot的创建者ByteTreeID */
  byte_tree_id?: string;
}

export interface ResVoiceContent {
  VoiceResType?: VoiceResType;
  VoiceID?: string;
  CharNum?: number;
  AudioLength?: number;
  RTCDuration?: number;
  BeginTime?: Int64;
  EndTime?: Int64;
}

export interface UserBasicInfo {
  UserLevel?: UserLevel;
  /** 火山账户信息。CN返回 */
  VolcAccountInfo?: VolcAccountInfo;
  /** 火山用户信息。CN返回 */
  VolcUserInfo?: VolcUserInfo;
}

export interface VoiceResInfo {
  /** 资源类型 */
  voice_type: VoiceResType;
  /** 语音转文字，字符数 */
  char_num?: number;
  /** 音频时长 */
  audio_length?: number;
  /** 通话时长 */
  rtc_duration?: number;
  /** 语音通话开始时间戳 */
  begin_time?: Int64;
  /** 语音通话结束时间戳 */
  end_time?: Int64;
  /** 对应的coze_id */
  coze_id?: string;
  /** 计数，适合按调用次数计费 */
  count?: number;
}

export interface VolcAccountInfo {
  /** 火山账户ID */
  account_id?: Int64;
  /** 是否为火山专业版账户，即是否开通过，当UserLevel in (100,110,120,130)时为true（即使账户关停回收仍为true） */
  is_volcano_pro_account?: boolean;
  /** 实例ID */
  instance_id?: string;
  /** 扣子专业版是否可用（含套餐及存量专业版）。存量专业版仅返回此字段 */
  coze_instance_status?: AccountStatus;
  /** 套餐（实例）状态。仅订阅套餐返回此字段 */
  instance_status?: InstanceStatus;
  /** 套餐（实例）是否受限（欠费）。仅订阅套餐返回此字段 */
  limit_status?: InstanceLimitStatus;
  /** 火山用户类型 */
  volcano_user_type?: VolcanoUserType;
  /** 权益生效时间（秒级） */
  instance_begin_time?: Int64;
  /** 权益失效时间（秒级） */
  instance_end_time?: Int64;
  /** 套餐对应周期资源包实例Id,如果用户购买的是仅版本，则该字段为空 */
  period_pack_instance_id?: string;
}

export interface VolcUserInfo {
  /** 火山身份中心实例ID */
  VolcAuthInstanceID?: string;
  /** 火山开通的套餐等级 */
  VolcUserLevel?: UserLevel;
  /** 火山用户实例版本 */
  VolcInstanceType?: VolcInstanceType;
}
/* eslint-enable */
