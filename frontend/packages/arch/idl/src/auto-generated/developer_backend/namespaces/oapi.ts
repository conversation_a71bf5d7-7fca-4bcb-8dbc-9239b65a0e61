/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export interface BenefitInfo {
  benefit_id?: string;
  benefit_type?: string;
  active_mode?: string;
  started_at?: Int64;
  ended_at?: Int64;
  duration?: number;
  limit?: Int64;
  status?: string;
  entity_type?: string;
  entity_id?: string;
  trigger_unit?: string;
  trigger_time?: Int64;
}

export interface BillBusinessData {
  task_infos?: Array<BillTaskInfo>;
  total?: number;
}

export interface BillTaskInfo {
  task_id?: string;
  status?: string;
  file_urls?: Array<string>;
  /** 过期时间，Unix 时间戳 */
  expires_at?: Int64;
  /** 创建时间，Unix 时间戳 */
  created_at?: Int64;
  /** 开始时间，Unix 时间戳 */
  started_at?: Int64;
  /** 结束时间，Unix 时间戳 */
  ended_at?: Int64;
}

export interface CreateBenefitLimitationData {
  benefit_info?: BenefitInfo;
}

export interface CreateBenefitLimitationRequest {
  entity_type?: string;
  entity_id?: string;
  benefit_info?: BenefitInfo;
  Base?: base.Base;
}

export interface CreateBenefitLimitationResponse {
  code?: number;
  msg?: string;
  data?: CreateBenefitLimitationData;
  BaseResp?: base.BaseResp;
}

export interface CreateBillDownloadTaskRequest {
  /** 开始时间，时间戳 */
  started_at?: Int64;
  /** 结束时间，时间戳 */
  ended_at?: Int64;
  Base?: base.Base;
}

export interface CreateBillDownloadTaskResponse {
  code?: number;
  msg?: string;
  data?: BillTaskInfo;
  BaseResp?: base.BaseResp;
}

export interface ListBenefitLimitationData {
  benefit_infos?: Array<BenefitInfo>;
  has_more?: boolean;
  page_token?: string;
}

export interface ListBenefitLimitationRequest {
  entity_type?: string;
  entity_id?: string;
  benefit_type?: string;
  status?: string;
  page_token?: string;
  page_size?: number;
  Base?: base.Base;
}

export interface ListBenefitLimitationResponse {
  code?: number;
  msg?: string;
  data?: ListBenefitLimitationData;
  BaseResp?: base.BaseResp;
}

export interface ListBillDownloadTaskRequest {
  task_ids?: Array<string>;
  page_num?: number;
  page_size?: number;
  Base?: base.Base;
}

export interface ListBillDownloadTaskResponse {
  code?: number;
  msg?: string;
  data?: BillBusinessData;
  BaseResp?: base.BaseResp;
}

export interface UpdateBenefitLimitationRequest {
  benefit_id?: string;
  active_mode?: string;
  started_at?: Int64;
  ended_at?: Int64;
  duration?: number;
  limit?: Int64;
  status?: string;
  trigger_unit?: string;
  trigger_time?: Int64;
  Base?: base.Base;
}

export interface UpdateBenefitLimitationResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
