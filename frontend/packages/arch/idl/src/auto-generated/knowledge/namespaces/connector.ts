/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum DocSourceType {
  DocSourceTypeDrive = 1,
  DocSourceTypeWiki = 2,
  DocSourceTypeWeChat = 3,
}

export enum FileNodeType {
  Folder = 1,
  Document = 2,
  Sheet = 3,
  Space = 4,
}

export enum TimeFilterEnum {
  TimeUnlimited = 0,
  WithinAWeek = 1,
  WithinAMonth = 2,
  WithinAYear = 3,
}

export interface FileNode {
  file_id?: string;
  file_node_type?: FileNodeType;
  file_name?: string;
  has_children_nodes?: boolean;
  children_nodes?: Array<FileNode>;
  icon?: string;
  file_type?: string;
  file_url?: string;
  /** wiki, 知识空间id */
  space_id?: string;
  /** wiki, 表示知识空间类型（团队空间 或 个人空间） */
  space_type?: string;
  /** wiki, 对应文档类型的token，可根据 obj_type 判断属于哪种文档类型 */
  obj_token?: string;
  /** wiki, 文档类型，对于快捷方式，该字段是对应的实体的obj_type */
  obj_type?: string;
  create_time?: Int64;
  update_time?: Int64;
}

export interface GetFileTreeDocListRequest {
  auth_id: string;
  file_type_list: Array<FileNodeType>;
  folder_id?: string;
  page_token?: string;
  space_id?: string;
  doc_source_type: DocSourceType;
  /** 本次新增 */
  time_filter?: TimeFilterEnum;
  search_keywords?: string;
  /** 强制获取最新，拉增量 */
  force_get_latest?: boolean;
  /** 本次新增 */
  page_size?: Int64;
  Base?: base.Base;
}

export interface GetFileTreeDocListResponse {
  /** 三方数据平台文件列表 */
  file_tree_doc_list?: Array<FileNode>;
  /** 是否还有下一页 */
  has_more?: boolean;
  /** 分页token */
  page_token?: string;
  TotalCount?: Int64;
  code: Int64;
  msg: string;
}

export interface SearchDocumentRequest {
  auth_id: string;
  search_query: string;
  file_type_list: Array<FileNodeType>;
  doc_source_type: DocSourceType;
  page_token?: string;
  offset?: Int64;
  Base?: base.Base;
}

export interface SearchDocumentResponse {
  documents?: Array<FileNode>;
  HasMore?: boolean;
  PageToken?: string;
  OffSet?: Int64;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
