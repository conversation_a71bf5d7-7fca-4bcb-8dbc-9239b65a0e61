/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AuditStatus {
  /** 审核中 */
  Processing = 0,
  /** 审核通过 */
  Enable = 1,
  /** 审核不通过 */
  Disable = 2,
  /** 历史存量数据 */
  Default = 100,
}

export enum CaptionType {
  /** 智能标注 */
  Auto = 0,
  /** 人工标注 */
  Manual = 1,
}

export enum ChunkType {
  DefaultChunk = 0,
  CustomChunk = 1,
  LevelChunk = 2,
}

export enum ContentSchema {
  DefaultSchema = 0,
  LinkReaderSchema = 1,
}

export enum DataSetStatus {
  DataSetprocessing = 0,
  DataSetReady = 1,
  DataSetFailed = 9,
}

export enum DocumentSource {
  /** 本地文件上传 */
  Document = 0,
  /** url */
  Web = 1,
  /** 自定义类型 */
  Custom = 2,
  /** 三方 */
  ThirdParty = 3,
  /** 前端抓取 */
  FrontCrawl = 4,
  /** openapi */
  OpenApi = 5,
  Notion = 101,
  GoogleDrive = 102,
  FeishuWeb = 103,
  LarkWeb = 104,
  WeChat = 109,
}

export enum DocumentStatus {
  /** 上传中 */
  Processing = 0,
  /** 生效 */
  Enable = 1,
  /** 失效 */
  Disable = 2,
  /** 删除 */
  Deleted = 3,
  /** 重新分片中，前端和上游不感知该状态 */
  Resegment = 4,
  /** 刷新中（刷新成功后会删除） */
  Refreshing = 5,
  /** 失败 */
  Failed = 9,
  /** 机审失败 */
  AuditFailed = 1000,
}

/** 兼容保留，使用 DocumentStatus 替代 */
export enum DocumentType {
  /** 上传中 */
  Processing = 0,
  /** 生效 */
  Enable = 1,
  /** 失效 */
  Disable = 2,
  /** 删除 */
  Deleted = 3,
  /** 失败 */
  Failed = 9,
}

/** 类型 */
export enum FormatType {
  /** 文本 */
  Text = 0,
  /** 表格 */
  Table = 1,
  /** 图片 */
  Image = 2,
  /** 数据库 */
  Database = 3,
  /** 火山结构化 火山知识库特有 */
  VolcanoStructured = 4,
  /** 火山非结构化 火山知识库特有 */
  VolcanoUnstructured = 5,
  /** 火山数据库 */
  VolcanoDatabase = 6,
}

export enum FrequencyType {
  /** 不更新 */
  None = 0,
  /** 每天追加最新 */
  EveryDay = 1,
  /** 每三天追加最新 */
  EveryThreeDay = 2,
  /** 每七天追加最新 */
  EverySevenDay = 3,
}

export enum Operation {
  /** "=" */
  EQUAL = 1,
  /** "<>" 或 "!=" */
  NOT_EQUAL = 2,
  /** ">" */
  GREATER_THAN = 3,
  /** "<" */
  LESS_THAN = 4,
  /** ">=" */
  GREATER_EQUAL = 5,
  /** "<=" */
  LESS_EQUAL = 6,
  /** "IN" */
  IN = 7,
  /** "NOT IN" */
  NOT_IN = 8,
}

/** 排序字段 */
export enum OrderField {
  CreateTime = 1,
  UpdateTime = 2,
}

/** 排序规则 */
export enum OrderType {
  Desc = 1,
  Asc = 2,
}

export enum ParamSource {
  /** 默认用户输入 */
  Input = 0,
  /** 引用变量 */
  Variable = 1,
}

export enum ParsingType {
  /** 快速解析 */
  FastParsing = 0,
  /** 精准解析 */
  AccurateParsing = 1,
}

export enum ReviewStatus {
  /** 处理中 */
  Processing = 0,
  /** 已完成 */
  Enable = 1,
  /** 失败 */
  Failed = 2,
  /** 失败 */
  ForceStop = 3,
}

export enum StorageLocation {
  Default = 0,
  OpenSearch = 1,
  Douyin = 2,
}

export enum TabType {
  /** list<string> */
  ListString = 1,
  /** string */
  String = 2,
  /** int64 */
  Integer = 3,
  /** float32 */
  Float = 4,
  /** bool */
  Boolean = 5,
}

/** 更新类型 */
export enum UpdateType {
  NoUpdate = 0,
  Cover = 1,
  Append = 2,
}

export enum VolcanoDatasetServiceStatus {
  DatasetServiceValid = 0,
  DatasetServiceInvalid = 1,
}

export enum VolcanoDatasetStatus {
  DatasetValid = 0,
  DatasetInvalid = 1,
}

export enum VolcanoDatasetType {
  /** 标准版 */
  Standard = 1,
  /** 旗舰版 */
  Ultimate = 2,
}

export enum WebStatus {
  /** 处理中 */
  Handling = 0,
  /** 已完成 */
  Finish = 1,
  /** 失败 */
  Failed = 2,
}

export interface ChunkStrategy {
  separator?: string;
  max_tokens?: Int64;
  remove_extra_spaces?: boolean;
  remove_urls_emails?: boolean;
  /** 如果为0, 则不使用以上字段的配置 */
  chunk_type?: ChunkType;
  /** 1 链接阅读器 (cici 长文) */
  content_schema?: ContentSchema;
  /** 图片类型，图片描述文字的标注方式 */
  caption_type?: CaptionType;
  /** 分段重叠度 */
  overlap?: Int64;
  /** 最大层级数（按层级分段时生效） */
  max_level?: Int64;
  /** 切片保留层级标题（按层级分段时生效） */
  save_title?: boolean;
}

export interface FilterStrategy {
  /** 过滤页数 */
  filter_page?: Array<number>;
  /** 过滤框位置 */
  filter_box_position?: Array<number>;
}

export interface IndexStrategy {
  /** 是否开启向量索引（默认为true） */
  vector_indexing?: boolean;
  /** 是否开启关键词索引（默认为true） */
  keyword_indexing?: boolean;
  /** 是否开启分层索引 */
  hierarchical_indexing?: boolean;
  /** 向量模型 */
  model?: string;
}

export interface OpenSearchConfig {
  region?: string;
  instance_id?: string;
  public_endpoint?: string;
  username?: string;
  password?: string;
  instance_name?: string;
}

export interface ParsingStrategy {
  /** 解析类型 */
  parsing_type?: ParsingType;
  /** 是否开启图片元素提取（精准解析时生效） */
  image_extraction?: boolean;
  /** 是否开启表格元素提取（精准解析时生效） */
  table_extraction?: boolean;
  /** 是否开启图片OCR（精准解析时生效） */
  image_ocr?: boolean;
}

export interface SinkStrategy {
  /** 是否检查索引成功 */
  check_index?: boolean;
}

export interface StorageStrategy {
  storage_location: StorageLocation;
  open_search_config?: OpenSearchConfig;
}

export interface TabValue {
  type?: TabType;
  /** 本地默认值 */
  local_input?: string;
  /** 入参的来源 */
  param_source?: ParamSource;
  /** 引用variable的key */
  variable_ref?: string;
}

export interface VolcanoDataset {
  /** 火山侧知识库id 字符串 */
  id?: string;
  /** 名称 */
  name?: string;
  /** 类型 结构化 or 非结构化知识库 */
  format_type?: FormatType;
  /** 火山知识库详情链接 */
  link?: string;
  /** 火山知识库状态 是否已失效 */
  status?: VolcanoDatasetStatus;
  desc?: string;
  /** 火山知识库版本 */
  version?: VolcanoDatasetType;
  /** 标签信息（标签名和标签信息） */
  tab_info?: Record<string, VolcanoDatasetTabInfo>;
}

export interface VolcanoDatasetProject {
  /** 目前只需要项目名称 */
  name?: string;
  /** 火山知识库空间跳转链接 */
  link?: string;
}

export interface VolcanoDatasetService {
  /** 火山侧知识服务id 字符串 */
  id?: string;
  /** 名称 */
  name?: string;
  /** 火山知识服务链接 */
  link?: string;
  /** 火山知识库状态 是否已失效 */
  status?: VolcanoDatasetServiceStatus;
  desc?: string;
}

export interface VolcanoDatasetTabInfo {
  name?: string;
  type?: TabType;
  /** 枚举值列表 */
  enumerated_val_list?: Array<string>;
}
/* eslint-enable */
