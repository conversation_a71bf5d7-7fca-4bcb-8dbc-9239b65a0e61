/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as base from './base';

export type Int64 = string | number;

export interface CreateDocumentReviewRequest {
  dataset_id?: string;
  reviews?: Array<ReviewInput>;
  chunk_strategy?: common.ChunkStrategy;
  parsing_strategy?: common.ParsingStrategy;
  Base?: base.Base;
}

export interface CreateDocumentReviewResponse {
  dataset_id?: string;
  reviews?: Array<Review>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface MGetDocumentReviewRequest {
  dataset_id?: string;
  review_ids?: Array<string>;
  Base?: base.Base;
}

export interface MGetDocumentReviewResponse {
  dataset_id?: string;
  reviews?: Array<Review>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface Review {
  review_id?: string;
  document_name?: string;
  document_type?: string;
  tos_url?: string;
  /** 状态 */
  status?: common.ReviewStatus;
  doc_tree_tos_url?: string;
  preview_tos_url?: string;
}

export interface ReviewInput {
  document_name?: string;
  document_type?: string;
  tos_uri?: string;
  document_id?: string;
}

export interface SaveDocumentReviewRequest {
  dataset_id?: string;
  review_id?: string;
  doc_tree_json?: string;
  Base?: base.Base;
}

export interface SaveDocumentReviewResponse {
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}
/* eslint-enable */
