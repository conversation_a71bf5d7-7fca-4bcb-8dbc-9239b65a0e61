/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as plugin_common from './plugin_common';

export type Int64 = string;

export enum PluginPricingCalculationType {
  /** 按次数 */
  ByTimes = 1,
  /** 按基本单位 (例如token) */
  ByUnit = 2,
  /** 无限制 */
  Unlimited = 3,
}

export enum PluginPricingStrategy {
  /** 免费 */
  Free = 0,
  /** 用量制 */
  Quantity = 1,
  /** 订阅制 */
  Subscribe = 2,
}

export enum PricingCurrencyType {
  /** 计价的货币类型 */
  USD = 0,
  CNY = 1,
}

/** Begin 插件计费 */
export enum PricingInterval {
  /** 计价的时间周期 */
  Second = 1,
  Minute = 2,
  Hour = 3,
  Day = 4,
  Month = 5,
  Year = 6,
}

export enum TimeUnit {
  Minute = 1,
  Hour = 2,
  Day = 3,
}

export interface Creator {
  id?: string;
  name?: string;
  avatar_url?: string;
  /** 是否是自己创建的 */
  self?: boolean;
}

export interface DeletePluginPricingRuleRequest {
  plugin_id: string;
  rule_id: string;
  Base?: base.Base;
}

export interface DeletePluginPricingRuleResponse {
  BaseResp?: base.BaseResp;
}

export interface DraftBot {
  /** bot id */
  id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  icon_url?: string;
  visibility?: plugin_common.VisibilityType;
  has_published?: plugin_common.Publish;
  create_time?: string;
  update_time?: string;
  creator_id?: string;
  space_id?: string;
  model_info?: ModelInfo;
  creator?: Creator;
  space_name?: string;
}

export interface EditDomainWhitelistRequest {
  domain_port_list: Array<string>;
  /** 操作类型 */
  operate_type: plugin_common.OperateType;
  Base?: base.Base;
}

export interface EditDomainWhitelistResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetPluginListData {
  plugin_list?: Array<PluginData>;
  total?: Int64;
}

export interface GetPluginListRequest {
  page?: number;
  size?: number;
  status?: plugin_common.PluginStatus;
  plugin_ids?: Array<string>;
  space_id?: string;
  plugin_name?: string;
  order_by?: plugin_common.OrderBy;
  Base?: base.Base;
}

export interface GetPluginListResponse {
  code?: Int64;
  msg?: string;
  data?: GetPluginListData;
  BaseResp?: base.BaseResp;
}

export interface GetPluginPricingRuleRequest {
  plugin_id: string;
  Base?: base.Base;
}

export interface GetPluginPricingRuleResponse {
  plugin_pricing_rules?: Array<PluginPricingRule>;
  BaseResp?: base.BaseResp;
}

export interface GetPluginQuotaRuleRequest {
  /** 插件ID */
  plugin_id: string;
  Base?: base.Base;
}

export interface GetPluginQuotaRuleResponse {
  /** 所有规则 */
  quota_rule_and_apis?: Array<QuotaRuleAndAPI>;
  BaseResp?: base.BaseResp;
}

export interface GetPluginQuoteBotData {
  /** bot列表 */
  bot_list?: Array<DraftBot>;
  total?: Int64;
}

export interface GetPluginQuoteBotRequest {
  page: number;
  size: number;
  /** plugin id */
  plugin_id: string;
  /** team id */
  space_id?: string;
  /** bot id */
  bot_id?: string;
  /** bot name 支持精确查询 */
  bot_name?: string;
  /** ConnectorID */
  connector_id?: plugin_common.ConnectorID;
  Base?: base.Base;
}

export interface GetPluginQuoteBotResponse {
  code?: Int64;
  msg?: string;
  data?: GetPluginQuoteBotData;
  BaseResp?: base.BaseResp;
}

export interface ModelInfo {
  model_name?: string;
}

export interface PluginAPI {
  /** operationId */
  name?: string;
  /** summary */
  desc?: string;
  parameters?: Array<PluginParameter>;
  plugin_id?: string;
  plugin_name?: string;
  /** 序号和playground保持一致 */
  api_id?: string;
  record_id?: string;
  /** path */
  path?: string;
  response?: Array<PluginParameter>;
}

export interface PluginData {
  id?: string;
  name?: string;
  /** description_for_human */
  desc_for_human?: string;
  plugin_icon?: string;
  plugin_type?: plugin_common.PluginType;
  status?: plugin_common.PluginStatus;
  /** json */
  plugin_desc?: string;
  update_time?: Int64;
  creator?: string;
  space_id?: string;
  space_name?: string;
  /** 引用数 */
  bot_quote?: number;
  /** 发布状态 */
  publish_status?: plugin_common.PluginPublishStatus;
  /** 插件渠道 */
  channel_id?: plugin_common.PluginChannel;
  /** 插件素材id */
  material_id?: string;
  /** tools */
  plugin_apis?: Array<PluginAPI>;
  /** server url */
  server_url?: string;
  /** plugin的商品上下架状态 */
  plugin_product_list_status?: plugin_common.ProductStatus;
  /** plugin的商品审核状态 */
  plugin_product_draft_status?: plugin_common.ProductDraftStatus;
  /** 插件商品id */
  channel_plugin_id?: string;
  /** 插件的计费规则数 */
  pricing_rule_count?: number;
}

export interface PluginParameter {
  name?: string;
  desc?: string;
  required?: boolean;
  type?: string;
  sub_parameters?: Array<PluginParameter>;
  /** 如果Type是数组，则有subtype */
  sub_type?: string;
  /** 如果入参的值是引用的则有fromNodeId */
  from_node_id?: string;
  /** 具体引用哪个节点的key */
  from_output?: Array<string>;
  /** 如果入参是用户手输 就放这里 */
  value?: string;
}

export interface PluginPriceInfo {
  /** 手动填的价格
价格 */
  price?: string;
  /** 价格对应的货币类型 */
  currency_type?: PricingCurrencyType;
  /** 价格对应的时间周期 */
  interval?: PricingInterval;
  /** 每次调用消耗的基本单位的数量 */
  units_for_once?: string;
  /** 基本单位的名称 (例如token) */
  unit_name?: string;
}

export interface PluginPriceLimit {
  /** 价格限制
次数限制 */
  times_limit?: string;
  /** 次数限制对应的时间周期 */
  times_interval?: PricingInterval;
  /** 基本单位的限制 */
  units_limit?: string;
  /** 基本单位的限制对应的时间周期 */
  units_interval?: PricingInterval;
}

export interface PluginPriceResult {
  /** 自动算的每次调用价格
价格 */
  price?: string;
  /** 价格对应的货币类型 */
  currency_type?: PricingCurrencyType;
  /** 每次调用消耗的token数量 */
  tokens_for_once?: string;
}

export interface PluginPricingRule {
  /** 为空:对整个plugin生效; 非空:对单个API生效 */
  api_name?: string;
  /** 备注信息, 比如采购方案 */
  comment?: string;
  /** 调用限制 */
  price_limit?: PluginPriceLimit;
  /** 手动填的成本 */
  price_info?: PluginPriceInfo;
  /** 计算出来的每次调用成本 */
  price_result?: PluginPriceResult;
  /** 一级规则，默认免费 */
  pricing_strategy?: PluginPricingStrategy;
  /** 二级规则 */
  pricing_calculation_type?: PluginPricingCalculationType;
  /** 规则ID, 新建时为0 */
  rule_id?: string;
}

export interface QuotaAlert {
  /** 触发告警的API名称 */
  api_name?: string;
  /** 触发告警时,已使用的容量 */
  used_quota?: string;
  /** 告警信息 */
  message?: string;
  /** 告警触发时间 */
  create_time?: string;
}

export interface QuotaRule {
  /** 容量规则ID */
  rule_id?: string;
  /** 容量: 1个计数周期内, 最大可调用次数 */
  quota?: string;
  /** 告警阈值的百分比, 0~100 */
  threshold?: string;
  /** 规则生效的开始时间 */
  start_time?: string;
  /** 计数周期的时间单位, 分钟/小时/天 */
  period_time_unit?: TimeUnit;
  /** 计数周期 */
  period?: string;
  /** 最近5条告警 */
  quota_alerts?: Array<QuotaAlert>;
}

export interface QuotaRuleAndAPI {
  /** 容量规则 */
  quota_rules?: Array<QuotaRule>;
  /** nil 表示对整个插件有效，否则仅对单个API有效 */
  api_name?: string;
  /** 是否无限量 */
  unlimited?: boolean;
}

export interface TransPluginOwnerRequest {
  /** 插件素材id */
  material_id: string;
  /** 目标开发者id */
  target_dev_id: string;
  Base?: base.Base;
}

export interface TransPluginOwnerResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface UpdatePluginPricingRuleRequest {
  plugin_id: string;
  pricing_rule?: PluginPricingRule;
  Base?: base.Base;
}

export interface UpdatePluginPricingRuleResponse {
  BaseResp?: base.BaseResp;
}

export interface UpdatePluginQuotaRuleRequest {
  /** 插件ID */
  plugin_id: string;
  /** 所有规则 */
  quota_rule_and_apis?: Array<QuotaRuleAndAPI>;
  Base?: base.Base;
}

export interface UpdatePluginQuotaRuleResponse {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
