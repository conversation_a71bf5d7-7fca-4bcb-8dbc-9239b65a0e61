/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agw_common from './namespaces/agw_common';
import * as agw_common_param from './namespaces/agw_common_param';
import * as api_manage from './namespaces/api_manage';
import * as auth from './namespaces/auth';
import * as bam_api from './namespaces/bam_api';
import * as banner from './namespaces/banner';
import * as base from './namespaces/base';
import * as benefit_common from './namespaces/benefit_common';
import * as byte_tree from './namespaces/byte_tree';
import * as common from './namespaces/common';
import * as copilot_common from './namespaces/copilot_common';
import * as home_banner from './namespaces/home_banner';
import * as menu from './namespaces/menu';
import * as model from './namespaces/model';
import * as oapi from './namespaces/oapi';
import * as op_bot from './namespaces/op_bot';
import * as open_api from './namespaces/open_api';
import * as prompt from './namespaces/prompt';
import * as punish from './namespaces/punish';
import * as voice from './namespaces/voice';
import * as workspace from './namespaces/workspace';

export {
  agw_common,
  agw_common_param,
  api_manage,
  auth,
  bam_api,
  banner,
  base,
  benefit_common,
  byte_tree,
  common,
  copilot_common,
  home_banner,
  menu,
  model,
  oapi,
  op_bot,
  open_api,
  prompt,
  punish,
  voice,
  workspace,
};
export * from './namespaces/agw_common';
export * from './namespaces/agw_common_param';
export * from './namespaces/api_manage';
export * from './namespaces/auth';
export * from './namespaces/bam_api';
export * from './namespaces/banner';
export * from './namespaces/base';
export * from './namespaces/benefit_common';
export * from './namespaces/byte_tree';
export * from './namespaces/common';
export * from './namespaces/copilot_common';
export * from './namespaces/home_banner';
export * from './namespaces/menu';
export * from './namespaces/model';
export * from './namespaces/oapi';
export * from './namespaces/op_bot';
export * from './namespaces/open_api';
export * from './namespaces/prompt';
export * from './namespaces/punish';
export * from './namespaces/voice';
export * from './namespaces/workspace';

export type Int64 = string | number;

export default class FlowBotOperationService<T> {
  private request: any = () => {
    throw new Error('FlowBotOperationService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/bot/opt/menu/create */
  CreateMenu(
    req: menu.CreateMenuRequest,
    options?: T,
  ): Promise<menu.CreateMenuResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/opt/menu/create');
    const method = 'POST';
    const data = {
      parent_id: _req['parent_id'],
      menu_name: _req['menu_name'],
      uri: _req['uri'],
      icon: _req['icon'],
      visible: _req['visible'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/bot/opt/menu/list
   *
   * 获取列表
   */
  QueryMenuList(
    req?: menu.QueryMenuListRequest,
    options?: T,
  ): Promise<menu.QueryMenuListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/opt/menu/list');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/bot/opt/menu/update */
  UpdateMenu(
    req: menu.UpdateMenuRequest,
    options?: T,
  ): Promise<menu.UpdateMenuResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/opt/menu/update');
    const method = 'POST';
    const data = {
      id: _req['id'],
      parent_id: _req['parent_id'],
      menu_name: _req['menu_name'],
      uri: _req['uri'],
      icon: _req['icon'],
      visible: _req['visible'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/opt/route/save */
  SaveApiRoute(
    req: menu.CreateApiRouteRequest,
    options?: T,
  ): Promise<menu.CreateApiRouteResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/opt/route/save');
    const method = 'POST';
    const data = {
      menu_id: _req['menu_id'],
      api_paths: _req['api_paths'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/opt/menu/del */
  DelMenu(
    req: menu.DelMenuRequest,
    options?: T,
  ): Promise<menu.DelMenuResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/opt/menu/del');
    const method = 'POST';
    const data = { id: _req['id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/bot/opt/route/list */
  QueryApiRoute(
    req: menu.QueryApiPathRequest,
    options?: T,
  ): Promise<menu.QueryApiPathResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/opt/route/list');
    const method = 'GET';
    const params = { menu_id: _req['menu_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/bot/opt/workspace/model/update */
  UpdateModelConfig(
    req?: workspace.UpdateModelConfigRequest,
    options?: T,
  ): Promise<workspace.UpdateModelConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/opt/workspace/model/update');
    const method = 'POST';
    const data = {
      config_id: _req['config_id'],
      model_status: _req['model_status'],
      is_default_model: _req['is_default_model'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/udpate_doc
   *
   * 更新API文档
   */
  UpdateAPIDoc(
    req?: api_manage.UpdateAPIDocRequest,
    options?: T,
  ): Promise<api_manage.UpdateAPIDocResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/udpate_doc');
    const method = 'POST';
    const data = {
      id: _req['id'],
      doc_content: _req['doc_content'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/update_visible
   *
   * 更新接口可见性
   */
  UpdateAPIVisible(
    req?: api_manage.UpdateAPIVisibleRequest,
    options?: T,
  ): Promise<api_manage.UpdateAPIVisibleResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/update_visible');
    const method = 'POST';
    const data = {
      id: _req['id'],
      source: _req['source'],
      open: _req['open'],
      valid_district: _req['valid_district'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/create_doc
   *
   * 创建API文档
   */
  CreateAPIDocument(
    req?: api_manage.CreateAPIDocumentRequest,
    options?: T,
  ): Promise<api_manage.CreateAPIDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/create_doc');
    const method = 'POST';
    const data = { id: _req['id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/create_psm
   *
   * 创建PSM
   */
  CreatePSM(
    req?: api_manage.CreatePSMRequest,
    options?: T,
  ): Promise<api_manage.CreatePSMResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/create_psm');
    const method = 'POST';
    const data = {
      ns_name: _req['ns_name'],
      psm: _req['psm'],
      branch: _req['branch'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/opt/workspace/model/list */
  GetModelConfigList(
    req?: workspace.GetModelConfigListRequest,
    options?: T,
  ): Promise<workspace.GetModelConfigListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/opt/workspace/model/list');
    const method = 'POST';
    const data = { space_id: _req['space_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/opt/workspace/model/add */
  AddModelConfig(
    req?: workspace.AddModelConfigRequest,
    options?: T,
  ): Promise<workspace.AddModelConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/opt/workspace/model/add');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      key_name: _req['key_name'],
      endpoint_id: _req['endpoint_id'],
      model_name: _req['model_name'],
      model_id: _req['model_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/opt/workspace/list
   *
   * workspace
   */
  GetSpaceList(
    req?: workspace.GetSpaceListRequest,
    options?: T,
  ): Promise<workspace.GetSpaceListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/opt/workspace/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      owner_uid: _req['owner_uid'],
      space_type: _req['space_type'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/bot/common/search_byte_tree
   *
   * 获取服务树
   */
  GetByteTreeByName(
    req?: byte_tree.GetByteTreeByNameReq,
    options?: T,
  ): Promise<byte_tree.GetByteTreeByNameResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/common/search_byte_tree');
    const method = 'GET';
    const params = { name: _req['name'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/bot/opt/workspace/bytetree/set */
  SetByteTree(
    req?: workspace.SetByteTreeRequest,
    options?: T,
  ): Promise<workspace.SetByteTreeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/opt/workspace/bytetree/set');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      byte_tree_node_id: _req['byte_tree_node_id'],
      byte_tree_node_name: _req['byte_tree_node_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/permission/upsert
   *
   * 创建权限
   */
  UpsertPermission(
    req: api_manage.UpsertPermissionReq,
    options?: T,
  ): Promise<api_manage.UpsertPermissionResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/permission/upsert');
    const method = 'POST';
    const data = {
      permission_info: _req['permission_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/bind
   *
   * 接口绑定权限
   */
  BindApiPermission(
    req: api_manage.BindApiPermissionReq,
    options?: T,
  ): Promise<api_manage.BindApiPermissionResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/api_manage/bind');
    const method = 'POST';
    const data = {
      permission_id: _req['permission_id'],
      api_id: _req['api_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/permission/list
   *
   * 获取权限列表
   */
  GetPermissionList(
    req?: api_manage.GetPermissionListReq,
    options?: T,
  ): Promise<api_manage.GetPermissionListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/permission/list');
    const method = 'POST';
    const data = {
      key_name: _req['key_name'],
      permission_id: _req['permission_id'],
      permission_type: _req['permission_type'],
      page_no: _req['page_no'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/permission/level_list
   *
   * 获取下级权限
   */
  GetPermissionListByParentId(
    req: api_manage.GetPermissionListByParentIdReq,
    options?: T,
  ): Promise<api_manage.GetPermissionListByParentIdResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/permission/level_list');
    const method = 'POST';
    const data = {
      parent_id: _req['parent_id'],
      page_no: _req['page_no'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/permission/delete
   *
   * 删除权限
   */
  DeletePermission(
    req: api_manage.DeletePermissionReq,
    options?: T,
  ): Promise<api_manage.DeletePermissionResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/permission/delete');
    const method = 'POST';
    const data = {
      permission_info: _req['permission_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/bot/punish/get_punish_meta_info
   *
   * 处罚中心
   */
  GetPunishMetaInfo(
    req?: punish.GetPunishMetaInfoRequest,
    options?: T,
  ): Promise<punish.GetPunishMetaInfoResponse> {
    const url = this.genBaseURL('/api/bot/punish/get_punish_meta_info');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /api/bot/punish/get_punish_task */
  GetPunishTask(
    req?: punish.GetPunishTaskRequest,
    options?: T,
  ): Promise<punish.GetPunishTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/punish/get_punish_task');
    const method = 'POST';
    const data = {
      punish_entity: _req['punish_entity'],
      punish_type: _req['punish_type'],
      operator_email: _req['operator_email'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/punish/get_punish_measure */
  GetPunishMeasure(
    req?: punish.GetPunishMeasureRequest,
    options?: T,
  ): Promise<punish.GetPunishMeasureResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/punish/get_punish_measure');
    const method = 'POST';
    const data = {
      punish_entity: _req['punish_entity'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/punish/create_punish_task */
  CreatePunishTask(
    req: punish.CreatePunishTaskRequest,
    options?: T,
  ): Promise<punish.CreatePunishTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/punish/create_punish_task');
    const method = 'POST';
    const data = {
      punish_entity: _req['punish_entity'],
      punish_messure_id: _req['punish_messure_id'],
      type: _req['type'],
      punish_duration: _req['punish_duration'],
      punish_object_ids: _req['punish_object_ids'],
      remark: _req['remark'],
      punish_source: _req['punish_source'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/punish/update_punish_measure */
  UpdatePunishMeasure(
    req: punish.UpdatePunishMeasureRequest,
    options?: T,
  ): Promise<punish.UpdatePunishMeasureResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/punish/update_punish_measure');
    const method = 'POST';
    const data = {
      punish_measure_id: _req['punish_measure_id'],
      psm: _req['psm'],
      idl_branch: _req['idl_branch'],
      status: _req['status'],
      punish_name: _req['punish_name'],
      punish_duration: _req['punish_duration'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/punish/create_punish_measure */
  CreatePunishMeasure(
    req: punish.CreatePunishMeasureRequest,
    options?: T,
  ): Promise<punish.CreatePunishMeasureResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/punish/create_punish_measure');
    const method = 'POST';
    const data = {
      punish_entity: _req['punish_entity'],
      punish_effect: _req['punish_effect'],
      allow_unpunish: _req['allow_unpunish'],
      punish_duration: _req['punish_duration'],
      psm: _req['psm'],
      idl_branch: _req['idl_branch'],
      punish_name: _req['punish_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/punish/get_punish_record */
  GetPunishRecord(
    req?: punish.GetPunishRecordRequest,
    options?: T,
  ): Promise<punish.GetPunishRecordResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/punish/get_punish_record');
    const method = 'POST';
    const data = {
      punish_entity: _req['punish_entity'],
      object_ids: _req['object_ids'],
      status: _req['status'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/bot/api_manage/detail
   *
   * 接口详情
   */
  GetAPIDetailById(
    req: api_manage.GetAPIDetailByIdReq,
    options?: T,
  ): Promise<api_manage.GetAPIDetailByIdResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/api_manage/detail');
    const method = 'GET';
    const params = { api_id: _req['api_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/bot/api_manage/init_attribute
   *
   * 接口初始化字段
   */
  InitAttributeInfo(
    req?: api_manage.InitAttributeInfoReq,
    options?: T,
  ): Promise<api_manage.InitAttributeInfoResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/init_attribute');
    const method = 'POST';
    const data = { api_id: _req['api_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/set_limit
   *
   * 接口设置限流
   */
  SetAPIRequestLimitRule(
    req?: api_manage.SetAPIRequestLimitRuleReq,
    options?: T,
  ): Promise<api_manage.SetAPIRequestLimitRuleResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/set_limit');
    const method = 'POST';
    const data = {
      api_id: _req['api_id'],
      limit_rule: _req['limit_rule'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/set_attribute
   *
   * 接口初始化字段
   */
  SetAttributeInfo(
    req?: api_manage.SetAttributeInfoReq,
    options?: T,
  ): Promise<api_manage.SetAttributeInfoResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/set_attribute');
    const method = 'POST';
    const data = {
      api_id: _req['api_id'],
      attribute_schema: _req['attribute_schema'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/model/update_model_func_config
   *
   * 模型相关
   */
  UpdateModelFuncConfig(
    req: model.UpdateModelFuncConfigRequest,
    options?: T,
  ): Promise<model.UpdateModelFuncConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/model/update_model_func_config');
    const method = 'POST';
    const data = {
      model_id: _req['model_id'],
      func_config: _req['func_config'],
      connector_white_list: _req['connector_white_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/model/get_model_func_config */
  GetModelFuncConfig(
    req?: model.GetModelFuncConfigRequest,
    options?: T,
  ): Promise<model.GetModelFuncConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/model/get_model_func_config');
    const method = 'POST';
    const data = { model_id: _req['model_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/model/get_model_list */
  GetModelList(
    req?: model.GetModelListRequest,
    options?: T,
  ): Promise<model.GetModelListResponse> {
    const url = this.genBaseURL('/api/bot/model/get_model_list');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/bot/opt/workspace/member/set */
  SetWorkSpaceMemberLimit(
    req?: workspace.SetWorkSpaceMemberLimitRequest,
    options?: T,
  ): Promise<workspace.SetWorkSpaceMemberLimitResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/opt/workspace/member/set');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      limit_num: _req['limit_num'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/banner/update_banner */
  UpdateBanner(
    req: banner.UpdateBannerRequest,
    options?: T,
  ): Promise<banner.UpdateBannerResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/banner/update_banner');
    const method = 'POST';
    const data = {
      action_type: _req['action_type'],
      banner_id: _req['banner_id'],
      banner_content: _req['banner_content'],
      color_scheme: _req['color_scheme'],
      region: _req['region'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      timezone: _req['timezone'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/banner/get_banner_list */
  GetBannerList(
    req?: banner.GetBannerListRequest,
    options?: T,
  ): Promise<banner.GetBannerListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/banner/get_banner_list');
    const method = 'POST';
    const data = { page: _req['page'], size: _req['size'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/prompt/fetch_msg_list
   *
   * prompt相关
   */
  FetchMsgList(
    req?: prompt.FetchMsgListRequest,
    options?: T,
  ): Promise<prompt.FetchMsgListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/prompt/fetch_msg_list');
    const method = 'POST';
    const data = {
      start_at: _req['start_at'],
      end_at: _req['end_at'],
      log_id: _req['log_id'],
      user_id: _req['user_id'],
      bot_id: _req['bot_id'],
      conn_id: _req['conn_id'],
      channel: _req['channel'],
      message_id: _req['message_id'],
      limit: _req['limit'],
      offset: _req['offset'],
      conversation_id: _req['conversation_id'],
      desc_by_start_time: _req['desc_by_start_time'],
      agw_common_param: _req['agw_common_param'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/prompt/get_trace_sdk */
  GetTraceSDK(
    req?: prompt.GetTraceSDKRequest,
    options?: T,
  ): Promise<prompt.GetTraceSDKResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/prompt/get_trace_sdk');
    const method = 'POST';
    const data = {
      trace_id: _req['trace_id'],
      log_id: _req['log_id'],
      agw_common_param: _req['agw_common_param'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/bot/prompt/get_channel */
  GetChannel(
    req?: prompt.GetChannelRequest,
    options?: T,
  ): Promise<prompt.GetChannelResponse> {
    const url = this.genBaseURL('/api/bot/prompt/get_channel');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/bot/api_manage/list
   *
   * 接口列表
   */
  GetAPIList(
    req?: api_manage.GetAPIListRequest,
    options?: T,
  ): Promise<api_manage.GetAPIListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      name_space: _req['name_space'],
      psm: _req['psm'],
      api_path: _req['api_path'],
      function_name: _req['function_name'],
      source: _req['source'],
      api_id: _req['api_id'],
      valid_district: _req['valid_district'],
      status: _req['status'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/voice/fetch_voice_list */
  FetchVoiceList(
    req?: voice.FetchVoiceListRequest,
    options?: T,
  ): Promise<voice.FetchVoiceListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/voice/fetch_voice_list');
    const method = 'POST';
    const data = {
      voice_id: _req['voice_id'],
      name: _req['name'],
      style_id: _req['style_id'],
      language_code: _req['language_code'],
      page_index: _req['page_index'],
      page_size: _req['page_size'],
      source: _req['source'],
      voice_type: _req['voice_type'],
      volcano_account_id: _req['volcano_account_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/voice/generate_voice */
  GenerateVoice(
    req?: voice.GenerateVoiceRequest,
    options?: T,
  ): Promise<voice.GenerateVoiceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/voice/generate_voice');
    const method = 'POST';
    const data = {
      voice_id: _req['voice_id'],
      preview_text: _req['preview_text'],
      source: _req['source'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/voice/update_voice_config */
  UpdateVoiceConfig(
    req: voice.UpdateVoiceConfigRequest,
    options?: T,
  ): Promise<voice.UpdateVoiceConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/voice/update_voice_config');
    const method = 'POST';
    const data = {
      voice_id: _req['voice_id'],
      status: _req['status'],
      preview_text: _req['preview_text'],
      preview_audio: _req['preview_audio'],
      is_default: _req['is_default'],
      name: _req['name'],
      language_code: _req['language_code'],
      source: _req['source'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/voice/get_support_language
   *
   * 音色管理相关
   */
  GetSupportLanguage(
    req?: voice.GetSupportLanguageRequest,
    options?: T,
  ): Promise<voice.GetSupportLanguageResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/voice/get_support_language');
    const method = 'POST';
    const data = { source: _req['source'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/home_banner/update_home_banner_task */
  UpdateHomeBannerTask(
    req: home_banner.UpdateHomeBannerTaskRequest,
    options?: T,
  ): Promise<home_banner.UpdateHomeBannerTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/home_banner/update_home_banner_task');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      task_name: _req['task_name'],
      task_status: _req['task_status'],
      banner_list: _req['banner_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/home_banner/get_home_banner_task_list */
  GetHomeBannerTaskList(
    req?: home_banner.GetHomeBannerTaskListRequest,
    options?: T,
  ): Promise<home_banner.GetHomeBannerTaskListResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/bot/home_banner/get_home_banner_task_list',
    );
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      task_name: _req['task_name'],
      task_status: _req['task_status'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/home_banner/imagex_upload
   *
   * home banner管理
   */
  ImageXUpload(
    req: home_banner.ImageXUploadRequest,
    options?: T,
  ): Promise<home_banner.ImageXUploadResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/home_banner/imagex_upload');
    const method = 'POST';
    const data = {
      file_info: _req['file_info'],
      file_suffix: _req['file_suffix'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/home_banner/create_home_banner_task */
  CreateHomeBannerTask(
    req: home_banner.CreateHomeBannerTaskRequest,
    options?: T,
  ): Promise<home_banner.CreateHomeBannerTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/home_banner/create_home_banner_task');
    const method = 'POST';
    const data = {
      task_name: _req['task_name'],
      banner_list: _req['banner_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/op_bot/get_draft_bot_model_detail
   *
   * bot管理
   */
  GetDraftBotModelDetail(
    req: op_bot.GetDraftBotModelDetailRequest,
    options?: T,
  ): Promise<op_bot.GetDraftBotModelDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/op_bot/get_draft_bot_model_detail');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      bot_info_type: _req['bot_info_type'],
      bot_version: _req['bot_version'],
      connector_id: _req['connector_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/bot/api_manage/update_api_playground
   *
   * 更新 api playground 配置
   */
  UpdateApiPlayground(
    req?: api_manage.UpdateApiPlaygroundReq,
    options?: T,
  ): Promise<api_manage.UpdateApiPlaygroundResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/bot/api_manage/update_api_playground');
    const method = 'POST';
    const data = {
      api_id: _req['api_id'],
      api_playground: _req['api_playground'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/bot/api_manage/ratelimit/get
   *
   * openapi 限流
   */
  GetRateLimitOperation(
    req: api_manage.GetRateLimitOperationReq,
    options?: T,
  ): Promise<api_manage.GetRateLimitOperationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/api_manage/ratelimit/get');
    const method = 'GET';
    const params = {
      path: _req['path'],
      http_method: _req['http_method'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/bot/api_manage/ratelimit/create */
  CreateRateLimitOperation(
    req: api_manage.CreateRateLimitOperationReq,
    options?: T,
  ): Promise<api_manage.CreateRateLimitOperationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/api_manage/ratelimit/create');
    const method = 'POST';
    const data = { rate_limit: _req['rate_limit'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/api_manage/ratelimit/update */
  UpdateRateLimitOperation(
    req: api_manage.UpdateRateLimitOperationReq,
    options?: T,
  ): Promise<api_manage.UpdateRateLimitOperationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/api_manage/ratelimit/update');
    const method = 'POST';
    const data = {
      id: _req['id'],
      duration: _req['duration'],
      limit_count: _req['limit_count'],
      valid_time_start_unix: _req['valid_time_start_unix'],
      valid_time_end_unix: _req['valid_time_end_unix'],
      remark: _req['remark'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/bot/api_manage/ratelimit/remove */
  RemoveRateLimitOperation(
    req: api_manage.RemoveRateLimitOperationReq,
    options?: T,
  ): Promise<api_manage.RemoveRateLimitOperationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/bot/api_manage/ratelimit/remove');
    const method = 'POST';
    const data = { id: _req['id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
