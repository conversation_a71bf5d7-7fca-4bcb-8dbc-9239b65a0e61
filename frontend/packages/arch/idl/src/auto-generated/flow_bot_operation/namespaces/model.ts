/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface GetModelFuncConfigRequest {
  model_id?: string;
}

export interface GetModelFuncConfigResponse {
  model_func_config_list: Array<ModelFuncConfig>;
  code: Int64;
  msg: string;
}

export interface GetModelListRequest {}

export interface GetModelListResponse {
  model_info_list: Array<Model>;
  code: Int64;
  msg: string;
}

export interface Model {
  model_id?: Int64;
  model_name?: string;
  model_icon?: string;
  /** 透传copilot version字段 */
  version?: string;
  token_limit?: Int64;
}

export interface ModelFuncConfig {
  model_id?: Int64;
  func_config?: string;
  connector_white_list?: string;
  create_time?: Int64;
  update_time?: Int64;
  latest_operator_email?: string;
}

export interface UpdateModelFuncConfigRequest {
  /** 已存在model_id即更新，否则创建 */
  model_id: Int64;
  func_config?: string;
  connector_white_list?: string;
}

export interface UpdateModelFuncConfigResponse {
  code: Int64;
  msg: string;
}
/* eslint-enable */
