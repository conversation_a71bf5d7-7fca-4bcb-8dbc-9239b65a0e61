/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum BannerRegionType {
  Inhouse = 1,
  Release = 2,
  InhouseAndRelease = 3,
}

export enum BannerStatus {
  /** 草稿 */
  Draft = 1,
  /** 展示中 */
  PublishedOnDisplay = 2,
  /** 即将展示 */
  PublishedToDisplay = 3,
  /** 已下线 */
  Offline = 4,
  /** 已结束 */
  End = 5,
}

export enum UpdateBannerActionType {
  Create = 1,
  Update = 2,
  Publish = 3,
  CreateAndPublish = 4,
  Delete = 5,
  Offline = 6,
}

export interface Banner {
  banner_id?: string;
  banner_content?: string;
  color_scheme?: string;
  region?: BannerRegionType;
  start_time?: Int64;
  end_time?: Int64;
  operator_email?: string;
  status?: BannerStatus;
  create_time?: Int64;
  update_time?: Int64;
  timezone?: string;
}

export interface GetBannerListData {
  total: Int64;
  banner_list: Array<Banner>;
}

export interface GetBannerListRequest {
  /** 分页 */
  page?: number;
  /** 分页大小 */
  size?: number;
}

export interface GetBannerListResponse {
  data?: GetBannerListData;
  code: Int64;
  msg: string;
}

export interface UpdateBannerRequest {
  /** create/createAndPublish时，除了banner_id，其余必传
delete/oofline只需要传banner_id
publish/update必传banner_id，其他字段看情况传 */
  action_type: UpdateBannerActionType;
  /** Update/Delete必传 */
  banner_id?: string;
  /** create必传 */
  banner_content?: string;
  /** create必传 */
  color_scheme?: string;
  /** create必传 */
  region?: BannerRegionType;
  start_time?: Int64;
  end_time?: Int64;
  timezone?: string;
}

export interface UpdateBannerResponse {
  code: Int64;
  msg: string;
}
/* eslint-enable */
