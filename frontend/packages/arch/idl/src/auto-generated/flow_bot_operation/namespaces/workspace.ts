/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as common from './common';

export type Int64 = string | number;

export enum ModelStatus {
  enable = 0,
  disable = 1,
}

export interface AddModelConfigData {
  config_id?: string;
}

export interface AddModelConfigRequest {
  space_id?: string;
  key_name?: string;
  endpoint_id?: string;
  model_name?: string;
  model_id?: string;
  Base?: base.Base;
}

export interface AddModelConfigResponse {
  data?: AddModelConfigData;
  code?: Int64;
  msg?: string;
}

export interface GetModelConfigListRequest {
  space_id?: string;
  Base?: base.Base;
}

export interface GetModelConfigListResponse {
  data?: ModelConfigData;
  code?: Int64;
  msg?: string;
}

export interface GetSpaceListRequest {
  space_id?: string;
  owner_uid?: string;
  space_type?: Int64;
  page?: Int64;
  size?: Int64;
  Base?: base.Base;
}

export interface GetSpaceListResponse {
  data?: SpaceListData;
  code?: Int64;
  msg?: string;
}

export interface ModelConfig {
  model_name?: string;
  /** 2:  string key_name */
  model_status?: ModelStatus;
  is_default_model?: boolean;
  model_id?: string;
  config_id?: string;
}

export interface ModelConfigData {
  configs?: Array<ModelConfig>;
}

export interface SetByteTreeRequest {
  space_id?: string;
  byte_tree_node_id?: string;
  byte_tree_node_name?: string;
  Base?: base.Base;
}

export interface SetByteTreeResponse {
  data?: common.EmptyData;
  code?: Int64;
  msg?: string;
}

export interface SetWorkSpaceMemberLimitRequest {
  space_id?: string;
  limit_num?: number;
  Base?: base.Base;
}

export interface SetWorkSpaceMemberLimitResponse {
  data?: common.EmptyData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface SpaceItem {
  space_id?: string;
  space_name?: string;
  owner_uid?: string;
  space_type?: Int64;
  remain_token?: Int64;
  user_count?: Int64;
  create_time?: string;
  description?: string;
  icon_url?: string;
  byte_tree_node_id?: string;
  byte_tree_node_name?: string;
  limit_num?: number;
}

export interface SpaceListData {
  workspace_list?: Array<SpaceItem>;
  total?: Int64;
}

export interface UpdateModelConfigRequest {
  config_id?: string;
  model_status?: ModelStatus;
  is_default_model?: boolean;
  Base?: base.Base;
}

export interface UpdateModelConfigResponse {
  data?: common.EmptyData;
  code?: Int64;
  msg?: string;
}
/* eslint-enable */
