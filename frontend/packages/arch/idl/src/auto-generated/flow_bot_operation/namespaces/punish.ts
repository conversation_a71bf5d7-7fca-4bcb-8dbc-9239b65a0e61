/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum CozePunishTaskStatus {
  Success = 0,
  Fail = 1,
}

/** 创建处罚措施 */
export enum PunishMeasureStatus {
  Testing = 0,
  Online = 1,
  Disabled = 2,
}

export enum PunishRecordStatus {
  ongoing = 0,
  end = 1,
  init = 2,
  failed = 3,
}

export enum PunishSource {
  Op = 1,
  System = 2,
}

export enum PunishStatus {
  normal = 1,
  banned = 2,
}

export enum PunishTaskStatus {
  processing = 0,
  success = 1,
  fail = 2,
  partial_success = 3,
}

/** 创建处罚任务 */
export enum PunishTaskType {
  Punish = 1,
  Unpunish = 2,
}

export enum UnPunishAllowConfig {
  Allow = 0,
  ForBid = 1,
}

export interface CozePunishRequest {
  /** 处罚措施id，通过这个id来选择执行哪个处罚 */
  PunishMeasureID: Int64;
  /** 处罚对象id列表 */
  ObjectIDs: Array<string>;
  /** 处罚人uid */
  OperatorUID?: Int64;
  /** 处罚人邮箱 */
  OperatorEmail?: string;
  /** 处罚源 */
  Source?: string;
  /** 处罚持续时间 */
  Duration?: Int64;
  /** 处罚原因 */
  Remark?: string;
  /** 处罚任务ID */
  PunishTaskID?: Int64;
  Base?: base.Base;
}

export interface CozePunishResponse {
  /** 处罚系统无法感知和校验处罚对象id，需要接入方返回处罚结果
key:对象id；value:处罚结果 */
  PunishResultMap: Record<string, CozePunishTaskResult>;
  BaseResp?: base.BaseResp;
}

export interface CozePunishTaskResult {
  status: CozePunishTaskStatus;
}

export interface CozeUnPunishRequest {
  PunishMeasureID: Int64;
  ObjectIDs: Array<string>;
  OperatorUID?: Int64;
  OperatorEmail?: string;
  Source?: string;
  Remark?: string;
  /** 解除处罚任务ID */
  PunishTaskID?: Int64;
  Base?: base.Base;
}

export interface CozeUnPunishResponse {
  UnPunishResultMap: Record<string, CozePunishTaskResult>;
  BaseResp?: base.BaseResp;
}

export interface CreatePunishMeasureData {
  punish_measure_id: string;
}

export interface CreatePunishMeasureRequest {
  /** 处罚主体 */
  punish_entity: string;
  /** 处罚效果 */
  punish_effect: string;
  /** 是否允许手动解除处罚 */
  allow_unpunish: UnPunishAllowConfig;
  /** 处罚持续时间 */
  punish_duration: Int64;
  /** 接入psm */
  psm: string;
  /** IDL分支 */
  idl_branch?: string;
  /** 处罚措施名称 */
  punish_name: string;
}

export interface CreatePunishMeasureResponse {
  data: CreatePunishMeasureData;
  code: Int64;
  msg: string;
}

export interface CreatePunishTaskRequest {
  /** 处罚主体 */
  punish_entity: string;
  /** 处罚措施 */
  punish_messure_id: string;
  /** 处罚任务类型 */
  type: PunishTaskType;
  /** 处罚时长，不传则使用处罚措施默认时长;-1代表永久 */
  punish_duration?: Int64;
  /** 处罚对象列表 */
  punish_object_ids: Array<string>;
  /** 处罚原因 */
  remark: string;
  /** 处罚来源 */
  punish_source: PunishSource;
}

export interface CreatePunishTaskResponse {
  code: Int64;
  msg: string;
}

export interface GetPunishMeasureData {
  /** 总条数 */
  total: Int64;
  /** 处罚措施列表 */
  punish_measures: Array<PunishMeasure>;
}

/** 获取处罚措施 */
export interface GetPunishMeasureRequest {
  /** 根据主体类型筛选处罚措施（给处罚执行页面用的） */
  punish_entity?: string;
  /** 分页 */
  page?: number;
  /** 分页大小 */
  size?: number;
}

export interface GetPunishMeasureResponse {
  data: GetPunishMeasureData;
  code: Int64;
  msg: string;
}

/** 获取处罚元信息 */
export interface GetPunishMetaInfoRequest {}

export interface GetPunishMetaInfoResponse {
  data: PunishMetaInfo;
  code: Int64;
  msg: string;
}

export interface GetPunishRecordData {
  /** 总条数 */
  total: Int64;
  /** 处罚数据列表 */
  punish_records: Array<PunishRecord>;
}

/** 查询处罚数据 */
export interface GetPunishRecordRequest {
  punish_entity?: string;
  object_ids?: Array<string>;
  status?: PunishRecordStatus;
  /** 分页 */
  page?: number;
  /** 分页大小 */
  size?: number;
}

export interface GetPunishRecordResponse {
  data?: GetPunishRecordData;
  code: Int64;
  msg: string;
}

export interface GetPunishTaskData {
  /** 总条数 */
  total: Int64;
  /** 处罚措施列表 */
  punish_tasks: Array<PunishTask>;
}

/** 查看处罚任务 */
export interface GetPunishTaskRequest {
  punish_entity?: string;
  punish_type?: PunishTaskType;
  /** 前缀匹配 */
  operator_email?: string;
  /** 分页 */
  page?: number;
  /** 分页大小 */
  size?: number;
}

export interface GetPunishTaskResponse {
  data?: GetPunishTaskData;
  code: Int64;
  msg: string;
}

export interface MGetEntityPunishStatusRequest {
  /** 处罚实体枚举 */
  EntityNum: Int64;
  /** 对应处罚措施 */
  PunishMeasureID: Int64;
  /** 处罚对象列表 */
  ObjectIDs: Array<string>;
  Base?: base.Base;
}

export interface MGetEntityPunishStatusResponse {
  PunishStatusMap: Record<string, PunishStatusInfo>;
  BaseResp: base.BaseResp;
}

export interface PunishMeasure {
  /** 处罚措施id */
  punish_measure_id?: string;
  /** 处罚措施名称 */
  punish_name?: string;
  /** 处罚主体 */
  punish_entity?: string;
  /** 处罚效果 */
  punish_effect?: string;
  /** 是否允许手动解除处罚 */
  allow_unpunish?: UnPunishAllowConfig;
  /** 创建人邮箱 */
  creator?: string;
  /** 处罚持续时间 */
  punish_duration?: Int64;
  /** 接入psm */
  psm?: string;
  /** IDL分支 */
  idl_branch?: string;
  /** 处罚措施状态 */
  status?: PunishMeasureStatus;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
}

export interface PunishMetaInfo {
  /** 处罚主体 */
  punish_entity_list: Array<string>;
  /** 可选处罚持续时间 */
  avaliable_punish_durations: Array<Int64>;
}

export interface PunishRecord {
  /** 任务id */
  task_id?: string;
  /** 处罚措施id */
  punish_measure_id?: string;
  /** 处罚措施名称 */
  punish_messure_name?: string;
  /** 处罚主体 */
  punish_entity?: string;
  /** 处罚对象 */
  object_id?: string;
  /** 创建人邮箱 */
  punisher?: string;
  /** 处罚持续时间 */
  punish_duration?: Int64;
  /** 处罚解除人邮箱 */
  unpunisher?: string;
  /** 处罚原因 */
  remark?: string;
  /** 处罚状态 */
  status?: PunishRecordStatus;
  /** 处罚执行时间 */
  create_time?: Int64;
  /** 处罚到期时间 */
  expire_time?: Int64;
}

export interface PunishScanRequest {
  Base?: base.Base;
}

export interface PunishScanResponse {
  BaseResp: base.BaseResp;
}

export interface PunishStatusInfo {
  ObjectID: string;
  Status: PunishStatus;
  /** 处罚开始时间，秒级时间戳 */
  BanStartTime?: Int64;
  /** 处罚到期时间，秒级时间戳 */
  BanExpireTime?: Int64;
}

export interface PunishTask {
  /** 任务id */
  task_id?: string;
  /** 处罚措施id */
  punish_measure_id?: string;
  /** 处罚主体 */
  punish_entity?: string;
  /** 创建人邮箱 */
  operator?: string;
  /** 处罚持续时间 */
  punish_duration?: Int64;
  /** 任务类别 */
  type?: PunishTaskType;
  /** 处罚任务执行状态 */
  status?: PunishTaskStatus;
  /** 处罚对象个数 */
  object_num?: Int64;
  /** 处罚来源 */
  punish_source?: PunishSource;
  /** 处罚原因 */
  remark?: string;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
}

/** 更新处罚措施 */
export interface UpdatePunishMeasureRequest {
  punish_measure_id: string;
  psm?: string;
  idl_branch?: string;
  /** 处罚措施状态 */
  status?: PunishMeasureStatus;
  /** 处罚名称 */
  punish_name?: string;
  /** 持续时间 */
  punish_duration?: Int64;
}

export interface UpdatePunishMeasureResponse {
  code: Int64;
  msg: string;
}
/* eslint-enable */
