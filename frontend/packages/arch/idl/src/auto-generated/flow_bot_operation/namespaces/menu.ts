/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export interface CreateApiRouteRequest {
  menu_id: string;
  api_paths: Array<string>;
  Base?: base.Base;
}

export interface CreateApiRouteResponse {
  data: base.EmptyData;
  code: Int64;
  msg: string;
}

export interface CreateMenuRequest {
  parent_id?: string;
  menu_name: string;
  uri?: string;
  icon?: string;
  visible: boolean;
  Base?: base.Base;
}

export interface CreateMenuResponse {
  data?: MenuInfo;
  code: Int64;
  msg: string;
}

export interface DelMenuRequest {
  id: string;
  Base?: base.Base;
}

export interface DelMenuResponse {
  data: base.EmptyData;
  code: Int64;
  msg: string;
}

export interface MenuInfo {
  id: string;
  parent_id: string;
  name: string;
  uri?: string;
  is_visible: boolean;
  icon?: string;
}

export interface QueryApiPathRequest {
  menu_id: string;
  Base?: base.Base;
}

export interface QueryApiPathResponse {
  data: Array<string>;
  code: Int64;
  msg: string;
}

export interface QueryMenuListRequest {
  Base?: base.Base;
}

export interface QueryMenuListResponse {
  data: Array<MenuInfo>;
  code: Int64;
  msg: string;
}

export interface UpdateMenuRequest {
  id: string;
  parent_id?: string;
  menu_name: string;
  uri?: string;
  icon?: string;
  visible: boolean;
  Base?: base.Base;
}

export interface UpdateMenuResponse {
  data?: MenuInfo;
  code: Int64;
  msg: string;
}
/* eslint-enable */
