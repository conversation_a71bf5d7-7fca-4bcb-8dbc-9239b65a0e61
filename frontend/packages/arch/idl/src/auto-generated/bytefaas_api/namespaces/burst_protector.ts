/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface BurstProtectorConfig {
  /** Maximum number of instances allowed during burst period */
  instance_quota: number;
  /** Time period for quota enforcement (seconds) */
  period: number;
  /** Service to redirect traffic to when burst is detected */
  redirect_service?: string;
  /** Cluster to redirect traffic to when burst is detected */
  redirect_cluster?: string;
  /** IDC (data center) to redirect traffic to when burst is detected */
  redirect_idc?: string;
  /** Stage of burst protection (e.g., development, production) */
  stage: string;
  /** Ratio for burst protection threshold calculation */
  ratio: number;
}

export interface BurstProtectorWithMetas {
  /** Method name for burst protection */
  method: string;
  /** Cluster making the call */
  caller_cluster: string;
  /** Cluster receiving the call */
  callee_cluster: string;
  /** Quota of instances allowed */
  instance_quota: number;
  /** Time period for quota enforcement (seconds or minutes) */
  period: number;
  /** Service to redirect to if burst is detected */
  redirect_service?: string;
  /** Cluster to redirect to if burst is detected */
  redirect_cluster?: string;
  /** IDC (data center) to redirect to if burst is detected */
  redirect_idc?: string;
  /** Ratio for burst protection */
  ratio: number;
}

export interface DeleteBurstProtectorRequest {
  /** Delete all burst protectors */
  is_all?: boolean;
  /** List of PSMs to delete */
  psms?: string;
  /** Single PSM to delete */
  psm?: string;
  /** Cluster to delete */
  cluster?: string;
}

export interface DeleteBurstProtectorResponse {
  /** Response code */
  code: number;
  /** Error message, if any */
  error?: string;
  /** Success or failure summary */
  message?: string;
}

export interface GetBurstProtectorSwitchRequest {
  /** PSM to fetch */
  psm?: string;
  /** Cluster to fetch */
  cluster?: string;
}

export interface GetBurstProtectorSwitchResponse {
  /** Response code */
  code: number;
  /** Error message, if any */
  error?: string;
  /** Burst protector configurations */
  data?: Array<BurstProtectorWithMetas>;
}

export interface PutBurstProtectorSwitchRequest {
  /** PSM to update */
  psm: string;
  /** Cluster to update */
  cluster: string;
  /** Caller PSM */
  caller_psm?: string;
  /** Caller cluster */
  caller_cluster?: string;
  /** Method */
  method?: string;
  /** Burst protector configuration */
  config: BurstProtectorConfig;
}

export interface PutBurstProtectorSwitchResponse {
  /** Response code */
  code: number;
  /** Error message, if any */
  error?: string;
  /** Updated configuration */
  data?: BurstProtectorConfig;
}

export interface SwitchBurstProtectorRequest {
  /** Switch stage for all PSMs */
  is_all?: boolean;
  /** List of PSMs to switch */
  psms?: string;
  /** Single PSM to switch */
  psm?: string;
  /** Cluster to switch */
  cluster?: string;
  /** Stage to switch to */
  stage: string;
  /** Debug mode */
  debug?: boolean;
}

export interface SwitchBurstProtectorResponse {
  /** Response code */
  code: number;
  /** Error message, if any */
  error?: string;
  /** Success or failure summary */
  message?: string;
}
/* eslint-enable */
