/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CreateHttpTriggerRequest {
  /** Whether to disable ByteFaas error response */
  bytefaas_error_response_disabled?: boolean;
  /** Whether to disable ByteFaas response header */
  bytefaas_response_header_disabled?: boolean;
  /** Name of the cluster for the service */
  cluster: string;
  /** Description of the HTTP trigger */
  description?: string;
  /** Whether the HTTP trigger is enabled */
  enabled?: boolean;
  /** Name of the HTTP trigger */
  name?: string;
  /** Region of the service */
  region: string;
  /** ID of the service */
  service_id: string;
  /** URL prefix for the trigger */
  url_prefix?: string;
  /** Type of version */
  version_type?: string;
  /** Value for the version type */
  version_value?: string;
  /** Runtime environment for the trigger */
  runtime?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface CreateHttpTriggerResponse {
  code?: number;
  data?: common.HttpTriggerResponse;
  error?: string;
}

export interface DeleteHttpTriggerRequest {
  /** cluster of service */
  cluster: string;
  /** region of service */
  region: string;
  /** ID of service */
  service_id: string;
  /** ID of trigger */
  trigger_id: string;
  'X-Jwt-Token'?: string;
}

export interface DeleteHttpTriggerResponse {
  code?: number;
  data?: common.HttpTriggerResponse;
  error?: string;
}

export interface GetHttpTriggerRequest {
  /** cluster of service */
  cluster: string;
  /** region of service */
  region: string;
  /** ID of service */
  service_id: string;
  /** ID of trigger */
  trigger_id: string;
  'X-Jwt-Token'?: string;
}

export interface GetHttpTriggerResponse {
  code?: number;
  data?: common.HttpTriggerResponse;
  error?: string;
}

export interface GetHttpTriggersRequest {
  /** cluster of service */
  cluster: string;
  /** region of service */
  region: string;
  /** ID of service */
  service_id: string;
  'X-Jwt-Token'?: string;
}

export interface GetHttpTriggersResponse {
  code?: number;
  data?: Array<common.HttpTriggerResponse>;
  error?: string;
}

export interface PatchHttpTriggerRequest {
  /** Whether to disable ByteFaas error response */
  bytefaas_error_response_disabled?: boolean;
  /** Whether to disable ByteFaas response header */
  bytefaas_response_header_disabled?: boolean;
  /** Name of the cluster for the service */
  cluster: string;
  /** Description of the HTTP trigger */
  description?: string;
  /** Whether the HTTP trigger is enabled */
  enabled?: boolean;
  /** Name of the HTTP trigger */
  name?: string;
  /** Region of the service */
  region: string;
  /** ID of the service */
  service_id: string;
  /** ID of the trigger */
  trigger_id: string;
  /** URL prefix for the trigger */
  url_prefix?: string;
  /** Type of version (e.g., 'revision' or 'alias') */
  version_type?: string;
  /** Value for the version type (e.g., revision ID if type is 'revision') */
  version_value?: string;
}

export interface PatchHttpTriggerResponse {
  code?: number;
  data?: common.HttpTriggerResponse;
  error?: string;
}

export interface UpdateHttpTriggerRequest {
  /** Whether to disable ByteFaas error response */
  bytefaas_error_response_disabled?: boolean;
  /** Whether to disable ByteFaas response header */
  bytefaas_response_header_disabled?: boolean;
  /** Name of the cluster for the service */
  cluster: string;
  /** Description of the HTTP trigger */
  description?: string;
  /** Whether the HTTP trigger is enabled */
  enabled?: boolean;
  /** Name of the HTTP trigger */
  name?: string;
  /** Region of the service */
  region: string;
  /** ID of the service */
  service_id: string;
  /** ID of the trigger */
  trigger_id: string;
  /** URL prefix for the trigger */
  url_prefix?: string;
  /** Type of version (e.g., 'revision' or 'alias') */
  version_type?: string;
  /** Value for the version type (e.g., revision ID if type is 'revision') */
  version_value?: string;
  /** Runtime environment for the trigger */
  runtime?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface UpdateHttpTriggerResponse {
  code?: number;
  /** Updated HTTP trigger data */
  data?: common.HttpTriggerResponse;
  error?: string;
}
/* eslint-enable */
