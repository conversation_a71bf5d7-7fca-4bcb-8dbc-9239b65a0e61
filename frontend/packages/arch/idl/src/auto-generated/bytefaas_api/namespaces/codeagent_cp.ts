/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum APIType {
  RPC = 1,
  HTTP = 2,
}

export enum ChatRole {
  USER = 1,
  AGENT = 2,
}

/** Enums */
export enum CodeType {
  AGENT = 1,
  SCRIPT = 2,
  HTTP_SERVER = 3,
}

export enum ProgrammingLanguage {
  PYTHON = 1,
  JAVASCRIPT = 2,
  TYPESCRIPT = 3,
  JAVA = 4,
  GO = 5,
  CPP = 6,
}

export enum PSMAPIType {
  OVERPAAS = 1,
  BAM = 2,
  MCP = 3,
}

export enum ReleaseType {
  MCP = 1,
  A2A = 2,
}

/** Basic Data Structures */
export interface APIMeta {
  name: string;
  source_psm: string;
  source_psm_type: PSMAPIType;
  api_type: APIType;
  method: string;
  version: string;
}

export interface CreateProjectRequest {
  name: string;
  description: string;
  code_type: CodeType;
  language: ProgrammingLanguage;
  psm: string;
  bytetree_node_id: Int64;
  admins?: Array<string>;
  api_dependencies?: Array<APIMeta>;
  prompt?: string;
}

export interface CreateProjectResponse {
  code: number;
  error: string;
  data?: Project;
}

export interface DeleteProjectRequest {
  name: string;
}

export interface DeleteProjectResponse {
  code: number;
  error: string;
  data?: Project;
}

export interface GetAPIRequest {
  psm: string;
  version: string;
  api_type: PSMAPIType;
}

export interface GetAPIResponse {
  code: number;
  error: string;
  data: Array<APIMeta>;
}

export interface GetProjectRequest {
  project_id: string;
}

export interface GetProjectResponse {
  code: number;
  error: string;
  data?: Project;
}

export interface ListProjectsRequest {
  name?: Array<string>;
  description?: Array<string>;
  code_type?: Array<CodeType>;
  language?: Array<ProgrammingLanguage>;
  psm?: Array<string>;
  page?: number;
  page_size?: number;
  id?: Array<string>;
}

export interface ListProjectsResponse {
  code: number;
  error: string;
  data?: Array<Project>;
}

export interface ListPSMAPIVersionsRequest {
  psm: string;
  api_type: PSMAPIType;
}

export interface ListPSMAPIVersionsResponse {
  code: number;
  error: string;
  data?: Array<string>;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  code_type: CodeType;
  language: ProgrammingLanguage;
  psm: string;
  bytetree_node_id: Int64;
  admins?: Array<string>;
  api_dependencies?: Array<APIMeta>;
  prompt?: string;
  created_at: Int64;
  updated_at: Int64;
  sandbox_id: string;
}

export interface UpdateProjectRequest {
  project_id: string;
  name?: string;
  description?: string;
  code_type?: CodeType;
  language?: ProgrammingLanguage;
  psm?: string;
  bytetree_node_id?: Int64;
  admins?: Array<string>;
  api_dependencies?: Array<APIMeta>;
  prompt?: string;
}

export interface UpdateProjectResponse {
  code: number;
  error: string;
  data?: Project;
}
/* eslint-enable */
