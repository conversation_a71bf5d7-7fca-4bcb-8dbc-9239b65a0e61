/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CreateConsulTriggerRequest {
  /** Name of the cluster for the service */
  cluster: string;
  /** Description of the Consul trigger */
  description?: string;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Name of the Consul trigger */
  name?: string;
  /** Region of the service */
  region: string;
  /** ID of the service */
  service_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface CreateConsulTriggerResponse {
  code?: number;
  data?: common.ConsulTriggerResponseData;
  error?: string;
}

export interface DeleteConsulTriggerRequest {
  /** Name of the cluster for the service */
  cluster: string;
  /** Region of the service */
  region: string;
  /** ID of the service */
  service_id: string;
  /** ID of the trigger to delete */
  trigger_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface DeleteConsulTriggerResponse {
  code?: number;
  data?: common.EmptyObject;
  error?: string;
}

export interface GetConsulTriggerRequest {
  /** Name of the cluster for the service */
  cluster: string;
  /** Region of the service */
  region: string;
  /** ID of the service */
  service_id: string;
  /** trigger_id of function */
  trigger_id: string;
  'X-Jwt-Token'?: string;
}

export interface GetConsulTriggerResponse {
  code?: number;
  data?: common.ConsulTriggerResponseData;
  error?: string;
}

export interface UpdateConsulTriggerRequest {
  /** Name of the cluster for the service */
  cluster: string;
  /** Description of the Consul trigger */
  description?: string;
  /** Whether the trigger is enabled */
  enabled?: boolean;
  /** Name of the Consul trigger */
  name?: string;
  /** Region of the service */
  region: string;
  /** ID of the service */
  service_id: string;
  /** ID of the trigger to update */
  trigger_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface UpdateConsulTriggerResponse {
  code?: number;
  data?: common.ConsulTriggerResponseData;
  error?: string;
}
/* eslint-enable */
