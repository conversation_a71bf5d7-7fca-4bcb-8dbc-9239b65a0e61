/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CreateMcpServerAclBpmTicketRequest {
  /** MCP server ID for which to create the BPM ticket */
  server_id: string;
  /** List of PSMs to add to the ACL */
  add_psms?: Array<string>;
  /** List of PSMs to remove from the ACL */
  remove_psms?: Array<string>;
  /** Reason for the ACL change */
  reason?: string;
  /** Whether to disable authentication for the server */
  disable_auth?: boolean;
}

export interface CreateMcpServerAclBpmTicketResponse {
  code?: number;
  error?: string;
  /** BPM ticket response data for ACL changes */
  data?: CreateMcpServerAclBpmTicketResponseData;
}

export interface CreateMcpServerAclBpmTicketResponseData {
  /** Link to the BPM ticket for ACL changes */
  bpm_link: string;
}

export interface CreateMcpServerRequest {
  /** PSM name */
  psm: string;
  /** Name of the MCP server */
  name: string;
  /** Description of the MCP server */
  description?: string;
  /** Owner of the MCP server */
  owner?: string;
  /** Parent PSM ID */
  psm_parent_id: number;
  /** Global configuration key-value pairs */
  global_config?: Array<KeyValue>;
  /** List of admin users (comma-separated) */
  admins?: string;
  /** List of authorizer users (comma-separated) */
  authorizers?: string;
  /** Service level (optional) */
  service_level?: string;
  /** Purpose of the service (optional) */
  service_purpose?: string;
  /** Whether authentication is enabled */
  auth_enabled?: boolean;
  /** List of allowed PSMs */
  allowed_psms?: Array<string>;
  /** List of meta parameters */
  meta_params?: Array<MetaParam>;
}

export interface CreateMcpServerResponse {
  code?: number;
  error?: string;
  data?: McpServer;
}

export interface CustomHandler {
  /** Handler type */
  type?: string;
  /** PSM name */
  psm?: string;
  /** Cluster name */
  cluster?: string;
  /** Service ID */
  service_id?: string;
}

export interface DeleteMcpServerRequest {
  /** MCP server ID to delete */
  server_id: string;
}

export interface FaasToolConfig {
  /** Tool type */
  type: string;
  /** PSM name */
  psm: string;
  /** Method to invoke */
  method: string;
  /** List of HTTP headers as key-value pairs */
  headers?: Array<KeyValue>;
  /** Schema for the request body (optional) */
  request_schema?: string;
  /** Schema for the response body (optional) */
  response_schema?: string;
  /** Whether to force parsing the response as JSON */
  force_parse_response_as_json?: boolean;
  /** Secret for service account authentication (optional) */
  service_account_secret?: string;
  /** Cluster name */
  cluster: string;
  /** Path for the function (optional) */
  path?: string;
}

export interface GenerateMcpServerSseTokenRequest {
  /** MCP server ID to generate SSE token for */
  server_id: string;
}

export interface GenerateMcpServerSseTokenResponse {
  code?: number;
  error?: string;
  /** SSE token data for MCP server */
  data?: McpServerSseToken;
}

export interface GetMcpServerAclRequest {
  /** MCP server ID to get ACL for */
  server_id: string;
}

export interface GetMcpServerAclResponse {
  code?: number;
  error?: string;
  /** MCP server ACL data */
  data?: McpServerAcl;
}

export interface GetMcpServerRequest {
  /** MCP server ID to retrieve */
  server_id: string;
}

export interface GetMcpServerResponse {
  /** Response code */
  code?: number;
  /** Error message, if any */
  error?: string;
  /** MCP server data */
  data?: McpServer;
}

export interface HttpToolConfig {
  /** URL to send the HTTP request to */
  url: string;
  /** HTTP method */
  method: string;
  /** List of HTTP headers as key-value pairs */
  headers?: Array<KeyValue>;
  /** Schema for the request body (optional) */
  request_schema?: string;
  /** Schema for the response body (optional) */
  response_schema?: string;
  /** Whether to force parsing the response as JSON */
  force_parse_response_as_json?: boolean;
  /** Secret for service account authentication (optional) */
  service_account_secret?: string;
  /** PSM name (optional) */
  psm?: string;
  /** Cluster name (optional) */
  cluster?: string;
  /** Tool connection timeout duration (optional) */
  connection_timeout?: Int64;
  /** Tool request timeout duration (optional) */
  request_timeout?: Int64;
}

export interface IDL {
  /** Main IDL file content */
  main_idl?: string;
  /** Additional IDL files as a map */
  idls?: Record<string, string>;
}

export interface KeyValue {
  /** Key name */
  key: string;
  /** Value associated with the key */
  value: string;
}

export interface ListMcpServersRequest {
  /** Whether to list all MCP servers (default: false) */
  all?: string;
  /** Environment name */
  env?: string;
  /** Limit for pagination */
  limit?: number;
  /** Filter by server name */
  name?: string;
  /** Offset for pagination */
  offset?: number;
  /** Filter by owner */
  owner?: string;
  /** Prefix search for multiple fields */
  search?: string;
  /** Search type: all/admin/own/subscribe */
  search_type?: string;
  /** Sort by field in MCP server model */
  sort_by?: string;
  /** Supported search fields: server_id/name/psm */
  search_fields?: string;
}

export interface ListMcpServersResponse {
  code?: number;
  error?: string;
  data?: Array<McpServer>;
}

export interface McpRateLimitRule {
  /** mcp rate limit rate */
  rate: Int64;
  /** mcp rate limit unit */
  unit?: Int64;
}

export interface McpServer {
  /** Unique identifier for the MCP server */
  server_id: string;
  /** PSM name */
  psm: string;
  /** Environment name */
  env_name: string;
  /** Name of the MCP server */
  name: string;
  /** Description of the MCP server */
  description?: string;
  /** Owner of the MCP server */
  owner?: string;
  /** List of subscribers to the MCP server */
  subscribers?: Array<string>;
  /** Global configuration key-value pairs */
  global_config?: Array<KeyValue>;
  /** Parent PSM ID */
  psm_parent_id?: number;
  /** Creation timestamp */
  created_at?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** Current revision ID */
  current_revision_id?: string;
  /** List of admin users (comma-separated) */
  admins?: string;
  /** List of authorizer users (comma-separated) */
  authorizers?: string;
  /** Whether authentication is enabled */
  auth_enabled?: boolean;
  /** List of allowed PSMs */
  allowed_psms?: Array<string>;
  /** Custom handler configuration */
  custom_handler?: CustomHandler;
  /** Whether sensitive data is redacted */
  redacted?: boolean;
  /** List of meta parameters */
  meta_params?: Array<MetaParam>;
}

export interface McpServerAcl {
  /** List of admin users for the MCP server */
  admins?: Array<string>;
  /** List of authorizer users for the MCP server */
  authorizers?: Array<string>;
  /** List of invoker users for the MCP server */
  invokers?: Array<string>;
  /** List of allowed Product Service Modules (PSMs) */
  allowed_psms?: Array<string>;
}

export interface McpServerRevision {
  /** Unique identifier for the revision */
  revision_id: string;
  /** Associated MCP server ID */
  server_id: string;
  /** PSM name */
  psm: string;
  /** Environment name */
  env_name: string;
  /** Name of the revision (optional) */
  name?: string;
  /** Description of the revision (optional) */
  description?: string;
  /** Owner of the revision (optional) */
  owner?: string;
  /** List of tools included in the revision */
  tools?: Array<McpTool>;
  /** Global configuration key-value pairs */
  global_config?: Array<KeyValue>;
  /** Creation timestamp */
  created_at?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** List of meta parameters */
  meta_params?: Array<MetaParam>;
}

export interface McpServerSseToken {
  /** SSE token string for MCP server */
  token: string;
}

export interface McpTool {
  /** Unique identifier for the tool */
  tool_id: string;
  /** Associated MCP server ID */
  server_id: string;
  /** Name of the tool */
  name: string;
  /** Tool name (may duplicate 'name' for compatibility) */
  tool_name: string;
  /** Description of the tool (optional) */
  tool_description?: string;
  /** Input schema for the tool */
  tool_input_schema: string;
  /** Type of the tool */
  tool_type: string;
  /** Tool configuration details */
  tool_config: McpToolConfig;
  /** Creation timestamp */
  created_at?: string;
  /** Last update timestamp */
  updated_at?: string;
  /** Rate limit rule for the tool (optional) */
  generic_rate_limit_rule?: McpRateLimitRule;
}

export interface McpToolConfig {
  /** HTTP tool configuration (optional) */
  http?: HttpToolConfig;
  /** FaaS tool configuration (optional) */
  faas?: FaasToolConfig;
  /** RPC tool configuration (optional) */
  rpc?: RPCToolConfig;
}

export interface MetaParam {
  /** Name of the parameter */
  name: string;
  /** Description of the parameter */
  description: string;
  /** Whether the parameter is required */
  required: boolean;
}

export interface ReleaseMcpServerRequest {
  /** MCP server ID to release */
  server_id: string;
}

export interface ReleaseMcpServerResponse {
  code?: number;
  error?: string;
  /** MCP server revision data */
  data?: McpServerRevision;
}

export interface RPCToolConfig {
  /** PSM name */
  psm: string;
  /** RPC method name */
  method: string;
  /** Cluster name (optional) */
  cluster?: string;
  /** IDC (Internet Data Center) name (optional) */
  idc?: string;
  /** Schema for the request body (optional) */
  request_schema?: string;
  /** Schema for the response body (optional) */
  response_schema?: string;
  /** IDL configuration (optional) */
  idl?: IDL;
  /** BAM endpoint ID (optional) */
  bam_endpoint_id?: number;
  /** BAM IDL version (optional) */
  bam_idl_version?: string;
  /** Connection timeout duration (optional) */
  connection_timeout?: Int64;
  /** Request timeout duration (optional) */
  request_timeout?: Int64;
  /** Whether to enable independent PSM authentication */
  enable_independent_psm_auth?: boolean;
}

export interface UpdateMcpServerAclRequest {
  /** MCP server ID to update ACL for */
  server_id: string;
  /** List of PSMs to add to the ACL */
  add_psms?: Array<string>;
  /** List of PSMs to remove from the ACL */
  remove_psms?: Array<string>;
  /** Whether to disable authentication for the server */
  disable_auth?: boolean;
}

export interface UpdateMcpServerRequest {
  /** MCP server ID to update */
  server_id: string;
  /** New name for the MCP server */
  name?: string;
  /** New description for the MCP server */
  description?: string;
  /** Updated global configuration key-value pairs */
  global_config?: Array<KeyValue>;
  /** New owner of the MCP server */
  owner?: string;
  /** Updated list of admin users (comma-separated) */
  admins?: string;
  /** Updated list of authorizer users (comma-separated) */
  authorizers?: string;
  /** Updated service level */
  service_level?: string;
  /** Updated service purpose */
  service_purpose?: string;
  /** Whether authentication is enabled */
  auth_enabled?: boolean;
  /** Updated list of allowed PSMs */
  allowed_psms?: Array<string>;
  /** Custom handler configuration */
  custom_handler?: CustomHandler;
  /** Updated list of meta parameters */
  meta_params?: Array<MetaParam>;
}

export interface UpdateMcpServerResponse {
  /** Response code */
  code?: number;
  /** Error message, if any */
  error?: string;
  /** Updated MCP server data */
  data?: McpServer;
}
/* eslint-enable */
