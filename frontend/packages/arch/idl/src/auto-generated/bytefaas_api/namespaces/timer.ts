/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CreateTimerTriggerResponse {
  /** API response code */
  code?: number;
  /** Created timer trigger object */
  data?: common.TimerTrigger;
  /** Error message if operation failed */
  error?: string;
}

export interface DeleteTimerTriggerRequest {
  /** Name of the cluster where the service is deployed */
  cluster: string;
  /** Region where the service is located */
  region: string;
  /** Unique identifier of the service */
  service_id: string;
  /** Unique identifier of the timer trigger to delete */
  timer_id: string;
  /** JWT token for authentication (optional) */
  'X-Jwt-Token'?: string;
}

export interface DeleteTimerTriggerResponse {
  /** API response code */
  code?: number;
  /** Empty object response data */
  data?: common.EmptyObject;
  /** Error message if operation failed */
  error?: string;
}

export interface GetTimerTriggerRequest {
  /** Name of the cluster where the service is deployed */
  cluster: string;
  /** Region where the service is located */
  region: string;
  /** Unique identifier of the service */
  service_id: string;
  /** Unique identifier of the timer trigger to retrieve */
  timer_id: string;
  /** JWT token for authentication (optional) */
  'X-Jwt-Token'?: string;
}

export interface GetTimerTriggerResponse {
  /** API response code */
  code?: number;
  /** Retrieved timer trigger object */
  data?: common.TimerTrigger;
  /** Error message if operation failed */
  error?: string;
}

export interface UpdateTimerTriggerRequest {
  /** Cell name (optional, for multi-cell clusters) */
  cell?: string;
  /** Name of the cluster where the service is deployed */
  cluster: string;
  /** Maximum number of concurrent executions allowed */
  concurrency_limit?: number;
  /** Creation timestamp of the timer trigger (ISO8601 format) */
  created_at?: string;
  /** Cron expression defining the schedule */
  cron?: string;
  /** Description of the timer trigger */
  description?: string;
  /** Whether the timer trigger is enabled */
  enabled?: boolean;
  /** Name of the timer trigger */
  name?: string;
  /** Payload to be sent when the timer triggers */
  payload?: string;
  /** Region where the service is located */
  region: string;
  /** Number of retry attempts on failure */
  retries?: number;
  /** Scheduled execution time (ISO8601 format) */
  scheduled_at?: string;
  /** Unique identifier of the service */
  service_id: string;
  /** Unique identifier of the timer trigger to update */
  timer_id: string;
  /** JWT token for authentication (optional) */
  'X-Jwt-Token'?: string;
}

export interface UpdateTimerTriggerResponse {
  /** API response code */
  code?: number;
  /** Updated timer trigger object */
  data?: common.TimerTrigger;
  /** Error message if operation failed */
  error?: string;
}
/* eslint-enable */
