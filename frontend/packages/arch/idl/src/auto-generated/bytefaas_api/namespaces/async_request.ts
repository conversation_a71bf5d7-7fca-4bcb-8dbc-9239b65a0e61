/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface AsyncRequestRecordResponse {
  /** Time when the async request was created */
  created_at?: string;
  /** Type of event that triggered the async request */
  event_type?: string;
  /** Time when execution of the request ended */
  execute_end_time?: string;
  /** Time when execution of the request started */
  execute_start_time?: string;
  /** Duration of execution as a string */
  execution_duration_str?: string;
  /** Time when the request finished */
  finished_time?: string;
  /** ID of the function invoked by the request */
  function_id?: string;
  /** Link to Kibana logs for the request */
  kibana_link?: string;
  /** Unique identifier for the request */
  request_id?: string;
  /** Metadata about the response */
  response_meta?: AsyncRequestRecordResponseResponseMetaMessage2;
  /** ID of the function revision used */
  revision_id?: string;
  /** Status of the async task */
  task_status?: string;
  /** Time when the record was last updated */
  updated_at?: string;
  /** Time when the user invoked the request */
  user_invoke_time?: string;
  /** User or system that invoked the request */
  invoker?: string;
  /** Type of async task */
  task_type?: string;
  /** Actual command run for the request */
  real_run_cmd?: string;
  /** Maximum CPU usage during execution */
  max_cpu_usage?: number;
  /** Maximum memory usage during execution */
  max_mem_usage?: number;
}

export interface AsyncRequestRecordResponseResponseMetaMessage2 {
  /** Error code returned by the async request */
  error_code?: string;
  /** Error message returned by the async request */
  error_message?: string;
  /** Body of the response from the async request */
  response_body?: string;
  /** Status of the response */
  response_status?: string;
}

export interface DataMessage71 {
  /** Time when the async request was created */
  created_at?: string;
  /** Type of event that triggered the async request */
  event_type?: string;
  /** Time when execution of the request ended */
  execute_end_time?: string;
  /** Time when execution of the request started */
  execute_start_time?: string;
  /** Duration of execution as a string */
  execution_duration_str?: string;
  /** Time when the request finished */
  finished_time?: string;
  /** ID of the function invoked by the request */
  function_id?: string;
  /** Link to Kibana logs for the request */
  kibana_link?: string;
  /** Unique identifier for the request */
  request_id?: string;
  /** Metadata about the response */
  response_meta?: AsyncRequestRecordResponseResponseMetaMessage2;
  /** ID of the function revision used */
  revision_id?: string;
  /** Status of the async task */
  task_status?: string;
  /** Time when the record was last updated */
  updated_at?: string;
  /** Time when the user invoked the request */
  user_invoke_time?: string;
  /** User or system that invoked the request */
  invoker?: string;
  /** Type of async task */
  task_type?: string;
  /** Actual command run for the request */
  real_run_cmd?: string;
  /** Maximum CPU usage during execution */
  max_cpu_usage?: number;
  /** Maximum memory usage during execution */
  max_mem_usage?: number;
  /** Name of the pod where the request was executed */
  pod_name?: string;
}

export interface GetAsyncRequestRequest {
  /** cluster of service */
  cluster: string;
  /** region of service */
  region: string;
  /** ID of service */
  service_id: string;
  /** the request id you want */
  'x-bytefaas-request-id': string;
}

export interface GetAsyncRequestResponse {
  code?: number;
  data?: AsyncRequestRecordResponse;
  error?: string;
}

export interface KillAsyncRequestsRequest {
  /** cluster of service */
  cluster: string;
  /** region of service */
  region: string;
  /** ID of service */
  service_id: string;
  /** the request id you want to kill */
  'x-bytefaas-request-id': string;
}

export interface KillAsyncRequestsResponse {}

export interface ListAsyncRequestsRequest {
  /** begin_time */
  begin_time?: string;
  /** cluster of service */
  cluster: string;
  /** end_time */
  end_time?: string;
  /** limit */
  limit?: string;
  /** offset */
  offset?: string;
  /** region of service */
  region: string;
  /** request_id */
  request_id?: string;
  /** ID of service */
  service_id: string;
  /** task_status */
  task_status?: string;
}

export interface ListAsyncRequestsResponse {
  code?: number;
  data?: Array<DataMessage71>;
  error?: string;
}
/* eslint-enable */
