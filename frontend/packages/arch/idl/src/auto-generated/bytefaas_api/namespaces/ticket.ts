/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

/** Batch ticket containing child tickets and their status */
export interface BatchTicket {
  /** Status of child tickets, keyed by ticket ID */
  status?: Record<string, common.EmptyObject>;
  /** List of child tickets in the batch */
  tickets?: Array<common.Ticket>;
  /** Number of child tickets in the batch */
  count?: number;
}

export interface BatchUpdateTicketStepActionRequest {
  /** retry/run/cancel */
  action?: string;
  /** ticket id */
  ticket_id: string;
  /** service id */
  service_id: string;
  /** steps */
  step_ids?: Array<string>;
}

export interface DynamicOvercommitSettings {
  /** Whether dynamic overcommit is disabled */
  disable_dynamic_overcommit?: boolean;
  /** Reserved overcommit ratio */
  reserved_overcommit_ratio?: number;
  /** Elastic overcommit ratio */
  elastic_overcommit_ratio?: number;
}

/** Function metadata parameters for ticket operations */
export interface FunctionMetaParams {
  /** Adaptive concurrency mode */
  adaptive_concurrency_mode?: string;
  /** List of admin users */
  admins?: string;
  /** Enable async mode */
  async_mode?: boolean;
  /** Enable authentication */
  auth_enable?: boolean;
  /** List of authorizers */
  authorizers?: string;
  /** Base image name */
  base_image?: string;
  /** Function category */
  category?: string;
  /** Maximum code file size (MB) */
  code_file_size_mb?: number;
  /** Disable cold start, true means disable */
  cold_start_disabled?: boolean;
  /** Cold start duration (seconds) */
  cold_start_sec?: number;
  /** Enable CORS */
  cors_enable?: boolean;
  /** List of dependencies */
  dependency?: Array<common.Dependency>;
  /** Deployment method. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** Function description */
  description?: string;
  /** Disable build install, true means disable */
  disable_build_install?: boolean;
  /** Disable PPE alarm */
  disable_ppe_alarm?: boolean;
  /** Enable scale optimization */
  enable_scale_optimise?: boolean;
  /** Enable scale strategy */
  enable_scale_strategy?: boolean;
  /** Environment name */
  env_name?: string;
  /** Environment variables (key-value pairs) */
  envs?: Record<string, Record<string, string>>;
  /** Enable exclusive mode */
  exclusive_mode?: boolean;
  /** Formatted environment variables */
  format_envs?: Record<string, Array<common.FormatEnvs>>;
  /** Function handler name */
  handler?: string;
  /** Initializer function name */
  initializer?: string;
  /** Initializer timeout (seconds) */
  initializer_sec?: number;
  /** Programming language */
  language?: string;
  /** Latency (seconds) */
  latency_sec?: number;
  /** Enable lazy loading */
  lazyload?: boolean;
  /** Maximum concurrency */
  max_concurrency?: number;
  /** Memory size (MB) */
  memory_mb?: number;
  /** Function name */
  name?: string;
  /** Require approval */
  need_approve?: boolean;
  /** Function origin */
  origin?: string;
  /** Function owner */
  owner?: string;
  /** Plugin name */
  plugin_name?: string;
  /** Protocol type */
  protocol?: string;
  /** PSM name */
  psm?: string;
  /** Parent PSM ID */
  psm_parent_id?: number;
  /** Resource limits */
  resource_limit?: common.ResourceLimit;
  /** Run command */
  run_cmd?: string;
  /** Runtime type */
  runtime?: string;
  /** Enable scaling */
  scale_enabled?: boolean;
  /** Scale threshold */
  scale_threshold?: number;
  /** Scale type */
  scale_type?: number;
  /** Service level */
  service_level?: string;
  /** Service purpose */
  service_purpose?: string;
  /** Source of the function */
  source?: string;
  /** Source type */
  source_type?: string;
  /** Template name */
  template_name?: string;
  /** Throttle log bytes per second */
  throttle_log_bytes_per_sec?: number;
  /** Enable log throttling */
  throttle_log_enabled?: boolean;
  /** Throttle stderr log bytes per second */
  throttle_stderr_log_bytes_per_sec?: number;
  /** Throttle stdout log bytes per second */
  throttle_stdout_log_bytes_per_sec?: number;
  /** Enable tracing */
  trace_enable?: boolean;
  /** Zone-specific throttle log bytes per second */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** Enable runtime file log */
  enable_runtime_file_log?: boolean;
  /** Enable runtime console log */
  enable_runtime_console_log?: boolean;
  /** Whether runtime stream log is enabled */
  enable_runtime_stream_log?: boolean;
  /** Whether runtime ES log is enabled */
  enable_runtime_es_log?: boolean;
  /** Whether runtime JSON log is enabled */
  enable_runtime_json_log?: boolean;
  /** Whether system stream log is enabled */
  enable_system_stream_log?: boolean;
  /** Whether system ES log is enabled */
  enable_system_es_log?: boolean;
  /** Runtime stream log bytes per second */
  runtime_stream_log_bytes_per_sec?: number;
  /** System stream log bytes per second */
  system_stream_log_bytes_per_sec?: number;
}

export interface GetBatchTicketDetailByIDRequest {
  /** Parent ticket ID (batch ticket ID) */
  id: string;
}

export interface GetBatchTicketDetailByIDResponse {
  /** API response code */
  code?: number;
  /** Batch ticket data */
  data?: BatchTicket;
  /** Error message if operation failed */
  error?: string;
}

export interface GetTicketDetailByTicketIDRequest {
  /** Ticket ID to query */
  ticket_id: string;
}

export interface GetTicketDetailByTicketIDResponse {
  /** API response code */
  code?: number;
  /** Ticket details data */
  data?: common.Ticket;
  /** Error message if operation failed */
  error?: string;
}

export interface RegionalMetaParams {
  /** Traffic aliases */
  aliases?: Record<string, common.Alias>;
  /** Whether async mode is enabled */
  async_mode?: boolean;
  /** Whether authentication is enabled */
  auth_enable?: boolean;
  /** Whether ByteFaaS error response is disabled */
  bytefaas_error_response_disabled?: boolean;
  /** Whether ByteFaaS response header is disabled */
  bytefaas_response_header_disabled?: boolean;
  /** Cell name */
  cell?: string;
  /** Whether cold start is disabled, true means disable */
  cold_start_disabled?: boolean;
  /** Whether CORS is enabled */
  cors_enable?: boolean;
  /** Whether dynamic load balancing data report is enabled */
  dynamic_load_balancing_data_report_enabled?: boolean;
  /** List of VDCs with dynamic load balancing enabled */
  dynamic_load_balancing_enabled_vdcs?: Array<string>;
  /** Whether dynamic load balancing weight is enabled */
  dynamic_load_balancing_weight_enabled?: boolean;
  /** Whether colocate scheduling is enabled */
  enable_colocate_scheduling?: boolean;
  /** Environment name */
  env_name?: string;
  /** Whether exclusive mode is enabled */
  exclusive_mode?: boolean;
  /** List of formatted environment variables */
  format_envs?: Array<common.FormatEnvs>;
  /** Function ID */
  function_id?: string;
  /** Whether gateway route is enabled */
  gateway_route_enable?: boolean;
  /** Whether GDPR is enabled */
  gdpr_enable?: boolean;
  /** List of global KV namespace IDs */
  global_kv_namespace_ids?: Array<string>;
  /** Whether HTTP trigger is disabled，true means disable */
  http_trigger_disable?: boolean;
  /** Whether IPv6 only is enabled */
  is_ipv6_only?: boolean;
  /** Disabled zones in a region (zone name to disabled flag) */
  is_this_zone_disabled?: Record<string, boolean>;
  /** Latency in seconds */
  latency_sec?: number;
  /** List of local cache namespace IDs */
  local_cache_namespace_ids?: Array<string>;
  /** Network class ID */
  net_class_id?: number;
  /** Network name */
  network?: string;
  /** Owner of the function */
  owner?: string;
  /** Protocol type */
  protocol?: string;
  /** PSM name */
  psm?: string;
  /** Region name */
  region?: string;
  /** Whether reserved DP is enabled */
  reserved_dp_enabled?: boolean;
  /** Revision ID */
  revision_id?: string;
  /** Revision number */
  revision_number?: number;
  /** Routing strategy, enums：prefer_reserved, prefer_elastic. */
  routing_strategy?: string;
  /** Runtime type */
  runtime?: string;
  /** Service ID */
  service_id?: string;
  /** Whether tracing is enabled */
  trace_enable?: boolean;
  /** Zone-specific throttle log bytes per second */
  zone_throttle_log_bytes_per_sec?: Record<string, number>;
  /** Whether ZTI is enabled */
  zti_enable?: boolean;
  /** Replica limits per zone (restricted access, only open to administrators) */
  replica_limit?: Record<string, common.EmptyObject>;
  /** Function scaling configuration settings */
  scale_settings?: common.FunctionScaleSettings;
  /** Whether online mode is enabled */
  online_mode?: boolean;
  /** Dynamic overcommit settings per zone */
  dynamic_overcommit_settings?: Record<string, DynamicOvercommitSettings>;
  /** Formatted elastic preferred cluster configurations */
  formatted_elastic_prefer_cluster?: Array<common.FormattedPreferCluster>;
  /** Formatted reserved preferred cluster configurations */
  formatted_reserved_prefer_cluster?: Array<common.FormattedPreferCluster>;
  /** Whether to enable reserved frozen instances */
  enable_reserve_frozen_instance?: boolean;
  /** Number of reserved frozen replicas */
  reserved_frozen_replicas?: number;
  /** Reserved frozen replicas per zone */
  zone_reserved_frozen_replicas?: Record<string, number>;
  /** Whether to allow cold start instances */
  allow_cold_start_instance?: boolean;
  /** Whether to disable cgroup v2 */
  disable_cgroup_v2?: boolean;
  /** Whether overload protection is enabled */
  overload_protect_enabled?: boolean;
  /** CPU allocation for frozen instances (in millicores) */
  frozen_cpu_milli?: number;
  /** Whether to enable federated on-demand resources per zone */
  enable_fed_on_demand_resource?: Record<string, boolean>;
  /** Priority class for frozen instances */
  frozen_priority_class?: string;
  /** Canary replica limits per zone */
  zone_canary_replica_limit?: Record<string, number>;
  /** Whether frozen scaling is enabled */
  frozen_scale_enabled?: boolean;
  /** enable privileged in pod */
  privileged?: boolean;
}

export interface TicketRuntimeUpdateRequest {
  /** User who approved the ticket */
  approved_by?: string;
  /** Type of user who approved the ticket. Enums: "person_account","service_account" */
  approved_by_usertype?: string;
  /** Function ID to update */
  function_id?: string;
  /** Function metadata parameters */
  function_meta?: FunctionMetaParams;
  /** Regional metadata parameters by region */
  regional_metas?: Record<string, RegionalMetaParams>;
  /** Service ID associated with the function */
  service_id?: string;
}

export interface TicketRuntimeUpdateResponse {
  /** API response code */
  code?: number;
  /** Updated ticket data */
  data?: common.Ticket;
  /** Error message if operation failed */
  error?: string;
}

export interface UpdateTicketActionRequest {
  /** Action to perform on the ticket */
  action?: string;
  /** Ticket ID to update */
  ticket_id: string;
  /** Service ID associated with the ticket */
  service_id?: string;
}

export interface UpdateTicketActionResponse {
  /** API response code */
  code?: number;
  /** Updated ticket data */
  data?: common.Ticket;
  /** Error message if operation failed */
  error?: string;
}

export interface UpdateTicketStepActionRequest {
  /** retry/run/cancel */
  action?: string;
  /** ticket id */
  ticket_id: string;
  /** step id */
  step_id: string;
}

export interface UpdateTicketStepActionResponse {
  /** response code */
  code?: number;
  /** reponse data */
  data?: common.EmptyObject;
  /** error msg */
  error?: string;
}
/* eslint-enable */
