/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CreatePipelineTemplateRequest {
  /** template name, for display purpose */
  name: string;
  /** pipeline type, e.g. MultiClusterNormalRelease */
  type: string;
  /** template description */
  description: string;
  /** whether the template is a system template, only <PERSON>aas admin can set this */
  system_template: boolean;
  /** whether the template is disabled, can be used to create draft template */
  disabled: boolean;
  stages: Array<CreatePipelineTemplateStageData>;
  /** template match rules */
  match_rules: PipelineTemplateMatchRules;
  /** whether the template is a default template */
  default_template?: boolean;
  /** template admins */
  admins?: Array<string>;
}

export interface CreatePipelineTemplateResponse {
  /** response code */
  code: number;
  /** error message, if any */
  error?: string;
  /** updated template */
  data: PipelineTemplate;
}

export interface CreatePipelineTemplateStageData {
  /** stage type */
  type: string;
  /** stage group */
  group: string;
  /** stage parameters, json encoded in base64. e.g {"rolling_interval": 10} */
  parameters: string;
}

export interface DeletePipelineTemplateRequest {
  /** template id to delete */
  template_id: string;
}

export interface DeletePipelineTemplateResponse {}

export interface GetPipelineTemplateRequest {
  /** template id */
  template_id: string;
}

export interface GetPipelineTemplateResponse {
  /** response code */
  code: number;
  /** error message, if any */
  error?: string;
  /** pipeline template */
  data?: PipelineTemplate;
}

export interface GetServiceDefaultPipelineTemplateRequest {
  /** service id */
  service_id: string;
  /** pipeline operation type, available: [release, release_lego] */
  operation: string;
}

export interface GetServiceDefaultPipelineTemplateResponse {
  /** response code */
  code: number;
  /** error message, if any */
  error?: string;
  /** selected pipeline template, can be null if no default template is found */
  data?: PipelineTemplate;
}

export interface GetServicePipelineTemplateRequest {
  /** service id */
  service_id: string;
  /** pipeline operation type, available: [release, release_lego] */
  operation?: string;
  /** whether to include system template */
  include_system_template?: boolean;
  /** whether to include disabled template */
  include_disabled?: boolean;
}

export interface GetServicePipelineTemplateResponse {
  /** response code */
  code: number;
  /** error message, if any */
  error?: string;
  /** list of pipeline templates */
  data?: Array<PipelineTemplate>;
}

export interface ListPipelineStageRequest {
  /** filter by system stage */
  system_stage?: boolean;
}

export interface ListPipelineStageResponse {
  /** response code */
  code: number;
  /** error message, if any */
  error?: string;
  /** updated template */
  data: Array<PipelineStageDefinition>;
}

export interface ListPipelineTemplateRequest {
  /** filter by base template */
  base_template?: boolean;
  /** filter by system template */
  system_template?: boolean;
  /** filter by service id */
  service_id?: string;
  /** filter by bytetree id */
  bytetree_id?: string;
  /** query result limit */
  limit?: number;
  /** query result offset */
  offset?: number;
}

export interface ListPipelineTemplateResponse {
  /** response code */
  code: number;
  /** error message, if any */
  error?: string;
  /** list of pipeline templates */
  data?: Array<PipelineTemplate>;
}

export interface PipelineStage {
  /** stage id, unique for all stages even with the same stage type */
  id: string;
  /** stage type */
  type: string;
  /** stage group, for frontend display purpose only */
  group: string;
  /** whether the stage's parameters are editable */
  editable: boolean;
  /** whether the stage can be removed from a template */
  deletable: boolean;
  /** stage parameters, json encoded in base64. e.g {"rolling_interval": 10} */
  parameters: string;
}

export interface PipelineStageDefinition {
  /** stage Chinese name */
  name_cn: string;
  /** stage English name */
  name_en: string;
  /** stage type */
  type: string;
  /** whether the stage is a system stage, system stage can't be added to a template by user */
  system_stage: boolean;
  /** defines stage parameter's properties such as type and display name, object key is the field name */
  parameter_properties: Record<string, PipelineStageParameterProperty>;
}

export interface PipelineStageParameterProperty {
  /** field name in Chinese */
  title_cn: string;
  /** field name in English */
  title_en: string;
  /** field type, [string, number, enum] */
  type: string;
  /** whether the field is required */
  required: boolean;
  /** default value */
  default: string;
  /** field name in Chinese */
  description_cn: string;
  /** field name in English */
  description_en: string;
}

export interface PipelineTemplate {
  /** template id */
  id: string;
  /** template name */
  name: string;
  /** pipeline type */
  type: string;
  /** template description */
  description: string;
  /** whether the template is disabled */
  disabled: boolean;
  /** pipeline operation type, available: [release, release_lego] */
  operation_type: string;
  /** pipeline stages */
  stages: Array<PipelineStage>;
  /** whether the template is a system template, system template will be available to all services */
  system_template: boolean;
  /** whether the template is a base template, base templates are used to create a new template */
  base_template: boolean;
  /** template created by user */
  created_by: string;
  /** template updated by user */
  updated_by: string;
  /** template created at */
  created_at: string;
  /** template updated at */
  updated_at: string;
  /** template match rules */
  match_rules: PipelineTemplateMatchRules;
  /** whether the template is a default template */
  default_template?: boolean;
  /** template admins */
  admins?: Array<string>;
}

export interface PipelineTemplateMatchRules {
  /** match by service ids */
  service_ids?: Array<string>;
  /** match by bytetree ids */
  bytetree_ids?: Array<Int64>;
}

export interface UpdatePipelineTemplateRequest {
  /** template name, for display purpose */
  name: string;
  /** template description */
  description: string;
  /** whether the template is disabled, can be used to create draft template */
  disabled: string;
  /** template stages */
  stages: Array<UpdatePipelineTemplateStageData>;
  /** template match rules */
  match_rules: PipelineTemplateMatchRules;
  /** whether the template is a default template */
  default_template?: boolean;
  /** template admins */
  admins?: Array<string>;
  /** template created by, for paas service account only */
  created_by?: string;
  /** template id */
  template_id: string;
}

export interface UpdatePipelineTemplateResponse {
  /** response code */
  code: number;
  /** error message, if any */
  error?: string;
  /** updated template */
  data: PipelineTemplate;
}

export interface UpdatePipelineTemplateStageData {
  /** stage group */
  group: string;
  /** stage parameters, json encoded in base64. e.g {"rolling_interval": 10} */
  parameters: string;
}
/* eslint-enable */
