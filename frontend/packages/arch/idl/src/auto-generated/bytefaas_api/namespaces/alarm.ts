/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** alarm model */
export interface Alarm {
  /** Notification methods for the alarm */
  alarm_methods?: string;
  /** Interval (in seconds or minutes) between alarm checks */
  check_interval?: number;
  /** Name or ID of the cluster associated with the alarm */
  cluster?: string;
  /** Time when the alarm ends (ISO8601 or timestamp string) */
  end_time?: string;
  /** Suggested actions to handle the alarm */
  handle_suggestion?: string;
  /** Unique identifier for the alarm */
  id?: number;
  /** User who last updated the alarm */
  last_updated_by?: string;
  /** Severity level of the alarm */
  level?: string;
  /** Name of the alarm */
  name?: string;
  /** PSM identifier */
  psm?: string;
  /** Rule expression or condition for the alarm */
  rule?: string;
  /** Alias for the alarm rule */
  rule_alias?: string;
  /** Format or type of the rule */
  rule_format?: string;
  /** Time when the alarm starts (ISO8601 or timestamp string) */
  start_time?: string;
  /** Current status of the alarm */
  status?: string;
  /** Threshold value for triggering the alarm */
  threshold?: number;
  /** Unit for the threshold value */
  threshold_unit?: string;
  /** Type/category of the alarm */
  type?: string;
  /** ID of the unit (subsystem/component) related to the alarm */
  unit_id?: number;
  /** Last update time for the alarm (ISO8601 or timestamp string) */
  updated_at?: string;
  /** Zone or availability zone for the alarm */
  zone?: string;
}

export interface GetClusterAlarmRequest {
  /** Name or ID of the cluster */
  cluster: string;
  /** Region of the cluster */
  region: string;
  /** ID of the service */
  service_id: string;
}

export interface GetClusterAlarmResponse {
  /** Response code for the get cluster alarm operation */
  code?: number;
  /** List of alarms for the cluster */
  data?: Array<Alarm>;
  /** Error message if the operation failed */
  error?: string;
}

export interface UpdateClusterAlarmRequest {
  /** ID of the alarm to update */
  alarm_id?: string;
  /** Notification methods for the alarm */
  alarm_methods?: string;
  /** Name or ID of the cluster */
  cluster: string;
  /** ID of the function related to the alarm */
  function_id?: string;
  /** Severity level of the alarm */
  level?: string;
  /** Region of the cluster */
  region: string;
  /** Alias for the alarm rule */
  rule_alias?: string;
  /** Format or type of the rule */
  rule_format?: string;
  /** ID of the service */
  service_id: string;
  /** Status of the alarm */
  status?: string;
  /** Threshold value for triggering the alarm */
  threshold?: number;
}

export interface UpdateClusterAlarmResponse {
  /** Response code for the update cluster alarm operation */
  code?: number;
  /** Updated alarm data */
  data?: Alarm;
  /** Error message if the operation failed */
  error?: string;
}
/* eslint-enable */
