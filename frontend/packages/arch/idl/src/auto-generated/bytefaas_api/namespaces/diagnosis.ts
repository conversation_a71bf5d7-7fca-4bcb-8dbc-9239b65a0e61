/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CreateDiagnosisRequest {
  /** cluster name */
  cluster: string;
  diagnosis_id?: string;
  end_at?: number;
  item_id?: string;
  item_type?: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
  set_time_range?: boolean;
  start_at?: number;
}

export interface CreateDiagnosisResponse {
  code?: number;
  data?: Diagnose;
  error?: string;
}

export interface DeleteDiagnosisByIDRequest {
  /** cluster name */
  cluster: string;
  /** diagnosis id */
  diagnosis_id: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface DeleteDiagnosisByIDResponse {
  code?: number;
  data?: Diagnose;
  error?: string;
}

export interface Diagnose {
  /** Time when the diagnosis was created */
  created_at?: string;
  /** Unique identifier for the diagnosis */
  diagnosis_id?: string;
  /** List of diagnosis items */
  diagnosis_items?: Array<DiagnoseDiagnosisItemsMessage>;
  /** End time of the diagnosis (timestamp) */
  end_at?: number;
  /** ID of the function being diagnosed */
  function_id?: string;
  /** ID of the diagnosis item */
  item_id?: string;
  /** Type of the diagnosis item */
  item_type?: string;
  /** Language used for the diagnosis */
  language?: string;
  /** Whether metadata is synced */
  meta_synced?: boolean;
  /** Time when metadata was synced */
  meta_synced_at?: string;
  /** Whether a time range is set for the diagnosis */
  set_time_range?: boolean;
  /** Start time of the diagnosis (timestamp) */
  start_at?: number;
  /** Time when the diagnosis was last updated */
  updated_at?: string;
}

export interface DiagnoseDiagnosisItemsMessage {
  /** Content of the diagnosis item */
  content?: string;
  /** Hint or suggestion for the diagnosis item */
  hint?: string;
  /** Result of the diagnosis item */
  result?: string;
}

export interface GetDiagnosisByIDRequest {
  /** cluster name */
  cluster: string;
  /** diagnosis id */
  diagnosis_id: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface GetDiagnosisByIDResponse {
  code?: number;
  data?: Diagnose;
  error?: string;
}

export interface GetDiagnosisRequest {
  /** cluster name */
  cluster: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface GetDiagnosisResponse {
  code?: number;
  data?: Array<Diagnose>;
  error?: string;
}
/* eslint-enable */
