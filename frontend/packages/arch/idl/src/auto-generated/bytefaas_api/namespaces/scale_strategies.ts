/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CreateScaleStrategyRequest {
  /** Cluster of the service */
  cluster: string;
  /** When the strategy will be effective */
  effective_time?: string;
  /** Whether the strategy is enabled */
  enabled?: boolean;
  /** When the strategy will expire */
  expired_time?: string;
  /** Function ID (not needed for post/patch method) */
  function_id?: string;
  /** Inner strategy details */
  inner_strategy?: common.InnerStrategy;
  /** Function ID or MQ event ID */
  item_id?: string;
  /** Item type: function or mqevent */
  item_type?: string;
  /** Region (not needed for post/patch method) */
  region: string;
  /** Service ID */
  service_id: string;
  /** Strategy ID (not needed for post/patch method) */
  strategy_id?: string;
  /** Strategy name */
  strategy_name?: string;
  /** Strategy type (only cron for now) */
  strategy_type?: string;
  /** Instance type: ReservedInstance or FrozenReservedInstance (default is ReservedInstance) */
  instance_type?: string;
}

export interface CreateScaleStrategyResponse {
  code?: number;
  data?: common.ScaleStrategy;
  error?: string;
}

export interface DeleteScaleStrategyRequest {
  /** Cluster of the service */
  cluster: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Strategy ID to delete */
  strategy_id: string;
}

export interface DeleteScaleStrategyResponse {
  code?: number;
  data?: common.ScaleStrategy;
  error?: string;
}

export interface GetScaleStrategiesRequest {
  /** Cluster of the service */
  cluster: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
}

export interface GetScaleStrategiesResponse {
  code?: number;
  data?: Array<common.ScaleStrategy>;
  error?: string;
}

export interface GetScaleStrategyRequest {
  /** Cluster of the service */
  cluster: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Strategy ID to query */
  strategy_id: string;
}

export interface GetScaleStrategyResponse {
  code?: number;
  data?: common.ScaleStrategy;
  error?: string;
}

export interface PatchScaleStrategyRequest {
  /** Required for inner BPM operation */
  bpm_update_type?: string;
  /** Cluster of the service */
  cluster: string;
  /** When the strategy will be effective */
  effective_time?: string;
  /** Whether the strategy is enabled */
  enabled?: boolean;
  /** When the strategy will expire */
  expired_time?: string;
  /** Function ID (not needed for post/patch method) */
  function_id?: string;
  /** Inner strategy details */
  inner_strategy?: common.InnerStrategy;
  /** Function ID or MQ event ID */
  item_id?: string;
  /** Item type: function or mqevent */
  item_type?: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Strategy ID to patch */
  strategy_id: string;
  /** Strategy name */
  strategy_name?: string;
  /** Strategy type (only cron for now) */
  strategy_type?: string;
  /** Instance type: ReservedInstance or FrozenReservedInstance (default is ReservedInstance) */
  instance_type?: string;
}

export interface PatchScaleStrategyResponse {
  code?: number;
  data?: common.ScaleStrategy;
  error?: string;
}
/* eslint-enable */
