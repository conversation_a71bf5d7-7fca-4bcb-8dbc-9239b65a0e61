/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface ActiveFunctionFrozenInstanceRequest {
  /** Cluster name where the pod is running */
  cluster: string;
  /** Pod name to activate */
  podname: string;
  /** Region name where the pod is running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** Zone where the pod is running */
  zone: string;
}

export interface ActiveFunctionFrozenInstanceResponse {
  /** Response code */
  code?: number;
  /** Empty object for activation response data */
  data?: common.EmptyObject;
  /** Error message if the request failed */
  error?: string;
}

export interface ContainerInfo {
  /** Container ID */
  containerID?: string;
  /** Image used by the container */
  image?: string;
  /** Environment variables for the container */
  env?: Record<string, string>;
}

export interface DataMessage130 {
  /** Link to the webshell for the MQ trigger instance */
  webshell_link?: string;
}

export interface DataMessage199 {
  /** Link to the webshell for the instance */
  webshell_link?: string;
}

export interface EsLog {
  /** Log content */
  content?: string;
  /** Log timestamp */
  datetime?: string;
  /** Function ID associated with the log */
  function_id?: string;
  /** Log class/category */
  log_class?: string;
  /** Log type */
  log_type?: string;
  /** Pod IP address */
  pod_ip?: string;
  /** Pod name */
  pod_name?: string;
  /** Revision ID associated with the log */
  revision_id?: string;
}

export interface GetClusterAllMqTriggerInstancesRequest {
  /** Cluster name where the MQ triggers are running */
  cluster: string;
  /** Region name where the MQ triggers are running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetClusterAllMqTriggerInstancesResponse {
  /** Response code */
  code?: number;
  /** Map of trigger ID to list of MQ trigger instances */
  data?: Record<string, Array<MqTriggerInstance>>;
  /** Error message if the request failed */
  error?: string;
}

export interface GetInstancesLogsRequest {
  /** Cluster name where the pod is running */
  cluster: string;
  /** Pod name to fetch logs for */
  podname: string;
  /** Region name where the pod is running */
  region: string;
  /** Revision ID of the function (optional) */
  revision_id?: string;
  /** ID of the service */
  service_id: string;
  /** Zone where the pod is running */
  zone: string;
}

export interface GetInstancesLogsResponse {
  /** Response code */
  code?: number;
  /** Log data as a string */
  data?: string;
  /** Error message if the request failed */
  error?: string;
}

export interface GetInstancesPodInfoRequest {
  /** Pod name to fetch info for */
  podname: string;
  /** Region name where the pod is running */
  region: string;
  /** Zone where the pod is running */
  zone: string;
  /** Cell name (optional) */
  cell?: string;
}

export interface GetInstancesPodInfoResponse {
  /** Response code */
  code?: number;
  /** Pod information returned in the response */
  data?: PodInfo;
  /** Error message if the request failed */
  error?: string;
}

export interface GetInstancesRequest {
  /** Cluster name where the instances are running */
  cluster: string;
  /** Region name where the instances are running */
  region: string;
  /** ID of the service to query instances for */
  service_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetInstancesResponse {
  /** Response code */
  code?: number;
  /** List of function instances returned in the response */
  data?: Array<Instance>;
  /** Error message if the request failed */
  error?: string;
}

export interface GetInstancesStageRequest {
  /** Cluster name where the pod is running */
  cluster: string;
  /** Pod name to fetch stage info for */
  podname: string;
  /** Region name where the pod is running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** Zone of the pod */
  zone: string;
  /** Revision ID of the function (optional) */
  revision_id?: string;
}

export interface GetInstancesStageResponse {
  /** Response code */
  code?: number;
  /** List of instance stages returned in the response */
  data?: Array<InstanceStages>;
  /** Error message if the request failed */
  error?: string;
}

export interface GetInstancesWebshellRequest {
  /** Cluster name where the pod is running */
  cluster: string;
  /** Pod name to open webshell for */
  podname: string;
  /** Region name where the pod is running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** Zone where the pod is running */
  zone: string;
}

export interface GetInstancesWebshellResponse {
  /** Response code */
  code?: number;
  /** Data containing the webshell link */
  data?: DataMessage199;
  /** Error message if the request failed */
  error?: string;
}

export interface GetLogsRequest {
  /** Whether to use advanced log search */
  advanced?: boolean;
  /** Whether to sort logs in ascending order */
  ascend?: boolean;
  /** Cluster name where the logs are collected */
  cluster: string;
  /** Start time for log search (ISO8601 or timestamp) */
  from?: string;
  /** Whether to include system logs */
  include_system?: boolean;
  /** Log type to filter */
  log_type: string;
  /** Pod IP address to filter logs */
  pod_ip?: string;
  /** Pod name to filter logs */
  pod_name?: string;
  /** Region name where the logs are collected */
  region: string;
  /** Revision ID to filter logs */
  revision_id?: string;
  /** Search keyword or pattern */
  search?: string;
  /** ID of the service */
  service_id: string;
  /** Number of log entries to return */
  size?: number;
  /** End time for log search (ISO8601 or timestamp) */
  to?: string;
}

export interface GetLogsResponse {
  /** Response code */
  code?: number;
  /** List of log entries returned in the response */
  data?: Array<EsLog>;
  /** Error message if the request failed */
  error?: string;
}

export interface GetMqTriggerInstancesRequest {
  /** Cluster name where the MQ trigger is running */
  cluster: string;
  /** Region name where the MQ trigger is running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** MQ trigger ID */
  trigger_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetMqTriggerInstancesResponse {
  /** Response code */
  code?: number;
  /** List of MQ trigger instances returned in the response */
  data?: Array<MqTriggerInstance>;
  /** Error message if the request failed */
  error?: string;
}

export interface GetMqTriggerInstancesWebshellRequest {
  /** Cluster name where the MQ trigger is running */
  cluster: string;
  /** Pod name to open webshell for */
  podname: string;
  /** Region name where the MQ trigger is running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** MQ trigger ID */
  trigger_id: string;
  /** Zone where the MQ trigger is running */
  zone: string;
}

export interface GetMqTriggerInstancesWebshellResponse {
  /** Response code */
  code?: number;
  /** Data containing the webshell link */
  data?: DataMessage130;
  /** Error message if the request failed */
  error?: string;
}

export interface Instance {
  /** Function ID associated with the instance */
  function_id?: string;
  /** Revision ID of the function */
  revision_id?: string;
  /** Name of the pod running the instance */
  pod_name?: string;
  /** Unique identifier for the pod */
  pod_uid?: string;
  /** List of container IDs in the pod */
  container_ids?: Array<string>;
  /** Host node where the pod is running */
  host?: string;
  /** IPv6 address of the pod */
  ipv6?: string;
  /** IPv4 address of the pod */
  pod_ip?: string;
  /** Main port exposed by the pod */
  port?: string;
  /** Debug port for the runtime container */
  runtime_debug_port?: string;
  /** List of other runtime ports exposed by the pod */
  runtime_other_ports?: Array<OtherRoutes>;
  /** Deployment environment */
  deploy_env?: string;
  /** Region where the instance is running */
  region?: string;
  /** Zone within the region */
  zone?: string;
  /** Status of the instance */
  status?: string;
  /** Status or error message for the instance */
  message?: string;
  /** CPU usage of the instance */
  cpu?: number;
  /** Memory usage of the instance */
  memory?: number;
  /** Creation timestamp of the instance */
  created_at?: string;
  /** Type of the instance */
  instance_type?: string;
  /** Link to instance metrics dashboard */
  instance_metric_link?: string;
  /** Link to host metrics dashboard */
  host_metric_link?: string;
  /** Link to lidar profile for the instance */
  lidar_profile_link?: string;
  /** Whether service discovery is disabled for this instance */
  sd_disabled?: boolean;
  /** List of dependencies for the revision */
  revision_dependency?: Array<common.Dependency>;
  /** Runtime container port as string */
  runtime_container_port?: string;
  /** Status category for the instance */
  status_category?: string;
}

export interface InstanceStages {
  /** Name of the stage */
  name?: string;
  /** Status of the stage */
  status?: string;
  /** List of logs for this stage */
  logs?: Array<StageLog>;
}

export interface MigrateInstancesRequest {
  /** Cluster name where the pod is running */
  cluster: string;
  /** Pod name to migrate */
  podname: string;
  /** Region name where the pod is running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** Zone where the pod is running */
  zone: string;
  /** Environment name (optional) */
  env?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface MigrateInstancesResponse {
  /** Response code */
  code?: number;
  /** Empty object for migration response data */
  data?: common.EmptyObject;
  /** Error message if the request failed */
  error?: string;
}

export interface MigrateMqTriggerInstanceRequest {
  /** Cluster name where the MQ trigger is running */
  cluster: string;
  /** Pod name to migrate */
  podname: string;
  /** Region name where the MQ trigger is running */
  region: string;
  /** ID of the service */
  service_id: string;
  /** MQ trigger ID */
  trigger_id: string;
  /** Zone where the MQ trigger is running */
  zone: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface MigrateMqTriggerInstanceResponse {
  /** Response code */
  code?: number;
  /** Empty object for migration response data */
  data?: common.EmptyObject;
  /** Error message if the request failed */
  error?: string;
}

export interface MigrationRecord {
  /** PSM name */
  psm?: string;
  /** Service ID associated with the migration */
  service_id?: string;
  /** Cluster ID where the migration occurred */
  cluster_id?: string;
  /** Pod IP address involved in the migration */
  pod_ip?: string;
  /** Pod IPv6 address involved in the migration */
  pod_ipv6?: string;
  /** Pod port involved in the migration */
  pod_port?: string;
  /** Pod status at the time of migration */
  pod_status?: string;
  /** Zone where the migration occurred */
  zone?: string;
  /** Region where the migration occurred */
  region?: string;
  /** Deletion timestamp (epoch milliseconds) */
  delete_time?: Int64;
  /** User who deleted the pod */
  delete_by?: string;
  /** Result of the migration */
  migration_result?: string;
  /** Report message for the migration */
  report_message?: string;
  /** Cluster name where the migration occurred */
  cluster_name?: string;
  /** Pod name involved in the migration */
  pod_name?: string;
  /** Detector that triggered the migration */
  detector?: string;
  /** Type of migration */
  migration_type?: string;
  /** Environment name for the migration */
  env?: string;
}

export interface MigrationRecordsRequest {
  /** Region to filter migration records */
  region?: string;
  /** Cluster to filter migration records */
  cluster?: string;
  /** User who deleted the pod */
  delete_by?: string;
  /** Pod name to filter migration records */
  pod_name?: string;
  /** Service ID to filter migration records */
  service_id: string;
  /** Page size for pagination */
  page_size?: number;
  /** Page number for pagination */
  page_num?: number;
  /** Start time for filtering (epoch milliseconds) */
  start?: Int64;
  /** End time for filtering (epoch milliseconds) */
  end?: Int64;
  /** Detector to filter migration records */
  detector?: string;
  /** Zone to filter migration records */
  zone?: string;
  /** IP address to filter migration records */
  ip?: string;
  /** Pod type to filter migration records, (e.g. "mqevent", "function") */
  pod_type?: string;
}

export interface MigrationRecordsResponse {
  /** Response code */
  code?: number;
  /** List of migration records returned in the response */
  data?: Array<MigrationRecord>;
  /** Error message if the request failed */
  error?: string;
}

export interface MqTriggerInstance {
  /** CPU usage of the MQ trigger instance */
  cpu?: number;
  /** Creation timestamp of the instance */
  created_at?: string;
  /** Function ID associated with the instance */
  function_id?: string;
  /** Host node where the instance is running */
  host?: string;
  /** IPv6 address of the host */
  host_ipv6?: string;
  /** Link to host metrics dashboard */
  host_metric_link?: string;
  /** Link to instance log dashboard */
  instance_log_link?: string;
  /** Link to instance metrics dashboard */
  instance_metric_link?: string;
  /** Memory usage of the instance */
  memory?: number;
  /** Status or error message for the instance */
  message?: string;
  /** MQ event agent port */
  mqevent_agent_port?: string;
  /** MQ event ID associated with the instance */
  mqevent_id?: string;
  /** Pod IP address */
  pod_ip?: string;
  /** Pod IPv6 address */
  pod_ipv6?: string;
  /** Pod name */
  pod_name?: string;
  /** Main port exposed by the instance */
  port?: string;
  /** Link to instance profile */
  profile_link?: string;
  /** Region where the instance is running */
  region?: string;
  /** Status of the instance */
  status?: string;
  /** Zone where the instance is running */
  zone?: string;
  /** Plugin function version used by the instance */
  plugin_function_version?: string;
  /** Container IDs associated with the instance (comma-separated) */
  container_ids?: string;
}

export interface OtherRoutes {
  /** Access port for the route */
  access_port?: string;
  /** Service port for the route */
  service_port?: string;
}

export interface PodInfo {
  /** Pod name */
  podName?: string;
  /** Namespace of the pod */
  namespace?: string;
  /** Labels assigned to the pod */
  labels?: Record<string, string>;
  /** Pod creation time */
  creatTime?: string;
  /** Node IP where the pod is scheduled */
  node?: string;
  /** Pod IP address */
  podIp?: string;
  /** Phase of the pod */
  podPhase?: string;
  /** Unique identifier for the pod */
  uid?: string;
  /** Whether the pod is not ready */
  podNotReady?: boolean;
  /** Whether the pod is not scheduled */
  podNotScheduled?: boolean;
  /** Whether the pod is deleted */
  deleted?: boolean;
  /** Container information for each container in the pod */
  containerInfos?: Record<string, ContainerInfo>;
}

export interface StageLog {
  /** Log level */
  level?: string;
  /** Log message content */
  message?: string;
  /** Timestamp of the log entry */
  time?: string;
  /** Phase of the stage */
  phase?: string;
  /** Component name generating the log */
  component?: string;
}

export interface UpdateFunctionInstanceServiceDiscoveryRequest {
  /** Whether to disable service discovery for the instance */
  disabled: boolean;
  /** ID of the service */
  service_id: string;
  /** Region name where the instance is running */
  region: string;
  /** Cluster name where the instance is running */
  cluster: string;
  /** Zone where the instance is running */
  zone: string;
  /** Pod name to update service discovery for */
  podname: string;
}

export interface UpdateFunctionInstanceServiceDiscoveryResponse {
  /** Response code */
  code?: number;
  /** Empty object for update response data */
  data?: common.EmptyObject;
  /** Error message if the request failed */
  error?: string;
}
/* eslint-enable */
