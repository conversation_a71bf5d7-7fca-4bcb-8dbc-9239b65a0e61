/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CopyTriggerSource {
  /** Type of trigger，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** ID of the trigger */
  trigger_id: string;
  /** Whether to enable the trigger after copying; default is false */
  enable?: boolean;
}

export interface CopyTriggersRequest {
  /** Target service ID */
  target_service_id: string;
  /** Target region */
  target_region: string;
  /** Target cluster */
  target_cluster: string;
  /** Source service ID */
  source_service_id: string;
  /** Source region */
  source_region: string;
  /** Source cluster */
  source_cluster: string;
  /** List of source triggers to copy */
  source_triggers: Array<CopyTriggerSource>;
}

export interface DataMessage194 {
  /** Whether abase binlog trigger is enabled */
  abase_binlog?: boolean;
  /** Whether consul trigger is enabled */
  consul?: boolean;
  /** Whether HTTP trigger is enabled */
  http?: boolean;
  /** Whether MQ events trigger is enabled */
  mqevents?: boolean;
  /** Whether timer trigger is enabled */
  timer?: boolean;
  /** Whether TOS trigger is enabled */
  tos?: boolean;
}

export interface GetAllTriggersRequest {
  /** Name of the cluster */
  cluster: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Whether to split mqevents into eventbus; defaults to false */
  split_eventbus?: string;
  /** Include prod service's mqtrigger when set to true; only works for env services, e.g., boe-xx/ppe-xx */
  with_env_trigger?: string;
  /** If true, will not return event_bridge type trigger */
  not_show_eb_trigger?: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetAllTriggersResponse {
  code?: number;
  data?: common.AllTriggers;
  error?: string;
}

export interface GetMqClustersRequest {
  /** Type of MQ, enums: kafka, rocketmq, tos, eventbus, abase_binlog, vefaas_async_tos, vefaas_bmq */
  mq_type: string;
  /** Region of the MQ */
  region: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetMqClustersResponse {
  code?: number;
  data?: Record<string, Record<string, Array<string>>>;
  error?: string;
}

export interface GetTriggersEnabledRequest {
  /** Cluster of the service */
  cluster: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
}

export interface GetTriggersEnabledResponse {
  code?: number;
  data?: DataMessage194;
  error?: string;
}

export interface MqPermissionRequest {
  /** Cluster name (in request body) */
  cluster?: string;
  /** Name of the cluster (in path) */
  cluster_name: string;
  /** MQ region (in request body) */
  mq_region?: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** Topic name (in request body) */
  topic?: string;
  /** Type of MQ (kafka/rocketmq/eventbus) */
  type?: string;
  /** Authorization type (psm/user), default is psm */
  auth_type?: string;
}

export interface MqPermissionResponse {
  code?: number;
  data?: string;
  error?: string;
}

export interface ResetMQOffsetRequest {
  /** Cluster of the service */
  cluster: string;
  /** Whether to perform a dry run */
  dryRun?: boolean;
  /** Whether to force stop */
  force_stop?: boolean;
  /** Offset to reset to (applies to all partitions if set) */
  offset?: number;
  /** Region of the service */
  region: string;
  /** Type of reset. Enums: offset, timestamp */
  resetType?: string;
  /** Details per partition for reset */
  reset_details_per_partition_array?: Array<ResetMQOffsetRequestResetDetailsPerPartitionArrayMessage>;
  /** Service ID */
  service_id: string;
  /** Timestamp for the reset (applies to all partitions if set) */
  timestamp?: number;
  /** Trigger ID */
  trigger_id: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Reset method */
  whence?: string;
}

export interface ResetMQOffsetRequestResetDetailsPerPartitionArrayMessage {
  /** Offset to reset to */
  offset?: number;
  /** Partition number */
  partition?: number;
  /** Timestamp for the reset */
  timestamp?: number;
  /** Reset method */
  whence?: string;
}

export interface ResetMQOffsetResponse {
  code?: number;
  data?: ResetMQOffsetResponseData;
  error?: string;
}

export interface ResetMQOffsetResponseData {
  /** Ticket ID for the reset operation */
  ticket_id?: string;
}

export interface RollbackRequest {
  /** Cluster of the service */
  cluster: string;
  /** Region of the service */
  region: string;
  /** Service ID */
  service_id: string;
  /** List of target rollback tickets */
  targets?: Array<RollbackRequestTargetsMessage>;
  /** Trigger ID */
  trigger_id: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
}

export interface RollbackRequestTargetsMessage {
  /** The ID of the target rollback ticket */
  ticket_id?: string;
}

export interface RollbackResponse {
  code?: number;
  data?: string;
  error?: string;
}
/* eslint-enable */
