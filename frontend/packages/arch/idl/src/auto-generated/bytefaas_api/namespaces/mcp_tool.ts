/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as mcp_server from './mcp_server';

export type Int64 = string | number;

export interface BamApiListRequest {
  /** Service name to list APIs for */
  psm: string;
  /** Version to filter APIs */
  version?: string;
}

export interface BamApiListResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** List of BAM OpenAPI metadata */
  data?: Array<BamOpenAPIData>;
}

export interface BamApiRequest {
  /** API ID to retrieve */
  api_id: string;
  /** Version to filter API */
  version?: string;
}

export interface BamApiResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** MCP API metadata */
  data?: McpAPIData;
}

/** BAM OpenAPI metadata */
export interface BamOpenAPIData {
  /** API ID */
  api_id?: number;
  /** HTTP method */
  method?: string;
  /** API path */
  path?: string;
  /** API name */
  name?: string;
  /** Request serializer */
  serializer?: string;
  /** Response serializer */
  resp_serializer?: string;
  /** Service name */
  psm?: string;
  /** Version */
  version?: string;
  /** RPC method name */
  rpc_method?: string;
  /** IDL definition */
  idl?: mcp_server.IDL;
}

export interface BamVersionData {
  /** Service name */
  psm?: string;
  /** Cluster name */
  cluster?: string;
  /** Version identifier */
  version?: string;
  /** Version note or description */
  note?: string;
  /** Whether to skip version check */
  no_check?: boolean;
  /** Commit ID for this version */
  commit_id?: string;
  /** Commit message for this version */
  commit_msg?: string;
  /** Service ID */
  service_id?: Int64;
  /** Creation time */
  ctime?: Int64;
  /** Status code */
  status?: Int64;
  /** IDL source path or identifier */
  idl_source?: string;
  /** Git branch name */
  git_branch?: string;
  /** Protocol type */
  protocol?: string;
  /** Creator of the version */
  creator?: string;
  /** Updater of the version */
  updater?: string;
}

export interface BamVersionListRequest {
  /** Service name to list versions for */
  psm: string;
}

export interface BamVersionListResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** List of BAM version metadata */
  data?: Array<BamVersionData>;
}

export interface CreateMcpRateLimitRuleBpmTicketResponse {
  /** link to the BPM ticket created for the rate limit rule update */
  bpm_link: string;
}

export interface CreateMcpToolRequest {
  /** Server ID to create tool in */
  server_id: string;
  /** Display name of the tool */
  name: string;
  /** Unique tool name */
  tool_name: string;
  /** Description of the tool */
  tool_description?: string;
  /** Input schema for the tool */
  tool_input_schema: string;
  /** Whether the tool is enabled */
  enabled?: boolean;
  /** Type of the tool */
  tool_type: string;
  /** Tool configuration */
  tool_config: mcp_server.McpToolConfig;
}

export interface CreateMcpToolResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** Created MCP tool details */
  data?: mcp_server.McpTool;
}

export interface DeleteMcpToolRequest {
  /** MCP server ID the tool belongs to */
  server_id: string;
  /** Tool ID to delete */
  tool_id: string;
}

export interface GetMcpRateLimitRuleBpmTicketRequest {
  /** Server ID the tool belongs to */
  server_id: string;
}

export interface GetMcpRateLimitRuleBpmTicketResponse {
  /** link to the BPM ticket created for the rate limit rule update */
  bpm_link: string;
}

export interface GetMcpToolRequest {
  /** Server ID the tool belongs to */
  server_id: string;
  /** Tool ID to retrieve */
  tool_id: string;
}

export interface GetMcpToolResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** MCP tool details */
  data?: mcp_server.McpTool;
}

export interface GlobalSearchMcpToolsRequest {
  /** Whether to return all results (default: false) */
  all?: string;
  /** Environment name (prod/ppe/boe_feature) */
  env?: string;
  /** Maximum number of results to return */
  limit?: number;
  /** Filter by tool name */
  name?: string;
  /** Pagination offset */
  offset?: number;
  /** Filter by owner */
  owner?: string;
  /** Prefix search for multiple fields */
  search?: string;
  /** Search type: all/admin/own/subscribe */
  search_type?: string;
  /** Sort by field in MCP tool model */
  sort_by?: string;
  /** Supported search fields: tool_id/tool_name/server_id */
  search_fields?: string;
}

export interface GlobalSearchMcpToolsResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** List of MCP tools matching the search */
  data?: Array<mcp_server.McpTool>;
}

export interface ListMcpCustomToolsRequest {
  /** Server ID to list tools for */
  server_id: string;
}

export interface ListMcpCustomToolsResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** List of MCP custom tools */
  data?: ListMcpCustomToolsResponseData;
}

export interface ListMcpCustomToolsResponseData {
  /** MCP tool details */
  tools: Array<McpCustomTool>;
}

export interface ListMcpToolsRequest {
  /** Server ID to list tools for */
  server_id: string;
}

export interface ListMcpToolsResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** List of MCP tools for the server */
  data?: Array<mcp_server.McpTool>;
}

/** MCP API metadata */
export interface McpAPIData {
  /** HTTP method */
  method?: string;
  /** API path */
  path?: string;
  /** API name */
  name?: string;
  /** Request serializer */
  serializer?: string;
  /** Response serializer */
  resp_serializer?: string;
  /** Service name */
  psm?: string;
  /** Version */
  version?: string;
  /** Input schema */
  input_schema?: string;
  /** Request schema */
  request_schema?: string;
  /** Response schema */
  response_schema?: string;
  /** API ID */
  api_id?: number;
  /** RPC method name */
  rpc_method?: string;
  /** IDL definition */
  idl?: mcp_server.IDL;
}

/** MCP custom tool */
export interface McpCustomTool {
  /** Name of the tool */
  name?: string;
  /** Description of the tool */
  description?: string;
}

export interface UpdateMcpRateLimitRule {
  /** ID of the tool that this rule applies to */
  tool_id: string;
  /** The rate limit rule to apply */
  generic_rate_limit_rule: mcp_server.McpRateLimitRule;
}

export interface UpdateMcpRateLimitRulesRequest {
  /** MCP server ID to update rules for */
  server_id: string;
  /** The rules to update */
  rules: Array<UpdateMcpRateLimitRule>;
  /** reason for the update */
  reason: string;
  /** Assignee for the review process */
  review_assignee: string;
}

export interface UpdateMcpToolRequest {
  /** MCP server ID the tool belongs to */
  server_id: string;
  /** Tool ID to update */
  tool_id: string;
  /** Display name of the tool */
  name?: string;
  /** Unique tool name (may duplicate 'name' for compatibility) */
  tool_name?: string;
  /** Description of the tool */
  tool_description?: string;
  /** Input schema for the tool */
  tool_input_schema?: string;
  /** Whether the tool is enabled */
  enabled?: boolean;
  /** Type of the tool */
  tool_type?: string;
  /** Tool configuration */
  tool_config?: mcp_server.McpToolConfig;
}

export interface UpdateMcpToolResponse {
  /** Error code */
  code?: number;
  /** Error message */
  error?: string;
  /** Updated MCP tool details */
  data?: mcp_server.McpTool;
}
/* eslint-enable */
