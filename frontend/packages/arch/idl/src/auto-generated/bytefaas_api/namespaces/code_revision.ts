/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CreateCodeRevisionRequest {
  /** List of code dependencies */
  dependency?: Array<common.Dependency>;
  /** Deployment method. 部署方式
How the code revision is deployed. Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method: string;
  /** Description of the code revision */
  description?: string;
  /** Whether build install is disabled, true means disable */
  disable_build_install?: boolean;
  /** Handler function name */
  handler?: string;
  /** Initializer function name */
  initializer?: string;
  /** Whether lazy loading is enabled */
  lazyload?: boolean;
  /** Code revision number, generated by server if empty. 版本号
Unique number for the code revision */
  number?: string;
  /** Protocol used by the code revision */
  protocol?: string;
  /** Command to run the code revision */
  run_cmd?: string;
  /** Runtime environment */
  runtime?: string;
  /** Port for the runtime container */
  runtime_container_port?: number;
  /** Debug port for the runtime container */
  runtime_debug_container_port?: number;
  /** Service ID */
  service_id: string;
  /** Source of code revision. 代码版本 URI
URI for the code revision source */
  source: string;
  /** Source type of code revision. 代码版本类型。Enums: url, tos, scm, ceph, image, image-scm, private-tos, lego */
  source_type: string;
  /** Whether image lazy loading is enabled */
  open_image_lazyload?: boolean;
  /** Additional container ports for runtime */
  runtime_other_container_ports?: Array<number>;
}

export interface CreateCodeRevisionResponse {
  code?: number;
  data?: common.CodeRevision;
  error?: string;
}

export interface DownloadCodeRevisionPackageRequest {
  /** Code revision number */
  revision_number: string;
  /** Service ID */
  service_id: string;
}

export interface DownloadCodeRevisionPackageResponse {
  code?: number;
  /** Downloaded package response data */
  data?: common.ApiResponseDataMessage2;
  error?: string;
}

export interface GetCodeRevisionByNumberRequest {
  /** Code revision number */
  revision_number: string;
  /** Service ID */
  service_id: string;
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
}

export interface GetCodeRevisionByNumberResponse {
  code?: number;
  /** Code revision data */
  data?: common.CodeRevision;
  error?: string;
}

export interface GetCodeRevisionsRequest {
  /** limit in pagination */
  limit?: string;
  /** offset in pagination */
  offset?: string;
  /** ID of service */
  service_id: string;
}

export interface GetCodeRevisionsResponse {
  code?: number;
  data?: Array<common.CodeRevision>;
  error?: string;
}

export interface GetOnlineCodeRevisionRequest {
  /** Region name */
  region: string;
  /** Service ID */
  service_id: string;
}

export interface GetOnlineCodeRevisionResponse {
  code?: number;
  /** Online code revision data by region */
  data?: Record<string, OnlineCodeRevision>;
  error?: string;
}

/** Online code revision information */
export interface OnlineCodeRevision {
  /** Creation time */
  created_at?: string;
  /** Creator of the code revision */
  created_by?: string;
  /** Deployment method. 部署方式
Enums: online-edit, scm, command-line, online-edit, image, lego */
  deploy_method?: string;
  /** Description of the code revision */
  description?: string;
  /** Whether build install is disabled, true means disable */
  disable_build_install?: boolean;
  /** Function ID */
  function_id?: string;
  /** Handler function name */
  handler?: string;
  /** Code revision ID */
  id?: string;
  /** Initializer function name */
  initializer?: string;
  /** Whether zone traffic exists */
  is_zone_traffic_exist?: boolean;
  /** Whether lazy loading is enabled */
  lazyload?: boolean;
  /** Code revision number */
  number?: string;
  /** Protocol used by the code revision */
  protocol?: string;
  /** Command to run the code revision */
  run_cmd?: string;
  /** Runtime environment */
  runtime?: string;
  /** Port for the runtime container */
  runtime_container_port?: number;
  /** Debug port for the runtime container */
  runtime_debug_container_port?: number;
  /** Service ID */
  service_id?: string;
  /** Source of code revision. 代码版本 URI
URI for the code revision source */
  source?: string;
  /** Source type of code revision. 代码版本类型。Enums: url, tos, scm, ceph, image, image-scm, private-tos, lego */
  source_type?: string;
  /** Traffic value for the code revision */
  traffic_value?: number;
  /** Whether image lazy loading is enabled */
  open_image_lazyload?: boolean;
  /** Additional container ports for runtime */
  runtime_other_container_ports?: Array<number>;
}
/* eslint-enable */
