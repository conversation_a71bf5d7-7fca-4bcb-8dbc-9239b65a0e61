/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface GetVolcSigninTokenRequest {
  /** Service ID for which to obtain the sign-in token */
  service_id?: string;
  /** Region of the service */
  region?: string;
  /** Cluster of the service */
  cluster?: string;
}

export interface GetVolcSigninTokenResponse {
  code: number;
  data: GetVolcSigninTokenResponseData;
  error: string;
}

export interface GetVolcSigninTokenResponseData {
  /** Sign-in token for Volcengine authentication */
  signin_token: string;
}

export interface GetVolcTlsConfigRequest {
  /** Service ID for which to obtain TLS config */
  service_id?: string;
  /** Region of the service */
  region?: string;
  /** Cluster of the service */
  cluster?: string;
}

export interface GetVolcTlsConfigResponse {
  code: number;
  data: GetVolcTlsConfigResponseData;
  error: string;
}

export interface GetVolcTlsConfigResponseData {
  /** Whether logging is enabled for Volcengine TLS */
  enable_log: boolean;
  /** Project ID for Volcengine TLS (optional) */
  tls_project_id?: string;
  /** Topic ID for Volcengine TLS (optional) */
  tls_topic_id?: string;
}
/* eslint-enable */
