/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface SubscribeServiceRequest {
  /** JWT token for authentication */
  'X-Jwt-Token'?: string;
  /** ID of function service */
  service_id: string;
  /** 订阅人列表
array of name of subscribers. */
  subscribers?: Array<string>;
}

export interface SubscribeServiceResponse {
  code?: number;
  data?: common.ApiResponseDataMessage2;
  error?: string;
}

export interface UnsubscribeServiceRequest {
  'X-Jwt-Token'?: string;
  /** ID of service to unsub */
  service_id: string;
}

export interface UnsubscribeServiceResponse {
  code?: number;
  data?: common.ApiResponseDataMessage2;
  error?: string;
}
/* eslint-enable */
