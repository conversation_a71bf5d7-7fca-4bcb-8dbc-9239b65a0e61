/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** Data message for resource statistics */
export interface DataMessage22 {
  /** Environment name */
  env?: string;
  /** Function ID */
  function_id?: string;
  /** PSM name */
  psm?: string;
  /** Region name */
  region?: string;
  /** Resource statistics */
  resource?: DataMessage22ResourceMessage2;
}

/** Resource statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2 {
  /** Resource limit statistics */
  limit?: DataMessage22ResourceMessage2LimitMessage2;
  /** Resource quota statistics */
  quota?: DataMessage22ResourceMessage2QuotaMessage2;
  /** Resource usage statistics */
  usage?: DataMessage22ResourceMessage2UsageMessage2;
}

/** Resource limit statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2LimitMessage2 {
  /** CPU limit statistics */
  cpu?: DataMessage22ResourceMessage2LimitMessage2CpuMessage2;
  /** Memory limit statistics */
  mem?: DataMessage22ResourceMessage2LimitMessage2MemMessage2;
}

/** CPU resource limit statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2LimitMessage2CpuMessage2 {
  /** Average CPU limit */
  avg?: number;
  /** Maximum CPU limit */
  max?: number;
  /** Minimum CPU limit */
  min?: number;
}

/** Memory resource limit statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2LimitMessage2MemMessage2 {
  /** Average memory limit */
  avg?: number;
  /** Maximum memory limit */
  max?: number;
  /** Minimum memory limit */
  min?: number;
}

/** Resource quota statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2QuotaMessage2 {
  /** CPU quota statistics */
  cpu?: DataMessage22ResourceMessage2QuotaMessage2CpuMessage2;
  /** Memory quota statistics */
  mem?: DataMessage22ResourceMessage2QuotaMessage2MemMessage2;
}

/** CPU resource quota statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2QuotaMessage2CpuMessage2 {
  /** Average CPU quota */
  avg?: number;
  /** Maximum CPU quota */
  max?: number;
  /** Minimum CPU quota */
  min?: number;
}

/** Memory resource quota statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2QuotaMessage2MemMessage2 {
  /** Average memory quota */
  avg?: number;
  /** Maximum memory quota */
  max?: number;
  /** Minimum memory quota */
  min?: number;
}

/** Resource usage statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2UsageMessage2 {
  /** CPU usage statistics */
  cpu?: DataMessage22ResourceMessage2UsageMessage2CpuMessage2;
  /** Memory usage statistics */
  mem?: DataMessage22ResourceMessage2UsageMessage2MemMessage2;
}

/** CPU resource usage statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2UsageMessage2CpuMessage2 {
  /** Average CPU usage */
  avg?: number;
  /** Maximum CPU usage */
  max?: number;
  /** Minimum CPU usage */
  min?: number;
}

/** Memory resource usage statistics for DataMessage22 */
export interface DataMessage22ResourceMessage2UsageMessage2MemMessage2 {
  /** Average memory usage */
  avg?: number;
  /** Maximum memory usage */
  max?: number;
  /** Minimum memory usage */
  min?: number;
}

/** Data message for reserved replica threshold */
export interface DataMessage24 {
  /** Reserved replica threshold value */
  reserved_replica_threshold?: number;
  /** Resource statistics */
  resource_statistics?: DataMessage24ResourceStatisticsMessage2;
}

/** Resource statistics for DataMessage24 */
export interface DataMessage24ResourceStatisticsMessage2 {
  /** CPU statistics */
  cpu?: DataMessage24ResourceStatisticsMessage2CpuMessage2;
  /** Memory statistics */
  mem?: DataMessage24ResourceStatisticsMessage2MemMessage2;
}

/** CPU resource statistics for DataMessage24 */
export interface DataMessage24ResourceStatisticsMessage2CpuMessage2 {
  /** Average CPU usage */
  avg?: number;
  /** Maximum CPU usage */
  max?: number;
  /** Minimum CPU usage */
  min?: number;
}

/** Memory resource statistics for DataMessage24 */
export interface DataMessage24ResourceStatisticsMessage2MemMessage2 {
  /** Average memory usage */
  avg?: number;
  /** Maximum memory usage */
  max?: number;
  /** Minimum memory usage */
  min?: number;
}

export interface GetMQEventResourceRequest {
  /** Service ID */
  service_id: string;
  /** Target environment name */
  env?: string;
}

export interface GetRealtimeResourceUsageRequest {
  /** get all regions */
  all_region?: boolean;
  /** Target env name */
  env?: string;
  /** psm */
  psm?: string;
  /** Target region name */
  region?: string;
}

export interface GetRealtimeResourceUsageResponse {
  code?: number;
  data?: Array<GetResourceRealtimeClusterData>;
  error?: string;
}

export interface GetReservedReplicaThresholdRequest {
  /** Cluster name */
  cluster: string;
  /** Duration in minutes (only for cron strategy) */
  duration_minutes?: string;
  /** Start hours for cron strategy (only for cron strategy) */
  hours?: string;
  /** Start minutes for cron strategy (only for cron strategy) */
  minutes?: string;
  /** Region name */
  region: string;
  /** Service ID */
  service_id: string;
}

export interface GetReservedReplicaThresholdResponse {
  code?: number;
  data?: DataMessage24;
  error?: string;
}

/** Real-time resource statistics for a cluster */
export interface GetResourceRealtimeClusterData {
  /** Cluster name */
  cluster?: string;
  /** List of zone resource statistics */
  zones?: Array<ResourceRealtimeZoneData>;
}

export interface GetResourceRequest {
  /** Query all regions */
  all_region?: boolean;
  /** Target environment name */
  env?: string;
  /** Function ID */
  function_id?: string;
  /** PSM name */
  psm?: string;
  /** Target region name */
  region?: string;
}

export interface GetResourceResponse {
  code?: number;
  data?: DataMessage22;
  error?: string;
}

export interface GetTriggerReservedReplicaThresholdRequest {
  /** Cluster name */
  cluster: string;
  /** Duration in minutes (only for cron strategy) */
  duration_minutes?: string;
  /** Start hours for cron strategy (only for cron strategy) */
  hours?: string;
  /** Start minutes for cron strategy (only for cron strategy) */
  minutes?: string;
  /** Region name */
  region: string;
  /** Service ID */
  service_id: string;
  /** Trigger type，enums: mqevents, tos, abase_binlog, timer, consul, http, event_bridge */
  trigger_type: string;
  /** Trigger ID */
  trigger_id: string;
}

export interface GetTriggerReservedReplicaThresholdResponse {
  code?: number;
  data?: DataMessage24;
  error?: string;
}

/** Resource statistics for a zone in real time */
export interface ResourceRealtimeZoneData {
  /** Average CPU usage in the zone */
  avg_cpu?: number;
  /** Average memory usage in the zone */
  avg_mem?: number;
  /** Number of instances in the zone */
  instances?: number;
  /** Zone name */
  zone?: string;
}
/* eslint-enable */
