/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface DownloadTemplateByNameRequest {
  /** Template name to download */
  template_name: string;
}

export interface DownloadTemplateByNameResponse {}

/** Function template definition */
export interface FunctionTemplate {
  /** Template description */
  description?: string;
  /** Documentation for the template */
  document?: string;
  /** Programming language of the template */
  language?: string;
  /** Template name */
  name?: string;
  /** Protocol type */
  protocol?: string;
  /** runtime. Optional values: golang/v1,node10/v1,python3/v1,rust1/v1,java8/v1,wasm/v1,v8/v1,native/v1,native-java8/v1 */
  runtime?: string;
  /** Source location of the template */
  source_location?: string;
  /** Author of the template */
  template_author?: string;
}

export interface getFunctionTemplatesRequest {}

export interface GetFunctionTemplatesResponse {
  code?: number;
  data?: Array<FunctionTemplate>;
  error?: string;
}

export interface GetTemplateByNameRequest {
  /** Template name to query */
  template_name: string;
}

export interface GetTemplateByNameResponse {
  code?: number;
  data?: FunctionTemplate;
  error?: string;
}

export interface UploadTemplateByNameRequest {
  /** Template name to upload */
  template_name: string;
}

export interface UploadTemplateByNameResponse {
  code?: number;
  data?: string;
  error?: string;
}
/* eslint-enable */
