/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CreateFilterPluginsRequest {
  /** cluster name */
  cluster: string;
  name?: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
  /** zip file binary */
  zip_file?: CreateFilterPluginsRequestZipFileMessage2;
  /** Size of the zip file in bytes */
  zip_file_size?: number;
}

export interface CreateFilterPluginsRequestZipFileMessage2 {}

export interface CreateFilterPluginsResponse {
  code?: number;
  data?: FilterPlugin;
  error?: string;
}

export interface DeleteFilterPluginsRequest {
  /** cluster name */
  cluster: string;
  /** id */
  filter_plugin_id: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface DeleteFilterPluginsResponse {
  code?: number;
  data?: FilterPlugin;
  error?: string;
}

export interface DownloadFilterPluginsRequest {
  /** cluster name */
  cluster: string;
  /** id */
  filter_plugin_id: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
}

export interface DownloadFilterPluginsResponse {
  code?: number;
  data?: string;
  error?: string;
}

export interface FilterPlugin {
  /** Time when the filter plugin was created */
  created_at?: string;
  /** Size of the filter plugin file in MB */
  file_size_mb?: number;
  /** ID of the function associated with the plugin */
  function_id?: string;
  /** Unique identifier for the filter plugin */
  id?: string;
  /** Name of the filter plugin */
  name?: string;
  /** Source location or URI of the plugin */
  source?: string;
  /** Type of the source */
  source_type?: string;
  /** Time when the filter plugin was last updated */
  updated_at?: string;
  /** User who last updated the plugin */
  updated_user?: string;
}

export interface GetFilterPluginsDetailRequest {
  /** Name of the cluster */
  cluster: string;
  /** ID of the filter plugin */
  filter_plugin_id: string;
  /** Name of the region */
  region: string;
  /** ID of the service */
  service_id: string;
}

export interface GetFilterPluginsDetailResponse {
  code?: number;
  data?: FilterPlugin;
  error?: string;
}

export interface GetFilterPluginsRequest {
  /** Name of the cluster */
  cluster: string;
  /** Name of the region */
  region: string;
  /** ID of the service */
  service_id: string;
  /** Pagination offset */
  offset?: number;
  /** Pagination limit */
  limit?: number;
}

export interface GetFilterPluginsResponse {
  code?: number;
  data?: Array<FilterPlugin>;
  error?: string;
}

export interface UpdateFilterPluginsRequest {
  /** cluster name */
  cluster: string;
  /** filter plugin id */
  filter_plugin_id: string;
  /** Name of the filter plugin */
  name?: string;
  /** region name */
  region: string;
  /** ID of service */
  service_id: string;
  /** zip file binary */
  zip_file?: UpdateFilterPluginsRequestZipFileMessage2;
  /** Size of the zip file in bytes */
  zip_file_size?: number;
}

export interface UpdateFilterPluginsRequestZipFileMessage2 {}

export interface UpdateFilterPluginsResponse {
  code?: number;
  data?: FilterPlugin;
  error?: string;
}
/* eslint-enable */
