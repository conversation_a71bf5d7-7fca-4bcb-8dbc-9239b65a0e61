/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as datasetv2 from './datasetv2';
import * as datasetv2lineage from './datasetv2lineage';
import * as base from './base';
import * as datasetv2job from './datasetv2job';

export type Int64 = string | number;

export interface ConflictField {
  /** 存在冲突的列名 */
  fieldName?: string;
  /** 冲突详情。key: 文件名，val：该文件中包含的类型 */
  detailM?: Record<string, datasetv2.FieldSchema>;
}

export interface DatasetItemWithSource {
  item?: datasetv2.DatasetItem;
  source?: datasetv2lineage.ItemSource;
  deepSources?: Array<datasetv2lineage.ItemSource>;
}

export interface OpenBatchCreateDatasetItemsRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  items: Array<datasetv2.DatasetItem>;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  /** 是否跳过无效数据 */
  skipInvalidItems?: boolean;
  /** 是否允许写入不超过容量限制的数据 */
  allowPartialAdd?: boolean;
  base?: base.Base;
}

export interface OpenBatchCreateDatasetItemsResponse {
  /** key: item在输入数据的索引, value:item的唯一ID */
  addedItems?: Record<number, Int64>;
  errors?: Array<datasetv2.ItemErrorGroup>;
  baseResp?: base.BaseResp;
}

export interface OpenBatchDeleteDatasetItemsRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  itemIDs: Array<string>;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenBatchDeleteDatasetItemsResponse {
  baseResp?: base.BaseResp;
}

export interface OpenBatchGetDatasetItemsByVersionRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  versionID: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  itemIDs: Array<string>;
  base?: base.Base;
}

export interface OpenBatchGetDatasetItemsByVersionResponse {
  data?: Array<datasetv2.DatasetItem>;
  baseResp?: base.BaseResp;
}

export interface OpenBatchGetDatasetItemsRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  itemIDs: Array<string>;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenBatchGetDatasetItemsResponse {
  data?: Array<datasetv2.DatasetItem>;
  baseResp?: base.BaseResp;
}

export interface OpenCancelDatasetIOJobReq {
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  jobID: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenCancelDatasetIOJobResp {
  baseResp?: base.BaseResp;
}

export interface OpenClearDatasetItemsRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenClearDatasetItemsResponse {
  baseResp?: base.BaseResp;
}

export interface OpenCreateDatasetReq {
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  name: string;
  description?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  fields?: Array<datasetv2.FieldSchema>;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenCreateDatasetResp {
  datasetID?: string;
  baseResp?: base.BaseResp;
}

export interface OpenCreateDatasetVersionRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  /** 展示版本号，SemVer2三段式，需要大于上一个版本 */
  version: string;
  desc?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenCreateDatasetVersionResponse {
  versionID?: string;
  baseResp?: base.BaseResp;
}

export interface OpenExportDatasetReq {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  /** 需要导出的数据集版本 id，为 0 表示导出草稿版本 */
  versionID?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  targetType: datasetv2job.SourceType;
  /** 此处填写一个文件夹，会将对应的文件生成到该文件夹下 */
  target: datasetv2job.DatasetIOEndpoint;
  base?: base.Base;
}

export interface OpenExportDatasetResp {
  jobID?: string;
  baseResp?: base.BaseResp;
}

export interface OpenGetDatasetIOJobReq {
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  jobID: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenGetDatasetIOJobResp {
  job?: datasetv2job.DatasetIOJob;
  baseResp?: base.BaseResp;
}

export interface OpenGetDatasetItemRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  itemID: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  withDeepSources?: boolean;
  base?: base.Base;
}

export interface OpenGetDatasetItemResponse {
  data?: DatasetItemWithSource;
  baseResp?: base.BaseResp;
}

export interface OpenImportDatasetReq {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  file?: datasetv2job.DatasetIOFile;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  fieldMappings?: Array<datasetv2job.FieldMapping>;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  option?: datasetv2job.DatasetIOJobOption;
  base?: base.Base;
}

export interface OpenImportDatasetResp {
  jobID?: string;
  baseResp?: base.BaseResp;
}

export interface OpenListDatasetItemsByVersionRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  versionID: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  cursor?: string;
  base?: base.Base;
}

export interface OpenListDatasetItemsByVersionResponse {
  data?: Array<datasetv2.DatasetItem>;
  nextCursor?: string;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface OpenListDatasetItemsRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  cursor?: string;
  base?: base.Base;
}

export interface OpenListDatasetItemsResponse {
  data?: Array<datasetv2.DatasetItem>;
  nextCursor?: string;
  baseResp?: base.BaseResp;
}

export interface OpenListDatasetVersionsRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  cursor?: string;
  base?: base.Base;
}

export interface OpenListDatasetVersionsResponse {
  data?: Array<datasetv2.DatasetVersion>;
  nextCursor?: string;
  baseResp?: base.BaseResp;
}

export interface OpenParseImportSourceFileReq {
  /** 如果 path 为文件夹，此处只默认解析当前路径级别下所有指定类型的文件，不嵌套解析 */
  file?: datasetv2job.DatasetIOFile;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  base?: base.Base;
}

export interface OpenParseImportSourceFileResp {
  /** 文件大小，单位为 byte */
  bytes?: string;
  /** 列名和类型，有多文件的话会取并集返回。如果文件中的列定义存在冲突，此处不返回解析结果，具体冲突详情通过 conflicts 返回 */
  fields?: Array<datasetv2.FieldSchema>;
  /** 冲突详情。key: 列名，val：冲突详情 */
  conflicts?: Array<ConflictField>;
  /** 存在列定义不明确的文件（即一个列被定义为多个类型），当前仅 jsonl 文件会出现该状况 */
  filesWithAmbiguousColumn?: Array<string>;
  /** base */
  baseResp?: base.BaseResp;
}

export interface OpenPatchDatasetItemRequest {
  datasetID: string;
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  itemID: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  /** 单轮数据内容，当数据集为单轮时，写入此处的值 */
  data?: Array<datasetv2.FieldData>;
  /** 多轮对话数据内容，当数据集为多轮对话时，写入此处的值 */
  repeatedData?: Array<datasetv2.ItemData>;
  base?: base.Base;
}

export interface OpenPatchDatasetItemResponse {
  baseResp?: base.BaseResp;
}

export interface OpenSearchDatasetsRequest {
  'FlowDevops-Agw-OpenAPI-AppId'?: string;
  name?: string;
  createdBys?: Array<string>;
  'FlowDevops-Agw-OpenAPI-SpaceId'?: string;
  'FlowDevops-Agw-OpenAPI-AccountId'?: string;
  cursor?: string;
  base?: base.Base;
}

export interface OpenSearchDatasetsResponse {
  data?: Array<datasetv2.Dataset>;
  nextCursor?: string;
  baseResp?: base.BaseResp;
}
/* eslint-enable */
