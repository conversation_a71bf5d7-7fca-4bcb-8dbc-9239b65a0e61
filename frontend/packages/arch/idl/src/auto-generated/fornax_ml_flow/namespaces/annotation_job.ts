/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as filter from './filter';
import * as flow_devops_prompt_common from './flow_devops_prompt_common';
import * as batch_infer from './batch_infer';

export type Int64 = string | number;

export enum AIAnnotatorCategory {
  /** 在线 */
  Online = 1,
  /** 离线 */
  Offline = 2,
}

export enum AnnotationType {
  /** 大模型打标 */
  AI = 1,
  /** 规则打标 */
  Rule = 2,
  /** 手动打标 */
  Manual = 3,
}

export enum CrowdsourcingAnnotateJobStatus {
  Undefined = 0,
  Sending = 1,
  In_Progress = 2,
  Abnormal = 3,
  Completed = 4,
  Terminated = 5,
}

export enum CrowdsourcingAnnotationPlatform {
  Undefined = 0,
  AIDP = 1,
  AIDP_Lite = 2,
}

export enum DataSelectScope {
  Undefined = 0,
  All = 1,
  Filtered = 2,
  Manually_Selected = 3,
}

export enum RuleAnnotatorCategory {
  /** python */
  Python = 1,
}

export enum ThinkingOutputBehavior {
  UNSPECIFIED = 0,
  WithoutThinking = 1,
  ThinkingToSpecifiedColumn = 2,
}

export interface AIAnnotator {
  /** 该字段和内层的PromptCategory含义不同，代表是否关联已有评估器，当前没有用到该字段 */
  category?: AIAnnotatorCategory;
  onlineAIAnnotator?: OnlineAIAnnotator;
  offlineAIAnnotator?: OfflineAIAnnotator;
}

export interface AIDPConfig {
  /** AIDP 用户 ID */
  userID: string;
  /** AIDP 任务 ID */
  taskID: string;
  /** 任务名称 */
  taskName: string;
  /** 任务详情链接，返回字段 */
  taskURL?: string;
  answerSchema?: string;
}

export interface AIDPField {
  key?: string;
  name?: string;
}

export interface AIDPFlowNode {
  nodeID?: string;
  name?: string;
}

export interface AIDPTaskFlowConfig {
  nodes?: Array<AIDPFlowNode>;
  requiredFields?: Array<AIDPField>;
}

export interface AIDPTaskSummary {
  /** 任务ID */
  taskID?: string;
  /** 任务名称 */
  title?: string;
  /** 项目名称 */
  projectName?: string;
  /** 回调配置 */
  callbackOpts?: CallbackOpts;
  /** 答案模板ID */
  templateID?: string;
  /** 任务链接 */
  taskURL?: string;
}

export interface AnnotateStatusDetail {
  /** 总数据量 */
  inputSize?: string;
  /** 已送标数据量 */
  sent?: string;
  /** 已取标数据量 */
  received?: string;
}

export interface AnnotationConfig {
  /** 标注任务类型 */
  annotationType?: AnnotationType;
  /** 大模型打标器配置 */
  aiAnnotator?: AIAnnotator;
  /** 规则打标器配置 */
  ruleAnnotator?: RuleAnnotator;
}

export interface AnnotationJob {
  /** 主键ID */
  id?: string;
  appId?: string;
  spaceId?: string;
  /** JobID */
  annotationJobId?: string;
  jobDomain?: string;
  targetID?: string;
  /** 任务的版本，非数据集版本 */
  version?: string;
  /** 状态：active/inactive */
  status?: string;
  name?: string;
  annotationConfig?: AnnotationConfig;
  targetResourceName?: string;
  extra?: string;
  /** 通用信息 */
  createdAt?: string;
  createdBy?: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface AnnotationJobRunInstance {
  /** 运行后产生的Job实例ID */
  annotationJobRunID?: string;
  /** 总条数 */
  total?: number;
  /** 成功条数 */
  successCnt?: number;
  /** 失败条数 */
  failedCnt?: number;
  /** 任务状态,running/success/failed/canceled/partial_success */
  status?: string;
  /** 仅在 GetAnnotationJobProcess 接口返回，用于轮询查询输出列关联的打标任务 */
  outputFieldKeys?: Array<string>;
  /** JobID非ID */
  jobID?: string;
  job?: AnnotationJob;
  createdBy?: string;
  createdAt?: string;
  endAt?: string;
  runLog?: AnnotationJobRunLog;
}

export interface AnnotationJobRunLog {
  /** 错误日志 */
  errorMsg?: string;
  /** 离线任务的跳转链接 */
  offlineJobProviderUrl?: string;
}

export interface Annotator {
  /** 类型: 手工manual、关联associated */
  category?: string;
  /** 手工配置 */
  manualAnnotator?: ManualAnnotator;
}

export interface CallbackOpts {
  CallbackType?: string;
}

export interface CrowdsourcingAnnotateInputConfig {
  /** 输入数据集ID */
  inputDatasetID: string;
  /** 输入数据集名 */
  inputDatasetName: string;
  /** 输入数据集版本 */
  inputDatasetVersionNum: string;
  /** 输入数据集Veriosn主键 */
  inputDatasetVersionID: string;
  /** 三段式版本号返回字段 */
  inputDatasetVersion?: string;
  /** 需要标注数据的itemID */
  itemIDs?: Array<Int64>;
  /** 数据筛选条件,待定看标签的具体实现 */
  filter?: filter.Filter;
  /** 返回字段 */
  fieldMeta?: filter.FieldMetaInfoData;
  /** 返回字段 */
  total?: number;
  /** 发送字段映射配置 */
  inputMappings: Array<InputMapping>;
  /** 数据范围 */
  dataSelectScope?: DataSelectScope;
}

export interface CrowdsourcingAnnotateJob {
  /** 主键 */
  id?: string;
  name?: string;
  description?: string;
  platform?: CrowdsourcingAnnotationPlatform;
  /** AIDP 配置 */
  aidpConfig?: AIDPConfig;
  /** 送标配置 */
  inputConfig?: CrowdsourcingAnnotateInputConfig;
  /** 取标配置 */
  outputConfig?: CrowdsourcingAnnotateOutputConfig;
  /** 数据详情存储的数据集ID */
  InternalDatasetID?: string;
  /** 任务状态 */
  status?: CrowdsourcingAnnotateJobStatus;
  /** 失败的原因 */
  terminateReason?: string;
  /** 任务进展 */
  statusDetail?: AnnotateStatusDetail;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间/结束时间 */
  updatedAt?: string;
  /** 创建人 */
  createdBy?: string;
}

export interface CrowdsourcingAnnotateOutputConfig {
  /** 是否自动取标 */
  isAutoFetchRes: boolean;
  /** 是否自动输出到新数据集 */
  isAutoExportToNewDataset: boolean;
  /** 输出数据集名称 */
  outputDatasetName?: string;
  /** 输出数据集ID 返回字段 */
  outputDatasetID?: string;
  /** 是否拆解标注结果 */
  isParseAnnotateRes: boolean;
  /** 输出映射 */
  outputMappings?: Array<OutputMapping>;
}

export interface InputMapping {
  /** 输入类型: 固定值fixed、关联字段use_column、之前输入former_model_input、之前输出former_model_output */
  sourceType?: string;
  /** 输入值 */
  sourceValue?: string;
  /** 输出类型: prompt变量名prompt_var_name */
  targetType?: string;
  /** 输出值 */
  targetValue?: string;
  /** 如果是列这个字段可以存储name */
  sourceName?: string;
}

export interface ManualAnnotator {
  /** 模型配置 */
  model?: flow_devops_prompt_common.ModelConfig;
  /** prompt类型：手工manual、关联associated */
  promptCategory?: string;
  /** 手工填入的数据内容 */
  promptContent?: string;
  /** 关联时 */
  promptID?: string;
  /** 关联时 */
  promptVersion?: string;
  userPromptColumnName?: string;
  /** 输入映射 */
  inputMappings?: Array<InputMapping>;
  /** 输出映射 */
  outputMappings?: Array<OutputMapping>;
  /** thinking输出映射，当ThinkingOutputBehavior=ThinkingToSpecifiedColumn时使用 */
  thinkingOutputMapping?: OutputMapping;
}

export interface OfflineAIAnnotator {
  /** 批量推理配置
模型来源 */
  batchInferprovider?: batch_infer.Provider;
  /** 模型配置 & 资源配置 */
  batchInferProviderInfo?: batch_infer.ProviderInfo;
  /** 模型参数 */
  batchInferParam?: batch_infer.BatchInferParam;
  /** 任务配置
prompt类型 manual/associated/none */
  promptCategory?: string;
  /** 手工填入的数据内容 */
  promptContent?: string;
  /** 关联时 */
  promptID?: string;
  /** 关联时 */
  promptVersion?: string;
  userPromptColumnName?: string;
  /** 输入映射 */
  inputMappings?: Array<InputMapping>;
  /** 输出映射 */
  outputMappings?: Array<OutputMapping>;
  /** 仅为空值打标 */
  onlyForEmpty?: boolean;
  /** 仅为失败打标 */
  onlyForFailed?: boolean;
  /** 允许未配置的标签选项 */
  allowUndefinedTagValues?: boolean;
}

export interface OnlineAIAnnotator {
  /** 标注器配置 */
  annotator?: Annotator;
  /** 推理次数 */
  inferRound?: number;
  /** 批处理数据量 */
  batchSize?: number;
  /** 仅为空值打标 */
  onlyForEmpty?: boolean;
  /** 仅为失败打标 */
  onlyForFailed?: boolean;
  /** 允许未配置的标签选项 */
  allowUndefinedTagValues?: boolean;
  /** 自动更新 */
  autoUpdate?: boolean;
  /** 打标并发度 */
  executeConcurrency?: number;
  /** 输出模型thinking过程的策略 */
  thinkingOutputBehavior?: ThinkingOutputBehavior;
}

export interface OutputMapping {
  /** 输入类型: plain、json_path */
  sourceType?: string;
  /** 输入值 */
  sourceValue?: string;
  /** 输出类型: use_column、plain */
  targetType?: string;
  /** 输出值 */
  targetValue?: string;
  /** targetType是use_column时标识是否是新建列 */
  isNewColumn?: boolean;
  /** 输入值的标签/name */
  sourceName?: string;
}

export interface PassKTask {
  /** 推理模型配置 */
  reasoner?: Annotator;
  /** 推理次数 */
  inferenceRound?: number;
  /** 评估器配置 */
  judge?: Annotator;
  /** 正确阈值 */
  positiveThreshold?: number;
}

export interface QualityScoreJob {
  /** 唯一ID，创建时不传 */
  id?: string;
  /** appID，创建时不传 */
  appID?: number;
  /** 空间ID，创建时不传 */
  spaceID?: string;
  /** 数据集ID，创建时不传 */
  datasetID?: string;
  /** 版本号，创建时不传 */
  version?: string;
  /** job ID，创建时不传 */
  jobID?: string;
  /** 任务名字, 可不传 */
  name?: string;
  /** 任务状态: active、inactive */
  status?: string;
  /** 标注任务类型: passk */
  category?: string;
  /** passKTask 任务内容 */
  passKTask?: PassKTask;
  /** 是否自动计算新增数据 */
  autoCalculateNewData?: boolean;
  /** 通用信息 */
  createdAt?: string;
  createdBy?: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface QualityScoreJobInstance {
  /** instance唯一id */
  id?: string;
  /** 任务ID */
  jobID?: string;
  /** 总条数 */
  total?: number;
  /** 成功条数 */
  successCnt?: number;
  /** 失败条数 */
  failedCnt?: number;
  /** 任务状态 */
  status?: string;
}

export interface RuleAnnotator {
  /** 规则类型 */
  category?: RuleAnnotatorCategory;
  /** 规则内容 */
  content?: string;
  /** 输入映射 */
  inputMappings?: Array<InputMapping>;
  /** 输出映射 */
  outputMappings?: Array<OutputMapping>;
  /** 允许未配置的标签选项 */
  allowUndefinedTagValues?: boolean;
  /** 仅为空值打标 */
  onlyForEmpty?: boolean;
  /** 仅为失败项打标 */
  onlyForFailed?: boolean;
}

export interface SearchConfig {
  /** 数据集ID */
  datasetID?: string;
  /** 搜索的任务名称 */
  jobName?: string;
  /** 搜索的任务ID */
  jobID?: string;
  /** 搜索的创建人 */
  createdBy?: Array<string>;
}
/* eslint-enable */
