/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as ml_flow_domain_model from './ml_flow_domain_model';

export type Int64 = string | number;

export interface GetModelFilterParamsRequest {
  /** 因为coze暂时给不了rpc接口，所以后端需要拿到cookie去请求coze的前端接口 */
  cookie?: string;
  base?: base.Base;
}

export interface GetModelFilterParamsResponse {
  modelFilterTags?: Partial<
    Record<ml_flow_domain_model.ModelFilterKey, Array<string>>
  >;
  modelContextRange?: ModelContextRange;
  modelVendors?: Array<string>;
  baseResp?: base.BaseResp;
}

export interface GetModelRequest {
  /** 本期只支持BotEngine(等于llm gateway等于coze) */
  provider?: ml_flow_domain_model.Provider;
  providerModelID?: string;
  spaceID?: Int64;
  /** 因为coze暂时给不了rpc接口，所以后端需要拿到cookie去请求coze的前端接口 */
  cookie?: string;
  base?: base.Base;
}

export interface GetModelResponse {
  model?: ml_flow_domain_model.Model;
  baseResp?: base.BaseResp;
}

export interface GetModelUsageRequest {
  modelIdentification?: string;
  /** 本期只支持llm gateway */
  provider?: ml_flow_domain_model.Provider;
  spaceID?: Int64;
  base?: base.Base;
}

export interface GetModelUsageResponse {
  modelUsages?: Array<ModelUsage>;
  baseResp?: base.BaseResp;
}

export interface ListModelRequest {
  cursorID?: string;
  limit?: number;
  /** 筛选项 */
  filter?: ModelFilter;
  /** coze空间id */
  spaceID?: Int64;
  /** 因为coze暂时给不了rpc接口，所以后端需要拿到cookie去请求coze的前端接口 */
  cookie?: string;
  base?: base.Base;
}

export interface ListModelResponse {
  models?: Array<ml_flow_domain_model.Model>;
  cursorID?: string;
  hasMore?: boolean;
  total?: Int64;
  baseResp?: base.BaseResp;
}

export interface ModelContextRange {
  /** 上限，不传代表不设限 */
  upperBound?: number;
  /** 下限，不传代表不设限 */
  lowerBound?: number;
}

export interface ModelFilter {
  /** 模型tag过滤项，value中list内部各个元素在过滤时是or关系，各个key之间在过滤时是and关系 */
  modelFilterTags?: Partial<
    Record<ml_flow_domain_model.ModelFilterKey, Array<string>>
  >;
  /** 模型状态 */
  modelStatuses?: Array<ml_flow_domain_model.CommercialModelStatus>;
  /** 模型支持的上下文长度的范围 */
  modelContextRange?: ModelContextRange;
  /** 模型厂商 */
  modelVendors?: Array<string>;
  /** 名称关键字 */
  name?: string;
  /** 特殊场景 */
  modelFilterScene?: ml_flow_domain_model.ModelFilterScene;
}

export interface ModelUsage {
  promptInputToken?: Int64;
  promptOutputToken?: Int64;
  evaluationInputToken?: Int64;
  evaluationOutputToken?: Int64;
  date?: string;
}
/* eslint-enable */
