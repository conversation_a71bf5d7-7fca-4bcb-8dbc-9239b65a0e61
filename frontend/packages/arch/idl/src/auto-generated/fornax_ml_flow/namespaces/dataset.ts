/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum DatasetIOTaskStatus {
  Undefined = 0,
  /** 正在初始化 */
  Initializing = 1,
  /** 正在运行 */
  Running = 2,
  /** 成功完成 */
  Done = 3,
  /** 失败 */
  Failed = 4,
  /** 手动终止 */
  Terminated = 5,
  /** 成功完成，但有错误 */
  DoneWithError = 6,
}

export enum DatasetIOType {
  Import = 1,
  Export = 2,
}

export enum FileFormat {
  JSONL = 1,
  Parquet = 2,
  CSV = 3,
  /** 压缩格式 */
  ZIP = 100,
}

export enum StorageProvider {
  TOS = 1,
  HDFS = 2,
  VETOS = 3,
  ImageX = 4,
  /** 后端测试使用 */
  LocalFS = 5,
}

/** DatasetIOTask 数据集导入导出文件任务 */
export interface DatasetIOTask {
  id: string;
  spaceID: string;
  datasetID: string;
  file: StorageFile;
  ioType: DatasetIOType;
  option?: DatasetIOTaskOption;
  /** 运行数据, [10, 100) */
  status?: DatasetIOTaskStatus;
  progress?: DatasetIOTaskProgress;
  logs?: Array<TaskLog>;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  startedAt?: string;
  endedAt?: string;
}

export interface DatasetIOTaskOption {
  /** 覆盖数据集 */
  overwriteDataset?: boolean;
  /** 导出文件时，覆盖已有文件 */
  overwriteFile?: boolean;
}

export interface DatasetIOTaskProgress {
  /** 可空, 表示具体文件导入进度时, 值为文件名 */
  name?: string;
  /** 总量 */
  total?: Int64;
  /** 已处理数量 */
  processed?: Int64;
  /** 已成功处理的数量 */
  added?: Int64;
  /** 各文件的进度, 导入源为文件夹时使用 */
  fileProgresses?: Array<DatasetIOTaskProgress>;
}

export interface StorageFile {
  provider: StorageProvider;
  path: string;
  /** 数据文件的格式 */
  format?: FileFormat;
  /** 压缩包格式 */
  compressFormat?: FileFormat;
  /** path 为文件夹或压缩包时，数据文件列表, 服务端设置 */
  files?: Array<string>;
}

export interface TaskLog {
  content: string;
  level: string;
  timestamp: string;
  hidden: boolean;
}
/* eslint-enable */
