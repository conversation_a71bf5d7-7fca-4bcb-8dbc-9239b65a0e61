/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as datasetv2 from './datasetv2';
import * as datasetv2job from './datasetv2job';

export type Int64 = string | number;

export enum BatchInferDatasetType {
  DatasetV2 = 0,
  HDFS = 1,
  TOS = 2,
}

export enum BatchInferTaskStatus {
  Preparing = 0,
  Launching = 1,
  Inferring = 2,
  Exporting = 3,
  Success = 4,
  Failed = 5,
  Terminated = 6,
}

export enum InputConfigType {
  Raw = 1,
}

export enum MerlinQuotaPoolType {
  /** 稳定资源 */
  Default = 0,
  /** 潮汐资源 */
  Hybrid = 1,
  /** 常混资源 */
  HybridShare = 2,
  /** 第三方资源ali */
  ALI = 3,
  /** 第三方资源hw */
  HW = 4,
  /** hw arm */
  HWARM = 5,
  /** 弹性售卖资源，随时可能被抢占 */
  Spot = 6,
  /** 可抢占的稳定资源，merlin seed离线推理不允许选这种资源 */
  Preemptible = 20,
}

export enum MerlinSeedDataProcessType {
  /** 普通生成任务 */
  RayDataset = 1,
  /** 多轮生成任务 */
  RayDatasetMultiround = 2,
}

export enum MerlinSeedModelType {
  HDFS = 1,
  ModelCard = 2,
}

export enum OutputConfigType {
  Raw = 1,
}

export enum Provider {
  /** GPTOpenAPI = 1 // GPT OpenAPI平台
火山方舟 */
  Maas = 2,
  /** BotEngine  = 3 // 暂时特指seed从bot_engine接入
merlin平台 */
  Merlin = 4,
  /** merlin-seed平台 */
  MerlinSeed = 5,
}

export enum TrainingMethod {
  LoRA = 1,
  Full = 2,
}

export enum TrainingType {
  SftFineTuning = 1,
}

export interface ArkModel {
  foundationModelName?: string;
  foundationModelVersion?: string;
  /** 如果是精调后的模型，这个id非空 */
  customModelID?: string;
  sftTaskID?: string;
  /** 训练类型 */
  trainingType?: TrainingType;
  /** 训练方法 */
  trainingMethod?: TrainingMethod;
}

export interface BatchInferDataset {
  datasetID?: string;
  inputConfig?: InputConfig;
  outputConfig?: OutputConfig;
  /** 默认是数据集v2，本期新增hdfs和tos */
  datasetType?: BatchInferDatasetType;
  /** 待推理的数据集hdfs路径 */
  hdfsPath?: string;
  /** 待推理的图片存储的文件夹路径 */
  imageHdfsPath?: string;
  /** 输出结果保存的hdfs路径 */
  outputHdfsPath?: string;
  /** 待推理的数据集的tos桶名 */
  tosBucketName?: string;
  /** 待推理的数据集的tos对象名称 */
  tosObjKey?: string;
  /** 输出结果保存的tos桶名 */
  outputTosBucketName?: string;
  /** 输出结果保存的tos对象名称 */
  outputTosObjKey?: string;
}

export interface BatchInferParam {
  temperature?: number;
  topP?: number;
  topK?: string;
  maxNewToken?: string;
  maxContextToken?: string;
  /** 推理次数 */
  inferTimes?: string;
  /** 推理批次大小 */
  batchSize?: string;
}

export interface BatchInferTask {
  /** 创建时不传 */
  id?: Int64;
  name?: string;
  desc?: string;
  batchInferParam?: BatchInferParam;
  provider?: Provider;
  providerInfo?: ProviderInfo;
  providerTaskID?: string;
  batchInferDatasets?: Array<BatchInferDataset>;
  ckptConfigs?: Record<string, CkptConfig>;
  status?: BatchInferTaskStatus;
  errCode?: string;
  errMsg?: string;
  ckptExecRes?: CkptExecResult;
  /** Fornax空间ID */
  spaceID?: string;
  /** 创建人ID */
  createdBy?: string;
  /** 创建时间，秒 */
  createdAt?: string;
  /** 更新人ID */
  updatedBy?: string;
  /** 更新时间，秒 */
  updatedAt?: string;
}

export interface CkptConfig {
  name?: string;
  maxRetryTime?: string;
  retryIntervalMilliSecond?: string;
  /** 重试时间间隔的变化方式，支持固定间隔和随时间渐进式变化 */
  retryIntervalChangeType?: string;
  /** 每重试x次，重试时间间隔会发生变化 */
  retryIntervalChangeTimes?: string;
  /** 每次重试时间间隔变化的步长，单位为ms，可以为负数 */
  retryIntervalChangeStep?: string;
  customConfigs?: Record<string, string>;
  /** 触发下一个checkpoint的时间间隔，单位为ms */
  triggerNextCkptIntervalMilliSecond?: string;
}

export interface CkptExecResult {
  /** 上传数据集到HDFS  [1,10)
数据集上传到hdfs的地址 */
  datasetHdfsAddress?: string;
  /** 上传进度，长度与 batchInferDatasets 相同 */
  datasetUploadProgresses?: Array<InferUploadProgress>;
  /** 传入离线推理任务的数据列名 */
  inferTaskColumnName?: string;
  /** item id 所在的列名 */
  itemIDColumnName?: string;
  /** dataset id 所在的列名 */
  datasetIDColumnName?: string;
  /** 数据集上传到 tos 的 bucket */
  datasetTosBucket?: string;
  /** 数据集上传到 tos 的object key */
  datasetTosObjectKey?: string;
  /** 创建推理任务 [10,20)
merlin seed离线推理任务实际上就是在merlin任务用例外包了一层，在这里记录这个merlin任务用例id */
  merlinJobID?: string;
  /** merlin seed离线推理任务链接 */
  merlinDataProcessTaskUrl?: string;
  /** 第三方平台任务链接 */
  providerTaskUrl?: string;
  /** 检查推理任务状态 [20,30)
merlin推理任务状态 */
  merlinDataProcessInstanceStatusGroup?: string;
  /** merlin推理任务状态详情 */
  merlinDataProcessInstanceStatus?: string;
  /** merlin 任务实例是否终止 */
  merlinJobTerminated?: boolean;
  /** merlin 任务实例状态 */
  merlinJobStatus?: string;
  /** merlin 任务实例错误信息 */
  merlinJobErrMsg?: string;
  /** 有merlin 任务实例上报的错误信息 */
  merlinJobUploadedErrMsg?: string;
  /** 方舟任务状态 */
  arkJobStatus?: string;
  /** 方舟任务状态说明 */
  arkJobDetail?: string;
  /** 方舟任务状态更新时间 */
  arkJobStatusUpdateTimeMs?: Int64;
  /** 导出推理结果 [30,40)
推理结果输出的列名 */
  inferResultColumnName?: string;
  /** 推理结果保存的hdfs地址，可能为文件夹 */
  inferResultHdfsAddress?: string;
  /** 结果是否导出完成 */
  resultExported?: boolean;
  /** 推理结果导出进度 */
  inferExportProgress?: InferExportProgress;
}

export interface InferExportProgress {
  /** 推理结果的存储平台 */
  provider?: datasetv2.StorageProvider;
  /** 推理结果的文件格式，如果结果的路径是文件夹，那么只导出这个文件夹下所有该格式的文件 */
  fileFormat?: datasetv2job.FileFormat;
  /** 导出结果的地址，可以是文件夹 */
  path?: string;
  /** 子进度（所有结果文件的进度） */
  subProgresses?: Array<InferExportProgress>;
  /** 进度信息
总行数 */
  total?: Int64;
  /** 已处理的行数 */
  processed?: Int64;
  /** 添加成功的行数 */
  added?: Int64;
  /** 运行日志
错误信息，子进度中无该字段 */
  errors?: Array<datasetv2.ItemErrorGroup>;
}

export interface InferUploadProgress {
  /** 需要上传到的存储平台 */
  provider?: datasetv2.StorageProvider;
  /** 需要上传的文件格式 */
  fileFormat?: datasetv2job.FileFormat;
  /** 上传的地址 */
  path?: string;
  /** 子进度（所有上传文件的进度） */
  subProgresses?: Array<InferExportProgress>;
  /** 需要上传的数据集 ID，在子进度中无该字段 */
  datasetID?: Int64;
  /** 当前数据集上传的 cursor，在子进度中无该字段 */
  cursor?: string;
  /** 进度信息
总行数 */
  total?: Int64;
  /** 已处理的行数 */
  processed?: Int64;
}

export interface InputConfig {
  type?: InputConfigType;
  /** 作为输入的数据集列名 */
  rawInput?: string;
  /** 每行数据的唯一标识的列名 */
  itemID?: string;
}

export interface MerlinModel {
  /** 多记录一些额外信息，比如是基座模型还是训练后的模型，如果是训练后的模型，那么还需要记录训练任务的id
模型文件保存地址 */
  hdfsPath?: string;
  /** 基座模型名称 */
  foundationModelName?: string;
  /** 3: optional string foundationModelFamily // 基座模型家族
4: optional string foundationModelVendor // 基座模型厂商
5: optional string foundationModelDisplayName // 基座模型显示名称
6: optional i64 foundationModelVersionUpdateTimeInMs // 基座模型版本更新时间
训练任务id，当训练任务非0时，说明此次批量推理使用的模型是训练后的产物 */
  sftTaskID?: string;
  /** 训练产物的名称(此时要求该产物已经导出到merlin的模型仓库中，因此这个name就是merlin的某个模型仓库名称) */
  merlinModelName?: string;
  /** merlin的某个模型仓库的版本 */
  merlinModelVersion?: string;
  /** 训练类型 */
  trainingType?: TrainingType;
  /** 训练方法 */
  trainingMethod?: TrainingMethod;
}

export interface MerlinResource {
  type?: MerlinQuotaPoolType;
  /** 用户组id，暂时只支持1个 */
  groupIDs?: Array<string>;
  /** 集群id */
  clusterID?: string;
  /** 是否使用可占用资源 */
  preemptible?: boolean;
  /** 角色配置，暂时只支持1个 */
  roles?: Array<MerlinResourceRole>;
  /** key是用户组id，val是用户组名称 */
  groupNames?: Record<Int64, string>;
  /** 集群名称 */
  clusterName?: string;
}

export interface MerlinResourceRole {
  /** 实例数，必填1 */
  num?: number;
  /** 虚拟gpu型号 */
  gpuv?: string;
  /** gpu数量 */
  gpu?: number;
  /** cpu数量 */
  cpu?: number;
  /** 内存大小，单位是MB */
  memory?: number;
}

export interface MerlinSeedModel {
  merlinSeedModelType?: MerlinSeedModelType;
  hdfsAddress?: string;
  modelCardID?: string;
  modelCardName?: string;
  modelParamConfigType?: string;
  tokenizerAddress?: string;
  networkConfig?: string;
  quantConfig?: string;
}

export interface ModelCard {
  id?: string;
  name?: string;
}

export interface OutputConfig {
  type?: OutputConfigType;
  /** 输出会保存在这个数据集列名 */
  rawOutput?: string;
  /** 完整输出会保存在这个数据集列名（目前只支持方舟/开源模型/训练后的开源模型） */
  completeOutput?: string;
}

export interface ProviderInfo {
  provider?: Provider;
  merlinSeedModel?: MerlinSeedModel;
  merlinResource?: MerlinResource;
  merlinCustomEnvs?: Record<string, string>;
  merlinSeedDataProcessType?: MerlinSeedDataProcessType;
  /** open source model */
  merlinModel?: MerlinModel;
  /** 方舟model */
  arkModel?: ArkModel;
  /** 方舟项目名称 */
  arkProjectName?: string;
}
/* eslint-enable */
