/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as tag from './tag';
import * as datasetv2 from './datasetv2';

export type Int64 = string | number;

export interface ArchiveOptionTagRequest {
  spaceID: string;
  tagKeyID: string;
  name: string;
  description?: string;
  base?: base.Base;
}

export interface ArchiveOptionTagResponse {
  baseResp?: base.BaseResp;
}

export interface BatchUpdateTagStatusRequest {
  spaceID: string;
  tagKeyIDs: Array<string>;
  status: tag.TagStatus;
  base?: base.Base;
}

export interface BatchUpdateTagStatusResponse {
  errInfo?: Record<Int64, string>;
  baseResp?: base.BaseResp;
}

export interface CreateTagRequest {
  spaceID: string;
  tagKeyName: string;
  tagType: tag.TagType;
  version: string;
  description?: string;
  tagValues: Array<tag.TagValue>;
  base?: base.Base;
}

export interface CreateTagResponse {
  tagKeyID?: string;
  baseResp?: base.BaseResp;
}

/** 每一个元素代表柱状图中的一个柱 */
export interface DistributeBucket {
  /** 代表没打标签的对象，若为true则只需要看count，代表没被打标签的个数 */
  isEmpty?: boolean;
  tagValueID?: string;
  tagKeyName?: string;
  tagValueName?: string;
  /** 数量 */
  count?: Int64;
}

export interface GetTagDetailRequest {
  spaceID: string;
  tagKeyID: string;
  base?: base.Base;
}

export interface GetTagDetailResponse {
  tags?: Array<tag.TagInfo>;
  baseResp?: base.BaseResp;
}

export interface GetTagSpecRequest {
  spaceID: string;
}

export interface GetTagSpecResponse {
  /** 最大高度 */
  maxHeight?: Int64;
  /** 最大宽度(一层最多有多少个) */
  maxWidth?: Int64;
  /** 最多个数(各层加一起总数) */
  maxTotal?: Int64;
}

export interface QueryFieldDistributeRequest {
  spaceID: string;
  datasetID: string;
  datasetVersion?: string;
  /** 按照列洞察的时候，列的字段 */
  fieldKeys?: Array<string>;
  base?: base.Base;
}

export interface QueryFieldDistributeResponse {
  fieldsDistributeMap?: Record<string, Array<DistributeBucket>>;
  baseResp?: base.BaseResp;
}

export interface SearchTagsRequest {
  spaceID: string;
  status?: Array<tag.TagStatus>;
  tagKeyNameLike?: string;
  createdBys?: Array<string>;
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  base?: base.Base;
}

export interface SearchTagsResponse {
  tags?: Array<tag.TagInfo>;
  nextCursor?: string;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface UpdateTagRequest {
  spaceID: string;
  tagKeyID: string;
  /** 三段式版本 */
  version: string;
  tagKeyName?: string;
  description?: string;
  tagType?: tag.TagType;
  tagValues?: Array<tag.TagValue>;
  base?: base.Base;
}

export interface UpdateTagResponse {
  baseResp?: base.BaseResp;
}
/* eslint-enable */
