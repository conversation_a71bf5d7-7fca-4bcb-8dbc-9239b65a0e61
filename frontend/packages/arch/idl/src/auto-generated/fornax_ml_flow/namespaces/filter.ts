/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface FieldMeta {
  /** 字段类型 */
  field_type: string;
  /** 当前字段支持的操作类型 */
  query_types: Array<string>;
  display_name: string;
  /** 支持的可选项 */
  field_options?: FieldOptions;
  /** 当前字段在schema中是否存在 */
  exist?: boolean;
}

export interface FieldMetaInfoData {
  /** 字段元信息 */
  field_metas: Record<string, FieldMeta>;
}

export interface FieldOptions {
  i32?: Array<number>;
  i64?: Array<string>;
  f64?: Array<number>;
  string?: Array<string>;
  obj?: Array<ObjectFieldOption>;
}

export interface Filter {
  queryAndOr?: string;
  filterFields: Array<FilterField>;
}

export interface FilterField {
  field_name: string;
  field_type: string;
  values?: Array<string>;
  query_type?: string;
  query_and_or?: string;
  sub_filter?: Filter;
}

export interface ObjectFieldOption {
  id: Int64;
  displayName: string;
}
/* eslint-enable */
