/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as datasetv2similarity from './datasetv2similarity';
import * as tag from './tag';

export type Int64 = string | number;

export enum ContentType {
  /** 基础类型 */
  Text = 1,
  Image = 2,
  Audio = 3,
  Video = 4,
  /** 图文混排 */
  MultiPart = 100,
}

export enum DatasetCategory {
  /** 数据集 */
  General = 1,
  /** 训练集 (暂无) */
  Training = 2,
  /** 验证集 (暂无) */
  Validation = 3,
  /** 评测集 (暂无) */
  Evaluation = 4,
}

export enum DatasetLockReason {
  Undefined = 0,
  /** 众包标注任务正在运行 */
  CrowdsourcingAnnotateJobRunning = 1,
}

export enum DatasetStatus {
  Available = 1,
  Deleted = 2,
  Expired = 3,
  Importing = 4,
  Exporting = 5,
  Indexing = 6,
}

export enum DatasetVisibility {
  /** 所有空间可见 */
  Public = 1,
  /** 当前空间可见 */
  Space = 2,
  /** 用户不可见 */
  System = 3,
}

export enum FieldDisplayFormat {
  PlainText = 1,
  Markdown = 2,
  JSON = 3,
  YAML = 4,
  Code = 5,
  SingleOption = 6,
}

export enum FieldStatus {
  Available = 1,
  Deleted = 2,
}

export enum FieldTransformationType {
  /** 移除未在当前列的 jsonSchema 中定义的字段（包括 properties 和 patternProperties），仅在列类型为 struct 时有效 */
  RemoveExtraFields = 1,
}

export enum ItemErrorType {
  /** schema 不匹配 */
  MismatchSchema = 1,
  /** 空数据 */
  EmptyData = 2,
  /** 单条数据大小超限 */
  ExceedMaxItemSize = 3,
  /** 数据集容量超限 */
  ExceedDatasetCapacity = 4,
  /** 文件格式错误 */
  MalformedFile = 5,
  /** 包含非法内容 */
  IllegalContent = 6,
  /** 缺少必填字段 */
  MissingRequiredField = 7,
  /** 数据嵌套层数超限 */
  ExceedMaxNestedDepth = 8,
  /** 数据转换失败 */
  TransformItemFailed = 9,
  /** system error */
  InternalError = 100,
  /** 清空数据集失败 */
  ClearDatasetFailed = 101,
  /** 读写文件失败 */
  RWFileFailed = 102,
}

export enum SchemaKey {
  String = 1,
  Integer = 2,
  Float = 3,
  Bool = 4,
  Message = 5,
  /** 单选 */
  SingleChoice = 6,
}

export enum SecurityLevel {
  L1 = 1,
  L2 = 2,
  L3 = 3,
  L4 = 4,
}

export enum SnapshotStatus {
  Unstarted = 1,
  InProgress = 2,
  Completed = 3,
  Failed = 4,
}

export enum StorageProvider {
  TOS = 1,
  VETOS = 2,
  HDFS = 3,
  ImageX = 4,
  /** 后端内部使用 */
  Abase = 100,
  RDS = 101,
  LocalFS = 102,
}

/** Dataset 数据集实体 */
export interface Dataset {
  id: string;
  appID?: number;
  spaceID: string;
  schemaID: string;
  name?: string;
  description?: string;
  status?: DatasetStatus;
  /** 业务场景分类 */
  category?: DatasetCategory;
  /** 提供给上层业务定义数据集类别 */
  bizCategory?: string;
  /** 当前数据集结构 */
  schema?: DatasetSchema;
  /** 密级 */
  securityLevel?: SecurityLevel;
  /** 可见性 */
  visibility?: DatasetVisibility;
  /** 规格限制 */
  spec?: DatasetSpec;
  /** 数据集功能开关 */
  features?: DatasetFeatures;
  /** 最新的版本号 */
  latestVersion?: string;
  /** 下一个的版本号 */
  nextVersionNum?: Int64;
  /** 数据条数 */
  itemCount?: string;
  /** 通用信息 */
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  expiredAt?: string;
  /** DTO 专用字段
是否有未提交的修改 */
  changeUncommitted?: boolean;
  /** 数据集锁定信息 */
  lockInfo?: Array<DatasetLockInfo>;
}

export interface DatasetFeatures {
  /** 变更 schema */
  editSchema?: boolean;
  /** 多轮数据 */
  repeatedData?: boolean;
  /** 多模态 */
  multiModal?: boolean;
}

/** DatasetItem 数据内容 */
export interface DatasetItem {
  /** 主键 ID，创建时可以不传 */
  id?: string;
  /** 冗余 app ID，创建时可以不传 */
  appID?: number;
  /** 冗余 space ID，创建时可以不传 */
  spaceID?: string;
  /** 所属的 data ID，创建时可以不传 */
  datasetID?: string;
  /** 插入时对应的 schema ID，后端根据 req 参数中的 datasetID 自动填充 */
  schemaID?: string;
  /** 数据在当前数据集内的唯一 ID，不随版本发生改变 */
  itemID?: string;
  /** 数据插入的幂等 key */
  itemKey?: string;
  /** 数据内容 */
  data?: Array<FieldData>;
  /** 多轮数据内容，与 data 互斥 */
  repeatedData?: Array<ItemData>;
  /** 通用信息 */
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  /** DTO 专用字段
数据（data 或 repeatedData）是否省略。列表查询 item 时，特长的数据内容不予返回，可通过单独 Item 接口获取内容 */
  dataOmitted?: boolean;
}

export interface DatasetLockInfo {
  reason?: DatasetLockReason;
  /** 众包标注任务ID */
  crowdsourcingAnnotateJobID?: string;
}

/** DatasetSchema 数据集 Schema，包含数据集列的类型限制等信息 */
export interface DatasetSchema {
  /** 主键 ID，创建时可以不传 */
  id?: string;
  /** schema 所在的空间 ID，创建时可以不传 */
  appID?: number;
  /** schema 所在的空间 ID，创建时可以不传 */
  spaceID?: string;
  /** 数据集 ID，创建时可以不传 */
  datasetID?: string;
  /** 数据集列约束 */
  fields?: Array<FieldSchema>;
  /** 是否不允许编辑 */
  immutable?: boolean;
  /** 通用信息 */
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  updateVersion?: string;
}

export interface DatasetSpec {
  /** 条数上限 */
  maxItemCount?: string;
  /** 字段数量上限 */
  maxFieldCount?: number;
  /** 单条数据字数上限 */
  maxItemSize?: string;
  /** 单条 array/struct 数据嵌套上限 */
  maxItemDataNestedDepth?: number;
}

/** DatasetVersion 数据集版本元信息，不包含数据本身 */
export interface DatasetVersion {
  id: string;
  appID?: number;
  spaceID: string;
  datasetID: string;
  schemaID: string;
  /** 展示的版本号，SemVer2 三段式 */
  version?: string;
  /** 后端记录的数字版本号，从 1 开始递增 */
  versionNum?: string;
  /** 版本描述 */
  description?: string;
  /** marshal 后的版本保存时的数据集元信息，不包含 schema */
  datasetBrief?: string;
  /** 数据条数 */
  itemCount?: string;
  /** 当前版本的快照状态 */
  snapshotStatus?: SnapshotStatus;
  /** 通用信息 */
  createdBy?: string;
  createdAt?: string;
  /** 版本禁用的时间 */
  disabledAt?: string;
  updatedBy?: string;
  updatedAt?: string;
}

export interface FieldData {
  key?: string;
  /** 字段名，写入 Item 时 key 与 name 提供其一即可，同时提供时以 key 为准 */
  name?: string;
  contentType?: ContentType;
  content?: string;
  /** 外部存储信息 */
  attachments?: Array<ObjectStorage>;
  /** 数据的渲染格式 */
  format?: FieldDisplayFormat;
  /** 图文混排时，图文内容 */
  parts?: Array<FieldData>;
  /** 这条数据生成traceID */
  traceID?: string;
  /** 是否生成失败 */
  genFail?: boolean;
  /** 标签回流失败后的展示名称 */
  fallbackDisplayName?: string;
}

export interface FieldSchema {
  /** 数据集 schema 版本变化中 key 唯一，新建时自动生成，不需传入 */
  key?: string;
  /** 展示名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 类型，如 文本，图片，etc. */
  contentType?: ContentType;
  /** 默认渲染格式，如 code, json, etc. */
  defaultFormat?: FieldDisplayFormat;
  /** 对应的内置 schema */
  schemaKey?: SchemaKey;
  /** [20,50) 内容格式限制相关
文本内容格式限制，格式为 JSON schema，协议参考 https://json-schema.org/specification */
  textSchema?: string;
  /** 多模态规格限制 */
  multiModelSpec?: MultiModalSpec;
  /** 当前列的数据是否必填，不填则会报错 */
  isRequired?: boolean;
  /** 用户是否不可见 */
  hidden?: boolean;
  /** 当前列的状态，创建/更新时可以不传 */
  status?: FieldStatus;
  /** 是否开启相似度索引 */
  similaritySearchConfig?: SimilaritySearchConfig;
  /** 质量分配置 */
  qualityScoreConfig?: QualityScoreConfig;
  /** 标签字段配置 */
  tagFieldConfig?: TagFieldConfig;
  /** 默认的预置转换配置，目前在数据校验后执行 */
  defaultTransformations?: Array<FieldTransformationConfig>;
}

export interface FieldTransformationConfig {
  /** 预置的转换类型 */
  transType?: FieldTransformationType;
  /** 当前转换配置在这一列上的数据及其嵌套的子结构上均生效 */
  global?: boolean;
}

export interface FileUploadToken {
  accessKeyID?: string;
  secretAccessKey?: string;
  sessionToken?: string;
  expiredTime?: string;
  currentTime?: string;
}

export interface ItemData {
  id?: string;
  data?: Array<FieldData>;
}

export interface ItemErrorDetail {
  message?: string;
  /** 单条错误数据在输入数据中的索引。从 0 开始，下同 */
  index?: number;
  /** [startIndex, endIndex] 表示区间错误范围, 如 ExceedDatasetCapacity 错误时 */
  startIndex?: number;
  endIndex?: number;
  /** ItemErrorType=MismatchSchema, key 为 FieldSchema.name, value 为错误信息 */
  messagesByField?: Record<string, string>;
}

export interface ItemErrorGroup {
  type?: ItemErrorType;
  summary?: string;
  /** 错误条数 */
  errorCount?: number;
  /** 批量写入时，每类错误至多提供 5 个错误详情；导入任务，至多提供 10 个错误详情 */
  details?: Array<ItemErrorDetail>;
}

export interface MultiModalSpec {
  /** 文件数量上限 */
  maxFileCount?: Int64;
  /** 文件大小上限 */
  maxFileSize?: Int64;
  /** 文件格式 */
  supportedFormats?: Array<string>;
}

export interface ObjectStorage {
  provider?: StorageProvider;
  name?: string;
  uri?: string;
  url?: string;
  thumbURL?: string;
}

export interface OrderBy {
  /** 排序字段 */
  field?: string;
  /** 升序，默认倒序 */
  isAsc?: boolean;
}

/** 质量分配置 */
export interface QualityScoreConfig {
  /** 列是否为质量分 */
  enabled?: boolean;
}

/** 相似度算法的配置 */
export interface SimilaritySearchConfig {
  /** 是否开启相似度索引 */
  enabled?: boolean;
  /** 配置了哪个相似度算法 */
  similarityAlgorithm?: datasetv2similarity.SimilarityAlgorithm;
  /** 所使用的相似度模型 */
  embeddingType?: datasetv2similarity.EmbeddingModel;
}

export interface TagFieldConfig {
  /** tag配置 */
  tagInfo?: tag.TagInfo;
}
/* eslint-enable */
