/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as annotation_job from './annotation_job';
import * as base from './base';

export type Int64 = string | number;

export interface CreateCrowdsourcingAnnotateJobReq {
  spaceID: string;
  /** 任务名称 */
  name: string;
  /** 任务描述 */
  description?: string;
  /** 标注任务平台 */
  platform: annotation_job.CrowdsourcingAnnotationPlatform;
  /** AIDP相关配置 */
  aidpConfig?: annotation_job.AIDPConfig;
  /** 送标配置 */
  inputConfig?: annotation_job.CrowdsourcingAnnotateInputConfig;
  /** 取标配置 */
  outputConfig?: annotation_job.CrowdsourcingAnnotateOutputConfig;
  base?: base.Base;
}

export interface CreateCrowdsourcingAnnotateJobResp {
  jobID?: string;
  baseResp?: base.BaseResp;
}

export interface DeleteCrowdsourcingAnnotateJobReq {
  spaceID: string;
  datasetID: string;
  jobID: string;
  base?: base.Base;
}

export interface DeleteCrowdsourcingAnnotateJobResp {
  baseResp?: base.BaseResp;
}

export interface GetAIDPTaskConfigReq {
  spaceID: string;
  taskID: string;
  platform: annotation_job.CrowdsourcingAnnotationPlatform;
  base?: base.Base;
}

export interface GetAIDPTaskConfigResp {
  flowConfig?: annotation_job.AIDPTaskFlowConfig;
  baseResp?: base.BaseResp;
}

export interface GetAnswerSchemaReq {
  spaceID: string;
  templateID: string;
  platform: annotation_job.CrowdsourcingAnnotationPlatform;
  base?: base.Base;
}

export interface GetAnswerSchemaResp {
  schema?: string;
  baseResp?: base.BaseResp;
}

export interface ListAuthorizedAIDPTasksReq {
  spaceID: string;
  platform: annotation_job.CrowdsourcingAnnotationPlatform;
  /** 搜索关键字 */
  keyword?: string;
  base?: base.Base;
}

export interface ListAuthorizedAIDPTasksResp {
  taskSummaries?: Array<annotation_job.AIDPTaskSummary>;
  aidpUserID?: string;
  baseResp?: base.BaseResp;
}

export interface ListCrowdsourcingAnnotateJobReq {
  spaceID: string;
  searchConfig?: annotation_job.SearchConfig;
  page?: number;
  pageSize?: number;
  base?: base.Base;
}

export interface ListCrowdsourcingAnnotateJobResp {
  tasks?: Array<annotation_job.CrowdsourcingAnnotateJob>;
  total?: number;
  baseResp?: base.BaseResp;
}

export interface RefreshCrowdsourcingAnnotateJobReq {
  spaceID: string;
  jobID: string;
  base?: base.Base;
}

export interface RefreshCrowdsourcingAnnotateJobResp {
  job?: annotation_job.CrowdsourcingAnnotateJob;
  baseResp?: base.BaseResp;
}

export interface TerminateCrowdsourcingAnnotateJobReq {
  spaceID: string;
  datasetID: string;
  jobID: string;
  base?: base.Base;
}

export interface TerminateCrowdsourcingAnnotateJobResp {
  baseResp?: base.BaseResp;
}
/* eslint-enable */
