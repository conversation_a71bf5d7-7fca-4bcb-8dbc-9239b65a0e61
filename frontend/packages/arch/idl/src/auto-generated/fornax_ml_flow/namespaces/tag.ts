/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ChangeTargetType {
  /** tag */
  Tag = 1,
  /** tag name */
  TagName = 2,
  /** tag description */
  TagDescription = 3,
  /** tag status */
  TagStatus = 4,
  /** tag type */
  TagType = 5,
  /** tag value name */
  TagValueName = 6,
  /** tag value status */
  TagValueStatus = 7,
}

export enum OperationType {
  /** 创建 */
  Create = 1,
  /** 更新 */
  Update = 2,
  /** 删除 */
  Delete = 3,
}

export enum TagStatus {
  /** 启用 */
  Active = 1,
  /** 禁用 */
  Inactive = 2,
  /** 弃用,旧版本状态 */
  Deprecated = 99,
}

export enum TagType {
  /** 标签类型 */
  Tag = 1,
  /** 单选类型 */
  Option = 2,
}

export interface ChangeLog {
  /** 变更的属性 */
  target?: ChangeTargetType;
  /** 变更类型: create, update, delete */
  operation?: OperationType;
  /** 变更前的值 */
  beforValue?: string;
  /** 变更后的值 */
  afterValue?: string;
  /** 变更属性的值：如果是标签选项变更，该值为变更属选项值名字 */
  targetValue?: string;
}

export interface TagInfo {
  ID?: string;
  appID?: number;
  spaceID?: string;
  /** 数字版本号 */
  versionNum?: number;
  /** SemVer 三段式版本号 */
  version?: string;
  /** tag key id */
  tagKeyID?: string;
  /** tag key name */
  tagKeyName?: string;
  /** 描述 */
  description?: string;
  /** 状态，启用active、禁用inactive、弃用deprecated(最新版之前的版本的状态) */
  status?: TagStatus;
  /** 类型: tag: 标签管理中的标签类型; option: 临时单选类型 */
  tagType?: TagType;
  parentTagKeyID?: string;
  /** 标签值 */
  tagValues?: Array<TagValue>;
  /** 变更历史 */
  changeLogs?: Array<ChangeLog>;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
}

export interface TagValue {
  /** 创建时不传 */
  ID?: string;
  /** 创建时不传 */
  appID?: number;
  /** 创建时不传 */
  spaceID?: string;
  /** 创建时不传 */
  tagKeyID?: string;
  /** 创建时不传 */
  tagValueID?: string;
  /** 标签值 */
  tagValueName?: string;
  /** 描述 */
  description?: string;
  /** 状态，启用active、禁用inactive、弃用deprecated(最新版之前的版本的状态) */
  status?: TagStatus;
  /** 数字版本号 */
  versionNum?: number;
  /** 父标签选项的ID */
  parentValueID?: string;
  /** 子标签 */
  children?: Array<TagValue>;
  /** 是否是系统标签而非用户标签 */
  isSystem?: boolean;
  createdBy?: string;
  createAt?: string;
  updatedBy?: string;
  updatedAt?: string;
}
/* eslint-enable */
