/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as datasetv2 from './datasetv2';
import * as datasetv2similarity from './datasetv2similarity';

export type Int64 = string | number;

export enum FileFormat {
  JSONL = 1,
  Parquet = 2,
  CSV = 3,
  /** [100, 200) 压缩格式 */
  ZIP = 100,
}

export enum ImportConfirmType {
  NotConfirmed = 0,
  ConfirmedDuplicated = 1,
  ConfirmedNotDuplicated = 2,
}

/** 通用任务状态 */
export enum JobStatus {
  Undefined = 0,
  /** 待处理 */
  Pending = 1,
  /** 处理中 */
  Running = 2,
  /** 已完成 */
  Completed = 3,
  /** 失败 */
  Failed = 4,
  /** 已取消 */
  Cancelled = 5,
}

/** 通用任务类型 */
export enum JobType {
  ImportFromFile = 1,
  ExportToFile = 2,
  ExportToDataset = 3,
}

export enum SourceType {
  File = 1,
  Dataset = 2,
}

export interface DatasetIODataset {
  spaceID?: string;
  datasetID: string;
  versionID?: string;
  /** 数据集详情，在接口上返回，不写入 */
  dataset?: datasetv2.Dataset;
  /** 版本详情，在接口上返回，不写入 */
  version?: datasetv2.DatasetVersion;
}

export interface DatasetIOEndpoint {
  file?: DatasetIOFile;
  dataset?: DatasetIODataset;
}

export interface DatasetIOFile {
  provider: datasetv2.StorageProvider;
  path: string;
  /** 数据文件的格式 */
  format?: FileFormat;
  /** 压缩包格式 */
  compressFormat?: FileFormat;
  /** path 为文件夹或压缩包时，数据文件列表, 服务端设置 */
  files?: Array<string>;
  /** 原始的文件名，创建文件时由前端写入。为空则与 path 保持一致 */
  originalFileName?: string;
}

/** DatasetIOJob 数据集导入导出任务 */
export interface DatasetIOJob {
  id: string;
  appID?: number;
  spaceID: string;
  /** 导入导出到文件时，为数据集 ID；数据集间转移时，为目标数据集 ID */
  datasetID: string;
  jobType: JobType;
  source: DatasetIOEndpoint;
  target: DatasetIOEndpoint;
  /** 字段映射 */
  fieldMappings?: Array<FieldMapping>;
  option?: DatasetIOJobOption;
  /** 运行数据, [20, 100) */
  status?: JobStatus;
  progress?: DatasetIOJobProgress;
  errors?: Array<datasetv2.ItemErrorGroup>;
  /** 通用信息 */
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  startedAt?: string;
  endedAt?: string;
}

export interface DatasetIOJobOption {
  /** 覆盖数据集，仅在导入任务中生效 */
  overwriteDataset?: boolean;
  /** 需要按照手动打标的taskID结果导入，被确认无需导入的不会被导入，仅在导入任务中生效 */
  jobID?: Int64;
}

export interface DatasetIOJobProgress {
  /** 总量 */
  total?: Int64;
  /** 已处理数量 */
  processed?: Int64;
  /** 已成功处理的数量 */
  added?: Int64;
  /** 已跳过的数量 */
  skipped?: Int64;
  /** 下一个扫描的游标，在数据源为数据集时生效 */
  cursor?: string;
  /** 子任务
可空, 表示子任务的名称 */
  name?: string;
  /** 子任务的进度 */
  subProgresses?: Array<DatasetIOJobProgress>;
}

export interface FieldMapping {
  source: string;
  target: string;
}

export interface ItemDeduplicateJob {
  id: string;
  spaceID: string;
  datasetID: string;
  /** 导入文件需要的数据 */
  jobType?: JobType;
  source?: DatasetIOEndpoint;
  target?: DatasetIOEndpoint;
  /** 字段映射 */
  fieldMappings?: Array<FieldMapping>;
  option?: DatasetIOJobOption;
  /** job信息
如果status=Completed,则表明已经处理完成 */
  status?: JobStatus;
  /** 任务当时的简要信息，冗余存储 */
  itemDedupJobBrief?: string;
  /** 根据哪一列去重 */
  fieldKey?: string;
  /** 去重算法 */
  similarityAlgorithm?: datasetv2similarity.SimilarityAlgorithm;
  /** 阈值 */
  threshold?: Int64;
  /** job中需要处理的总数据，不跟随筛选条件变动 */
  jobTotal?: Int64;
  /** 已确认的重复条数，不跟随筛选条件变动 */
  confirmedDedupItemsCount?: Int64;
  /** 已确认的不重复条数，不跟随筛选条件变动 */
  confirmedNotDedupItemsCount?: Int64;
  /** 错误信息，当 JobStatus=Failed时使用 */
  error?: string;
  /** 去重列表信息
去重列表的内容 */
  pairs?: Array<ItemDeduplicatePair>;
  /** pairs总条数，跟随筛选条件变动 */
  total?: Int64;
  /** 通用信息 */
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  startedAt?: string;
  endedAt?: string;
}

export interface ItemDeduplicatePair {
  id: string;
  /** 本条主键 */
  uniqKey: string;
  /** 新导入的内容 */
  newItem?: datasetv2.DatasetItem;
  /** 可能重复的内容 */
  items?: Array<SuspectedDupItemInfo>;
  /** 是否确认 */
  importConfirmType?: ImportConfirmType;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
}

export interface SuspectedDupItemInfo {
  /** 行内容 */
  item?: datasetv2.DatasetItem;
  /** 相似度评分 */
  score?: Int64;
}
/* eslint-enable */
