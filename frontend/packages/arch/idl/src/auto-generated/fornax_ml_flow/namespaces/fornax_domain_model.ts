/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ByteTreeNodeResourceProvider {
  TCE = 1,
  TCC = 2,
  Bernard = 3,
}

export enum DataImportMode {
  Undefined = 0,
  /** 追加 */
  Append = 1,
  /** 覆盖 */
  Overwrite = 2,
}

export enum DataSourceType {
  Undefined = 0,
  TOS = 1,
}

export enum Family {
  Undefined = 0,
  GPT = 1,
  Seed = 2,
  <PERSON> = 3,
  <PERSON> = 4,
  <PERSON> = 5,
  <PERSON>chuan = 6,
  <PERSON>wen = 7,
  <PERSON><PERSON> = 8,
  SkyLark = 9,
  <PERSON>shot = 10,
  Minimax = 11,
  Doubao = 12,
  Baichuan2 = 13,
  DeepSeekV2 = 14,
  DeepSeekCoderV2 = 15,
  DeepseekCoder = 16,
  InternLM2_5 = 17,
  Qwen2 = 18,
  Qwen2_5 = 19,
  Qwen2_5_Coder = 20,
  MiniCPM = 21,
  MiniCPM3 = 22,
  ChatGLM3 = 23,
  Mistral = 24,
  Gemma = 25,
  Gemma2 = 26,
  InternVL2 = 27,
  InternVL2_5 = 28,
  DeepSeekV3 = 29,
  DeepSeekR1 = 30,
}

export enum IDC {
  HL = 1,
  LF = 2,
  LQ = 3,
  YG = 4,
  GL = 5,
  MALIVA = 101,
  SG1 = 201,
  MY = 202,
  MY2 = 203,
  MYCISB = 204,
}

export enum MerlinFramework {
  LLMServerPublic = 1,
  Laplace = 2,
  /** 电商团队专用协议，详见  */
  Mixinfer = 3,
}

export enum MerlinLLMInterface {
  MatxInference = 1,
}

export enum MerlinQuotaPoolType {
  /** 稳定资源 */
  Default = 0,
  /** 潮汐资源 */
  Hybrid = 1,
  /** 常混资源 */
  HybridShare = 2,
  /** 第三方资源ali */
  ALI = 3,
  /** 第三方资源hw */
  HW = 4,
  /** hw arm */
  HWARM = 5,
  /** 弹性售卖资源，随时可能被抢占 */
  Spot = 6,
  /** 可抢占的稳定资源 */
  Preemptible = 20,
}

export enum ModelFilterKey {
  ModelType = 1,
  ModelUserRight = 2,
  ModelFeature = 3,
  ModelFunction = 4,
  ModelScenario = 5,
  Custom = 20,
}

export enum ModelParamType {
  Unknown = 0,
  Float = 1,
  Int = 2,
  Boolean = 3,
  String = 4,
}

export enum ModelStatus {
  Undefined = 0,
  /** 健康可用 */
  Available = 1,
  /** 部署中 */
  Deploying = 2,
  /** 不可用（已下线） */
  Unavailable = 3,
  /** 下线中 */
  Offlining = 4,
}

export enum Provider {
  /** GPT OpenAPI平台 */
  GPTOpenAPI = 1,
  /** 火山方舟 */
  Maas = 2,
  /** 暂时特指seed从bot_engine接入 */
  BotEngine = 3,
  /** merlin平台 */
  Merlin = 4,
  /** merlin-seed平台 */
  MerlinSeed = 5,
}

export enum ProviderAccountType {
  AKSK = 1,
  APIKey = 2,
}

export enum Region {
  CN = 1,
  SG = 2,
  US = 3,
}

export enum RuntimeCustomParamType {
  Unknown = 0,
  StringList = 1,
  IntList = 2,
  FloatList = 3,
  String = 4,
  Int = 5,
  Float = 6,
  Bool = 7,
}

export enum RuntimeHookType {
  Unknown = 0,
  MsgPreHook = 1,
}

export enum SftTaskBaseModelType {
  Foundation = 0,
  Custom = 1,
}

export enum SftTaskErrCode {
  Default = 0,
  /** 前2位代表错误分类，后4位代表具体错误
10 通用错误 */
  InternalErr = 100000,
  /** 训练集上传到平台方时出错 */
  TrainingSetUploadedErr = 100001,
  /** 在平台方创建精调任务出错 */
  CreateProviderSftTaskErr = 100002,
  /** 20 Merlin任务实例整体报错 */
  MerlinTaskInternalError = 200000,
  /** gpu cpu mem 套餐配比错误 */
  MerlinGpuCpuMemRatioBad = 200001,
  /** 21 Merlin训练脚本主动上报的错误 */
  MerlinReportedInternalErr = 210000,
  MerlinOOM = 210001,
  /** 没找到模型训练产物 */
  MerlinNoModelGenerated = 210002,
  /** 30 火山方舟任务失败
方舟通用错误 */
  MaaSInternalErr = 300000,
  /** 验证集比例分割异常 */
  MaaSValidationSetSplitErr = 300001,
  /** 训练数据格式不符合预期 */
  MaaSTrainingSetNoValid = 300002,
  /** 业务方的火山账号没有开通模型服务 */
  MaaSAccountModelServiceIsNotActivated = 300003,
  /** 缺少必要的请求参数。请确认请求参数后重试。 */
  MaaSRequiredParamMissing = 300004,
  /** 请求参数值不合法。请检查参数值的正确性后重试。 */
  MaaSRequestParamInvalid = 300005,
  /** 对象的标签存在重复Key */
  MaaSDuplicateTags = 300006,
  /** 无法同时上传验证集和设置训练集取样为验证集百分比，不支持该操作 */
  MaaSNotSupportedToConfigureValidationSetAndPercentage = 300007,
  /** 您没有权限访问基础模型的配置，不支持该操作 */
  MaaSNotPermittedToAccessModel = 300008,
  /** 模型不支持该训练方法，不支持该操作 */
  MaaSModelNotSupportTheTrainingMethod = 300009,
  /** 基础模型的版本不支持该训练方法，不支持该操作 */
  MaaSFoundationModelNotSupportTheTrainingMethod = 300010,
  /** 您的账单已逾期，不支持该操作。请前往火山费用中心充值 */
  MaaSAccountBalanceOverdue = 300011,
  /** 未知错误，请稍后重试。如果多次尝试仍失败，请提交工单 */
  MaaSUnknownErr = 300012,
}

export enum SftTaskHyperParamType {
  Int = 1,
  Float = 2,
  String = 3,
  Bool = 4,
  Percentage = 5,
}

export enum SftTaskOutputExportType {
  /** 导出到新模型 */
  NewModel = 1,
  /** 导出到已有模型 */
  ExistModel = 2,
}

export enum SftTaskOutputStatus {
  /** 未导出 */
  Available = 1,
  /** 已导出 */
  Exported = 2,
  /** 已过期 */
  Expired = 3,
  /** 导出中 */
  Exporting = 4,
  /** 导出失败 */
  ExportFailed = 5,
}

export enum SftTaskResourceMerlinVersion {
  RemoveAnyGPU = 1,
}

export enum SftTaskRunEventType {
  Undefined = 0,
  ErrorOccured = 1,
  UploadCkpt = 2,
  ReportMetricsURL = 3,
  ReportProgress = 5,
  ReportTrainingStart = 6,
  ReportTrainingFinished = 7,
}

export enum SftTaskStatusPhase {
  Preprocessing = 0,
  Queued = 1,
  Deploying = 2,
  Running = 3,
  Completing = 4,
  Completed = 5,
  Terminating = 6,
  Terminated = 7,
  Failed = 8,
}

export enum SftTaskTrainingMethod {
  LoRA = 1,
  Full = 2,
}

export enum SftTaskTrainingType {
  SftFineTuning = 1,
}

export enum SftTaskValidationSetType {
  SplitFromTrainingSet = 1,
}

export enum TaskStatus {
  Undefined = 0,
  /** 正在初始化 */
  Initializing = 1,
  /** 正在运行 */
  Running = 2,
  /** 成功完成 */
  Done = 3,
  /** 失败 */
  Failed = 4,
  /** 手动终止 */
  Terminated = 5,
  /** 成功完成，但有错误 */
  DoneWithError = 6,
}

export enum TrainingDataFileType {
  Undefined = 0,
  JSONL = 1,
}

export enum TrainingDatasetType {
  Undefined = 0,
  SFTChat = 1,
  SFTFunctionCall = 2,
  SFTMultiModalUnderstanding = 3,
}

export enum TrainingFileOpType {
  Undefined = 0,
  Get = 1,
  Put = 2,
}

export enum UsageScenario {
  /** 默认场景 */
  Default = 1,
  /** 评测场景 */
  Evaluation = 2,
  /** Prompt as a Service调用 */
  PromptAsAService = 3,
  /** AI打标 */
  AIAnnotate = 4,
  /** 质量分 */
  AIScore = 5,
  /** 数据标签 */
  AITag = 6,
}

export enum ValidateStatus {
  Pass = 1,
  Failed = 2,
  Skip = 3,
}

export enum VisibleMode {
  /** 默认（仅模型所属空间可见） */
  Default = 1,
  /** 指定空间可见 */
  Specified = 2,
  /** 所有空间可见 */
  All = 3,
}
/* eslint-enable */
