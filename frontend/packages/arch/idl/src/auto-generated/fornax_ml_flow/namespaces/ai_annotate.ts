/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';

export type Int64 = string | number;

export enum AIAnnotateTaskItemStatus {
  NotStarted = 0,
  Running = 1,
  Succeeded = 2,
  Failed = 3,
}

export enum AIAnnotateTaskRunType {
  Undefined = 0,
  RunAllWithOverwrite = 1,
  RunEmpty = 2,
}

export enum AIAnnotateTaskStatus {
  Undefined = 0,
  NotStarted = 1,
  Running = 2,
  Finished = 3,
  Failed = 4,
  Terminated = 5,
}

export enum ErrorType {
  Undefined = 0,
  SystemError = 1,
  UserError = 2,
}

export enum PromptVariableValueType {
  Undefined = 0,
  /** 固定值 */
  Fixed = 1,
  /** 数据集列，使用数据集列的值 */
  useColumn = 2,
}

export interface AIAnnotateResultItem {
  /** key: 变量名，value: 变量值 */
  variables?: Record<string, string>;
  userPromptColumnValue?: flow_devops_evaluation_callback_common.Content;
  /** 执行结果 */
  output?: string;
  /** 执行错误，为空表示执行成功 */
  error?: string;
}

export interface AIAnnotateTask {
  id?: string;
  name?: string;
  datasetID?: string;
  datasetColumnName?: string;
  promptID?: string;
  promptVersion?: string;
  userPromptColumnName?: string;
  promptVariables?: Array<PromptVariable>;
  latestTaskRunID?: string;
  /** 打标并发度 */
  executeConcurrency?: number;
  spaceID?: string;
  /** 创建人ID */
  createdBy?: string;
  /** 创建时间，ms */
  createdAt?: string;
  /** 更新人ID */
  updatedBy?: string;
  /** 更新时间，ms */
  updatedAt?: string;
}

export interface AIAnnotateTaskRun {
  id?: string;
  taskID?: string;
  taskRunType?: AIAnnotateTaskRunType;
  status?: AIAnnotateTaskStatus;
  totalCount?: Int64;
  /** 执行成功的数量 */
  succeedCount?: Int64;
  /** 执行失败的数量 */
  failedCount?: Int64;
  /** 成功插入的数量 */
  updatedCount?: Int64;
  taskRunErrorInfos?: Array<TaskRunErrorInfo>;
  taskBrief?: AIAnnotateTask;
  LastOutputCursor?: Int64;
  /** 创建人ID */
  createdBy?: string;
  /** 创建时间，ms */
  createdAt?: string;
  /** 更新人ID */
  updatedBy?: string;
  /** 更新时间，ms */
  updatedAt?: string;
}

export interface ErrorInfo {
  errorType?: ErrorType;
  errorMessage?: string;
}

export interface PromptVariable {
  name?: string;
  valueType?: PromptVariableValueType;
  value?: string;
  datasetColumnName?: string;
}

export interface TaskRunErrorInfo {
  rowGroupID?: string;
  errorInfo?: ErrorInfo;
}
/* eslint-enable */
