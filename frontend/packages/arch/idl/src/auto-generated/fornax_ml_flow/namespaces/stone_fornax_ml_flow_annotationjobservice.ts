/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as annotation_job from './annotation_job';
import * as base from './base';
import * as datasetv2 from './datasetv2';
import * as ai_annotate from './ai_annotate';
import * as filter from './filter';

export type Int64 = string | number;

export interface CreateAnnotationJobRequest {
  spaceID: string;
  datasetID: string;
  name?: string;
  content?: annotation_job.AnnotationConfig;
  base?: base.Base;
}

export interface CreateAnnotationJobResponse {
  /** JobID */
  jobID?: string;
  baseResp?: base.BaseResp;
}

export interface CreateQualityScoreJobRequest {
  spaceID: string;
  datasetID: string;
  /** 质量分任务内容 */
  job: annotation_job.QualityScoreJob;
  base?: base.Base;
}

export interface CreateQualityScoreJobResponse {
  jobID?: string;
  baseResp?: base.BaseResp;
}

export interface DeleteAnnotationJobRequest {
  spaceID: string;
  datasetID: string;
  /** JobID非ID */
  jobID: string;
  base?: base.Base;
}

export interface DeleteAnnotationJobResponse {
  baseResp?: base.BaseResp;
}

export interface DeleteQualityScoreJobRequest {
  spaceID: string;
  datasetID: string;
  jobID: string;
  base?: base.Base;
}

export interface DeleteQualityScoreJobResponse {
  baseResp?: base.BaseResp;
}

export interface DryRunQualityScoreJobRequest {
  spaceID: string;
  datasetID: string;
  job: annotation_job.QualityScoreJob;
  /** 不传，默认5条 */
  sampleCount?: number;
  base?: base.Base;
}

export interface DryRunQualityScoreJobResponse {
  items?: Array<datasetv2.DatasetItem>;
  qualityScoreFieldKey?: string;
  baseResp?: base.BaseResp;
}

export interface GetAnnotationJobDetailRequest {
  spaceID: string;
  datasetID: string;
  /** JobID非ID */
  jobID?: string;
  base?: base.Base;
}

export interface GetAnnotationJobDetailResponse {
  jobDetail?: annotation_job.AnnotationJob;
  baseResp?: base.BaseResp;
}

export interface GetAnnotationJobProcessRequest {
  spaceID: string;
  datasetID: string;
  base?: base.Base;
}

export interface GetAnnotationJobProcessResponse {
  annotationJobRunInstances?: Array<annotation_job.AnnotationJobRunInstance>;
  baseResp?: base.BaseResp;
}

export interface GetAnnotationJobsWithDatasetRequest {
  spaceID: string;
  datasetID: string;
  base?: base.Base;
}

export interface GetAnnotationJobsWithDatasetResponse {
  /** 作为输入的字段和任务的映射关系，key:schema中的字段key; val:任务详情列表 */
  inputJobMap?: Record<string, Array<annotation_job.AnnotationJob>>;
  /** 作为输出的字段和任务的映射关系，key:schema中的字段key; val:任务详情列表 */
  outputJobMap?: Record<string, Array<annotation_job.AnnotationJob>>;
  baseResp?: base.BaseResp;
}

export interface GetQualityScoreJobInstanceRequest {
  spaceID: string;
  datasetID: string;
  jobID: string;
  base?: base.Base;
}

export interface GetQualityScoreJobInstanceResponse {
  instance?: annotation_job.QualityScoreJobInstance;
  baseResp?: base.BaseResp;
}

export interface GetQualityScoreJobRequest {
  spaceID: string;
  datasetID: string;
  jobID: string;
  base?: base.Base;
}

export interface GetQualityScoreJobResponse {
  job?: annotation_job.QualityScoreJob;
  baseResp?: base.BaseResp;
}

export interface ListQualityScoreJobsRequest {
  spaceID: string;
  datasetID: string;
  /** pagination */
  page?: number;
  pageSize?: number;
  cursor?: string;
  base?: base.Base;
}

export interface ListQualityScoreJobsResponse {
  jobs?: Array<annotation_job.QualityScoreJob>;
  nextCursor?: string;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface PreviewAnnotationRequest {
  spaceID: string;
  datasetID: string;
  job: annotation_job.AnnotationJob;
  /** 任务新增列的schema信息,创建时需要传入 */
  fields: Array<datasetv2.FieldSchema>;
  base?: base.Base;
}

export interface PreviewAnnotationResponse {
  items?: Array<datasetv2.DatasetItem>;
  errorMsg?: string;
  baseResp?: base.BaseResp;
}

export interface RunAnnotationJobRequest {
  spaceID: string;
  datasetID: string;
  /** JobID非ID */
  jobID: string;
  /** 离线推理任务需要显示传入 */
  jwtToken?: string;
  /** 重新运行的instanceID */
  annotationJobRunID?: string;
  base?: base.Base;
}

export interface RunAnnotationJobResponse {
  annotationJobRun?: string;
  baseResp?: base.BaseResp;
}

export interface RunQualityScoreJobRequest {
  spaceID: string;
  datasetID: string;
  jobID: string;
  taskRunType: ai_annotate.AIAnnotateTaskRunType;
  filter?: filter.Filter;
  base?: base.Base;
}

export interface RunQualityScoreJobResponse {
  jobInstanceID?: string;
  baseResp?: base.BaseResp;
}

export interface RunQualityScoreSyncRequest {
  spaceID: string;
  datasetID: string;
  jobID: string;
  itemIDs: Array<string>;
  base?: base.Base;
}

export interface RunQualityScoreSyncResponse {
  items?: Array<datasetv2.DatasetItem>;
  baseResp?: base.BaseResp;
}

export interface SearchAnnotationjobInstancesRequest {
  spaceID: string;
  datasetID: string;
  typeFilter?: Array<annotation_job.AnnotationType>;
  createdBys?: Array<string>;
  page?: number;
  pageSize?: number;
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  base?: base.Base;
}

export interface SearchAnnotationjobInstancesResponse {
  instances?: Array<annotation_job.AnnotationJobRunInstance>;
  nextCursor?: string;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface SearchAnnotationJobsRequest {
  spaceID: string;
  nameLike?: string;
  page?: number;
  pageSize?: number;
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  base?: base.Base;
}

export interface SearchAnnotationJobsResponse {
  annotationJobs?: Array<annotation_job.AnnotationJob>;
  total?: Int64;
  nextCursor?: string;
  baseResp?: base.BaseResp;
}

export interface TerminateAnnotationJobRequest {
  spaceID: string;
  datasetID: string;
  /** JobID非ID */
  jobID: string;
  jobRunID: string;
  jwtToken?: string;
  base?: base.Base;
}

export interface TerminateAnnotationJobResponse {
  baseResp?: base.BaseResp;
}

export interface TerminateQualityScoreJobInstanceRequest {
  spaceID: string;
  datasetID: string;
  jobID: string;
  /** 任务实例id */
  instanceID: string;
  base?: base.Base;
}

export interface TerminateQualityScoreJobInstanceResponse {
  baseResp?: base.BaseResp;
}

export interface UpdateAnnotationJobRequest {
  spaceID: string;
  datasetID: string;
  /** JobID */
  jobID: string;
  name?: string;
  content?: annotation_job.AnnotationConfig;
  base?: base.Base;
}

export interface UpdateAnnotationJobResponse {
  baseResp?: base.BaseResp;
}

export interface UpdateQualityScoreJobRequest {
  spaceID: string;
  datasetID: string;
  jobID: string;
  job: annotation_job.QualityScoreJob;
  base?: base.Base;
}

export interface UpdateQualityScoreJobResponse {
  baseResp?: base.BaseResp;
}
/* eslint-enable */
