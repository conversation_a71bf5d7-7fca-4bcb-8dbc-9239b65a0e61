/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as datasetv2 from './datasetv2';
import * as base from './base';
import * as datasetv2job from './datasetv2job';
import * as datasetv2similarity from './datasetv2similarity';
import * as datasetv2lineage from './datasetv2lineage';
import * as filter from './filter';

export type Int64 = string | number;

export interface BatchCreateDatasetItemsReq {
  spaceID?: string;
  datasetID: string;
  items?: Array<datasetv2.DatasetItem>;
  /** items 中存在无效数据时，默认不会写入任何数据；设置 skipInvalidItems=true 会跳过无效数据，写入有效数据 */
  skipInvalidItems?: boolean;
  /** 批量写入 items 如果超出数据集容量限制，默认不会写入任何数据；设置 partialAdd=true 会写入不超出容量限制的前 N 条 */
  allowPartialAdd?: boolean;
  base?: base.Base;
}

export interface BatchCreateDatasetItemsResp {
  /** key: item 在 items 中的索引 */
  addedItems?: Record<number, Int64>;
  errors?: Array<datasetv2.ItemErrorGroup>;
  /** base */
  baseResp?: base.BaseResp;
}

export interface BatchDeleteDatasetItemsReq {
  spaceID?: string;
  datasetID: string;
  itemIDs?: Array<string>;
  base?: base.Base;
}

export interface BatchDeleteDatasetItemsResp {
  baseResp?: base.BaseResp;
}

export interface BatchGetDatasetItemsByVersionReq {
  spaceID?: string;
  datasetID: string;
  versionID: string;
  itemIDs: Array<string>;
  base?: base.Base;
}

export interface BatchGetDatasetItemsByVersionResp {
  items?: Array<datasetv2.DatasetItem>;
  baseResp?: base.BaseResp;
}

export interface BatchGetDatasetItemsReq {
  spaceID?: string;
  datasetID: string;
  itemIDs: Array<string>;
  base?: base.Base;
}

export interface BatchGetDatasetItemsResp {
  items?: Array<datasetv2.DatasetItem>;
  baseResp?: base.BaseResp;
}

export interface BatchGetDatasetsReq {
  spaceID: string;
  datasetIDs: Array<string>;
  withDeleted?: boolean;
  base?: base.Base;
}

export interface BatchGetDatasetsResp {
  datasets?: Array<datasetv2.Dataset>;
  baseResp?: base.BaseResp;
}

export interface BatchGetVersionedDatasetsReq {
  spaceID?: string;
  versionIDs: Array<string>;
  /** 是否返回已删除的数据，默认不返回 */
  withDeleted?: boolean;
  base?: base.Base;
}

export interface BatchGetVersionedDatasetsResp {
  versionedDataset?: Array<VersionedDataset>;
  baseResp?: base.BaseResp;
}

export interface CancelDatasetIOJobReq {
  spaceID: string;
  jobID: string;
  base?: base.Base;
}

export interface CancelDatasetIOJobResp {
  baseResp?: base.BaseResp;
}

export interface ClearDatasetItemRequest {
  spaceID?: string;
  dataset_id: string;
  Base?: base.Base;
}

export interface ClearDatasetItemResponse {
  BaseResp?: base.BaseResp;
}

export interface ConfirmItemDeduplicateReq {
  spaceID?: string;
  jobID: string;
  /** 批量确认 */
  pairs: Array<ConfirmItemPair>;
  /** base */
  base?: base.Base;
}

export interface ConfirmItemDeduplicateResp {
  baseResp?: base.BaseResp;
}

export interface ConfirmItemPair {
  /** 新导入的条目主键 */
  newItemsUniqKey: string;
  importConfirmType: datasetv2job.ImportConfirmType;
}

export interface ConflictField {
  /** 存在冲突的列名 */
  fieldName?: string;
  /** 冲突详情。key: 文件名，val：该文件中包含的类型 */
  detailM?: Record<string, datasetv2.FieldSchema>;
}

export interface CreateDatasetItemReq {
  spaceID: string;
  datasetID: string;
  /** 数据插入的幂等 key，前端创建时可以不传 */
  itemKey?: string;
  /** 数据内容 */
  data?: Array<datasetv2.FieldData>;
  /** 多轮数据内容，与 data 互斥 */
  repeatedData?: Array<datasetv2.ItemData>;
  /** 如果有来源 item，可以通过该字段指定是否保留与克隆的源 item 的血缘关系 */
  keepLineage?: boolean;
  /** 源 item id，在 keepLineage 为 true 时必填 */
  sourceItemID?: string;
  base?: base.Base;
}

export interface CreateDatasetItemResp {
  itemID?: string;
  error?: datasetv2.ItemErrorGroup;
  baseResp?: base.BaseResp;
}

export interface CreateDatasetReq {
  spaceID: string;
  appID?: number;
  name: string;
  description?: string;
  category?: datasetv2.DatasetCategory;
  bizCategory?: string;
  fields?: Array<datasetv2.FieldSchema>;
  securityLevel?: datasetv2.SecurityLevel;
  visibility?: datasetv2.DatasetVisibility;
  spec?: datasetv2.DatasetSpec;
  features?: datasetv2.DatasetFeatures;
  userID?: string;
  base?: base.Base;
}

export interface CreateDatasetResp {
  datasetID?: string;
  baseResp?: base.BaseResp;
}

export interface CreateDatasetVersionReq {
  spaceID?: string;
  datasetID: string;
  /** 展示的版本号，SemVer2 三段式，需要大于上一版本 */
  version: string;
  desc?: string;
  base?: base.Base;
}

export interface CreateDatasetVersionResp {
  id?: string;
  baseResp?: base.BaseResp;
}

export interface CreateDatasetWithImportReq {
  spaceID?: string;
  appID?: number;
  sourceType?: datasetv2job.SourceType;
  source: datasetv2job.DatasetIOEndpoint;
  fieldMappings?: Array<datasetv2job.FieldMapping>;
  option?: datasetv2job.DatasetIOJobOption;
  /** 新建数据集名称 */
  targetDatasetName: string;
  /** 新建数据集描述 */
  targetDatasetDesc?: string;
  category?: datasetv2.DatasetCategory;
  fields?: Array<datasetv2.FieldSchema>;
  /** base */
  base?: base.Base;
}

export interface CreateDatasetWithImportResp {
  datasetID?: string;
  jobID?: string;
  baseResp?: base.BaseResp;
}

export interface CreateItemDeduplicateJobReq {
  spaceID?: string;
  datasetID: Int64;
  file?: datasetv2job.DatasetIOFile;
  fieldMappings?: Array<datasetv2job.FieldMapping>;
  option?: datasetv2job.DatasetIOJobOption;
  /** 任务id，重入时用 */
  jobID?: string;
  /** 根据哪一列去重 */
  fieldKey?: string;
  /** 去重算法 */
  similarityAlgorithm?: datasetv2similarity.SimilarityAlgorithm;
  /** 阈值 */
  threshold?: Int64;
  /** base */
  base?: base.Base;
}

export interface CreateItemDeduplicateJobResp {
  /** 任务id，前端后续用这个id去获取 待确认列表 */
  jobID: string;
  baseResp?: base.BaseResp;
}

export interface DeleteDatasetItemReq {
  spaceID?: string;
  datasetID: string;
  itemID: string;
  base?: base.Base;
}

export interface DeleteDatasetItemResp {
  baseResp?: base.BaseResp;
}

export interface DeleteDatasetReq {
  spaceID?: string;
  datasetID: string;
  base?: base.Base;
}

export interface DeleteDatasetResp {
  baseResp?: base.BaseResp;
}

export interface ExportDatasetReq {
  spaceID: string;
  datasetID: string;
  /** 需要导出的数据集版本 id，为 0 表示导出草稿版本 */
  versionID?: string;
  targetType: datasetv2job.SourceType;
  /** 此处填写一个文件夹，会将对应的文件生成到该文件夹下 */
  target: datasetv2job.DatasetIOEndpoint;
  /** base */
  base?: base.Base;
}

export interface ExportDatasetResp {
  jobID?: string;
  /** base */
  baseResp?: base.BaseResp;
}

export interface FieldMeta {
  /** 字段类型 */
  field_type: string;
  /** 当前字段支持的操作类型 */
  query_types: Array<string>;
  display_name: string;
  /** 支持的可选项 */
  field_options?: FieldOptions;
  /** 当前字段在schema中是否存在 */
  exist?: boolean;
}

export interface FieldMetaInfoData {
  /** 字段元信息 */
  field_metas: Record<string, FieldMeta>;
}

export interface FieldOptions {
  i32?: Array<number>;
  i64?: Array<string>;
  f64?: Array<number>;
  string?: Array<string>;
  obj?: Array<ObjectFieldOption>;
}

export interface GetDatasetIOJobReq {
  spaceID?: string;
  jobID: string;
  base?: base.Base;
}

export interface GetDatasetIOJobResp {
  job?: datasetv2job.DatasetIOJob;
  baseResp?: base.BaseResp;
}

export interface GetDatasetItemDeepSourcesReq {
  spaceID: string;
  datasetID: string;
  /** item 的主键 id */
  id: string;
  base?: base.Base;
}

export interface GetDatasetItemDeepSourcesResp {
  /** 按照从 root 到当前 item 的顺序返回 */
  deepSources?: Array<datasetv2lineage.ItemSource>;
  baseResp?: base.BaseResp;
}

export interface GetDatasetItemReq {
  spaceID?: string;
  datasetID: string;
  itemID: string;
  base?: base.Base;
}

export interface GetDatasetItemResp {
  item?: datasetv2.DatasetItem;
  baseResp?: base.BaseResp;
}

export interface GetDatasetItemSourceReq {
  spaceID: string;
  datasetID: string;
  /** item 的主键 id */
  id: string;
  base?: base.Base;
}

export interface GetDatasetItemSourceResp {
  source?: datasetv2lineage.ItemSource;
  baseResp?: base.BaseResp;
}

export interface GetDatasetReq {
  spaceID?: string;
  datasetID: string;
  /** 数据集已删除时是否返回 */
  withDeleted?: boolean;
  base?: base.Base;
}

export interface GetDatasetResp {
  dataset?: datasetv2.Dataset;
  baseResp?: base.BaseResp;
}

export interface GetDatasetSchemaReq {
  spaceID?: string;
  datasetID: string;
  /** 是否获取已经删除的列，默认不返回 */
  withDeleted?: boolean;
  base?: base.Base;
}

export interface GetDatasetSchemaResp {
  fields?: Array<datasetv2.FieldSchema>;
  baseResp?: base.BaseResp;
}

export interface GetDatasetVersionReq {
  spaceID?: string;
  versionID: string;
  /** 是否返回已删除的数据，默认不返回 */
  withDeleted?: boolean;
  base?: base.Base;
}

export interface GetDatasetVersionResp {
  version?: datasetv2.DatasetVersion;
  dataset?: datasetv2.Dataset;
  baseResp?: base.BaseResp;
}

export interface GetFieldsMetaInfoRequest {
  spaceID: Int64;
  datasetID: Int64;
}

export interface GetFieldsMetaInfoResponse {
  data: FieldMetaInfoData;
}

export interface GetItemDeduplicateJobReq {
  spaceID?: string;
  jobID: string;
  confirmType?: datasetv2job.ImportConfirmType;
  page?: number;
  pageSize?: number;
  base?: base.Base;
}

export interface GetItemDeduplicateJobResp {
  job?: datasetv2job.ItemDeduplicateJob;
  baseResp?: base.BaseResp;
}

export interface ImportDatasetReq {
  spaceID?: string;
  datasetID: string;
  file?: datasetv2job.DatasetIOFile;
  /** 待外场前端修复后再加上 vt.elem.skip = "false" */
  fieldMappings?: Array<datasetv2job.FieldMapping>;
  option?: datasetv2job.DatasetIOJobOption;
  /** base */
  base?: base.Base;
}

export interface ImportDatasetResp {
  jobID?: string;
  baseResp?: base.BaseResp;
}

export interface ListDatasetIOJobsOfDatasetReq {
  spaceID?: string;
  datasetID: string;
  types?: Array<datasetv2job.JobType>;
  statuses?: Array<datasetv2job.JobStatus>;
  base?: base.Base;
}

export interface ListDatasetIOJobsOfDatasetResp {
  jobs?: Array<datasetv2job.DatasetIOJob>;
  baseResp?: base.BaseResp;
}

export interface ListDatasetItemsByVersionReq {
  spaceID?: string;
  datasetID: string;
  versionID: string;
  /** pagination */
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  /** 与 page 同时提供时，优先使用 cursor */
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  filter?: filter.Filter;
  base?: base.Base;
}

export interface ListDatasetItemsByVersionResp {
  items?: Array<datasetv2.DatasetItem>;
  /** pagination */
  nextCursor?: string;
  total?: Int64;
  filterTotal?: Int64;
  baseResp?: base.BaseResp;
}

export interface ListDatasetItemsReq {
  spaceID?: string;
  datasetID: string;
  /** pagination */
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  /** 与 page 同时提供时，优先使用 cursor */
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  filter?: filter.Filter;
  base?: base.Base;
}

export interface ListDatasetItemsResp {
  items?: Array<datasetv2.DatasetItem>;
  /** pagination */
  nextCursor?: string;
  total?: string;
  filterTotal?: string;
  baseResp?: base.BaseResp;
}

export interface ListDatasetVersionsReq {
  spaceID?: string;
  datasetID: string;
  /** 根据版本号模糊匹配 */
  versionLike?: string;
  /** pagination */
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  /** 与 page 同时提供时，优先使用 cursor */
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  base?: base.Base;
}

export interface ListDatasetVersionsResp {
  versions?: Array<datasetv2.DatasetVersion>;
  /** pagination */
  nextCursor?: string;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface ObjectFieldOption {
  id: Int64;
  displayName: string;
}

export interface ParseImportSourceFileReq {
  spaceID: string;
  /** 如果 path 为文件夹，此处只默认解析当前路径级别下所有指定类型的文件，不嵌套解析 */
  file?: datasetv2job.DatasetIOFile;
  /** base */
  base?: base.Base;
}

export interface ParseImportSourceFileResp {
  /** 文件大小，单位为 byte */
  bytes?: string;
  /** 列名和类型，有多文件的话会取并集返回。如果文件中的列定义存在冲突，此处不返回解析结果，具体冲突详情通过 conflicts 返回 */
  fields?: Array<datasetv2.FieldSchema>;
  /** 冲突详情。key: 列名，val：冲突详情 */
  conflicts?: Array<ConflictField>;
  /** 存在列定义不明确的文件（即一个列被定义为多个类型），当前仅 jsonl 文件会出现该状况 */
  filesWithAmbiguousColumn?: Array<string>;
  /** base */
  baseResp?: base.BaseResp;
}

export interface SearchDatasetIOJobsOfDatasetReq {
  spaceID?: string;
  datasetID: string;
  types?: Array<datasetv2job.JobType>;
  statuses?: Array<datasetv2job.JobStatus>;
  base?: base.Base;
}

export interface SearchDatasetIOJobsOfDatasetResp {
  jobs?: Array<datasetv2job.DatasetIOJob>;
  baseResp?: base.BaseResp;
}

export interface SearchDatasetItemsByVersionReq {
  spaceID?: string;
  datasetID: string;
  versionID: string;
  /** pagination */
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  /** 与 page 同时提供时，优先使用 cursor */
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  filter?: filter.Filter;
  base?: base.Base;
}

export interface SearchDatasetItemsByVersionResp {
  items?: Array<datasetv2.DatasetItem>;
  /** pagination */
  nextCursor?: string;
  total?: Int64;
  filterTotal?: Int64;
  baseResp?: base.BaseResp;
}

export interface SearchDatasetItemsReq {
  spaceID?: string;
  datasetID: string;
  /** pagination */
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  /** 与 page 同时提供时，优先使用 cursor */
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  filter?: filter.Filter;
  base?: base.Base;
}

export interface SearchDatasetItemsResp {
  items?: Array<datasetv2.DatasetItem>;
  /** pagination */
  nextCursor?: string;
  total?: string;
  filterTotal?: string;
  baseResp?: base.BaseResp;
}

export interface SearchDatasetsReq {
  spaceID: string;
  datasetIDs?: Array<string>;
  category?: datasetv2.DatasetCategory;
  /** 支持模糊搜索 */
  name?: string;
  createdBys?: Array<string>;
  bizCategories?: Array<string>;
  /** pagination */
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  /** 与 page 同时提供时，优先使用 cursor */
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  base?: base.Base;
}

export interface SearchDatasetsResp {
  datasets?: Array<datasetv2.Dataset>;
  /** pagination */
  nextCursor?: string;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface SearchDatasetVersionsReq {
  spaceID?: string;
  datasetID: string;
  /** 根据版本号模糊匹配 */
  versionLike?: string;
  /** pagination */
  page?: number;
  /** 分页大小(0, 200]，默认为 20 */
  pageSize?: number;
  /** 与 page 同时提供时，优先使用 cursor */
  cursor?: string;
  orderBy?: datasetv2.OrderBy;
  base?: base.Base;
}

export interface SearchDatasetVersionsResp {
  versions?: Array<datasetv2.DatasetVersion>;
  /** pagination */
  nextCursor?: string;
  total?: string;
  baseResp?: base.BaseResp;
}

export interface SignUploadFileTokenReq {
  spaceID?: string;
  /** 支持 ImageX, TOS */
  storage?: datasetv2.StorageProvider;
  fileName?: string;
  /** base */
  base?: base.Base;
}

export interface SignUploadFileTokenResp {
  url?: string;
  token?: datasetv2.FileUploadToken;
  imageXServiceID?: string;
  /** base */
  baseResp?: base.BaseResp;
}

export interface UpdateDatasetItemReq {
  spaceID?: string;
  datasetID: string;
  itemID: string;
  /** 单轮数据内容，当数据集为单轮时，写入此处的值 */
  data?: Array<datasetv2.FieldData>;
  /** 多轮对话数据内容，当数据集为多轮对话时，写入此处的值 */
  repeatedData?: Array<datasetv2.ItemData>;
  base?: base.Base;
}

export interface UpdateDatasetItemResp {
  baseResp?: base.BaseResp;
}

export interface UpdateDatasetReq {
  spaceID?: string;
  datasetID: string;
  name?: string;
  description?: string;
  base?: base.Base;
}

export interface UpdateDatasetResp {
  baseResp?: base.BaseResp;
}

export interface UpdateDatasetSchemaReq {
  spaceID?: string;
  datasetID: string;
  /** fieldSchema.key 为空时：插入新的一列
fieldSchema.key 不为空时：更新对应的列
使用示例参考： */
  fields?: Array<datasetv2.FieldSchema>;
  base?: base.Base;
}

export interface UpdateDatasetSchemaResp {
  baseResp?: base.BaseResp;
}

export interface UpdateDatasetVersionReq {
  spaceID: string;
  versionID: string;
  desc?: string;
  base?: base.Base;
}

export interface UpdateDatasetVersionResp {
  baseResp?: base.BaseResp;
}

export interface ValidateDatasetItemsReq {
  spaceID?: string;
  items?: Array<datasetv2.DatasetItem>;
  /** 添加到已有数据集时提供 */
  datasetID?: string;
  /** 新建数据集并添加数据时提供 */
  datasetCategory?: datasetv2.DatasetCategory;
  /** 新建数据集并添加数据时，必须提供；添加到已有数据集时，如非空，则覆盖已有 schema 用于校验 */
  datasetFields?: Array<datasetv2.FieldSchema>;
  /** 添加到已有数据集时，现有数据条数，做容量校验时不做考虑，仅考虑提供 items 数量是否超限 */
  ignoreCurrentItemCount?: boolean;
}

export interface ValidateDatasetItemsResp {
  /** 合法的 item 索引，与 ValidateCreateDatasetItemsReq.items 中的索引对应 */
  validItemIndices?: Array<number>;
  errors?: Array<datasetv2.ItemErrorGroup>;
  /** base */
  baseResp?: base.BaseResp;
}

export interface VersionedDataset {
  version?: datasetv2.DatasetVersion;
  dataset?: datasetv2.Dataset;
}
/* eslint-enable */
