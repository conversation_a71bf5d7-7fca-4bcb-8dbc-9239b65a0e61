/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as batch_infer from './batch_infer';

export type Int64 = string | number;

export interface GetBatchInferTaskReq {
  task_id?: string;
  userJwtToken?: string;
  /** 空间ID */
  space_id?: string;
  base?: base.Base;
}

export interface GetBatchInferTaskResp {
  batchInferTask?: batch_infer.BatchInferTask;
  baseResp?: base.BaseResp;
}

export interface GetRecommendResourceReq {
  modelCardID?: string;
  userJwtToken?: string;
  /** 空间ID */
  space_id?: string;
  base?: base.Base;
}

export interface GetRecommendResourceResp {
  /** 是否存在推荐的资源配置 */
  exist?: boolean;
  /** 集群id */
  merlinClusterID?: string;
  /** gpu类型 */
  gpuv?: string;
  /** 用户组id */
  groupID?: string;
  /** 资源池类型 */
  quotaPoolType?: batch_infer.MerlinQuotaPoolType;
  baseResp?: base.BaseResp;
}

export interface ListModelCardsReq {
  /** 必填。model card来源 */
  provider?: batch_infer.Provider;
  /** 选填。model card名称关键字。 */
  nameKeyword?: string;
  /** 必填。用户jwt token。用于调用merlin等api时的鉴权 */
  userJwtToken?: string;
  /** 必填。pageSize。最大值为100 */
  limit?: string;
  /** 必填。翻页偏移量。初始值填0，每翻一页这个值+=limit */
  offset?: string;
  /** 空间ID */
  space_id?: string;
  base?: base.Base;
}

export interface ListModelCardsResp {
  total?: string;
  hasMore?: boolean;
  modelCards?: Array<batch_infer.ModelCard>;
  baseResp?: base.BaseResp;
}

export interface ParseModelConfigReq {
  provider?: batch_infer.Provider;
  seedHdfsAddress?: string;
  seedModelCardID?: string;
  userJwtToken?: string;
  /** 空间ID */
  space_id?: string;
  base?: base.Base;
}

export interface ParseModelConfigResp {
  /** merlin seed 模型配置 */
  modelParamConfigType?: string;
  tokenizerAddress?: string;
  networkConfig?: string;
  quantConfig?: string;
  checkpointHdfsPath?: string;
  baseResp?: base.BaseResp;
}
/* eslint-enable */
