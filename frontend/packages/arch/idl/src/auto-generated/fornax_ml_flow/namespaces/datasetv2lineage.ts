/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as datasetv2 from './datasetv2';

export type Int64 = string | number;

export enum LineageSourceType {
  Manual = 1,
  /** 需要根据 ItemSource.dataset.category 字段区分评测集/数据集/... */
  Dataset = 2,
  /** 需要根据 ItemSource.file.storage 字段区分HDFS/本地上传/... */
  FileStorage = 3,
  /** 暂无 */
  DataReflow = 4,
  /** 暂无 */
  DataAnnotation = 5,
  /** 暂无 */
  DataProcessing = 6,
  /** 暂无 */
  DataGenerate = 7,
  OpenAPI = 8,
  /** 众包标注任务 */
  CrowdsourcingAnnotation = 9,
}

export enum TrackedJobType {
  /** 数据导入任务 */
  DatasetIOJob = 1,
  /** 数据回流任务 */
  DataReflow = 2,
  /** 标注任务 */
  DataAnnotation = 3,
  /** 数据处理任务 */
  DataProcessing = 4,
  /** 数据生成任务 */
  DataGenerate = 5,
  /** 众包标注任务 */
  CrowdsourcingAnnotation = 6,
}

export interface ItemSource {
  type: LineageSourceType;
  /** 源 item 信息，可以为空 */
  trackedItem?: TrackedItem;
  /** 任务类型，根据该字段区分数据导入任务/数据回流任务/... */
  jobType?: TrackedJobType;
  /** item 关联的任务 id，为 0 表示无相应任务(例如数据是通过克隆另一数据行产生的) */
  jobID?: string;
  /** type = Dataset 时，从该字段获取数据集具体信息 */
  dataset?: TrackedDataset;
  /** type = FileStorage 时，从该字段获取文件信息 */
  file?: TrackedFile;
  createdAt?: string;
}

/** 源数据集信息（数据集 id 从 TrackedItem 中获取，此处不额外返回） */
export interface TrackedDataset {
  /** 数据集类别，根据该字段区分评测集/数据集/... */
  category?: datasetv2.DatasetCategory;
  datasetName?: string;
}

export interface TrackedFile {
  /** 存储介质，根据该字段区分 hdfs/本地文件(即 ImageX)/... */
  storage?: datasetv2.StorageProvider;
  /** 用户上传文件的原始文件名 */
  originalFileName?: string;
}

export interface TrackedItem {
  spaceID?: string;
  datasetID?: string;
  itemID?: string;
  /** 版本号提交后的版本 id，为 0 表示为草稿版本 */
  versionID?: string;
  /** 版本号（三段式） */
  version?: string;
}
/* eslint-enable */
