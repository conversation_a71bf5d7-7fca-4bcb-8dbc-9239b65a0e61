/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AuditResultEnum {
  /** 未检查 */
  Init = 1,
  /** 标记合格 */
  Pass = 2,
  /** 标记不合格 */
  Reject = 3,
}

export enum AuditStatus {
  /** 被判定作弊 */
  Fraud = 1,
  /** AIDP审核通过 */
  Pass = 2,
  /** AIDP审核未通过 */
  Fail = 3,
}

export enum AuditStatusEnum {
  /** 不涉及审核操作 */
  None = 0,
  /** 通过 */
  Pass = 1,
  /** 回收 */
  Recycle = 2,
  /** 打回 */
  Back = 3,
}

/** 题目状态: 未领取、被领取、被回收 */
export enum ItemStatus {
  /** 未领取 */
  Init = 1,
  /** 被领取 */
  Received = 2,
  /** 被回收 */
  Recycled = 3,
}

export enum MsgOpType {
  /** 未知类型提交，暂时不会出现，只做该字段兜底取值使用 */
  Unknown = 0,
  /** 题目送标，等同于消息里的 IsInit = true */
  InitItem = 1,
  /** 提交后被通过，题目向后置节点流转，等同于 AuditNodeList[$x].AuditStatus = 1 */
  PassSubmit = 2,
  /** 提交后被打回，题目向前置节点流转，等同于 AuditNodeList[$x].AuditStatus = 3 */
  BackSubmit = 3,
  /** 废弃提交，等同于消息里的 IsDiscard = true */
  DiscardSubmit = 4,
  /** 点击无效后提交 */
  AbandonSubmit = 5,
  /** 直接提交，等同于消息里的 IsDirectSubmit = true */
  DirectSubmit = 6,
  /** 更新答案，等同于消息里的 IsUpdate = true */
  UpdateAnswer = 7,
}

/** 节点ID */
export enum NodeID {
  Invalid = 0,
  /** 标注/初审 */
  Start = 1,
  /** 结束 */
  End = 2,
  /** 标检模式-检查 */
  Check = 4,
  /** 标检模式-质检 */
  QA = 5,
  /** 标检模式-验收 */
  Accept = 6,
  /** QA/盲审模式保留-盲审 */
  BlindCheck = 102,
  /** QA/盲审模式保留-质检 */
  BlindQA = 104,
  /** QA/盲审模式保留-合并后的标注节点（包含标注和盲审） */
  BlindCombinedLabel = 1001,
}

export enum PriceType {
  DEFAULT = 1,
  TYPEDOUBLE = 2,
}

export enum TaskMode {
  /** 自定义 */
  Custom = 0,
  /** 经典 */
  Classical = 1,
  /** 盲审（标准） */
  BlindAudit = 12,
}

/** 模版类型 */
export enum TemplateType {
  /** App端模版
视频多选项 */
  VIDEO_MULTI_OPTIONS = 1,
  /** 视频/图片sidebyside */
  VIDEO_IMG_SBS = 2,
  /** 视频判断 */
  VIDEO_SINGLE_OPTIONS = 3,
  /** 图片判断 */
  IMG_SINGLE_OPTIONS = 4,
  /** 图片多选项 */
  IMG_MULTI_OPTIONS = 5,
  /** 图片拉框 */
  IMG_FRAME = 6,
  /** 文本多选项 */
  TEXT_MULTI_OPTIONS = 7,
  /** 视频输入 */
  VIDEO_INPUT = 11,
  /** 图片输入 */
  IMG_INPUT = 12,
  /** SBS输入 */
  SBS_INPUT = 13,
  /** 视频输入无答案 */
  VIDEO_INPUTANY = 14,
  /** 图片输入无答案 */
  IMG_INPUTANY = 15,
  /** SBS输入无答案 */
  SBS_INPUTANY = 16,
  /** POI */
  POI = 21,
  DOUYIN_INPUT = 51,
  DOUYIN_INPUTANY = 52,
  /** 多结果多判断 */
  DOUYIN_MULTI_OPTIONS = 53,
  /** PC端模版
pc端图片判断 */
  PC_IMG_SINGLE_OPTIONS = 101,
  /** 自建模板类型
图片类型 */
  CUSTOM_IMAGE_TEMPLATE = 201,
  /** 语音类型 */
  CUSTOM_VOICE_TEMPLATE = 202,
  /** 文字类型 */
  CUSTOM_TEXT_TEMPLATE = 203,
  /** 视频类型 */
  CUSTOM_VIDEO_TEMPLATE = 204,
  /** 网页类型 */
  CUSTOM_WEB_TEMPLATE = 205,
  /** TTS类型模板 */
  CUSTOM_TTS_TEMPLATE = 206,
  /** 组合数据分类标签模板 */
  CUSTOM_CLASS_TEMPLATE = 207,
  /** 几何图形标注签模板 */
  CUSTOM_GEOMETRY_TEMPLATE = 208,
  /** 文本切分模板 */
  CUSTOM_TEXT_SPLIT_TEMPLATE = 209,
  /** 点云模版 */
  CUSTOM_POINT_CLOUD_TEMPLATE = 210,
  /** EA企业库模板 */
  CUSTOM_EA_ENTERPRISE_TEMPLATE = 211,
  /** POI模版 */
  CUSTOM_POI_TEMPLATE = 212,
  /** 飞书云文档模板类型 */
  CUSTOM_LARK_DOC_TEMPLATE = 213,
  /** PF模版 */
  CUSTOM_PF_TEMPLATE = 214,
  /** DCC模版 */
  DCC = 301,
  /** DCC新模板 */
  DccNew = 302,
  /** Neeko类型模板 */
  Neeko = 1000,
}

export interface AuditNodeInfo {
  /** 节点名称 */
  Name?: string;
  /** 操作人 */
  Operator?: string;
  /** 提交时间 */
  SubmitTime?: Int64;
  /** 备注（修改建议等信息） */
  Comment?: string;
  /** 节点ID */
  NodeID?: number;
  /** 审核结果，合格 or 不合格 or 未抽中 */
  AuditResult?: AuditResultEnum;
  /** 审核状态，通过 or 打回 */
  AuditStatus?: AuditStatusEnum;
}

export interface DispatcherNode {
  /** 节点名称 */
  Name?: string;
  /** 节点ID，可见NodeID */
  ID?: number;
}

export interface ExportMsg {
  /** AIDP 任务队列 ID */
  TaskID?: Int64;
  /** 所属项目ID */
  ProjectID?: Int64;
  /** 题目唯一标识 */
  ObjectID?: string;
  /** 透传信息 */
  Penetrate?: string;
  /** 原始数据 */
  RawData?: string;
  /** 标注结果和原始数据沉底，避免过大导致日志被截断
标注结果 */
  Answer?: string;
  /** 审核&检查等人员信息 */
  AuditNodeList?: Array<AuditNodeInfo>;
  /** 时间戳 */
  Timestamp?: Int64;
  /** 下个节点 */
  ToNode?: DispatcherNode;
  /** 上个节点 */
  FromNode?: DispatcherNode;
  /** 题目送标时间 */
  CreateTime?: Int64;
  /** 题目回调唯一标识 */
  UUID?: string;
  /** 是否废弃 */
  IsDiscard?: boolean;
  /** AIDP 题目 ID */
  ItemID?: Int64;
  /** 模版类型 */
  TemplateType?: TemplateType;
  /** 模版子类型 ID，后端只存储不感知具体值（目前只有 POI 模版类型有） */
  TemplateSubType?: Int64;
  /** 任务版本 */
  TaskVersion?: number;
  /** 是否为更新答案 */
  IsUpdate?: boolean;
  /** 是否为直接提交 */
  IsDirectSubmit?: boolean;
  /** 是否为送标时创建题目事件 */
  IsInit?: boolean;
  /** 消息类型 */
  OpType?: MsgOpType;
}
/* eslint-enable */
