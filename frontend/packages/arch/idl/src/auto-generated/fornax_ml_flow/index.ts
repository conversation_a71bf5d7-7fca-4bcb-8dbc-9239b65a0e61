/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as ai_annotate from './namespaces/ai_annotate';
import * as aidpcallback from './namespaces/aidpcallback';
import * as annotation_job from './namespaces/annotation_job';
import * as base from './namespaces/base';
import * as batch_infer from './namespaces/batch_infer';
import * as dataprocess from './namespaces/dataprocess';
import * as dataset from './namespaces/dataset';
import * as datasetv2 from './namespaces/datasetv2';
import * as datasetv2job from './namespaces/datasetv2job';
import * as datasetv2lineage from './namespaces/datasetv2lineage';
import * as datasetv2similarity from './namespaces/datasetv2similarity';
import * as filter from './namespaces/filter';
import * as flow_devops_evaluation_callback_common from './namespaces/flow_devops_evaluation_callback_common';
import * as flow_devops_prompt_common from './namespaces/flow_devops_prompt_common';
import * as fornax_domain_model from './namespaces/fornax_domain_model';
import * as ml_flow_domain_model from './namespaces/ml_flow_domain_model';
import * as openapi from './namespaces/openapi';
import * as stone_fornax_ml_flow_annotateservice from './namespaces/stone_fornax_ml_flow_annotateservice';
import * as stone_fornax_ml_flow_annotationjobservice from './namespaces/stone_fornax_ml_flow_annotationjobservice';
import * as stone_fornax_ml_flow_batchinferservice from './namespaces/stone_fornax_ml_flow_batchinferservice';
import * as stone_fornax_ml_flow_crowdsourcingannotationjobservice from './namespaces/stone_fornax_ml_flow_crowdsourcingannotationjobservice';
import * as stone_fornax_ml_flow_dataprocessservice from './namespaces/stone_fornax_ml_flow_dataprocessservice';
import * as stone_fornax_ml_flow_datasetservice from './namespaces/stone_fornax_ml_flow_datasetservice';
import * as stone_fornax_ml_flow_datasetservicev2 from './namespaces/stone_fornax_ml_flow_datasetservicev2';
import * as stone_fornax_ml_flow_modelservice from './namespaces/stone_fornax_ml_flow_modelservice';
import * as stone_fornax_ml_flow_openapi_annotationjobservice from './namespaces/stone_fornax_ml_flow_openapi_annotationjobservice';
import * as stone_fornax_ml_flow_openapi_batchinferservice from './namespaces/stone_fornax_ml_flow_openapi_batchinferservice';
import * as stone_fornax_ml_flow_openapi_datasetservicev2 from './namespaces/stone_fornax_ml_flow_openapi_datasetservicev2';
import * as stone_fornax_ml_flow_tagservice from './namespaces/stone_fornax_ml_flow_tagservice';
import * as tag from './namespaces/tag';

export {
  ai_annotate,
  aidpcallback,
  annotation_job,
  base,
  batch_infer,
  dataprocess,
  dataset,
  datasetv2,
  datasetv2job,
  datasetv2lineage,
  datasetv2similarity,
  filter,
  flow_devops_evaluation_callback_common,
  flow_devops_prompt_common,
  fornax_domain_model,
  ml_flow_domain_model,
  openapi,
  stone_fornax_ml_flow_annotateservice,
  stone_fornax_ml_flow_annotationjobservice,
  stone_fornax_ml_flow_batchinferservice,
  stone_fornax_ml_flow_crowdsourcingannotationjobservice,
  stone_fornax_ml_flow_dataprocessservice,
  stone_fornax_ml_flow_datasetservice,
  stone_fornax_ml_flow_datasetservicev2,
  stone_fornax_ml_flow_modelservice,
  stone_fornax_ml_flow_openapi_annotationjobservice,
  stone_fornax_ml_flow_openapi_batchinferservice,
  stone_fornax_ml_flow_openapi_datasetservicev2,
  stone_fornax_ml_flow_tagservice,
  tag,
};
export * from './namespaces/ai_annotate';
export * from './namespaces/aidpcallback';
export * from './namespaces/annotation_job';
export * from './namespaces/base';
export * from './namespaces/batch_infer';
export * from './namespaces/dataprocess';
export * from './namespaces/dataset';
export * from './namespaces/datasetv2';
export * from './namespaces/datasetv2job';
export * from './namespaces/datasetv2lineage';
export * from './namespaces/datasetv2similarity';
export * from './namespaces/filter';
export * from './namespaces/flow_devops_evaluation_callback_common';
export * from './namespaces/flow_devops_prompt_common';
export * from './namespaces/fornax_domain_model';
export * from './namespaces/ml_flow_domain_model';
export * from './namespaces/openapi';
export * from './namespaces/stone_fornax_ml_flow_annotateservice';
export * from './namespaces/stone_fornax_ml_flow_annotationjobservice';
export * from './namespaces/stone_fornax_ml_flow_batchinferservice';
export * from './namespaces/stone_fornax_ml_flow_crowdsourcingannotationjobservice';
export * from './namespaces/stone_fornax_ml_flow_dataprocessservice';
export * from './namespaces/stone_fornax_ml_flow_datasetservice';
export * from './namespaces/stone_fornax_ml_flow_datasetservicev2';
export * from './namespaces/stone_fornax_ml_flow_modelservice';
export * from './namespaces/stone_fornax_ml_flow_openapi_annotationjobservice';
export * from './namespaces/stone_fornax_ml_flow_openapi_batchinferservice';
export * from './namespaces/stone_fornax_ml_flow_openapi_datasetservicev2';
export * from './namespaces/stone_fornax_ml_flow_tagservice';
export * from './namespaces/tag';

export type Int64 = string | number;

export default class FornaxMlFlowService<T> {
  private request: any = () => {
    throw new Error('FornaxMlFlowService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /api/ml_flow/v1/dataset_io_tasks/:taskID */
  GetDatasetIOTask(
    req: stone_fornax_ml_flow_datasetservice.GetDatasetIOTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservice.GetDatasetIOTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v1/dataset_io_tasks/${_req['taskID']}`,
    );
    const method = 'GET';
    const params = { spaceID: _req['spaceID'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v1/dataset_io_tasks
   *
   * 数据集导入导出
   */
  CreateDatasetIOTask(
    req: stone_fornax_ml_flow_datasetservice.CreateDatasetIOTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservice.CreateDatasetIOTaskResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ml_flow/v1/dataset_io_tasks');
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      datasetID: _req['datasetID'],
      file: _req['file'],
      ioType: _req['ioType'],
      option: _req['option'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks
   *
   * 创建打标规则
   */
  CreateAIAnnotateTask(
    req?: stone_fornax_ml_flow_annotateservice.CreateAIAnnotateTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.CreateAIAnnotateTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      datasetID: _req['datasetID'],
      datasetColumnName: _req['datasetColumnName'],
      promptID: _req['promptID'],
      promptVersion: _req['promptVersion'],
      userPromptColumnName: _req['userPromptColumnName'],
      promptVariables: _req['promptVariables'],
      executeConcurrency: _req['executeConcurrency'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks
   *
   * 获取打标规则列表
   */
  ListAIAnnotateTask(
    req?: stone_fornax_ml_flow_annotateservice.ListAIAnnotateTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.ListAIAnnotateTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks`,
    );
    const method = 'GET';
    const params = { dataset_id: _req['dataset_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks/:task_id
   *
   * 删除打标规则
   */
  DeleteAIAnnotateTask(
    req?: stone_fornax_ml_flow_annotateservice.DeleteAIAnnotateTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.DeleteAIAnnotateTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks/${_req['task_id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks/:task_id
   *
   * 获取打标规则详情
   */
  GetAIAnnotateTask(
    req?: stone_fornax_ml_flow_annotateservice.GetAIAnnotateTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.GetAIAnnotateTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks/${_req['task_id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks/:task_id/run
   *
   * 运行打标任务
   */
  RunAIAnnotate(
    req?: stone_fornax_ml_flow_annotateservice.RunAIAnnotateReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.RunAIAnnotateResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks/${_req['task_id']}/run`,
    );
    const method = 'POST';
    const data = { taskRunType: _req['taskRunType'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks/:task_id/task_runs/:task_run_id
   *
   * 获取任务执行情况信息
   */
  GetAIAnnotateTaskRun(
    req?: stone_fornax_ml_flow_annotateservice.GetAIAnnotateTaskRunReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.GetAIAnnotateTaskRunResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks/${_req['task_id']}/task_runs/${_req['task_run_id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks/:task_id
   *
   * 更新打标规则
   */
  UpdateAIAnnotateTask(
    req?: stone_fornax_ml_flow_annotateservice.UpdateAIAnnotateTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.UpdateAIAnnotateTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks/${_req['task_id']}`,
    );
    const method = 'PUT';
    const data = {
      name: _req['name'],
      promptID: _req['promptID'],
      promptVersion: _req['promptVersion'],
      userPromptColumnName: _req['userPromptColumnName'],
      promptVariables: _req['promptVariables'],
      executeConcurrency: _req['executeConcurrency'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks/:task_id/task_runs/:task_run_id/terminate
   *
   * 终止任务
   */
  TerminateAIAnnotateTaskRun(
    req?: stone_fornax_ml_flow_annotateservice.TerminateAIAnnotateTaskRunReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.TerminateAIAnnotateTaskRunResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks/${_req['task_id']}/task_runs/${_req['task_run_id']}/terminate`,
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v1/spaces/:space_id/ai_annotate_tasks/dry_run
   *
   * 试运行
   */
  DryRunAIAnnotate(
    req?: stone_fornax_ml_flow_annotateservice.DryRunAIAnnotateTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotateservice.DryRunAIAnnotateResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/ai_annotate_tasks/dry_run`,
    );
    const method = 'POST';
    const data = {
      datasetID: _req['datasetID'],
      datasetColumnName: _req['datasetColumnName'],
      promptID: _req['promptID'],
      promptVersion: _req['promptVersion'],
      userPromptColumnName: _req['userPromptColumnName'],
      promptVariables: _req['promptVariables'],
      sampleCount: _req['sampleCount'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID
   *
   * 数据集当前信息（不包括数据）
   */
  GetDataset(
    req: stone_fornax_ml_flow_datasetservicev2.GetDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      withDeleted: _req['withDeleted'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/ml_flow/v2/datasets/:datasetID
   *
   * 删除数据集
   */
  DeleteDataset(
    req: stone_fornax_ml_flow_datasetservicev2.DeleteDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.DeleteDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}`,
    );
    const method = 'DELETE';
    const params = { spaceID: _req['spaceID'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets
   *
   * 新增数据集
   */
  CreateDataset(
    req: stone_fornax_ml_flow_datasetservicev2.CreateDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.CreateDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets`,
    );
    const method = 'POST';
    const data = {
      appID: _req['appID'],
      name: _req['name'],
      description: _req['description'],
      category: _req['category'],
      bizCategory: _req['bizCategory'],
      fields: _req['fields'],
      securityLevel: _req['securityLevel'],
      visibility: _req['visibility'],
      spec: _req['spec'],
      features: _req['features'],
      userID: _req['userID'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/ml_flow/v2/datasets/:datasetID
   *
   * 修改数据集
   */
  UpdateDataset(
    req: stone_fornax_ml_flow_datasetservicev2.UpdateDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.UpdateDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}`,
    );
    const method = 'PUT';
    const data = {
      spaceID: _req['spaceID'],
      name: _req['name'],
      description: _req['description'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/items
   *
   * 分页查询当前数据
   */
  ListDatasetItems(
    req: stone_fornax_ml_flow_datasetservicev2.ListDatasetItemsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ListDatasetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      filter: _req['filter'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/versions
   *
   * 版本列表
   */
  ListDatasetVersions(
    req: stone_fornax_ml_flow_datasetservicev2.ListDatasetVersionsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ListDatasetVersionsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/versions`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      versionLike: _req['versionLike'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/versions
   *
   * 生成一个新版本
   */
  CreateDatasetVersion(
    req: stone_fornax_ml_flow_datasetservicev2.CreateDatasetVersionReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.CreateDatasetVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/versions`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      version: _req['version'],
      desc: _req['desc'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/ml_flow/v2/datasets/:datasetID/items/:itemID
   *
   * 更新数据
   */
  UpdateDatasetItem(
    req: stone_fornax_ml_flow_datasetservicev2.UpdateDatasetItemReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.UpdateDatasetItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/${_req['itemID']}`,
    );
    const method = 'PUT';
    const data = {
      spaceID: _req['spaceID'],
      data: _req['data'],
      repeatedData: _req['repeatedData'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/items/batch
   *
   * 批量新增数据
   */
  BatchCreateDatasetItems(
    req: stone_fornax_ml_flow_datasetservicev2.BatchCreateDatasetItemsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.BatchCreateDatasetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/batch`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      items: _req['items'],
      skipInvalidItems: _req['skipInvalidItems'],
      allowPartialAdd: _req['allowPartialAdd'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/ml_flow/v2/datasets/:datasetID/items/:itemID
   *
   * 删除数据
   */
  DeleteDatasetItem(
    req: stone_fornax_ml_flow_datasetservicev2.DeleteDatasetItemReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.DeleteDatasetItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/${_req['itemID']}`,
    );
    const method = 'DELETE';
    const params = { spaceID: _req['spaceID'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/items/:itemID
   *
   * 获取一行数据
   */
  GetDatasetItem(
    req: stone_fornax_ml_flow_datasetservicev2.GetDatasetItemReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetDatasetItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/${_req['itemID']}`,
    );
    const method = 'GET';
    const params = { spaceID: _req['spaceID'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/ml_flow/v2/datasets/:datasetID/schema
   *
   * 覆盖更新 schema
   */
  UpdateDatasetSchema(
    req: stone_fornax_ml_flow_datasetservicev2.UpdateDatasetSchemaReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.UpdateDatasetSchemaResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/schema`,
    );
    const method = 'PUT';
    const data = {
      spaceID: _req['spaceID'],
      fields: _req['fields'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/items/batch_delete
   *
   * 批量删除数据
   */
  BatchDeleteDatasetItems(
    req: stone_fornax_ml_flow_datasetservicev2.BatchDeleteDatasetItemsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.BatchDeleteDatasetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/batch_delete`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      itemIDs: _req['itemIDs'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/schema
   *
   * 获取数据集当前的 schema
   */
  GetDatasetSchema(
    req: stone_fornax_ml_flow_datasetservicev2.GetDatasetSchemaReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetDatasetSchemaResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/schema`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      withDeleted: _req['withDeleted'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/import
   *
   * 导入数据
   */
  ImportDataset(
    req: stone_fornax_ml_flow_datasetservicev2.ImportDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ImportDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/import`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      file: _req['file'],
      fieldMappings: _req['fieldMappings'],
      option: _req['option'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/search
   *
   * 获取数据集列表
   */
  SearchDatasets(
    req: stone_fornax_ml_flow_datasetservicev2.SearchDatasetsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.SearchDatasetsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/search`,
    );
    const method = 'POST';
    const data = {
      datasetIDs: _req['datasetIDs'],
      category: _req['category'],
      name: _req['name'],
      createdBys: _req['createdBys'],
      bizCategories: _req['bizCategories'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/versions/:versionID/items
   *
   * 分页查询指定版本的数据
   */
  ListDatasetItemsByVersion(
    req: stone_fornax_ml_flow_datasetservicev2.ListDatasetItemsByVersionReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ListDatasetItemsByVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/versions/${_req['versionID']}/items`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      filter: _req['filter'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/get_model
   *
   * 获取模型详情(商业化)
   */
  GetModel(
    req?: stone_fornax_ml_flow_modelservice.GetModelRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_modelservice.GetModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/get_model`,
    );
    const method = 'POST';
    const data = {
      provider: _req['provider'],
      providerModelID: _req['providerModelID'],
      base: _req['base'],
    };
    const headers = { cookie: _req['cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/get_model_usage
   *
   * 获取模型Token用量(商业化)
   */
  GetModelUsage(
    req?: stone_fornax_ml_flow_modelservice.GetModelUsageRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_modelservice.GetModelUsageResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/get_model_usage`,
    );
    const method = 'POST';
    const data = {
      modelIdentification: _req['modelIdentification'],
      provider: _req['provider'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/v1/list
   *
   * 获取模型列表(商业化)
   */
  ListModel(
    req?: stone_fornax_ml_flow_modelservice.ListModelRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_modelservice.ListModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/v1/list`,
    );
    const method = 'POST';
    const data = {
      cursorID: _req['cursorID'],
      limit: _req['limit'],
      filter: _req['filter'],
      base: _req['base'],
    };
    const headers = { cookie: _req['cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/get_model_filter_params
   *
   * 获取模型列表过滤参数(商业化)
   */
  GetModelFilterParams(
    req?: stone_fornax_ml_flow_modelservice.GetModelFilterParamsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_modelservice.GetModelFilterParamsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/model_manage/v1/get_model_filter_params');
    const method = 'POST';
    const data = { base: _req['base'] };
    const headers = { cookie: _req['cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/ml_flow/v2/files/upload_token
   *
   * Dataset IO Job
   */
  SignUploadFileToken(
    req?: stone_fornax_ml_flow_datasetservicev2.SignUploadFileTokenReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.SignUploadFileTokenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/ml_flow/v2/files/upload_token');
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      storage: _req['storage'],
      fileName: _req['fileName'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/dataset_io_jobs/:jobID
   *
   * 任务(导入、导出、转换)详情
   */
  GetDatasetIOJob(
    req: stone_fornax_ml_flow_datasetservicev2.GetDatasetIOJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetDatasetIOJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/dataset_io_jobs/${_req['jobID']}`,
    );
    const method = 'GET';
    const params = { spaceID: _req['spaceID'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/items/batch_get
   *
   * 批量获取数据
   */
  BatchGetDatasetItems(
    req: stone_fornax_ml_flow_datasetservicev2.BatchGetDatasetItemsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.BatchGetDatasetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/batch_get`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      itemIDs: _req['itemIDs'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/versions/:versionID/items/batch_get
   *
   * 批量获取指定版本的数据
   */
  BatchGetDatasetItemsByVersion(
    req: stone_fornax_ml_flow_datasetservicev2.BatchGetDatasetItemsByVersionReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.BatchGetDatasetItemsByVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/versions/${_req['versionID']}/items/batch_get`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      itemIDs: _req['itemIDs'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/batch_get
   *
   * 批量获取数据集
   */
  BatchGetDatasets(
    req: stone_fornax_ml_flow_datasetservicev2.BatchGetDatasetsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.BatchGetDatasetsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/batch_get`,
    );
    const method = 'POST';
    const data = {
      datasetIDs: _req['datasetIDs'],
      withDeleted: _req['withDeleted'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/versioned_datasets/batch_get
   *
   * 批量获取指定版本的数据集详情
   */
  BatchGetVersionedDatasets(
    req: stone_fornax_ml_flow_datasetservicev2.BatchGetVersionedDatasetsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.BatchGetVersionedDatasetsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/versioned_datasets/batch_get`,
    );
    const method = 'POST';
    const data = {
      versionIDs: _req['versionIDs'],
      withDeleted: _req['withDeleted'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/io_jobs
   *
   * 数据集任务列表，用于获取当前数据集的导入任务
   */
  ListDatasetIOJobsOfDataset(
    req: stone_fornax_ml_flow_datasetservicev2.ListDatasetIOJobsOfDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ListDatasetIOJobsOfDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/io_jobs`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      types: _req['types'],
      statuses: _req['statuses'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/dataset_versions/:versionID
   *
   * 获取指定版本的数据集详情
   */
  GetDatasetVersion(
    req: stone_fornax_ml_flow_datasetservicev2.GetDatasetVersionReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetDatasetVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/dataset_versions/${_req['versionID']}`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      withDeleted: _req['withDeleted'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/versions/:versionID/items/batch_get
   *
   * 批量获取固定版本数据行
   */
  OpenBatchGetDatasetItemsByVersion(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchGetDatasetItemsByVersionRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchGetDatasetItemsByVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/versions/${_req['versionID']}/items/batch_get`,
    );
    const method = 'POST';
    const data = { itemIDs: _req['itemIDs'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /open-api/ml_flow/v2/datasets/:datasetID/items
   *
   * 草稿态的数据行列表
   */
  OpenListDatasetItems(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenListDatasetItemsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenListDatasetItemsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/items`,
    );
    const method = 'GET';
    const params = { cursor: _req['cursor'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * PATCH /open-api/ml_flow/v2/datasets/:datasetID/items/:itemID
   *
   * 更新数据行的数据内容
   */
  OpenPatchDatasetItem(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenPatchDatasetItemRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenPatchDatasetItemResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/items/${_req['itemID']}`,
    );
    const method = 'PATCH';
    const data = {
      data: _req['data'],
      repeatedData: _req['repeatedData'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/items/batch_get
   *
   * 批量获取草稿态数据行
   */
  OpenBatchGetDatasetItems(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchGetDatasetItemsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchGetDatasetItemsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/items/batch_get`,
    );
    const method = 'POST';
    const data = { itemIDs: _req['itemIDs'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/items/batch_delete
   *
   * 批量删除草稿态数据行
   */
  OpenBatchDeleteDatasetItems(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchDeleteDatasetItemsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchDeleteDatasetItemsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/items/batch_delete`,
    );
    const method = 'POST';
    const data = { itemIDs: _req['itemIDs'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/versions
   *
   * 创建数据集版本
   */
  OpenCreateDatasetVersion(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenCreateDatasetVersionRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenCreateDatasetVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/versions`,
    );
    const method = 'POST';
    const data = {
      version: _req['version'],
      desc: _req['desc'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/items/clear
   *
   * 清空草稿态数据行
   */
  OpenClearDatasetItems(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenClearDatasetItemsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenClearDatasetItemsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/items/clear`,
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /open-api/ml_flow/v2/datasets/:datasetID/versions/:versionID/items
   *
   * 固定版本的数据行列表
   */
  OpenListDatasetItemsByVersion(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenListDatasetItemsByVersionRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenListDatasetItemsByVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/versions/${_req['versionID']}/items`,
    );
    const method = 'GET';
    const params = { cursor: _req['cursor'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /open-api/ml_flow/v2/datasets/:datasetID/versions
   *
   * 获取数据集版本列表
   */
  OpenListDatasetVersions(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenListDatasetVersionsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenListDatasetVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/versions`,
    );
    const method = 'GET';
    const params = { cursor: _req['cursor'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/items/batch
   *
   * 批量新增
   */
  OpenBatchCreateDatasetItems(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchCreateDatasetItemsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenBatchCreateDatasetItemsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/items/batch`,
    );
    const method = 'POST';
    const data = {
      items: _req['items'],
      skipInvalidItems: _req['skipInvalidItems'],
      allowPartialAdd: _req['allowPartialAdd'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/search
   *
   * 获取数据集列表
   */
  OpenSearchDatasets(
    req?: stone_fornax_ml_flow_openapi_datasetservicev2.OpenSearchDatasetsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenSearchDatasetsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/open-api/ml_flow/v2/datasets/search');
    const method = 'POST';
    const data = {
      name: _req['name'],
      createdBys: _req['createdBys'],
      cursor: _req['cursor'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/ml_flow/v2/spaces/:spaceID/dataset_io_jobs/:jobID/cancel
   *
   * 取消一个任务
   */
  CancelDatasetIOJob(
    req: stone_fornax_ml_flow_datasetservicev2.CancelDatasetIOJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.CancelDatasetIOJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/dataset_io_jobs/${_req['jobID']}/cancel`,
    );
    const method = 'PUT';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/ml_flow/v2/spaces/:spaceID/dataset_versions/:versionID
   *
   * 更新一个版本
   */
  UpdateDatasetVersion(
    req: stone_fornax_ml_flow_datasetservicev2.UpdateDatasetVersionReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.UpdateDatasetVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/dataset_versions/${_req['versionID']}`,
    );
    const method = 'PUT';
    const data = { desc: _req['desc'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs/:jobID
   *
   * 更新质量分任务
   */
  UpdateQualityScoreJob(
    req: stone_fornax_ml_flow_annotationjobservice.UpdateQualityScoreJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.UpdateQualityScoreJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs/${_req['jobID']}`,
    );
    const method = 'PUT';
    const data = { job: _req['job'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs
   *
   * 创建质量分任务
   */
  CreateQualityScoreJob(
    req: stone_fornax_ml_flow_annotationjobservice.CreateQualityScoreJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.CreateQualityScoreJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs`,
    );
    const method = 'POST';
    const data = { job: _req['job'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs/:jobID
   *
   * 获取质量分任务
   */
  GetQualityScoreJob(
    req: stone_fornax_ml_flow_annotationjobservice.GetQualityScoreJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.GetQualityScoreJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs/${_req['jobID']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs/:jobID
   *
   * 删除质量分任务
   */
  DeleteQualityScoreJob(
    req: stone_fornax_ml_flow_annotationjobservice.DeleteQualityScoreJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.DeleteQualityScoreJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs/${_req['jobID']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs/dry_run
   *
   * 试运行质量分任务
   */
  DryRunQualityScoreJob(
    req: stone_fornax_ml_flow_annotationjobservice.DryRunQualityScoreJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.DryRunQualityScoreJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs/dry_run`,
    );
    const method = 'POST';
    const data = {
      job: _req['job'],
      sampleCount: _req['sampleCount'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs
   *
   * 获取质量分任务
   */
  ListQualityScoreJobs(
    req: stone_fornax_ml_flow_annotationjobservice.ListQualityScoreJobsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.ListQualityScoreJobsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs`,
    );
    const method = 'GET';
    const params = {
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/items/:id/source
   *
   * 查询 item 的来源信息
   */
  GetDatasetItemSource(
    req: stone_fornax_ml_flow_datasetservicev2.GetDatasetItemSourceReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetDatasetItemSourceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/${_req['id']}/source`,
    );
    const method = 'GET';
    const params = { spaceID: _req['spaceID'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/deduplicate/dedup_jobs/:jobID
   *
   * 获取判重任务
   */
  GetItemDeduplicateJob(
    req: stone_fornax_ml_flow_datasetservicev2.GetItemDeduplicateJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetItemDeduplicateJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/deduplicate/dedup_jobs/${_req['jobID']}`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      confirmType: _req['confirmType'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/deduplicate/dedup_jobs
   *
   * 创建判重任务
   */
  CreateItemDeduplicateJob(
    req: stone_fornax_ml_flow_datasetservicev2.CreateItemDeduplicateJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.CreateItemDeduplicateJobResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ml_flow/v2/deduplicate/dedup_jobs');
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      datasetID: _req['datasetID'],
      file: _req['file'],
      fieldMappings: _req['fieldMappings'],
      option: _req['option'],
      jobID: _req['jobID'],
      fieldKey: _req['fieldKey'],
      similarityAlgorithm: _req['similarityAlgorithm'],
      threshold: _req['threshold'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/deduplicate/dedup_jobs/:jobID/confirm
   *
   * 确认疑似重复任务
   */
  ConfirmItemDeduplicate(
    req: stone_fornax_ml_flow_datasetservicev2.ConfirmItemDeduplicateReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ConfirmItemDeduplicateResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/deduplicate/dedup_jobs/${_req['jobID']}/confirm`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      pairs: _req['pairs'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_job_instances/:jobID
   *
   * 获取任务执行情况信息
   */
  GetQualityScoreJobInstance(
    req: stone_fornax_ml_flow_annotationjobservice.GetQualityScoreJobInstanceRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.GetQualityScoreJobInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_job_instances/${_req['jobID']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs/:jobID/instances/:instanceID/terminate
   *
   * 终止任务
   */
  TerminateQualityScoreJobInstance(
    req: stone_fornax_ml_flow_annotationjobservice.TerminateQualityScoreJobInstanceRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.TerminateQualityScoreJobInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs/${_req['jobID']}/instances/${_req['instanceID']}/terminate`,
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /open-api/ml_flow/v2/datasets/:datasetID/items/:itemID
   *
   * 获取某条数据行&血缘数据，主键ID
   */
  OpenGetDatasetItem(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenGetDatasetItemRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenGetDatasetItemResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/items/${_req['itemID']}`,
    );
    const method = 'GET';
    const params = {
      withDeepSources: _req['withDeepSources'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/io_jobs/search
   *
   * 数据集任务列表，用于获取当前数据集的导入任务(POST 方法，便于传参)
   */
  SearchDatasetIOJobsOfDataset(
    req: stone_fornax_ml_flow_datasetservicev2.SearchDatasetIOJobsOfDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.SearchDatasetIOJobsOfDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/io_jobs/search`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      types: _req['types'],
      statuses: _req['statuses'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/versions/:versionID/items/search
   *
   * 分页查询指定版本的数据(POST 方法，便于传参)
   */
  SearchDatasetItemsByVersion(
    req: stone_fornax_ml_flow_datasetservicev2.SearchDatasetItemsByVersionReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.SearchDatasetItemsByVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/versions/${_req['versionID']}/items/search`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      filter: _req['filter'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/versions/search
   *
   * 版本列表(POST 方法，便于传参)
   */
  SearchDatasetVersions(
    req: stone_fornax_ml_flow_datasetservicev2.SearchDatasetVersionsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.SearchDatasetVersionsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/versions/search`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      versionLike: _req['versionLike'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:datasetID/items/search
   *
   * 分页查询当前数据(POST 方法，便于传参)
   */
  SearchDatasetItems(
    req: stone_fornax_ml_flow_datasetservicev2.SearchDatasetItemsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.SearchDatasetItemsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/search`,
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      filter: _req['filter'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/create_with_import
   *
   * 从数据集导入数据
   */
  CreateDatasetWithImport(
    req: stone_fornax_ml_flow_datasetservicev2.CreateDatasetWithImportReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.CreateDatasetWithImportResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ml_flow/v2/datasets/create_with_import');
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      appID: _req['appID'],
      sourceType: _req['sourceType'],
      source: _req['source'],
      fieldMappings: _req['fieldMappings'],
      option: _req['option'],
      targetDatasetName: _req['targetDatasetName'],
      targetDatasetDesc: _req['targetDatasetDesc'],
      category: _req['category'],
      fields: _req['fields'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/datasets/:datasetID/items/:id/deep_sources
   *
   * 查询 item 的溯源信息
   */
  GetDatasetItemDeepSources(
    req: stone_fornax_ml_flow_datasetservicev2.GetDatasetItemDeepSourcesReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetDatasetItemDeepSourcesResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['datasetID']}/items/${_req['id']}/deep_sources`,
    );
    const method = 'GET';
    const params = { spaceID: _req['spaceID'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs/:jobID/run
   *
   * 运行打标任务
   */
  RunQualityScoreJob(
    req: stone_fornax_ml_flow_annotationjobservice.RunQualityScoreJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.RunQualityScoreJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs/${_req['jobID']}/run`,
    );
    const method = 'POST';
    const data = {
      taskRunType: _req['taskRunType'],
      filter: _req['filter'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/quality_score_jobs/:jobID/sync_run
   *
   * 同步运行几条打分任务
   */
  RunQualityScoreSync(
    req: stone_fornax_ml_flow_annotationjobservice.RunQualityScoreSyncRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.RunQualityScoreSyncResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/quality_score_jobs/${_req['jobID']}/sync_run`,
    );
    const method = 'POST';
    const data = { itemIDs: _req['itemIDs'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/items
   *
   * 创建数据行
   */
  CreateDatasetItem(
    req: stone_fornax_ml_flow_datasetservicev2.CreateDatasetItemReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.CreateDatasetItemResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/items`,
    );
    const method = 'POST';
    const data = {
      itemKey: _req['itemKey'],
      data: _req['data'],
      repeatedData: _req['repeatedData'],
      keepLineage: _req['keepLineage'],
      sourceItemID: _req['sourceItemID'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/fields_meta_info
   *
   * 获取筛选元数据
   */
  GetFieldsMetaInfo(
    req: stone_fornax_ml_flow_datasetservicev2.GetFieldsMetaInfoRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.GetFieldsMetaInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/fields_meta_info`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/tags
   *
   * 创建标签
   */
  CreateTag(
    req: stone_fornax_ml_flow_tagservice.CreateTagRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.CreateTagResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/tags`,
    );
    const method = 'POST';
    const data = {
      tagKeyName: _req['tagKeyName'],
      tagType: _req['tagType'],
      version: _req['version'],
      description: _req['description'],
      tagValues: _req['tagValues'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/tags/search
   *
   * 查询标签列表
   */
  SearchTags(
    req: stone_fornax_ml_flow_tagservice.SearchTagsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.SearchTagsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/tags/search`,
    );
    const method = 'POST';
    const data = {
      status: _req['status'],
      tagKeyNameLike: _req['tagKeyNameLike'],
      createdBys: _req['createdBys'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PATCH /api/ml_flow/v2/spaces/:spaceID/tags/:tagKeyID
   *
   * 更新标签
   */
  UpdateTag(
    req: stone_fornax_ml_flow_tagservice.UpdateTagRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.UpdateTagResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/tags/${_req['tagKeyID']}`,
    );
    const method = 'PATCH';
    const data = {
      version: _req['version'],
      tagKeyName: _req['tagKeyName'],
      description: _req['description'],
      tagType: _req['tagType'],
      tagValues: _req['tagValues'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/tags/batch_update_status
   *
   * 批量更新标签状态
   */
  BatchUpdateTagStatus(
    req: stone_fornax_ml_flow_tagservice.BatchUpdateTagStatusRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.BatchUpdateTagStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/tags/batch_update_status`,
    );
    const method = 'POST';
    const data = {
      tagKeyIDs: _req['tagKeyIDs'],
      status: _req['status'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/export
   *
   * 导出数据
   */
  ExportDataset(
    req: stone_fornax_ml_flow_datasetservicev2.ExportDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ExportDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/export`,
    );
    const method = 'POST';
    const data = {
      versionID: _req['versionID'],
      targetType: _req['targetType'],
      target: _req['target'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/tags/:tagKeyID/archive_option_tag
   *
   * 将单选标签归档进标签管理
   */
  ArchiveOptionTag(
    req: stone_fornax_ml_flow_tagservice.ArchiveOptionTagRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.ArchiveOptionTagResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/tags/${_req['tagKeyID']}/archive_option_tag`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/tags/:tagKeyID
   *
   * 获取标签明细
   */
  GetTagDetail(
    req: stone_fornax_ml_flow_tagservice.GetTagDetailRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.GetTagDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/tags/${_req['tagKeyID']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /open-api/ml_flow/v2/dataset_io_jobs/:jobID
   *
   * 任务(导入、导出、转换)详情
   */
  OpenGetDatasetIOJob(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenGetDatasetIOJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenGetDatasetIOJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/dataset_io_jobs/${_req['jobID']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/import
   *
   * 导入数据
   */
  OpenImportDataset(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenImportDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenImportDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/import`,
    );
    const method = 'POST';
    const data = {
      file: _req['file'],
      fieldMappings: _req['fieldMappings'],
      option: _req['option'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/export
   *
   * 导出数据
   */
  OpenExportDataset(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenExportDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenExportDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/export`,
    );
    const method = 'POST';
    const data = {
      versionID: _req['versionID'],
      targetType: _req['targetType'],
      target: _req['target'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /open-api/ml_flow/v2/dataset_io_jobs/:jobID/cancel
   *
   * 取消一个任务
   */
  OpenCancelDatasetIOJob(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenCancelDatasetIOJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenCancelDatasetIOJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/dataset_io_jobs/${_req['jobID']}/cancel`,
    );
    const method = 'PUT';
    const data = { base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/parse_import_source_file
   *
   * 解析源文件
   */
  ParseImportSourceFile(
    req: stone_fornax_ml_flow_datasetservicev2.ParseImportSourceFileReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ParseImportSourceFileResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/parse_import_source_file`,
    );
    const method = 'POST';
    const data = { file: _req['file'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/:jobID/run
   *
   * 运行标注任务
   */
  RunAnnotationJob(
    req: stone_fornax_ml_flow_annotationjobservice.RunAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.RunAnnotationJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}/run`,
    );
    const method = 'POST';
    const data = {
      jwtToken: _req['jwtToken'],
      annotationJobRunID: _req['annotationJobRunID'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/:jobID/job_run_instances/:jobRunID/terminate
   *
   * 终止正在运行的打标任务
   */
  TerminateAnnotationJob(
    req: stone_fornax_ml_flow_annotationjobservice.TerminateAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.TerminateAnnotationJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}/job_run_instances/${_req['jobRunID']}/terminate`,
    );
    const method = 'POST';
    const data = { jwtToken: _req['jwtToken'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/process
   *
   * 查看当前数据集进行中标注任务进度
   */
  GetAnnotationJobProcess(
    req: stone_fornax_ml_flow_annotationjobservice.GetAnnotationJobProcessRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.GetAnnotationJobProcessResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/process`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/associated
   *
   * 获取数据集每列关联的标注任务
   */
  GetAnnotationJobsWithDataset(
    req: stone_fornax_ml_flow_annotationjobservice.GetAnnotationJobsWithDatasetRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.GetAnnotationJobsWithDatasetResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/associated`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/:jobID
   *
   * 获取单个标注任务详情
   */
  GetAnnotationJobDetail(
    req: stone_fornax_ml_flow_annotationjobservice.GetAnnotationJobDetailRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.GetAnnotationJobDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /open-api/ml_flow/v2/datasets/:datasetID/annotation_jobs/:jobID/instances
   *
   * 查看任务实例状态
   */
  OpenGetAnnotationJobInstance(
    req: stone_fornax_ml_flow_openapi_annotationjobservice.OpenGetAnnotationJobInstanceRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_annotationjobservice.OpenGetAnnotationJobInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}/instances`,
    );
    const method = 'GET';
    const params = { jobInstanceID: _req['jobInstanceID'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/annotation_jobs/:jobID/run
   *
   * 运行任务
   */
  OpenRunAnnotationJob(
    req: stone_fornax_ml_flow_openapi_annotationjobservice.OpenRunAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_annotationjobservice.OpenRunAnnotationJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}/run`,
    );
    const method = 'POST';
    const data = { jwtToken: _req['jwtToken'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /open-api/ml_flow/v2/datasets/:datasetID/annotation_jobs
   *
   * 列举数据集的标注任务
   */
  OpenListAnnotationJobs(
    req: stone_fornax_ml_flow_openapi_annotationjobservice.OpenListAnnotationJobsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_annotationjobservice.OpenListAnnotationJobsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/annotation_jobs`,
    );
    const method = 'GET';
    const params = { cursor: _req['cursor'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/annotation_jobs/:jobID/instances/:instanceID/terminate
   *
   * 终止标注任务
   */
  OpenTerminateAnnotationJob(
    req: stone_fornax_ml_flow_openapi_annotationjobservice.OpenTerminateAnnotationJobInstanceRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_annotationjobservice.OpenTerminateAnnotationJobInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}/instances/${_req['instanceID']}/terminate`,
    );
    const method = 'POST';
    const data = { jwtToken: _req['jwtToken'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/ml_flow/v2/datasets/:dataset_id/items/clear
   *
   * 清除(草稿)数据项
   */
  ClearDatasetItem(
    req: stone_fornax_ml_flow_datasetservicev2.ClearDatasetItemRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ClearDatasetItemResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/datasets/${_req['dataset_id']}/items/clear`,
    );
    const method = 'POST';
    const data = { spaceID: _req['spaceID'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/job_run_instances/search
   *
   * 搜索标注任务列表
   */
  SearchAnnotationJobInstances(
    req: stone_fornax_ml_flow_annotationjobservice.SearchAnnotationjobInstancesRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.SearchAnnotationjobInstancesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/job_run_instances/search`,
    );
    const method = 'POST';
    const data = {
      typeFilter: _req['typeFilter'],
      createdBys: _req['createdBys'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/insight/field_distribute
   *
   * 获取洞察分布
   */
  QueryFieldDistribute(
    req: stone_fornax_ml_flow_tagservice.QueryFieldDistributeRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.QueryFieldDistributeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/insight/field_distribute`,
    );
    const method = 'GET';
    const params = {
      datasetVersion: _req['datasetVersion'],
      fieldKeys: _req['fieldKeys'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/:jobID
   *
   * 更新标注任务
   */
  UpdateAnnotationJob(
    req: stone_fornax_ml_flow_annotationjobservice.UpdateAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.UpdateAnnotationJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}`,
    );
    const method = 'PUT';
    const data = {
      name: _req['name'],
      content: _req['content'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs
   *
   * 创建标注任务
   */
  CreateAnnotationJob(
    req: stone_fornax_ml_flow_annotationjobservice.CreateAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.CreateAnnotationJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      content: _req['content'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/dataset_items/validate
   *
   * 校验数据
   */
  ValidateDatasetItems(
    req?: stone_fornax_ml_flow_datasetservicev2.ValidateDatasetItemsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_datasetservicev2.ValidateDatasetItemsResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/ml_flow/v2/dataset_items/validate');
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      items: _req['items'],
      datasetID: _req['datasetID'],
      datasetCategory: _req['datasetCategory'],
      datasetFields: _req['datasetFields'],
      ignoreCurrentItemCount: _req['ignoreCurrentItemCount'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/datasets/:datasetID/annotation_jobs/:jobID/clone
   *
   * 复用标注任务
   */
  OpenCloneAnnotationJob(
    req: stone_fornax_ml_flow_openapi_annotationjobservice.OpenCloneAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_annotationjobservice.OpenCloneAnnotationJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}/clone`,
    );
    const method = 'POST';
    const data = {
      targetDatasetID: _req['targetDatasetID'],
      jobName: _req['jobName'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * DELETE /open-api/ml_flow/v2/datasets/:datasetID/annotation_jobs/:jobID
   *
   * 删除数据集的标注任务
   */
  OpenDeleteAnnotationJob(
    req?: stone_fornax_ml_flow_openapi_annotationjobservice.OpenDeleteAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_annotationjobservice.OpenDeleteAnnotationJobResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * DELETE /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/:jobID
   *
   * 删除标注任务
   */
  DeleteAnnotationJob(
    req: stone_fornax_ml_flow_annotationjobservice.DeleteAnnotationJobRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.DeleteAnnotationJobResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/${_req['jobID']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/annotation_jobs/preview
   *
   * 预览标注任务效果
   */
  PreviewAnnotation(
    req: stone_fornax_ml_flow_annotationjobservice.PreviewAnnotationRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.PreviewAnnotationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/annotation_jobs/preview`,
    );
    const method = 'POST';
    const data = {
      job: _req['job'],
      fields: _req['fields'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v2/spaces/:spaceID/annotation_jobs/search
   *
   * 搜索标注任务
   */
  SearchAnnotationJobs(
    req: stone_fornax_ml_flow_annotationjobservice.SearchAnnotationJobsRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_annotationjobservice.SearchAnnotationJobsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/annotation_jobs/search`,
    );
    const method = 'POST';
    const data = {
      nameLike: _req['nameLike'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      cursor: _req['cursor'],
      orderBy: _req['orderBy'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v1/batch_infer/list_model_cards
   *
   * 获取模型card列表
   */
  ListModelCards(
    req?: stone_fornax_ml_flow_batchinferservice.ListModelCardsReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_batchinferservice.ListModelCardsResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/ml_flow/v1/batch_infer/list_model_cards');
    const method = 'POST';
    const data = {
      provider: _req['provider'],
      nameKeyword: _req['nameKeyword'],
      userJwtToken: _req['userJwtToken'],
      limit: _req['limit'],
      offset: _req['offset'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v1/batch_infer/parse_model_config
   *
   * 解析模型配置
   */
  ParseModelConfig(
    req?: stone_fornax_ml_flow_batchinferservice.ParseModelConfigReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_batchinferservice.ParseModelConfigResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/ml_flow/v1/batch_infer/parse_model_config',
    );
    const method = 'POST';
    const data = {
      provider: _req['provider'],
      seedHdfsAddress: _req['seedHdfsAddress'],
      seedModelCardID: _req['seedModelCardID'],
      userJwtToken: _req['userJwtToken'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ml_flow/v1/batch_infer/get_recommend_resource
   *
   * 根据模型card获得推荐的merlin资源信息
   */
  GetRecommendResource(
    req?: stone_fornax_ml_flow_batchinferservice.GetRecommendResourceReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_batchinferservice.GetRecommendResourceResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/ml_flow/v1/batch_infer/get_recommend_resource',
    );
    const method = 'POST';
    const data = {
      modelCardID: _req['modelCardID'],
      userJwtToken: _req['userJwtToken'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ml_flow/v2/spaces/:spaceID/tags/spec
   *
   * 获取spaceid维度标签配置
   */
  GetTagSpec(
    req: stone_fornax_ml_flow_tagservice.GetTagSpecRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_tagservice.GetTagSpecResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/tags/spec`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /open-api/ml_flow/v2/datasets */
  OpenCreateDataset(
    req: stone_fornax_ml_flow_openapi_datasetservicev2.OpenCreateDatasetReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenCreateDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/open-api/ml_flow/v2/datasets');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      fields: _req['fields'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/ml_flow/v1/data_process/user_script_template */
  GetUserScriptTemplate(
    req?: stone_fornax_ml_flow_dataprocessservice.GetUserScriptTemplateRequest,
    options?: T,
  ): Promise<stone_fornax_ml_flow_dataprocessservice.GetUserScriptTemplateResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/ml_flow/v1/data_process/user_script_template',
    );
    const method = 'GET';
    const params = { template_type: _req['template_type'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/crowdsourcing_annotate_jobs/:jobID/terminate */
  TerminateCrowdsourcingAnnotateJob(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.TerminateCrowdsourcingAnnotateJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.TerminateCrowdsourcingAnnotateJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/crowdsourcing_annotate_jobs/${_req['jobID']}/terminate`,
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/ml_flow/v2/spaces/:spaceID/crowdsourcing_annotate_jobs/:jobID/refresh */
  RefreshCrowdsourcingAnnotateJob(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.RefreshCrowdsourcingAnnotateJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.RefreshCrowdsourcingAnnotateJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/crowdsourcing_annotate_jobs/${_req['jobID']}/refresh`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** DELETE /api/ml_flow/v2/spaces/:spaceID/datasets/:datasetID/crowdsourcing_annotate_jobs/:jobID */
  DeleteCrowdsourcingAnnotateJob(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.DeleteCrowdsourcingAnnotateJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.DeleteCrowdsourcingAnnotateJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/crowdsourcing_annotate_jobs/${_req['jobID']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/ml_flow/v2/spaces/:spaceID/crowdsourcing_annotate_jobs/list */
  ListCrowdsourcingAnnotateJob(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.ListCrowdsourcingAnnotateJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.ListCrowdsourcingAnnotateJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/crowdsourcing_annotate_jobs/list`,
    );
    const method = 'POST';
    const data = {
      searchConfig: _req['searchConfig'],
      page: _req['page'],
      pageSize: _req['pageSize'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/ml_flow/v2/spaces/:spaceID/crowdsourcing_annotate_jobs/authorized_aidp_info */
  ListAuthorizedAIDPTaskSummaries(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.ListAuthorizedAIDPTasksReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.ListAuthorizedAIDPTasksResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/crowdsourcing_annotate_jobs/authorized_aidp_info`,
    );
    const method = 'GET';
    const params = {
      platform: _req['platform'],
      keyword: _req['keyword'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/ml_flow/v2/spaces/:spaceID/templateID/:templateID/answer_schema */
  GetAnswerSchema(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.GetAnswerSchemaReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.GetAnswerSchemaResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/templateID/${_req['templateID']}/answer_schema`,
    );
    const method = 'GET';
    const params = { platform: _req['platform'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ml_flow/v1/spaces/:space_id/batch_infer/:task_id
   *
   * 获取批量推理任务详情
   */
  GetBatchInferTask(
    req?: stone_fornax_ml_flow_batchinferservice.GetBatchInferTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_batchinferservice.GetBatchInferTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/ml_flow/v1/spaces/${_req['space_id']}/batch_infer/${_req['task_id']}`,
    );
    const method = 'POST';
    const data = { userJwtToken: _req['userJwtToken'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /open-api/ml_flow/v2/batch_infer/spaces/:space_id/tasks/:task_id */
  OpenGetBatchInferTask(
    req?: stone_fornax_ml_flow_openapi_batchinferservice.OpenGetBatchInferTaskReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_batchinferservice.OpenGetBatchInferTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/batch_infer/spaces/${_req['space_id']}/tasks/${_req['task_id']}`,
    );
    const method = 'GET';
    const params = { userJwtToken: _req['userJwtToken'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /open-api/ml_flow/v2/batch_infer/spaces/:space_id/tasks/:task_id/report_progress */
  OpenReportBatchInferProgress(
    req?: stone_fornax_ml_flow_openapi_batchinferservice.OpenReportBatchInferProgressReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_batchinferservice.OpenReportBatchInferProgressResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/open-api/ml_flow/v2/batch_infer/spaces/${_req['space_id']}/tasks/${_req['task_id']}/report_progress`,
    );
    const method = 'POST';
    const data = { errorMsg: _req['errorMsg'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/ml_flow/v2/spaces/:spaceID/crowdsourcing_annotate_jobs */
  CreateCrowdsourcingAnnotateJob(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.CreateCrowdsourcingAnnotateJobReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.CreateCrowdsourcingAnnotateJobResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/crowdsourcing_annotate_jobs`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      platform: _req['platform'],
      aidpConfig: _req['aidpConfig'],
      inputConfig: _req['inputConfig'],
      outputConfig: _req['outputConfig'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open-api/ml_flow/v2/parse_import_source_file
   *
   * 解析导入文件的列配置
   */
  OpenParseImportSourceFile(
    req?: stone_fornax_ml_flow_openapi_datasetservicev2.OpenParseImportSourceFileReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_openapi_datasetservicev2.OpenParseImportSourceFileResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/open-api/ml_flow/v2/parse_import_source_file',
    );
    const method = 'POST';
    const data = { file: _req['file'], base: _req['base'] };
    const headers = {
      'FlowDevops-Agw-OpenAPI-AppId': _req['FlowDevops-Agw-OpenAPI-AppId'],
      'FlowDevops-Agw-OpenAPI-SpaceId': _req['FlowDevops-Agw-OpenAPI-SpaceId'],
      'FlowDevops-Agw-OpenAPI-AccountId':
        _req['FlowDevops-Agw-OpenAPI-AccountId'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /** GET /api/ml_flow/v2/spaces/:spaceID/taskID/:taskID/aidp_flow_config */
  GetAIDPTaskConfig(
    req: stone_fornax_ml_flow_crowdsourcingannotationjobservice.GetAIDPTaskConfigReq,
    options?: T,
  ): Promise<stone_fornax_ml_flow_crowdsourcingannotationjobservice.GetAIDPTaskConfigResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ml_flow/v2/spaces/${_req['spaceID']}/taskID/${_req['taskID']}/aidp_flow_config`,
    );
    const method = 'GET';
    const params = { platform: _req['platform'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
