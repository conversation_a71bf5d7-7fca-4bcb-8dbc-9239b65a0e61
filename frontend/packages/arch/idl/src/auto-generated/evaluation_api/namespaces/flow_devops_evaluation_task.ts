/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';
import * as base from './base';
import * as flow_devops_evaluation_object_callback from './flow_devops_evaluation_object_callback';
import * as flow_devops_evaluation_manual_annotation from './flow_devops_evaluation_manual_annotation';
import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_evaluator_callback from './flow_devops_evaluation_evaluator_callback';
import * as flow_devops_evaluation_evaluator from './flow_devops_evaluation_evaluator';

export type Int64 = string | number;

/** 聚合方式 */
export enum AggregatorMode {
  Unknown = 0,
  /** 按照评测维度标签聚合 */
  EvaluatorTag = 1,
}

/** 聚合器类型 */
export enum AggregatorType {
  Average = 1,
  Sum = 2,
  Max = 3,
  Min = 4,
  /** 使用double类型来表示百分比。例如50.5%设置为0.505 */
  PassingRate = 5,
  ExcellentRate = 6,
  /** 人工选项打分，count 各选项并给出比例分布 */
  Count = 7,
  Pct50 = 8,
  Pct90 = 9,
  Pct99 = 10,
}

export enum BatchTaskRetryMode {
  All = 1,
  Unsuccessful = 2,
}

export enum BitableStatus {
  Unknown = 0,
  /** 任务未开始 */
  Running = 1,
  /** 任务成功 */
  Success = 2,
  /** 任务失败 */
  Failed = 3,
}

/** ChainTask模版类型 */
export enum ChainTaskTemplate {
  ChainTaskTemplate_Unknow = 0,
  ChainTaskTemplate_BotTemplate = 1,
}

/** 分析图表的类型 */
export enum ChartType {
  Unknown = 0,
  /** 指标卡 */
  KPI = 1,
  /** 饼图 */
  Pie = 2,
  /** 条形图 */
  Bar = 3,
  /** 条形堆积图 */
  StackedBar = 4,
  /** 雷达图 */
  Radar = 5,
  /** 折线图 */
  Line = 6,
}

export enum DataType {
  /** 默认，有小数的浮点数值类型 */
  Double = 0,
  JSON = 1,
}

/** 评估器资源 */
export enum EvaluateMode {
  /** 人工 */
  Manual = 0,
  /** 自动 */
  Auto = 1,
  /** 重置 */
  Reset = 2,
}

/** 关联评估器 */
export enum EvaluateScope {
  /** data row 范围 */
  Row = 0,
  /** 全case默认生效 */
  Case = 1,
}

export enum ExecutionPolicy {
  /** error 停止运行 */
  StopOnAnyError = 1,
  /** error 继续执行后续任务 */
  ContinueOnAnyError = 2,
  /** 部分失败继续执行后续任务 */
  OnlyContinueOnPartialError = 3,
}

export enum ExportCSVSourceType {
  /** 后续可能迁移单报告为服务端导出 */
  EvaluationTaskReport = 1,
  ContrastReport = 2,
}

export enum FilterField {
  Unknown = 0,
  /** 得分 */
  Score = 1,
  /** 数值 */
  Value = 2,
  /** 评测结果option value */
  OptionValue = 3,
  /** 标注 */
  Plaintext = 4,
  /** 数据集标签 */
  DataSetTag = 5,
  /** 评测维度,对应于ruleID */
  RuleID = 6,
}

export enum FilterLogicOp {
  Unknown = 0,
  And = 1,
  Or = 2,
}

export enum FilterOperatorType {
  Unknown = 0,
  /** 等于 */
  Equal = 1,
  /** 不等于 */
  NotEqual = 2,
  /** 包含 */
  Contains = 3,
  /** 不包含 */
  NotContains = 4,
  /** 大于 */
  Greater = 5,
  /** 大于等于 */
  GreaterOrEqual = 6,
  /** 小于 */
  Less = 7,
  /** 小于等于 */
  LessOrEqual = 8,
  /** 空 */
  IsNull = 9,
  /** 非空 */
  IsNotNull = 10,
}

export enum FornaxAgentOpenAPIKey {
  Unknown = 0,
  Invoke = 1,
  Stream = 2,
}

export enum FornaxAgentTmplType {
  Unknown = 0,
  ChatBot = 1,
  OpenAPI = 2,
}

export enum GenAnnotationTaskCustomFilterLanguage {
  Unknown = 0,
  Golang = 1,
  Typescript = 2,
  Python = 3,
}

/** 分组方式 */
export enum GroupMode {
  Unknown = 0,
  /** 数据集标签 */
  DataSetTag = 1,
  /** 按照option结果分组 */
  OptionResult = 2,
}

export enum ManualStatus {
  /** 不需要人工标注 */
  NoNeed = 0,
  /** 需要人工标注 */
  Need = 1,
}

export enum ModelResponseFormat {
  Text = 0,
  Markdown = 1,
  JSON = 2,
}

export enum ModelStyle {
  Custom = 0,
  Creative = 1,
  Balance = 2,
  Precise = 3,
}

/** 评估器资源 */
export enum PromptTemplateFormat {
  PromptTemplateFormat_FString = 0,
  PromptTemplateFormat_Jinja2 = 1,
}

export enum RetryMode {
  All = 1,
  /** 重试未成功的rowGroup */
  Unsuccessful = 2,
  /** 指定rowGroupID重试 */
  SpecifyRowGroup = 3,
}

export enum RowGroupRunState {
  Unknown = -1,
  /** 排队中 */
  Queueing = 0,
  /** 执行中 */
  Processing = 1,
  /** 成功 */
  Success = 2,
  /** 失败 */
  Fail = 3,
  /** 结果待评估 */
  Evaluating = 4,
  /** 终止执行 */
  Terminal = 5,
}

export enum RowRunState {
  /** 未开始执行 */
  Queueing = 0,
  /** 执行成功 */
  Success = 1,
  /** 执行失败 */
  Fail = 2,
}

/** 表单展示元素类型 */
export enum ShowEntityType {
  /** 评测对象coze bot，对应使用 11:CozeEntityMap */
  CozeBot = 1,
  /** 评测对象prompt */
  Prompt = 2,
  /** 用户信息 */
  User = 3,
  /** 注册agent */
  Agent = 4,
}

/** SpecialObjectType 后面可单独在 评估对象管理平台上进行动态注册，注册的会动态分配 */
export enum SpecialObjectType {
  CozeBot = 0,
  Prompt = 1,
  ChainTask = 2,
  /** 接入 Fornax 的应用，Eino 框架默认集成 */
  FornaxApp = 3,
  /** CloudIDE 提供的 FornaxAgent */
  FornaxAgent = 4,
  PlaygroundCozeBot = 5,
  PlaygroundCozeBotV2 = 6,
  /** 结果集批量评测类型 */
  EvalResultDatasetCollection = 7,
  PlaygroundCozeModel = 8,
  CiciCrawl = 9,
  /** 自定义评测规则，idgen id 不可枚举, 此 enum 仅用于搜索&过滤的 request */
  Custom = 100,
}

export enum TaskAggrReportGenStatus {
  /** 未生成，任务未执行完成 */
  NotGenerated = 0,
  /** 更新中 */
  Updating = 1,
  /** 运行时可查看报告详情，完成 后展示整体得分和【查看聚合报告可点击】 */
  Generated = 2,
  /** 完成后如果评分有更新，需要重新聚合计算 */
  NeedUpdate = 3,
}

export enum TaskManualStatus {
  NoNeed = 0,
  Need = 1,
  Completed = 2,
}

export enum TaskMode {
  Unknown = 0,
  /** 平台手动运行Case */
  SubmitExec = 1,
  /** 在线评测场景 */
  OnlineSyncExec = 2,
}

export enum TaskStatus {
  /** 评测任务执行中，前端展示【运行中】 */
  Processing = 1,
  /** 评测任务创建中，初始话rowGroup状态等操作，前端展示【创建中】 */
  Creating = 4,
  /** rowgroup持续评测并添加到任务，在线评测场景中使用 */
  Appending = 10,
  /** 执行完成且需要人工评分，前端展示【需要人工评分】 */
  NeedManualEval = 20,
  /** 用户手动取消运行, 前端展示【已终止，用户取消】 */
  Termination = 21,
  /** 系统主动终止任务，前端展示【已终止: 系统异常】 */
  SystemTermination = 22,
  /** 任务执行完成，前端展示【成功】 */
  Completed = 30,
  /** 执行完成，全部 rowGroup 都执行失败，前端展示【失败】 */
  Error = 31,
  /** 执行完成, 部分rowGroup失败。前端展示【部分失败】 */
  PartialFailure = 41,
  /** 任务已创建,等待任务被调度执行 */
  AwaitExecution = 42,
  /** 等待任务被重试 */
  AwaitRetry = 43,
}

export enum UserChangeMode {
  /** 无限制，用户可读写、运行 */
  Default = 0,
  /** 不可编辑、不可触发运行，可读 */
  ReadOnly = 5,
}

export interface AggregateData {
  value?: number;
  json_info?: string;
  data_type?: DataType;
}

/** 聚合报告 */
export interface AggregateReport {
  /** 数据集的标签树结构 */
  tag_tree_node?: flow_devops_evaluation_entity.Node;
  /** node_id对应的标签节点聚合结果 */
  node_results?: Record<Int64, AggregateResultRow>;
  /** 报告的评估规则列结构 */
  column_rule_info?: Array<ColumnRuleInfo>;
  /** 所有数据集标签汇总结果,对应报告最后一行 */
  summarized_result?: AggregateResultRow;
}

/** 聚合统计结果 一个单元格数据，对应一种聚合器类型 */
export interface AggregateResultCell {
  aggregator_type?: AggregatorType;
  aggregate_data?: AggregateData;
}

/** 聚合报告一行数据 */
export interface AggregateResultRow {
  /** 规则维度聚合结果 */
  rule_results?: Array<RuleAggregateResult>;
  /** rowgroup总数 */
  total_num?: Int64;
  /** 所有评测规则汇总结果,对应报告最后一列 */
  summarized_result?: Array<AggregateResultCell>;
}

export interface AggregatorConfig {
  aggregator_type?: AggregatorType;
  intersection_evaluator_infos?: Array<IntersectionEvaluatorInfo>;
}

export interface AnalysisChartReportConfig {
  /** 图表类型 */
  chart_type: ChartType;
  /** 指标粒度 */
  evaluator_granularity: flow_devops_evaluation_entity.EvaluatorGranularity;
  /** 聚合统计方式 */
  aggregator_type: AggregatorType;
  /** 评测维度list，即对应的rule_ids */
  rule_ids: Array<Int64>;
  /** 聚合方式list */
  aggregator_modes?: Array<AggregatorMode>;
  /** 分组方式 */
  group_mode?: GroupMode;
  /** 过滤条件(树结构，过滤条件均在叶子节点上) */
  filter_node?: FilterNode;
  /** 报告名称 */
  report_name?: string;
  /** 分组方式过滤条件(树结构，过滤条件均在叶子节点上) */
  group_mode_filter?: FilterNode;
}

export interface AnalysisChartReportInfo {
  /** 分析图表id */
  analysis_chart_report_id: Int64;
  aggregate_report?: AggregateReport;
  analysis_chart_report_config?: AnalysisChartReportConfig;
}

export interface AssociateAnalysisChartReportRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  /** 分析图表id list */
  analysis_chart_report_ids: Array<Int64>;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface AssociateAnalysisChartReportResponse {
  BaseResp?: base.BaseResp;
}

export interface AssociateDatasetRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  cid?: string;
  Base?: base.Base;
}

export interface AssociateDatasetResponse {
  new_dataset_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface AssociateDatasetV2Request {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  dataset_id: Int64;
  Base?: base.Base;
}

export interface AssociateDatasetV2Response {
  BaseResp?: base.BaseResp;
}

export interface AssociateEvalObjectRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  /** 评估对象 */
  eval_object?: EvalObject;
  cid?: string;
  Base?: base.Base;
}

export interface AssociateEvalObjectResponse {
  BaseResp?: base.BaseResp;
}

export interface AssociateEvaluatorRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  mode: EvaluateMode;
  scope: EvaluateScope;
  row_id?: Int64;
  row_group_id?: Int64;
  cid?: string;
  threshold?: ScoringThreshold;
  Base?: base.Base;
}

export interface AssociateEvaluatorResponse {
  /** EvaluateMode 是人工模式时， 不返回 NewRuleGroupID */
  new_rule_group_id?: Int64;
  BaseResp?: base.BaseResp;
}

/** ## bactch task run */
export interface BatchCaseTask {
  case_id: Int64;
  space_id_of_case: Int64;
  list_exec_runtime_parameter: Array<ExecRuntimeParameter>;
}

export interface BatchCreateCaseRequest {
  'FlowDevops-Agw-UserId'?: string;
  name_eval_object?: Record<string, EvalObject>;
  'FlowDevops-Agw-AppId'?: number;
  space_id?: Int64;
  region?: string;
  Base?: base.Base;
}

export interface BatchCreateCaseResponse {
  cases?: Array<Case>;
  BaseResp?: base.BaseResp;
}

export interface BatchGetRowEvalResRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  row_ids: Array<Int64>;
  space_id: Int64;
  Base?: base.Base;
}

export interface BatchGetRowEvalResResponse {
  /** key:rowID */
  row_eval_res_list?: Record<Int64, RowEvalRes>;
  BaseResp?: base.BaseResp;
}

export interface BatchTaskRet {
  batch_task_id: Int64;
  /** case 下的本次运行的task列表 */
  list_case_tasks?: Array<CaseTasks>;
}

export interface BizError {
  err_msg: string;
  err_code: Int64;
}

/** 用例实体 */
export interface Case {
  /** 用例ID */
  id?: Int64;
  /** 用例名称 */
  name?: string;
  /** 用例描述 */
  desc?: string;
  /** 关联的数据集信息 */
  dataset_source?: DatasetSource;
  /** 关联评估集合 */
  evaluator_source?: EvaluatorSource;
  /** 评估对象 */
  eval_object?: EvalObject;
  /** 创建者 */
  creator_id?: Int64;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
  /** Case 删除时间 */
  delete_at?: Int64;
  /** 空间ID */
  space_id?: Int64;
  last_run_time?: Int64;
  region?: string;
  /** deprecated
该用例下最新的task的运行时参数，如果用例下没有task，返回默认值 */
  latest_runtime_parameter?: string;
  /** UserChangeMode 用户编辑用例权限
for在线评测场景，在线评测任务未完成前用户不可编辑用例，在线评测任务完成后用例才可编辑 */
  user_change_mode?: UserChangeMode;
  runtime_parameter_tip_content?: string;
  /** 这个字段有值代表是引用了已有数据集 */
  original_dataset_id?: Int64;
  /** 运行时参数和用例直接绑定，若用户未编辑则为默认值 */
  runtime_parameter?: string;
  /** 运行时参数默认值，自动化任务批量设置运行时参数场景使用 */
  default_runtime_parameter?: string;
}

/** case 维度评估器 */
export interface CaseEvaluatorSource {
  /** 人工模式下, 自动评估Evaluator资源失效 */
  mode?: EvaluateMode;
  /** task绑定Evaluator资源 */
  rule_group_id?: Int64;
}

export interface CaseTasks {
  case: Case;
  tasks?: Array<Task>;
}

export interface ChainTask {
  task_id: string;
  task_name?: string;
  /** 评测时候，chainTask的版本，需要前端传 */
  version?: number;
  model_info?: string;
  chain_task_template?: ChainTaskTemplate;
  prompt_template_format?: PromptTemplateFormat;
  app_id?: string;
}

export interface CheckCaseEvaluatorRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface CheckCaseEvaluatorResponse {
  pass: boolean;
  check_result?: CheckResult;
  BaseResp?: base.BaseResp;
}

export interface CheckResult {
  over_limit_rows?: Array<Int64>;
  /** 没有设置默认规则，存在大于0个row没有配置默认规则 */
  lack_row_rule?: boolean;
  /** case 关联数据集没有数据 */
  lack_dataset_row?: boolean;
}

export interface CiciCrawl {
  bot_id: string;
  name?: string;
  avatar_url?: string;
  crawl_project_id?: string;
}

export interface CloneCaseRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  name: string;
  desc?: string;
  eval_object?: EvalObject;
  region?: string;
  Base?: base.Base;
}

export interface CloneCaseResponse {
  case_id: Int64;
}

export interface ColumnRuleInfo {
  rule_id: Int64;
  evaluator_type: Int64;
  /** 自定义评估器名称 */
  evaluator_type_name: string;
  /** 规则名称 */
  name?: string;
  /** 用于对话组粒度规则的合并单元格 */
  granularity?: flow_devops_evaluation_entity.EvaluatorGranularity;
}

export interface ComparisonAggregateReportInfo {
  task_id: Int64;
  case_id: Int64;
  aggregate_report?: AggregateReport;
}

export interface ComparisonTaskInfo {
  task_id: Int64;
  case_id: Int64;
}

export interface ContrastReportMetaInfo {
  contrast_report_id: Int64;
  name: string;
  /** 对比任务列表 */
  contrast_tasks?: Array<ContrastTask>;
}

export interface ContrastReportRowGroup {
  serial_num?: number;
  row_group_id?: Int64;
  tags?: Array<string>;
  /** row对比报告信息 */
  row_contrast_report_infos: Array<RowContrastReportInfo>;
  /** rowGroup对比报告信息 */
  row_group_contrast_report_info?: RowGroupContrastReportInfo;
}

export interface ContrastTask {
  case_id: Int64;
  task_id: Int64;
}

export interface CozeAgent {
  AgentID: Int64;
  AgentName: string;
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

/** 评测CozeBot */
export interface CozeBot {
  bot_id?: Int64;
  /** 默认0， 是draft版本
deprecated */
  is_draft?: number;
  /** 创建case version 为空，task实体会携带当前case run 时候 bot version */
  version?: Int64;
  /** Bot类型，默认0， 是draft版本 */
  bot_info_type?: flow_devops_evaluation_object_callback.CozeBotInfoType;
  /** 创建case connector_id 为空，task实体会携带当前case run 时候 bot connector_id */
  connector_id?: string;
  model_info?: flow_devops_evaluation_entity.ModelInfo;
  bot_name?: string;
  avatar_url?: string;
  env?: string;
  bot_version?: string;
  Agents?: Array<CozeAgent>;
}

export interface CreateBatchTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  batch_case_tasks?: Array<BatchCaseTask>;
  'FlowDevops-Agw-AppId'?: number;
  default_exec_runtime_parameter?: ExecRuntimeParameter;
  execution_policy?: ExecutionPolicy;
  Base?: base.Base;
}

export interface CreateBatchTaskResponse {
  batch_task_id: Int64;
  BaseResp?: base.BaseResp;
}

export interface CreateCaseRequest {
  'FlowDevops-Agw-UserId'?: string;
  /** 用例名称 */
  name: string;
  'FlowDevops-Agw-AppId'?: number;
  /** 用例所处空间 */
  space_id: Int64;
  /** 用例描述 */
  desc?: string;
  cid?: string;
  /** 是否打上tag, 默认不打上 */
  tag_list?: Array<flow_devops_evaluation_entity.TagInfo>;
  region?: string;
  UserChangeMode?: UserChangeMode;
  eval_object?: EvalObject;
  Base?: base.Base;
}

export interface CreateCaseResponse {
  /** 用例实体信息 */
  case?: Case;
  BaseResp?: base.BaseResp;
}

export interface CreateContrastReportReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  task_id: Int64;
  name?: string;
  /** 对比任务列表 */
  contrast_tasks?: Array<ContrastTask>;
  Base?: base.Base;
}

export interface CreateContrastReportResp {
  id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface CreatePostColumnsReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 人工标注项列表 */
  manual_annotation_items?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationItem>;
  task_id?: Int64;
  case_id?: Int64;
  Base?: base.Base;
}

export interface CreatePostColumnsResp {
  id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface CreatePostColumnsV2Req {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id?: Int64;
  'FlowDevops-Agw-AppId'?: number;
  task_id?: Int64;
  case_id?: Int64;
  /** 人工标注项列表 */
  manual_annotation_items?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationItem>;
  source_type?: flow_devops_evaluation_manual_annotation.SourceType;
  Base?: base.Base;
}

export interface CreatePostColumnsV2Resp {
  id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DashboardRow {
  /** 数据集结构 */
  row_id: Int64;
  /** 数据集cells（包含input, output, variable FIXME MultiCell上线后，废弃 */
  cells: Array<string>;
  /** 评估报告新增
大模型真实输出 FIXME  MultiOutput上线后废弃 */
  output: Output;
  /** 评估器报告 */
  row_eval_cell: RowEvalCell;
  /** 二级详情rule_group_id
deprecated 不为0时表示该行包括了所有通用规则与行级规则的rule_group_id, */
  rule_group_id: Int64;
  /** 含多模态的输出，兼容字段，全量上线后，废弃Output */
  multi_output?: flow_devops_evaluation_object_callback.Output;
  /** 扩展2号字段，数据集多模态展示 */
  multi_cells?: Array<flow_devops_evaluation_callback_common.Content>;
  /** 轨迹信息 */
  trajectory?: flow_devops_evaluation_evaluator_callback.Trajectory;
  run_state?: RowRunState;
  /** deprecated */
  error_message?: string;
  /** Row执行时关联的logID */
  log_id?: string;
  error?: RowRunError;
  /** 存在行级评估器时,行级评估器的group_id */
  row_rule_group_id?: Int64;
  /** 是否跳转评估对象调用 trace */
  direct_object_trace?: boolean;
}

export interface DashboardRowGroup {
  row_group_id?: Int64;
  group_name?: string;
  rows: Array<DashboardRow>;
  run_state?: RowGroupRunState;
  tags?: Array<string>;
  /** rowGroup粒度评测规则ruleID -> 结果 */
  rule_eval_report_map?: Record<Int64, RowEvalReport>;
  serial_num?: number;
}

export interface DashboardTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  row_group_run_state?: RowGroupRunState;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface DashboardTaskResponse {
  /** 每一行是一条数据集的(input, reference_output, variable)
和数据集里的ColumnName一致 */
  column_name: Array<string>;
  dashboard_row_groups: Array<DashboardRowGroup>;
  column_rule_info?: Array<ColumnRuleInfo>;
  default_rule_group_id?: Int64;
  task_id?: Int64;
  /** 人工标注项 */
  manual_annotation_items?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationItem>;
  /** key是人工标注项ID，value是标注任务ID */
  manual_annotation_item_map?: Record<Int64, Int64>;
  /** 是否允许导出数据集内容 */
  not_allow_export_dataset_content?: boolean;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DashboardTaskRowGroupRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  row_group_id: Int64;
  Base?: base.Base;
}

export interface DashboardTaskRowGroupResponse {
  /** 每一行是一条数据集的(input, reference_output, variable)
和数据集里的ColumnName一致 */
  column_name: Array<string>;
  dashboard_row_group: DashboardRowGroup;
  column_rule_info?: Array<ColumnRuleInfo>;
  default_rule_group_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DashboardTaskV2Request {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  row_group_run_state?: RowGroupRunState;
  custom_filter?: GenAnnotationTaskCustomFilter;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface DashboardTaskV2Response {
  /** 每一行是一条数据集的(input, reference_output, variable)
和数据集里的ColumnName一致 */
  column_name: Array<string>;
  dashboard_row_groups: Array<DashboardRowGroup>;
  column_rule_info?: Array<ColumnRuleInfo>;
  default_rule_group_id?: Int64;
  task_id?: Int64;
  /** 人工标注项 */
  manual_annotation_items?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationItem>;
  /** key是人工标注项ID，value是标注任务ID */
  manual_annotation_item_map?: Record<Int64, Int64>;
  custom_filter_err_msg?: string;
  /** 是否允许导出数据集内容 */
  not_allow_export_dataset_content?: boolean;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DatasetSource {
  dataset_id?: Int64;
  /** 数据集名称：目前只有关联已有的数据集有值，其他都是内部创建的没有name */
  dataset_name?: string;
}

export interface DeleteAnalysisChartReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 分析图表id */
  analysis_chart_report_id: Int64;
  space_id: Int64;
  Base?: base.Base;
}

export interface DeleteAnalysisChartReportResponse {
  BaseResp?: base.BaseResp;
}

export interface DeleteCaseRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface DeleteCaseResponse {
  BaseResp?: base.BaseResp;
}

export interface DeleteContrastReportReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  Base?: base.Base;
}

export interface DeleteContrastReportResp {
  BaseResp?: base.BaseResp;
}

export interface DeleteTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface DeleteTaskResponse {
  BaseResp?: base.BaseResp;
}

export interface EvalObject {
  /** 评测对象的类型。每一个 RPC 接口，视为一种类型 */
  object_type: Int64;
  /** ObjectType=0 时，传参此字段。 评测对象为 CozeBot 时, 需要设置 CozeBot 信息 */
  coze_bot?: CozeBot;
  /** ObjectType=1 时，传参此字段。 评测对象为 EvalPrompt 时, 需要设置 Prompt 信息 */
  prompt?: EvalPrompt;
  /** ObjectType 为其他时，传参此字段 */
  object?: flow_devops_evaluation_object_callback.Object;
  /** ObjectType=2 时，传参此字段。 评测对象为 ChainTask 时, 需要设置 ChainTask 信息 */
  chain_task?: ChainTask;
  fornax_app_object?: FornaxAppObject;
  fornax_agent_object?: FornaxAgentObject;
  playground_coze_bot_v2?: PlaygroundCozeBotV2;
  /** EvalResultDatasetCollection 类型对象数据 */
  eval_result_dataset_collection?: EvalResultDatasetCollection;
  playground_coze_model?: PlaygroundCozeModel;
  cici_crawl?: CiciCrawl;
}

export interface EvalPrompt {
  /** 一个prompt的唯一标识 */
  prompt_id: string;
  /** 评测时候，prompt的版本，需要前端传 */
  version?: string;
  name?: string;
}

export interface EvalResultDatasetCollection {
  items: Array<EvalResultDatasetObject>;
}

export interface EvalResultDatasetObject {
  dataset_id: Int64;
  rule_group_id: Int64;
  dataset_name?: string;
}

export interface EvaluateResult {
  /** 打分 */
  score?: number;
  /** 打分过程与结果相关信息 */
  reasoning?: string;
  /** 是否需要人工打分, 当前rule 没有自动评测结果时候， ManualStatus = ManualStatus */
  manual_status?: ManualStatus;
  /** 评估器错误 */
  error?: RowRunError;
  data?: EvaluateResultData;
  /** Row 维度打分范围，如 Coze 场景下由用户 LLM Prompt 决定评估器输出打分范围 */
  scoring_scope?: flow_devops_evaluation_entity.ScoringScope;
}

export interface EvaluateResultData {
  score?: number;
  value?: string;
  option?: flow_devops_evaluation_entity.EvaluateResultOption;
  plain_text?: string;
  data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
  value_type?: flow_devops_evaluation_entity.EvaluateResultValueType;
}

export interface EvaluatorSource {
  case_source?: CaseEvaluatorSource;
  /** row_id vs RowEvaluateSource */
  row_sources?: Record<Int64, RowEvaluatorSource>;
  /** 及格优秀阈值 */
  threshold?: ScoringThreshold;
}

export interface ExecCaseDynamicRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  cid?: string;
  /** 运行评测用例的运行时参数 json序列化 */
  runtime_parameter?: string;
  exec_times?: Int64;
  dataset_source?: DatasetSource;
  /** 关联评估集合 */
  evaluator_source?: EvaluatorSource;
  /** 评估对象 */
  eval_object?: EvalObject;
  Base?: base.Base;
}

export interface ExecCaseDynamicResponse {
  task?: Task;
  task_ids?: Array<Int64>;
  tasks?: Array<Task>;
  BaseResp?: base.BaseResp;
}

export interface ExecCaseRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  cid?: string;
  /** 运行评测用例的运行时参数 json序列化 */
  runtime_parameter?: string;
  exec_times?: Int64;
  task_description?: string;
  Base?: base.Base;
}

export interface ExecCaseResponse {
  task?: Task;
  task_ids?: Array<Int64>;
  tasks?: Array<Task>;
  BaseResp?: base.BaseResp;
}

export interface ExecRuntimeParameter {
  /** map */
  runtime_parameter: string;
  exec_times: Int64;
}

export interface ExportBitableReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  task_id: Int64;
  /** 人工评分盲评人数 */
  manual_scoring_user_num?: Int64;
  Base?: base.Base;
}

export interface ExportBitableReportResponse {
  BaseResp?: base.BaseResp;
}

export interface ExportReportToCsvRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  /** 评测报告导出需要caseID */
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  source_id?: Int64;
  source_type?: ExportCSVSourceType;
  Base?: base.Base;
}

export interface ExportReportToCsvResponse {
  /** 下载链接 */
  download_url?: string;
  BaseResp?: base.BaseResp;
}

export interface FilterCondition {
  /** 过滤字段，比如数据集标签，评测维度，评测维度标签等 */
  field?: FilterField;
  /** 操作符，比如等于、包含、大于、小于等 */
  operator?: FilterOperatorType;
  /** 操作值;支持多种类型的操作值；Operator为包含的时候Value按照逗号分隔 */
  value?: string;
}

export interface FilterNode {
  /** 节点名称(不必须) */
  name?: string;
  /** 标识该节点的标识ID */
  node_id?: Int64;
  /** 该节点上的过滤条件 */
  filter_conditions?: FilterCondition;
  /** 子节点 */
  children?: Array<FilterNode>;
  /** 逻辑操作，比如AND, OR */
  logic_op?: FilterLogicOp;
}

export interface FornaxAgentAPI {
  open_api_key?: FornaxAgentOpenAPIKey;
}

export interface FornaxAgentObject {
  agent_id: Int64;
  faas_id: string;
  name?: string;
  avatar_url?: string;
  tmpl_type?: FornaxAgentTmplType;
  /** 评测的目标 api */
  api?: FornaxAgentAPI;
}

export interface FornaxAppObject {
  psm: string;
  env: string;
  cluster: string;
  region: string;
  app_id: string;
  client_id: string;
  /** for 二级搜索 */
  object?: flow_devops_evaluation_object_callback.Object;
}

export interface GenAggregateComparisonReportRequest {
  aggregator_configs?: Array<AggregatorConfig>;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GenAggregateComparisonReportResponse {
  comparison_aggregate_report_infos?: Array<ComparisonAggregateReportInfo>;
  BaseResp?: base.BaseResp;
}

export interface GenAggregateReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GenAggregateReportResponse {
  BaseResp?: base.BaseResp;
}

export interface GenAnalysisChartReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 分析报告的详细配置 */
  analysis_chart_report_config: AnalysisChartReportConfig;
  space_id: Int64;
  Base?: base.Base;
}

export interface GenAnalysisChartReportResponse {
  analysis_chart_report_info?: AnalysisChartReportInfo;
  BaseResp?: base.BaseResp;
}

export interface GenAnnotationTaskCustomFilter {
  language?: GenAnnotationTaskCustomFilterLanguage;
  /** 用户自定义代码 */
  user_code?: string;
}

export interface GetAggregateReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetAggregateReportResponse {
  aggregate_report?: AggregateReport;
  BaseResp?: base.BaseResp;
}

export interface GetBatchTaskHistoryRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  object_id: Int64;
  object_type: Int64;
  Base?: base.Base;
}

export interface GetBatchTaskHistoryResponse {
  batch_task_rets?: Array<BatchTaskRet>;
  BaseResp?: base.BaseResp;
}

export interface GetBatchTaskRetRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  batch_task_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  Base?: base.Base;
}

export interface GetBatchTaskRetResponse {
  batch_task_ret?: BatchTaskRet;
  BaseResp?: base.BaseResp;
}

export interface GetBitableReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  task_id: Int64;
  Base?: base.Base;
}

export interface GetBitableReportResponse {
  /** 多维表格token */
  app_token?: string;
  url?: string;
  export_status: BitableStatus;
  import_status: BitableStatus;
  BaseResp?: base.BaseResp;
}

export interface GetCaseRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetCaseResponse {
  case?: Case;
  BaseResp?: base.BaseResp;
}

export interface GetContrastReportReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface GetContrastReportResp {
  /** 数据集列名 */
  column_name?: Array<string>;
  /** 规则信息 */
  column_rule_infos?: Array<ColumnRuleInfo>;
  /** 指标信息 */
  metrics_columns?: Array<ColumnRuleInfo>;
  /** 人工标注项 */
  manual_annotation_items?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationItem>;
  /** 基准任务 */
  task_id?: Int64;
  /** 对比任务列表 */
  contrast_tasks?: Array<ContrastTask>;
  contrast_report_row_groups?: Array<ContrastReportRowGroup>;
  default_rule_group_id?: Int64;
  /** key是人工标注项ID，value是标注任务ID */
  manual_annotation_item_map?: Record<Int64, Int64>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface GetContrastReportV2Req {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  custom_filter?: GenAnnotationTaskCustomFilter;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface GetContrastReportV2Resp {
  /** 数据集列名 */
  column_name?: Array<string>;
  /** 规则信息 */
  column_rule_infos?: Array<ColumnRuleInfo>;
  /** 指标信息 */
  metrics_columns?: Array<ColumnRuleInfo>;
  /** 人工标注项 */
  manual_annotation_items?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationItem>;
  /** 基准任务 */
  task_id?: Int64;
  /** 对比任务列表 */
  contrast_tasks?: Array<ContrastTask>;
  contrast_report_row_groups?: Array<ContrastReportRowGroup>;
  default_rule_group_id?: Int64;
  /** key是人工标注项ID，value是标注任务ID */
  manual_annotation_item_map?: Record<Int64, Int64>;
  custom_filter_err_msg?: string;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface GetIntersectionEvaluatorDimensionsRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_infos: Array<ComparisonTaskInfo>;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetIntersectionEvaluatorDimensionsResponse {
  intersection_evaluator_infos?: Array<IntersectionEvaluatorInfo>;
  BaseResp?: base.BaseResp;
}

export interface GetTaskContrastReportMetaReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  Base?: base.Base;
}

export interface GetTaskContrastReportMetaResp {
  contrast_report_meta: ContrastReportMetaInfo;
  Base?: base.Base;
}

export interface GetTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetTaskResponse {
  task?: Task;
  BaseResp?: base.BaseResp;
}

export interface GetTaskRuleGroupIdRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetTaskRuleGroupIdResponse {
  rule_group_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface GetTasksRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_ids: Array<Int64>;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetTasksResponse {
  tasks?: Array<Task>;
  BaseResp?: base.BaseResp;
}

export interface GroupManualScore {
  row_group_id: Int64;
  rule_scores?: Record<Int64, EvaluateResult>;
}

export interface GroupTaskInfo {
  task_ids?: Array<Int64>;
  /** 秒级时间戳 */
  create_time?: Int64;
}

export interface ImportBitableReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  task_id: Int64;
  Base?: base.Base;
}

export interface ImportBitableReportResponse {
  BaseResp?: base.BaseResp;
}

export interface InitiateManualAnnotationTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id?: Int64;
  'FlowDevops-Agw-AppId'?: number;
  task_id?: Int64;
  case_id?: Int64;
  /** 人工标注项列表 */
  item_ids?: Array<Int64>;
  name?: string;
  description?: string;
  /** 标识范围 */
  source_range?: flow_devops_evaluation_manual_annotation.SourceRange;
  /** 盲评数 */
  blind_count?: Int64;
  source_type?: flow_devops_evaluation_manual_annotation.SourceType;
  custom_filter?: GenAnnotationTaskCustomFilter;
  Base?: base.Base;
}

export interface InitiateManualAnnotationTaskResp {
  id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface IntersectionEvaluatorInfo {
  /** 共有的EvaluatorType */
  evaluator_type: Int64;
  intersection_rule_infos?: Array<IntersectionRuleInfo>;
}

export interface IntersectionRuleInfo {
  rule_id: Int64;
  task_id: Int64;
  case_id: Int64;
  /** 规则名称 */
  name?: string;
  /** 自定义评估器名称 */
  evaluator_type_name?: string;
  evaluator_process_type?: flow_devops_evaluation_evaluator.EvaluatorProcessType;
  evaluate_result_data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
}

export interface KillBatchTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  batch_task_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  Base?: base.Base;
}

export interface KillBatchTaskResponse {
  batch_task_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface KillRunningTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface KillRunningTaskResponse {
  task?: Task;
  BaseResp?: base.BaseResp;
}

export interface ListTaskContrastReportReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  task_id: Int64;
  Base?: base.Base;
}

export interface ListTaskContrastReportResp {
  contrast_report_meta_infos: Array<ContrastReportMetaInfo>;
  BaseResp?: base.BaseResp;
}

export interface ManualScore {
  row_id: Int64;
  /** key = ruleID, 规则失败, 规则可以进行人工打分 */
  rule_scores?: Record<Int64, EvaluateResult>;
  /** Row选择人工评测时候, Row 人工打分 */
  row_score?: EvaluateResult;
}

export interface ManualScoreFowRowsRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** key:rowID */
  manual_scores?: Record<Int64, ManualScore>;
  space_id: Int64;
  /** key:rowGroupID */
  group_manual_scores?: Record<Int64, GroupManualScore>;
  Base?: base.Base;
}

export interface ManualScoreFowRowsResponse {
  /** key:rowID */
  row_eval_res_list?: Record<Int64, RowEvalRes>;
  BaseResp?: base.BaseResp;
}

export interface MGetCaseRequest {
  case_ids: Array<Int64>;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface MGetCaseResponse {
  cases?: Array<Case>;
  BaseResp?: base.BaseResp;
}

export interface Output {
  prediction: string;
}

/** Coze2.0Bot */
export interface PlaygroundCozeBotV2 {
  bot_id?: Int64;
  /** 创建case version 为空，task实体会携带当前case run 时候 bot version */
  bot_version?: string;
  /** Bot类型，默认0， 是draft版本 */
  bot_info_type?: flow_devops_evaluation_object_callback.CozeBotInfoType;
  model_info?: flow_devops_evaluation_entity.ModelInfo;
  bot_name?: string;
  avatar_url?: string;
}

/** Coze2.0 model */
export interface PlaygroundCozeModel {
  /** 模型id */
  model_id?: string;
  /** 温度，模型输出随机性，值越大越随机，越小越保守(0-1] */
  temperature?: number;
  /** 回复最大Token数 */
  max_tokens?: number;
  /** 另一种模型的输出随机性，值越大越随机[0,1] */
  top_p?: number;
  /** 生成时，采样候选集的大小 */
  top_k?: number;
  /** 频率惩罚，调整生成内容中的单词频率，正值单词越少见[-1.0,1.0] */
  frequency_penalty?: number;
  /** 存在惩罚，调整生成内容中新词语频率，正值避免重复单词，用新词[-1.0,1.0] */
  presence_penalty?: number;
  /** 模型回复内容格式 */
  response_format?: ModelResponseFormat;
  /** 模型名称 */
  model_name?: string;
}

export interface PullCaseExecHistoryRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  cursor: string;
  'FlowDevops-Agw-AppId'?: number;
  limit: number;
  space_id: Int64;
  /** 起始时间，Unix时间戳 */
  start_time?: Int64;
  /** 结束时间，Unix时间戳 */
  end_time?: Int64;
  DatasetIDs?: Array<Int64>;
  /** 任务描述 */
  task_description?: string;
  Base?: base.Base;
}

export interface PullCaseExecHistoryResponse {
  tasks?: Array<Task>;
  has_more?: boolean;
  next_cursor?: string;
  /** 分组展示任务信息 */
  group_task_info_list?: Array<GroupTaskInfo>;
  /** deprecated */
  coze_entity_map?: Record<Int64, ShowEntity>;
  /** deprecated */
  prompt_entity_map?: Record<Int64, ShowEntity>;
  user_entity_map?: Record<Int64, ShowEntity>;
  /** deprecated */
  agent_entity_map?: Record<Int64, ShowEntity>;
  BaseResp?: base.BaseResp;
}

export interface PullCaseExecHistoryV2Request {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  cursor: string;
  'FlowDevops-Agw-AppId'?: number;
  limit: number;
  space_id: Int64;
  /** 起始时间，Unix时间戳 */
  start_time?: Int64;
  /** 结束时间，Unix时间戳 */
  end_time?: Int64;
  dataset_ids?: Array<Int64>;
  creator_ids?: Array<Int64>;
  /** 任务描述 */
  task_description?: string;
  Base?: base.Base;
}

export interface PullCaseExecHistoryV2Response {
  tasks?: Array<Task>;
  has_more?: boolean;
  next_cursor?: string;
  /** 分组展示任务信息 */
  group_task_info_list?: Array<GroupTaskInfo>;
  /** deprecated */
  coze_entity_map?: Record<Int64, ShowEntity>;
  /** deprecated */
  prompt_entity_map?: Record<Int64, ShowEntity>;
  user_entity_map?: Record<Int64, ShowEntity>;
  /** deprecated */
  agent_entity_map?: Record<Int64, ShowEntity>;
  BaseResp?: base.BaseResp;
}

export interface PullCaseRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  cursor: string;
  'FlowDevops-Agw-AppId'?: number;
  limit: number;
  tag_name?: string;
  tag_value?: string;
  /** -- search fields -- */
  fuzzy_name?: string;
  creator_id?: Int64;
  region?: string;
  /** 评测对象类型，see EvalObject.ObjectType */
  object_type?: Int64;
  dataset_ids?: Array<Int64>;
  Base?: base.Base;
}

export interface PullCaseResponse {
  cases?: Array<Case>;
  has_more?: boolean;
  next_cursor?: string;
  /** Deprecated: CozeEntityMap 已不再使用获取coze信息从EvalObject获取
deprecated */
  coze_entity_map?: Record<Int64, ShowEntity>;
  /** PromptEntityMap UserEntityMap AgentEntityMap 后续全部迁移至从EvalObject获取
deprecated */
  prompt_entity_map?: Record<Int64, ShowEntity>;
  user_entity_map?: Record<Int64, ShowEntity>;
  /** deprecated */
  agent_entity_map?: Record<Int64, ShowEntity>;
  BaseResp?: base.BaseResp;
}

export interface PullCaseV2Request {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  cursor: string;
  'FlowDevops-Agw-AppId'?: number;
  limit: number;
  tag_name?: string;
  tag_value?: string;
  /** -- search fields -- */
  fuzzy_name?: string;
  creator_id?: Int64;
  region?: string;
  /** 评测对象类型，see EvalObject.ObjectType */
  object_type?: Int64;
  dataset_ids?: Array<Int64>;
  Base?: base.Base;
}

export interface PullCaseV2Response {
  cases?: Array<Case>;
  has_more?: boolean;
  next_cursor?: string;
  /** Deprecated: CozeEntityMap 已不再使用获取coze信息从EvalObject获取
deprecated */
  coze_entity_map?: Record<Int64, ShowEntity>;
  /** PromptEntityMap UserEntityMap AgentEntityMap 后续全部迁移至从EvalObject获取
deprecated */
  prompt_entity_map?: Record<Int64, ShowEntity>;
  user_entity_map?: Record<Int64, ShowEntity>;
  /** deprecated */
  agent_entity_map?: Record<Int64, ShowEntity>;
  BaseResp?: base.BaseResp;
}

export interface PullTaskAnalysisChartReportsRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  cursor: string;
  limit: number;
  space_id: Int64;
  Base?: base.Base;
}

export interface PullTaskAnalysisChartReportsResponse {
  analysis_chart_report_infos?: Array<AnalysisChartReportInfo>;
  has_more?: boolean;
  next_cursor?: string;
  BaseResp?: base.BaseResp;
}

export interface RetryExecBatchTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  batch_task_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 重试模式 */
  mode: BatchTaskRetryMode;
  Base?: base.Base;
}

export interface RetryExecBatchTaskResponse {
  batch_task_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface RetryExecTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  /** 重试模式 */
  mode?: RetryMode;
  row_group_id?: Array<Int64>;
  Base?: base.Base;
}

export interface RetryExecTaskResponse {
  task?: Task;
  BaseResp?: base.BaseResp;
}

export interface RowContrastReportInfo {
  /** key是任务ID */
  row_report_map?: Record<Int64, RowReport>;
}

export interface RowEvalCell {
  /** 人工评分, 仅在 ManualStatus =  Completed 有效 */
  manual_result?: EvaluateResult;
  /** key:rule_id, 行级数据，单个规则的评测结果 */
  rule_eval_report_map?: Record<Int64, RowEvalReport>;
  token?: Int64;
  consuming?: Int64;
  create_time?: Int64;
  end_time?: Int64;
  space_id?: Int64;
  /** 该row所有自动评测rule或人工评分维度按权重聚合后的总分 */
  score?: number;
  rule_eval_reports?: Array<RowEvalReport>;
  row_metrics?: Array<RowEvalReport>;
  manual_annotation_reports?: Array<flow_devops_evaluation_manual_annotation.ManualAnnotationLabelTask>;
}

/** 每个评估器对应的评分 */
export interface RowEvalReport {
  /** TODO:后期需要转为真实的evaluatorName，先暂时用EvaluatorType表示一个评估器 */
  evaluator_type?: Int64;
  row_eval_result?: EvaluateResult;
  /** 评估器的名称 */
  evaluator_type_name?: string;
  weight?: Int64;
  /** 如果是行级规则需要在单独的列展示 */
  is_row_evaluator?: boolean;
  name?: string;
}

export interface RowEvalRes {
  task_id?: Int64;
  /** 人工评分, 仅在 ManualStatus =  Completed 有效 */
  manual_result?: EvaluateResult;
  /** 数据的RowID */
  row_id?: Int64;
  row_group_id?: Int64;
  evaluator_id?: Int64;
  /** key:RuleID, 行级数据，单个规则的评测结果 */
  rule_eval_results?: Record<Int64, EvaluateResult>;
  token?: Int64;
  consuming?: Int64;
  /** bot运行结果 */
  output?: string;
  create_time?: Int64;
  end_time?: Int64;
  space_id?: Int64;
  case_id?: Int64;
}

/** row 维度评估器 */
export interface RowEvaluatorSource {
  /** 人工模式下, 自动评估Evaluator资源失效 */
  mode?: EvaluateMode;
  row_id?: Int64;
  row_group_id?: Int64;
  /** 当前行绑定Evaluator资源 */
  rule_group_id?: Int64;
}

export interface RowGroupContrastReportInfo {
  /** key是任务ID */
  row_group_report_map?: Record<Int64, RowGroupReport>;
}

export interface RowGroupReport {
  row_eval_reports?: Array<RowEvalReport>;
}

export interface RowReport {
  /** 数据集结构 */
  row_id: Int64;
  /** 数据集cell信息 */
  cells?: Array<flow_devops_evaluation_callback_common.Content>;
  /** 实际输出 */
  multi_output?: flow_devops_evaluation_object_callback.Output;
  /** 评估器报告信息 */
  row_eval_cell: RowEvalCell;
  /** 轨迹信息 */
  trajectory?: flow_devops_evaluation_evaluator_callback.Trajectory;
  run_state?: RowRunState;
  /** Row执行时关联的logID */
  log_id?: string;
  error?: RowRunError;
  /** 存在行级评估器时,行级评估器的group_id */
  row_rule_group_id?: Int64;
  /** 是否跳转评估对象调用 trace */
  direct_object_trace?: boolean;
}

export interface RowRunError {
  code: Int64;
  message: string;
  /** for prompt platform */
  detail: string;
  BizError?: BizError;
}

export interface RuleAggregateResult {
  aggregate_result_cells?: Array<AggregateResultCell>;
  rule_id?: Int64;
}

export interface ScoringThreshold {
  /** 及格率阈值 */
  pass_threshold?: number;
  /** 优秀率阈值 */
  excellent_threshold?: number;
}

/** 仅用于表单中展示 */
export interface ShowEntity {
  /** 展示ID, user_id, prompt_id */
  entity_id: Int64;
  /** 展示的名字, 例如prompt_name,bot_name，user_name */
  entity_name?: string;
  entity_type?: ShowEntityType;
  /** 展示的小图标，例如头像 */
  image_url?: string;
  /** 如果是用户有邮箱 */
  email?: string;
}

export interface Task {
  id?: Int64;
  status?: TaskStatus;
  dataset_id?: Int64;
  /** run case时候的评测对象实体，包含版本信息 */
  eval_object?: EvalObject;
  /** 运行的测数据行数 */
  row_run_cnt?: Int64;
  /** 任务整体开销 */
  token?: Int64;
  /** 任务整体耗时 */
  consuming?: Int64;
  /** 任务开始执行时间 */
  start_time?: Int64;
  /** 任务执行完成时间 */
  end_time?: Int64;
  creator_id?: Int64;
  /** 执行本次任务的log_id */
  log_id?: string;
  /** 本次执行失败的原因文案 */
  object_output_err?: string;
  /** 任务统计, RowGroup维度 */
  task_stats?: TaskStats;
  /** 任务人工评分统计, Row维度 */
  task_manual_stats?: TaskManualStats;
  /** 任务人工评分状态 */
  task_manual_status?: TaskManualStatus;
  /** 聚合报告生成状态 */
  aggr_report_gen_status?: TaskAggrReportGenStatus;
  score?: number;
  passing_rate?: number;
  excellent_rate?: number;
  /** 及格优秀阈值 */
  threshold?: ScoringThreshold;
  /** deprecated 评测对象整体 token消耗 */
  object_token_usage?: TokenUsage;
  /** 运行时参数，json序列化 */
  runtime_parameter?: string;
  /** 评测对象整体 token 消耗 */
  object_token_cost?: TokenUsage;
  /** 数据集名称 */
  dataset_name?: string;
  /** 原始数据集ID */
  original_dataset_id?: Int64;
  /** 评估器整体 token 消耗 */
  evaluator_token_usage?: TokenUsage;
  credit_cost?: number;
  description?: string;
}

export interface TaskManualStats {
  needed_row_count?: Int64;
  completed_row_count?: Int64;
  /** 需要group粒度人工评分的对话组数 */
  needed_row_group_count?: Int64;
  /** 已完成group粒度人工评分的对话组数 */
  completed_row_group_count?: Int64;
}

export interface TaskStats {
  uncompleted_count?: Int64;
  success_count?: Int64;
  fail_count?: Int64;
  /** 最终执行成功的 row 数量 */
  success_row_count?: Int64;
  /** 最终执行失败的 row 数量 */
  fail_row_count?: Int64;
  /** 执行过的 row 数量总和 */
  total_row_count?: Int64;
}

export interface TokenUsage {
  /** input token消耗 */
  input_token: Int64;
  /** output token消耗 */
  output_token: Int64;
}

export interface UnbindDatasetRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  dataset_id: Int64;
  Base?: base.Base;
}

export interface UnbindDatasetResponse {
  BaseResp?: base.BaseResp;
}

export interface UpdateAnalysisChartReportRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_id: Int64;
  case_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 分析图表id */
  analysis_chart_report_id: Int64;
  /** 图表类型 */
  analysis_chart_report_config: AnalysisChartReportConfig;
  space_id: Int64;
  Base?: base.Base;
}

export interface UpdateAnalysisChartReportResponse {
  analysis_chart_report_info?: AnalysisChartReportInfo;
  BaseResp?: base.BaseResp;
}

export interface UpdateCaseRequest {
  case_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  name?: string;
  desc?: string;
  runtime_parameter?: string;
  Base?: base.Base;
}

export interface UpdateCaseResponse {
  case?: Case;
  BaseResp?: base.BaseResp;
}

export interface UpdateContrastReportReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  contrast_report_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  name?: string;
  Base?: base.Base;
}

export interface UpdateContrastReportResp {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
