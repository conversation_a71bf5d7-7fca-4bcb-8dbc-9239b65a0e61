/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';

export type Int64 = string | number;

export interface Cell {
  column_name: string;
  /** deprecated */
  content?: flow_devops_evaluation_callback_common.Content;
  value?: OpenContent;
}

export interface MultiContentInfo {
  multi_contents?: Array<OpenContent>;
}

export interface OpenContent {
  data_type: string;
  plain_text?: string;
  markdown_box?: flow_devops_evaluation_callback_common.MarkdownBox;
  image_info?: flow_devops_evaluation_callback_common.ImageInfo;
  file_info?: flow_devops_evaluation_callback_common.FileInfo;
  json_info?: flow_devops_evaluation_callback_common.JSONInfo;
  text_file?: flow_devops_evaluation_callback_common.TextFile;
  multi_content_info?: MultiContentInfo;
  defined_text?: flow_devops_evaluation_callback_common.DefinedText;
}

export interface Row {
  row_id: string;
  cells?: Array<Cell>;
}

export interface RowGroup {
  row_group_id: string;
  group_name: string;
  /** 新增创建时指定tags */
  tags?: Array<string>;
  rows?: Array<Row>;
}
/* eslint-enable */
