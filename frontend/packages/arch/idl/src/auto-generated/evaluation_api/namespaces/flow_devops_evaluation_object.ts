/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';
import * as flow_devops_evaluation_object_callback from './flow_devops_evaluation_object_callback';

export type Int64 = string | number;

export enum ObjectTypeCategory {
  /** 内置评估对象类型 */
  Builtin = 1,
  /** 自定义评估对象类型 */
  Custom = 2,
}

export interface ChainTaskVersionInfo {
  version_info?: string;
  version?: number;
}

export interface CozeBotVersionInfo {
  bot_version?: string;
  connector_id?: string;
  env?: string;
}

export interface DeleteObjectTypeRequest {
  'FlowDevops-Agw-UserId'?: string;
  object_type: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  cid?: string;
}

export interface DeleteObjectTypeResponse {
  BaseResp?: base.BaseResp;
}

export interface FaasCallbackObjectParams {
  psm?: string;
  cluster?: string;
  /** 单位ms */
  timeout?: Int64;
  faas_id?: string;
  agent_execute_path?: string;
  search_object_path?: string;
  http_auth_type?: flow_devops_evaluation_entity.HTTPAuthType;
  /** NeedSearchObjectMetaInfo为true的情况下，需要填写SearchObjectPath */
  need_search_object_meta_info?: boolean;
  search_object_method?: flow_devops_evaluation_entity.HTTPMethod;
  agent_execute_method?: flow_devops_evaluation_entity.HTTPMethod;
}

export interface FornaxAppSearchObjectRequest {
  'FlowDevops-Agw-UserId'?: string;
  object_type: string;
  'FlowDevops-Agw-AppId'?: number;
  search_key: flow_devops_evaluation_object_callback.SearchKey;
  app_client_id: string;
  cursor?: string;
  limit?: string;
}

export interface FornaxAppSearchObjectResponse {
  objects: Array<flow_devops_evaluation_object_callback.Object>;
  has_more: boolean;
  next_cursor: string;
  BaseResp?: base.BaseResp;
}

export interface GetObjectTypeRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  object_type: Int64;
}

export interface GetObjectTypeResponse {
  object_type_detail_info: ObjectTypeDetailInfo;
  BaseResp?: base.BaseResp;
}

export interface ListChainTaskVersionsRequest {
  'FlowDevops-Agw-UserId'?: string;
  task_name?: string;
  'FlowDevops-Agw-AppId'?: number;
  task_id?: string;
  space_id: Int64;
  region?: string;
  app_id?: Int64;
  /** first 为空 */
  cursor?: string;
  limit?: Int64;
}

export interface ListChainTaskVersionsResponse {
  chain_task_version_infos?: Array<ChainTaskVersionInfo>;
  has_more: boolean;
  next_cursor: string;
}

export interface ListCozeBotVersionsRequest {
  bot_id: Int64;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  region?: string;
  /** first 为空 */
  cursor?: string;
  limit?: Int64;
}

export interface ListCozeBotVersionsResponse {
  coze_bot_version_infos?: Array<CozeBotVersionInfo>;
  has_more: boolean;
  next_cursor: string;
}

export interface ListObjectMetaByTypeRequest {
  'FlowDevops-Agw-UserId'?: string;
  object_type: string;
  'FlowDevops-Agw-AppId'?: number;
  search_key: flow_devops_evaluation_object_callback.SearchKey;
  region?: string;
  /** SpaceID 部分场景依赖，如 Fornax 平台拉取空间下注册的 CozeBot */
  space_id?: Int64;
  /** first 为空 */
  cursor?: string;
  limit?: Int64;
}

export interface ListObjectMetaByTypeResponse {
  objects: Array<flow_devops_evaluation_object_callback.Object>;
  has_more: boolean;
  next_cursor: string;
  Session?: flow_devops_evaluation_entity.Session;
  BaseResp?: base.BaseResp;
}

export interface ListObjectTypeDetailRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  list_object_type_filter?: ListObjectTypeFilter;
  /** first 为空 */
  cursor?: string;
  limit?: Int64;
}

export interface ListObjectTypeDetailResponse {
  object_type_detail_infos?: Array<ObjectTypeDetailInfo>;
  has_more?: boolean;
  next_cursor?: string;
  BaseResp?: base.BaseResp;
}

/** ListObjectFilter 请求过滤规则 */
export interface ListObjectTypeFilter {
  object_name?: string;
  callback_type?: flow_devops_evaluation_entity.CallbackType;
  creator_id?: Int64;
  /** 仅 ListObjectTypeDetail 生效 */
  region?: string;
}

export interface ListObjectTypeRequest {
  cursor: string;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  limit: Int64;
  /** 用于filter space可见性 */
  space_id: Int64;
  list_object_type_filter?: ListObjectTypeFilter;
}

export interface ListObjectTypeRequestV2 {
  cursor: string;
  'FlowDevops-Agw-UserId'?: string;
  'FlowDevops-Agw-AppId'?: number;
  limit: Int64;
  /** 用于filter space可见性 */
  space_id: Int64;
  region: string;
  list_object_type_filter?: ListObjectTypeFilter;
}

export interface ListObjectTypeResponse {
  /** list接口里的object只返回基本展示信息和objctType, 用户点击后，除了coze,prompt类型的meta前端内置，其他会根据objctType去搜索返回 */
  object_type_infos?: Array<ObjectTypeInfo>;
  has_more?: boolean;
  next_cursor?: string;
  BaseResp?: base.BaseResp;
}

export interface ObjectTypeDetailInfo {
  object_type?: Int64;
  object_name: string;
  space_id: string;
  callback_type: flow_devops_evaluation_entity.CallbackType;
  description?: string;
  regions?: Array<string>;
  creator_id?: Int64;
  rpc_callback_object_params?: RPCCallbackObjectParams;
  faas_callback_object_params?: FaasCallbackObjectParams;
}

/** 展示注册对象信息 */
export interface ObjectTypeInfo {
  object_type: Int64;
  name: string;
  /** 为true的情况：coze有bot_name模糊查询，prompt的prompt_key也是模糊查询，该类need_search_object=true
如果子实体key是唯一确定的，不需要查询二级页面；need_search_object=false */
  need_search_object_meta: boolean;
  regions?: Array<string>;
  callback_type?: flow_devops_evaluation_entity.CallbackType;
  category?: ObjectTypeCategory;
}

export interface RegisterObjectTypeRequest {
  'FlowDevops-Agw-UserId'?: string;
  /** 只存储需要去调用业务client的basic的字段，其余meta信息由业务提供 */
  object_type_detail_info: ObjectTypeDetailInfo;
  'FlowDevops-Agw-AppId'?: number;
  cid?: string;
}

export interface RegisterObjectTypeResponse {
  object_type_detail_info: ObjectTypeDetailInfo;
  BaseResp?: base.BaseResp;
}

export interface RPCCallbackObjectParams {
  psm?: string;
  cluster?: string;
  /** 单位ms */
  timeout?: Int64;
  need_search_object_meta_info?: boolean;
}

export interface UpdateObjectTypeRequest {
  'FlowDevops-Agw-UserId'?: string;
  object_type_detail_info: ObjectTypeDetailInfo;
  'FlowDevops-Agw-AppId'?: number;
}

export interface UpdateObjectTypeResponse {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
