/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AnnotateType {
  /** GSB评估规则场景, 仅包含badcase */
  BadCase = 1,
}

/** 主体类型 */
export enum AuthPrincipalType {
  Undefined = 0,
  /** 用户 */
  User = 1,
  /** 部门 */
  Department = 2,
}

export enum ComboType {
  ComboTypeUndefined = 0,
  ComboTypeAnd = 1,
  ComboTypeOr = 2,
}

export enum CompareType {
  Undefined = 0,
  /** SP 比较 */
  SystemPromptCmp = 1,
  /** 模型比较 */
  ModelCmp = 2,
  /** 自由比较 */
  SelfCmp = 3,
}

export enum ConditionType {
  ConditionTypeUndefined = 0,
  ConditionTypeExpr = 1,
  ConditionTypeCombo = 2,
}

export enum ContentType {
  Text = 1,
  Image = 2,
  Binary = 3,
  ImageVariable = 4,
}

export enum Env {
  Undefined = 0,
  /** boe */
  BOE = 1,
  /** onlien */
  Online = 2,
}

export enum EvalStrategyStrategyEnum {
  Undefined = 0,
  /** 一期仅支持AIPaas GSB */
  AIPassGSB = 1,
  /** 二期迁移Fornax */
  FornaxEval = 2,
}

export enum GrayReleaseFieldType {
  Undefined = 0,
  Uid = 1,
  Did = 2,
  CustomKey = 3,
}

export enum GrayReleaseStrategy {
  /** 不开启灰度 */
  None = 0,
  /** 实例灰度 */
  InstanceGrayRelease = 1,
}

export enum GSBResult {
  Undefined = 0,
  Good = 1,
  Same = 2,
  Bad = 3,
}

export enum MessageType {
  System = 1,
  User = 2,
  Assistant = 3,
  Tool = 4,
  Placeholder = 20,
}

/** 模型系列，特别指不同模型接入商 */
export enum ModelClass {
  Undefined = 0,
  /** gpt */
  GPT = 1,
  /** 字节 */
  SEED = 2,
  /** google */
  Gemini = 3,
  /** 亚马逊 */
  Claude = 4,
  /** 文心一言 */
  Ernie = 5,
  /** 百川 */
  Baichuan = 6,
  /** 阿里 */
  Qwen = 7,
  /** 智谱 */
  GML = 8,
  /** 深度求索 */
  DeepSeek = 9,
}

export enum OperatorType {
  OperatorTypeUndefined = 0,
  OperatorTypeEq = 1,
  OperatorTypeIn = 2,
  OperatorTypeNotIn = 3,
  OperatorTypeNotEq = 4,
  OperatorTypeGt = 5,
  OperatorTypeGte = 6,
  OperatorTypeLt = 7,
  OperatorTypeLte = 8,
  OperatorTypeIsNull = 9,
  OperatorTypeIsNotNull = 10,
}

export enum OptimizeEngine {
  Undefined = 0,
  AIPaas_GradientDescent = 1,
}

export enum OptimizeExecutionStatus {
  Undefined = 0,
  Init = 1,
  Running = 2,
  Canceled = 3,
  Fail = 4,
  Completed = 5,
}

export enum OptimizeTargetType {
  Prompt = 1,
}

export enum OrderField {
  CreateTime = 1,
  LastedPublishTime = 2,
}

/** Prompt 加密类型 */
export enum PromptEncryptOption {
  Undefined = 0,
  /** 加密且返回明文 */
  EncryptWithPlainText = 1,
  /** 加密且不返回明文 */
  EncryptWithoutPlainText = 2,
}

/** 提示词类型 */
export enum PromptType {
  Undefined = 0,
  /** 补全模式 */
  Completion = 1,
  /** 聊天模式 */
  Chat = 2,
  /** 补全模式V2 */
  Completion_V2 = 3,
  /** Prompt片段 */
  Segment = 4,
}

/** 发布状态 */
export enum PublishStatus {
  Undefined = 0,
  /** 未发布 */
  UnPublish = 1,
  /** 已发布 */
  Published = 2,
}

export enum ReleaseStatus {
  Undefined = 0,
  /** 在线 */
  Online = 1,
  /** 下线 */
  Offline = 2,
  /** 灰度中(废弃) */
  InGray = 3,
  /** 小流量 */
  Canary = 4,
  /** 单机房 */
  SingleDC = 5,
}

export enum ReleaseSubtaskStatus {
  /** 未开始 */
  PendingStart = 1,
  /** 进行中 */
  InProgress = 2,
  /** 执行成功待确认 */
  ExecuteSuccess = 3,
  /** 待审批 */
  PendingApproval = 4,
  /** 审批通过 */
  ApprovalPassed = 5,
  /** 审批驳回 */
  ApprovalRejected = 6,
  /** 已完成 */
  Finished = 7,
  /** 失败 */
  Failed = 8,
  /** 已跳过 */
  Skipped = 9,
  /** 已回滚 */
  Rollbacked = 10,
  /** 已取消 */
  Canceled = 11,
}

export enum ReleaseTaskStatus {
  /** 未开始 */
  PendingStart = 1,
  /** 进行中 */
  Inprogress = 2,
  /** 待审批 */
  PendingApproval = 3,
  /** 审批通过 */
  ApprovalPass = 4,
  /** 审批驳回 */
  ApprovalRejected = 5,
  /** 灰度发布中 */
  GrayReleasing = 6,
  /** 发布完成 */
  Finished = 7,
  /** 已取消 */
  Canceled = 8,
  /** 已回滚 */
  Rollbacked = 9,
}

export enum ReleaseType {
  Undefined = 0,
  /** 发布 */
  Release = 1,
  /** 回滚 */
  RollBack = 2,
  /** 下线 */
  Offline = 3,
  /** 灰度发布(废弃) */
  GrayRelease = 4,
  /** 灰度取消（废弃） */
  GrayCancel = 5,
  /** 小流量发布 */
  CanaryRelease = 6,
  /** 单机房发布 */
  SingleDCRelease = 7,
}

export enum ReplyType {
  /** 最终结果 */
  ReplyTypeFinalAnswer = 0,
  /** 工具调用 */
  ReplyTypeToolCall = 1,
}

export enum ReportEventType {
  Undefined = 0,
  /** 调试官方 Prompt */
  DebugOfficialPrompt = 1,
  /** 采用官方 Prompt */
  AdoptOfficialPrompt = 2,
}

/** 资源类型 */
export enum ResourceType {
  Undefined = 0,
  Space = 1,
  Prompt = 2,
  Application = 3,
  Evaluation = 4,
  Trace = 5,
  Agent = 6,
}

/** 密级标签 */
export enum SecurityLevel {
  Undefined = 0,
  L1 = 1,
  L2 = 2,
  L3 = 3,
  L4 = 4,
}

/** 空间角色类型 */
export enum SpaceRoleType {
  Undefined = 0,
  /** 负责人 */
  Owner = 1,
  /** 开发者 */
  Developer = 2,
  /** 测试人员 */
  Tester = 3,
}

/** 空间类型 */
export enum SpaceType {
  Undefined = 0,
  Personal = 1,
  Team = 2,
  /** 官方空间 */
  Official = 3,
}

export enum StreamState {
  /** 非流式 */
  StreamStateNone = 1,
  /** 流式传输开始（首包） */
  StreamStateBegin = 2,
  /** 流式传输中 */
  StreamStateStreaming = 3,
  /** 流失传输结束（尾包） */
  StreamStateEnd = 4,
}

export enum TenantType {
  /** 字节 */
  ByteDance = 0,
  /** 懂车帝 */
  Dcar = 1,
}

export enum ToolChoiceType {
  Auto = 1,
  None = 2,
  Specific = 3,
}

export enum ToolType {
  Function = 1,
  /** for gemini native tool */
  GoogleSearch = 2,
}

export enum TriggerOperation {
  /** 开始 */
  Start = 1,
  /** 审批通过 */
  ApprovalPass = 2,
  /** 审批驳回 */
  ApprovalReject = 3,
  /** 完成 */
  Finish = 4,
  /** 重试 */
  Retry = 5,
  /** 跳过 */
  Skip = 6,
  /** 下一步(目前自动触发，不需要前端触发) */
  Next = 7,
}

/** 变量类型 */
export enum VariableType {
  Undefined = 0,
  String = 1,
  /** 废弃，使用Number 不分区整数和浮点数 */
  Integer = 2,
  Boolean = 3,
  Number = 4,
  Array = 5,
  Object = 6,
  Placeholder = 7,
  Image = 8,
}
/* eslint-enable */
