/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_openapi_common from './flow_devops_evaluation_openapi_common';
import * as base from './base';
import * as flow_devops_evaluation_dataset from './flow_devops_evaluation_dataset';

export type Int64 = string | number;

export interface BatchInsertRowGroupsData {
  row_groups: Array<flow_devops_evaluation_openapi_common.RowGroup>;
}

export interface BatchInsertRowGroupsRequest {
  dataset_id: string;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  row_groups: Array<flow_devops_evaluation_openapi_common.RowGroup>;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  Base?: base.Base;
}

export interface BatchInsertRowGroupsResponse {
  code: number;
  msg: string;
  data?: BatchInsertRowGroupsData;
  BaseResp?: base.BaseResp;
}

export interface ClearEvalDatasetRequest {
  dataset_id: Int64;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  Base?: base.Base;
}

export interface ClearEvalDatasetResponse {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CreateEvalDatasetData {
  dataset_id?: Int64;
}

export interface CreateEvalDatasetRequest {
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  name?: string;
  desc?: string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  column_schema?: Array<flow_devops_evaluation_dataset.ColumnInfo>;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  /** 是否发布到 dataset 列表, 默认不发部 */
  publish_option?: flow_devops_evaluation_dataset.PublishOption;
  Base?: base.Base;
}

export interface CreateEvalDatasetResponse {
  code: number;
  msg: string;
  data?: CreateEvalDatasetData;
  BaseResp?: base.BaseResp;
}

export interface PullRowGroupsData {
  row_groups?: Array<flow_devops_evaluation_openapi_common.RowGroup>;
  total?: Int64;
}

export interface PullRowGroupsRequest {
  dataset_id: Int64;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface PullRowGroupsResponse {
  code: number;
  msg: string;
  data?: PullRowGroupsData;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
