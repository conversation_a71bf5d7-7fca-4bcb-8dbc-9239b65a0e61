/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';
import * as flow_devops_evaluation_dataset from './flow_devops_evaluation_dataset';
import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_object_callback from './flow_devops_evaluation_object_callback';
import * as flow_devops_evaluation_evaluator_callback from './flow_devops_evaluation_evaluator_callback';

export type Int64 = string | number;

export enum AnnotationItemDataType {
  Unknown = 0,
  /** 数值打分 */
  Score = 1,
  /** 数值 */
  Value = 2,
  /** 选项 */
  Select = 3,
  /** 纯文本描述 */
  PlainText = 4,
}

export enum ManualAnnotationLabelTaskStatus {
  Created = 10,
  Finished = 50,
}

export enum ManualAnnotationTaskCreateStatus {
  Unknown = 0,
  Creating = 1,
  Failed = 2,
  Success = 3,
}

/** 标注任务回写状态 */
export enum ManualAnnotationTaskWriteBackStatus {
  Unknown = 0,
  /** 任务进行中 */
  Running = 1,
  /** 任务成功 */
  Success = 2,
  /** 任务失败 */
  Failed = 3,
}

export enum ManualStatus {
  /** 不需要人工标注 */
  NoNeed = 0,
  /** 需要人工标注 */
  Need = 1,
}

export enum ObjectType {
  /** 评测任务row，objectID为task_id+row_id */
  EvaluationTaskRow = 1,
}

export enum RowGroupRunState {
  Unknown = -1,
  /** 排队中 */
  Queueing = 0,
  /** 执行中 */
  Processing = 1,
  /** 成功 */
  Success = 2,
  /** 失败 */
  Fail = 3,
  /** 结果待评估 */
  Evaluating = 4,
  /** 终止执行 */
  Terminal = 5,
}

export enum RowRunState {
  /** 未开始执行 */
  Queueing = 0,
  /** 执行成功 */
  Success = 1,
  /** 执行失败 */
  Fail = 2,
}

export enum SourceType {
  /** 评测对比报告，sourceID为评测对比报告ID */
  EvaluationContrastReport = 1,
  /** 评测任务，sourceID为评测任务ID */
  EvaluationTask = 2,
  /** 无来源 */
  NoSource = 100,
}

export enum VisibleArea {
  Unknown = 0,
  /** 在评测模块可见 */
  Evaluation = 1,
  /** 在标注模块可见 */
  ManuaAnnotation = 2,
}

export interface BatchAssignManualAnnotationLabelTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id?: Int64;
  manual_annotation_label_task_ids?: Array<Int64>;
  assign_annotator_id?: Int64;
  Base?: base.Base;
}

export interface BatchAssignManualAnnotationLabelTaskResp {
  BaseResp?: base.BaseResp;
}

export interface BatchCreateManualAnnotationItemReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 人工标注项 */
  manual_annotation_items?: Array<ManualAnnotationItem>;
  Base?: base.Base;
}

export interface BatchCreateManualAnnotationItemResp {
  item_ids?: Array<Int64>;
  BaseResp?: base.BaseResp;
}

export interface BatchCreateManualAnnotationLabelTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id: Int64;
  /** 人工标注打标任务列表 */
  manual_annotation_label_tasks?: Array<ManualAnnotationLabelTask>;
  Base?: base.Base;
}

export interface BatchCreateManualAnnotationLabelTaskResp {
  BaseResp?: base.BaseResp;
}

export interface BatchGetManualAnnotationItemReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  item_ids?: Array<Int64>;
  Base?: base.Base;
}

export interface BatchGetManualAnnotationItemResp {
  manual_annotation_items?: Array<ManualAnnotationItem>;
  BaseResp?: base.BaseResp;
}

export interface BizError {
  err_msg: string;
  err_code: Int64;
}

export interface BlindLabelProgress {
  blind_num?: Int64;
  finished?: Int64;
  all?: Int64;
}

export interface CancelSelectAnnotationLabelResultForWriteBackReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 人工标注打标任务ID */
  manual_annotation_label_task_id?: Int64;
  item_id?: Int64;
  Base?: base.Base;
}

export interface CancelSelectAnnotationLabelResultForWriteBackResp {
  BaseResp?: base.BaseResp;
}

export interface ColumnRuleInfo {
  rule_id: Int64;
  evaluator_type: Int64;
  /** 自定义评估器名称 */
  evaluator_type_name: string;
  /** 规则名称 */
  name?: string;
  /** 用于对话组粒度规则的合并单元格 */
  granularity?: flow_devops_evaluation_entity.EvaluatorGranularity;
}

export interface ContrastTask {
  case_id: Int64;
  task_id: Int64;
}

export interface ContrastTaskPayload {
  case_id: Int64;
  task_id: Int64;
  payload: Array<DashboardRowGroupPayload>;
}

export interface CreateManualAnnotationTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  source_id: Int64;
  source_type: SourceType;
  /** 人工标注项列表 */
  manual_annotation_items?: Array<ManualAnnotationItem>;
  invisible?: boolean;
  name?: string;
  description?: string;
  /** 标识范围 */
  source_range?: SourceRange;
  /** 盲评数 */
  blind_count?: Int64;
  /** 可见性 */
  visible_area?: VisibleArea;
  /** 其他信息 */
  extra?: Extra;
  /** 任务创建状态 */
  create_status?: ManualAnnotationTaskCreateStatus;
  Base?: base.Base;
}

export interface CreateManualAnnotationTaskResp {
  id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DashboardAnnotationTaskRequest {
  'FlowDevops-Agw-UserId'?: string;
  manual_annotation_task_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  space_id: Int64;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface DashboardAnnotationTaskResponse {
  /** 数据集表头信息, 每一行是一条数据集的(input, reference_output, variable) */
  column_name: Array<string>;
  /** 规则表头信息 */
  column_rule_info?: Array<ColumnRuleInfo>;
  /** 标注项表头信息
人工标注项 */
  manual_annotation_items?: Array<ManualAnnotationItem>;
  /** 盲评组编号表头信息 */
  column_blind_nums?: Array<Int64>;
  /** 打标对象信息 */
  payloads?: Array<DashboardRowGroupPayload>;
  /** 对比任务打标对象信息 */
  contrast_task_payloads?: Array<ContrastTaskPayload>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DashboardRowGroupPayload {
  row_group_id?: Int64;
  rows?: Array<DashboardRowPayload>;
  row_group_system_info?: RowGroupSystemInfo;
  /** rowGroup粒度评测规则ruleID -> 结果 */
  row_group_rule_eval_reports?: Array<RowEvalReport>;
}

export interface DashboardRowPayload {
  row_id?: Int64;
  /** 评测数据准备阶段产生 */
  dataset?: RowDataset;
  /** 评测对象结果上报产生 */
  object_output?: RowObjectOutput;
  /** 评测规则执行上报产生 */
  rule_output?: RowRuleOutput;
  /** 人工规则执行上报产生 */
  manual_rule_output?: RowManualRuleOutput;
  /** 评测指标上报产生 */
  metrics_output?: RowMetricsOutput;
  /** 评测系统托管任务运行产生 */
  system_info?: RowSystemInfo;
  /** 标注项结果信息 */
  annotation_payload?: RowAnnotationPayload;
}

export interface DeleteManualAnnotationItemReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_item_id: Int64;
  Base?: base.Base;
}

export interface DeleteManualAnnotationItemResp {
  BaseResp?: base.BaseResp;
}

export interface DeleteManualAnnotationTaskItemReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id: Int64;
  manual_annotation_item_id: Int64;
  Base?: base.Base;
}

export interface DeleteManualAnnotationTaskItemResp {
  BaseResp?: base.BaseResp;
}

export interface DeleteManualAnnotationTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id?: Int64;
  Base?: base.Base;
}

export interface DeleteManualAnnotationTaskResp {
  BaseResp?: base.BaseResp;
}

export interface EvaluateResult {
  /** 打分 */
  score?: number;
  /** 打分过程与结果相关信息 */
  reasoning?: string;
  /** 是否需要人工打分, 当前rule 没有自动评测结果时候， ManualStatus = ManualStatus */
  manual_status?: ManualStatus;
  /** 评估器错误 */
  error?: RowRunError;
  data?: EvaluateResultData;
}

export interface EvaluateResultData {
  score?: number;
  value?: string;
  option?: flow_devops_evaluation_entity.EvaluateResultOption;
  plain_text?: string;
  data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
  value_type?: flow_devops_evaluation_entity.EvaluateResultValueType;
}

export interface ExportAnnotationTaskToCsvRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  annotation_task_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  Base?: base.Base;
}

export interface ExportAnnotationTaskToCsvResponse {
  /** 下载链接 */
  download_url?: string;
  BaseResp?: base.BaseResp;
}

export interface Extra {
  case_id?: Int64;
  contrast_tasks?: Array<ContrastTask>;
}

export interface GetManualAnnotationLabelTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_label_task_id?: Int64;
  Base?: base.Base;
}

export interface GetManualAnnotationLabelTaskResp {
  manual_annotation_label_tasks?: Array<ManualAnnotationLabelTask>;
  BaseResp?: base.BaseResp;
}

export interface GetManualAnnotationTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id: Int64;
  Base?: base.Base;
}

export interface GetManualAnnotationTaskResp {
  manual_annotation_task?: ManualAnnotationTask;
  BaseResp?: base.BaseResp;
}

export interface LabelProgress {
  blind_label_progress_list?: Array<BlindLabelProgress>;
}

export interface ListManualAnnotationItemReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  source_id?: Int64;
  source_type?: SourceType;
  /** -- search fields -- */
  fuzzy_name?: string;
  tag_ids?: Array<Int64>;
  annotation_item_data_types?: Array<flow_devops_evaluation_entity.EvaluateResultDataType>;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface ListManualAnnotationItemResp {
  manual_annotation_items?: Array<ManualAnnotationItem>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface ListManualAnnotationLabelTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id?: Int64;
  object_ids?: Array<string>;
  object_type?: ObjectType;
  status?: Array<ManualAnnotationLabelTaskStatus>;
  assign_annotator_ids?: Array<Int64>;
  actual_annotator_ids?: Array<Int64>;
  blind_nums?: Array<Int64>;
  serial_nums?: Array<Int64>;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface ListManualAnnotationLabelTaskResp {
  manual_annotation_label_tasks?: Array<ManualAnnotationLabelTask>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface ListManualAnnotationTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  source_id?: Int64;
  source_types?: Array<SourceType>;
  /** 可见性 */
  visible_area?: VisibleArea;
  /** 创建人 */
  creator_id?: Int64;
  /** 名称 */
  name?: string;
  assign_annotator_ids?: Array<Int64>;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface ListManualAnnotationTaskResp {
  manual_annotation_tasks?: Array<ManualAnnotationTask>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface ManualAnnotationItem {
  id?: Int64;
  name?: string;
  /** 数据类型：数值评分、选项、纯文本 */
  annotation_item_data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
  /** 评分范围 */
  scope?: ScoringScope;
  options?: Array<flow_devops_evaluation_entity.EvaluateResultOption>;
  space_id?: Int64;
  source_id?: Int64;
  source_type?: SourceType;
  /** fornax 标签管理平台的标签 */
  tags?: Array<flow_devops_evaluation_entity.Tag>;
  creator_id?: Int64;
  delete_time?: Int64;
  create_time?: Int64;
  update_time?: Int64;
}

export interface ManualAnnotationLabelResult {
  /** 人工标注项ID */
  manual_annotation_item_id?: Int64;
  score?: number;
  value?: string;
  option?: string;
  plain_text?: string;
  reasoning?: string;
  data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
  /** 是否已选用 */
  selected?: boolean;
}

export interface ManualAnnotationLabelTask {
  /** 人工标注子任务ID */
  id?: Int64;
  /** 人工标注任务ID */
  manual_annotation_task_id?: Int64;
  space_id?: Int64;
  /** 标注对象行唯一标识 */
  object_id?: string;
  /** 标注对象行类型 */
  object_type?: ObjectType;
  /** 人工标注结果 */
  manual_annotation_label_results?: Array<ManualAnnotationLabelResult>;
  status?: ManualAnnotationLabelTaskStatus;
  assign_annotator_id?: Int64;
  actual_annotator_id?: Int64;
  blind_num?: Int64;
  source_num?: Int64;
  serial_num?: Int64;
  payload?: DashboardRowGroupPayload;
  /** 打标任务对应row在rowGroup中的轮次 */
  round_in_row_group?: Int64;
  creator_id?: Int64;
  delete_time?: Int64;
  create_time?: Int64;
  update_time?: Int64;
}

export interface ManualAnnotationTask {
  id?: Int64;
  space_id?: Int64;
  /** 触发来源ID */
  source_id?: Int64;
  /** 触发来源类型 */
  source_type?: SourceType;
  /** 人工标注项列表 */
  manual_annotation_items?: Array<ManualAnnotationItem>;
  name?: string;
  description?: string;
  /** 标识范围 */
  source_range?: SourceRange;
  /** 盲评数 */
  blind_count?: Int64;
  /** 可见区域 */
  visible_area?: VisibleArea;
  /** 打标进度 */
  label_progress?: LabelProgress;
  /** 其他信息 */
  extra?: Extra;
  /** 任务创建状态 */
  create_status?: ManualAnnotationTaskCreateStatus;
  /** 回写状态 */
  write_back_status?: ManualAnnotationTaskWriteBackStatus;
  creator_id?: Int64;
  delete_time?: Int64;
  create_time?: Int64;
  update_time?: Int64;
}

export interface Range {
  start?: Int64;
  end?: Int64;
}

export interface RowAnnotationPayload {
  manual_annotation_label_tasks?: Array<ManualAnnotationLabelTask>;
}

export interface RowDataset {
  columns?: Array<flow_devops_evaluation_dataset.ColumnInfo>;
  data?: Array<flow_devops_evaluation_callback_common.Content>;
}

/** 每个评估器对应的评分 */
export interface RowEvalReport {
  evaluator_type?: Int64;
  row_eval_result?: EvaluateResult;
  /** 评估器的名称 */
  evaluator_type_name?: string;
  weight?: Int64;
  /** 如果是行级规则需要在单独的列展示 */
  is_row_evaluator?: boolean;
  name?: string;
}

export interface RowGroupSystemInfo {
  /** rowGroup在标注详情页的的编号，从1开始 */
  row_group_serial_num?: Int64;
  /** rowGroup在数据集中的编号，从1开始 */
  source_num?: Int64;
  tags?: Array<string>;
}

export interface RowManualRuleOutput {
  columns?: Array<ColumnRuleInfo>;
  rule_eval_reports?: Array<RowEvalReport>;
}

export interface RowMetricsOutput {
  columns?: Array<ColumnRuleInfo>;
  row_metrics?: Array<RowEvalReport>;
}

/** 对话粒度的评测对象输出数据 */
export interface RowObjectOutput {
  /** 评测对象输出
实际输出 */
  multi_output?: flow_devops_evaluation_object_callback.Output;
  /** 评测中间过程信息
轨迹信息 */
  trajectory?: flow_devops_evaluation_evaluator_callback.Trajectory;
}

export interface RowRuleOutput {
  columns?: Array<ColumnRuleInfo>;
  rule_eval_reports?: Array<RowEvalReport>;
  /** 该row所有自动评测rule按权重聚合后的总分 */
  score?: number;
}

export interface RowRunError {
  code: Int64;
  message: string;
  /** for prompt platform */
  detail: string;
  BizError?: BizError;
}

export interface RowSystemInfo {
  run_state?: RowRunState;
  /** Row执行时关联的logID */
  log_id?: string;
  error?: RowRunError;
  /** 是否跳转评估对象调用 trace */
  direct_object_trace?: boolean;
}

export interface ScoringScope {
  /** 评分范围上限 */
  max_score: number;
  /** 评分范围下限 */
  min_score: number;
}

export interface SelectAnnotationLabelResultForWriteBackReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 人工标注打标任务ID */
  manual_annotation_label_task_id?: Int64;
  item_id?: Int64;
  Base?: base.Base;
}

export interface SelectAnnotationLabelResultForWriteBackResp {
  BaseResp?: base.BaseResp;
}

export interface SourceRange {
  range?: Range;
}

export interface SubmitManualAnnotationLabelTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 人工标注打标任务ID */
  manual_annotation_label_task_id?: Int64;
  /** 人工标注打标结果 */
  manual_annotation_label_results?: Array<ManualAnnotationLabelResult>;
  Base?: base.Base;
}

export interface SubmitManualAnnotationLabelTaskResp {
  BaseResp?: base.BaseResp;
}

export interface UpdateManualAnnotationItemReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_item_id: Int64;
  manual_annotation_item?: ManualAnnotationItem;
  Base?: base.Base;
}

export interface UpdateManualAnnotationItemResp {
  BaseResp?: base.BaseResp;
}

export interface UpdateManualAnnotationTaskReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id: Int64;
  manual_annotation_task: ManualAnnotationTask;
  Base?: base.Base;
}

export interface UpdateManualAnnotationTaskResp {
  BaseResp?: base.BaseResp;
}

export interface UpdateManualAnnotationTaskVisibleReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  manual_annotation_task_id: Int64;
  Base?: base.Base;
}

export interface UpdateManualAnnotationTaskVisibleResp {
  BaseResp?: base.BaseResp;
}

export interface WriteBackAnnotationResultsReq {
  'FlowDevops-Agw-UserId'?: string;
  space_id: Int64;
  'FlowDevops-Agw-AppId'?: number;
  /** 人工标注任务ID */
  manual_annotation_task_id?: Int64;
  Base?: base.Base;
}

export interface WriteBackAnnotationResultsResp {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
