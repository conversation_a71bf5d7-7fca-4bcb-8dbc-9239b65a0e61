/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_task from './flow_devops_evaluation_task';

export type Int64 = string | number;

export enum AutomationObjectType {
  Unknown = 0,
  CozeBot = 1,
  FornaxApp = 2,
}

export enum BatchExecCaseTaskOp {
  Unknown = 0,
  Create = 1,
  Delete = 2,
}

export enum EvalRunEventTaskOp {
  Unknown = 0,
  Create = 1,
  Delete = 2,
}

export interface AutomationObject {
  Type: AutomationObjectType;
  CozeBot?: CozeBot;
  FornaxApp?: FornaxApp;
}

/** AutomationTask 用户创建的自动化任务信息 */
export interface AutomationTask {
  /** 自动化任务ID */
  TaskID: Int64;
  /** 创建自动化任务的用户ID，鉴权需要 */
  UserID: Int64;
  SpaceID: Int64;
}

export interface CozeBot {
  BotID: Int64;
}

export interface FornaxApp {
  PSM: string;
  Env?: string;
  Cluster?: string;
  Region: string;
}

export interface SubTaskSummary {
  TaskID: Int64;
  CaseID: Int64;
  Status?: flow_devops_evaluation_task.TaskStatus;
}
/* eslint-enable */
