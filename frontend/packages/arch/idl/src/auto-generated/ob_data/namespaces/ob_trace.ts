/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as field_filter from './field_filter';
import * as ob_span from './ob_span';
import * as base from './base';

export type Int64 = string | number;

export interface BatchGetTracesAdvanceInfoData {
  traces_advance_info: Array<TraceAdvanceInfo>;
}

export interface BatchGetTracesAdvanceInfoRequest {
  space_id: string;
  bot_id: string;
  traces: Array<TraceQueryParams>;
}

export interface BatchGetTracesAdvanceInfoResponse {
  data: BatchGetTracesAdvanceInfoData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface FieldMeta {
  /** 字段类型 */
  value_type: field_filter.ValueType;
  /** 支持的操作类型 */
  filter_types: Array<field_filter.FieldFilterType>;
  /** 支持的可选项 */
  field_options?: field_filter.FieldOptions;
}

export interface GetTraceByLogIDRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  log_id: string;
  /** ms, 如果未传默认向前搜索十分钟 */
  start_time?: string;
  /** ms, end_at >= start_at, 如果未传默认向后搜索十分钟 */
  end_time?: string;
}

export interface GetTraceByLogIDResponse {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetTraceData {
  spans: Array<ob_span.Span>;
}

export interface GetTraceRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  trace_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
}

export interface GetTraceResponse {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetTracesMetaInfoData {
  /** 字段元信息 */
  field_metas: Record<string, FieldMeta>;
  /** span分类, key是分类，value是span type */
  span_category: Partial<Record<ob_span.SpanCategory, Array<ob_span.SpanType>>>;
}

export interface GetTracesMetaInfoRequest {}

export interface GetTracesMetaInfoResponse {
  data?: GetTracesMetaInfoData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface ListDebugQueriesRequest {
  startAtMS: Int64;
  endAtMS: Int64;
  spaceID: Int64;
  botID: Int64;
  status?: Array<ob_span.SpanStatus>;
  inputSearch?: string;
  limit?: number;
  pageToken?: string;
  Base?: base.Base;
}

export interface ListDebugQueriesResponse {
  data: ListTracesData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface ListTracesData {
  spans: Array<ob_span.Span>;
  /** 下一页的分页token，前端拉取下一页数据时回传。 */
  next_page_token: string;
  /** 是否有更多数据 */
  has_more: boolean;
}

export interface TokenCost {
  /** 输入消耗token数 */
  input: number;
  /** 输出消耗token数 */
  output: number;
}

export interface TraceAdvanceInfo {
  trace_id: string;
  tokens: TokenCost;
  status: ob_span.SpanStatus;
}

/** Trace查询参数 */
export interface TraceQueryParams {
  trace_id: string;
  start_time: string;
  end_time: string;
}
/* eslint-enable */
