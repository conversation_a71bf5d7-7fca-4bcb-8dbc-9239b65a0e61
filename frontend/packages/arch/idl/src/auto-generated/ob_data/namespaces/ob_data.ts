/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agw_common from './agw_common';
import * as common from './common';
import * as queries from './queries';

export type Int64 = string | number;

export enum AuthStatus {
  AuthStatusNormal = 0,
  AuthStatusGeoLocationUnableParse = 1,
  AuthStatusGeoLocationIllegal = 2,
}

export enum BotAuthType {
  AuthTypeGeoLocation = 1,
}

export enum Operation {
  And = 0,
  Or = 1,
  Not = 2,
}

export interface AddBotAuthReq {
  /** Bot机器人ID */
  bot_id: string;
  /** 授权类型 */
  auth_type: BotAuthType;
  /** AGW loader通用参数( */
  agw_common_param?: agw_common.AgwCommonParam;
}

export interface AddBotAuthResp {
  code: number;
  msg: string;
}

export interface APIQueryRequest {
  /** 空间ID，用于权限校验 */
  space_id: string;
  /** Bot机器人ID */
  bot_id: string;
  /** 指标名。枚举值：dau/dnu/wau/wnu/message_count/retention_rate/token */
  metrics_type: string;
  /** 时间类型。枚举值：hour/day/week/month/quarter/year */
  time_type: string;
  /** 时间范围。 */
  time_range: TimeRange;
  /** 其他过滤维度 */
  filter?: Filter;
  /** 过滤器抽象 */
  criterias?: Array<Criteria>;
}

export interface APIQueryResponse {
  data: ObQueryData;
  /** 给API接口使用。RPC接口不要使用该字段 */
  code: Int64;
  /** 给API接口使用。RPC接口不要使用该字段 */
  msg: string;
}

export interface BatchAPIQueryRequest {
  /** 空间ID，用于权限校验 */
  space_id: string;
  /** Bot机器人ID */
  bot_id: string;
  /** 多指标名称 */
  metrics_types: Array<string>;
  /** 时间范围 */
  time_range: TimeRange;
  /** 需要返回的数据类型，枚举value/group_list/point_list/ranking，因为同一个指标可能需要不同的数据类型 */
  data_type: string;
  /** 全局过滤维度 */
  filter?: BatchFilter;
  /** 聚合维度，维度聚合指标需要，目前只有单维度 */
  group_type?: Array<string>;
  /** 时间聚合类型，多轴图/排名需要，枚举：hour/day/week/month/quarter/year */
  period_type?: string;
  /** 固定时间范围类型，不可累加指标需要 */
  time_range_type?: string;
  /** 是不是请求实时数据，实时数据用于展示实时图标 */
  is_realtime?: boolean;
}

export interface BatchAPIQueryResponse {
  data: Array<BatchObData>;
  /** 给API接口使用。RPC接口不要使用该字段 */
  code: Int64;
  /** 给API接口使用。RPC接口不要使用该字段 */
  msg: string;
}

export interface BatchAPIQueryV2Request {
  /** 场景参数  不同场景使用的通用参数 */
  scene_param: common.SceneCommonParam;
  /** 多指标名称 */
  metrics_types: Array<string>;
  /** 需要返回的数据类型，枚举value/group_list/point_list/ranking，因为同一个指标可能需要不同的数据类型 */
  data_type: string;
  /** 数据源。枚举：user_overview，connector_group，user_engagement，error_code_group_value。一个数据源可能可以按不同的 data_type 进行解析 */
  data_source_type: string;
  /** 全局过滤维度 */
  filter?: BatchFilter;
  /** 聚合维度，维度聚合指标需要，目前只有单维度 */
  group_type?: Array<string>;
  /** 时间聚合类型，多轴图/排名需要，枚举：day、week、month、quarter */
  period_type?: string;
  /** 固定时间范围类型。枚举：last_7_days、last_30_days、last_90_days、last_180_days、acc */
  time_range_type?: string;
}

export interface BatchAPIQueryV2Response {
  data: Array<BatchObData>;
  /** 给API接口使用。RPC接口不要使用该字段 */
  code: Int64;
  /** 给API接口使用。RPC接口不要使用该字段 */
  msg: string;
}

export interface BatchFilter {
  /** 渠道名 */
  connector_id?: Array<string>;
}

export interface BatchObData {
  /** 指标类型 */
  metrics_type: string;
  /** 返回的数据类型 */
  data_type: string;
  /** 指标总值 */
  value?: Value;
  /** 聚合结果列表，按照维度拆分 */
  group_list?: Array<Record<string, string>>;
  /** 折线图point列表 */
  point_list?: Array<Point>;
  /** 排名 */
  ranking?: RankValue;
}

export interface BotAuthData {
  /** 权限类型 */
  auth_type: BotAuthType;
  /** 授权是否有异常情况，比如地理位置授权时强制改写了location（因为无法解析/位置不合规） */
  auth_status?: AuthStatus;
  /** 地理位置授权信息，内含location */
  geo_location?: GeoLocation;
}

export interface BotSatisfaction {
  score: number;
  increment: number;
  hasScore: boolean;
}

export interface BotSatisfactionRequest {
  space_id: string;
  bot_id: string;
  /** 时间范围。 */
  time_range: TimeRange;
  /** 指定的渠道列表 */
  connector_ids?: Array<string>;
}

export interface BotSatisfactionResponse {
  data: BotSatisfaction;
  /** 给API接口使用。RPC接口不要使用该字段 */
  code: Int64;
  /** 给API接口使用。RPC接口不要使用该字段 */
  msg: string;
}

export interface Criteria {
  criteria?: Criteria;
  op?: Operation;
  key: string;
  value: string;
}

export interface DeleteBotAuthReq {
  /** Bot机器人ID */
  bot_id: string;
  /** 权限类型 */
  auth_type: BotAuthType;
}

export interface DeleteBotAuthResp {
  code: number;
  msg: string;
}

export interface Dim {
  user_type: string;
  connector_name: string;
  retention_period: string;
  model_name: string;
  message_type: string;
}

export interface EvalSupportChannel {
  status: string;
  channels: Array<string>;
}

export interface EvalSupportChannelRequest {
  bot_id: string;
  space_id: string;
}

export interface EvalSupportChannelResponse {
  data: EvalSupportChannel;
  /** 给API接口使用。RPC接口不要使用该字段 */
  code: Int64;
  /** 给API接口使用。RPC接口不要使用该字段 */
  msg: string;
}

export interface Filter {
  /** 用户类型。枚举值：new_user/old_user。每个字段不传的话，代表不进行过滤。 */
  user_type?: Array<string>;
  /** 渠道名 */
  connector_name?: Array<string>;
  /** 留存周期。枚举值：1d/7d/30d */
  retention_period?: Array<string>;
  /** 大模型名称，如GPT-3.5 */
  model_name?: Array<string>;
}

export interface GeoLocation {
  /** 大地区，注意: 中国大陆、香港、澳门、台湾都有自己的code */
  country_identifier?: string;
  country_name?: string;
  /** 局地，国内是省份 */
  subdivision_identifier?: string;
  subdivision_name?: string;
  /** 城市 */
  city_identifier?: string;
  city_name?: string;
  /** 大地区，注意: 中国大陆、香港、澳门、台湾都有自己的code */
  origin_country_identifier?: string;
  origin_country_name?: string;
  /** 局地，国内是省份 */
  origin_subdivision_identifier?: string;
  origin_subdivision_name?: string;
  /** 城市 */
  origin_city_identifier?: string;
  origin_city_name?: string;
  /** 原客户端ip */
  client_ip?: string;
  /** 授权时间戳 */
  auth_timestamp?: string;
}

export interface GetBotFgRequest {
  space_id: string;
  bot_id: string;
}

export interface GetBotFgResponse {
  /** queries页面的fg开关 */
  enable_queries?: boolean;
  code: Int64;
  msg: string;
}

export interface GetQueriesDataRequest {
  space_id: string;
  bot_id: string;
  /** 时间范围 */
  time_range: TimeRange;
  /** key: field名称，对应 GetQueriesFieldMetasResponse.field_metas 的key */
  filters?: Record<string, queries.QueriesFilter>;
  /** 排序字段，填入filters的key */
  order_by?: string;
  /** asc/desc */
  sort_order?: string;
  /** offset */
  page_token?: Int64;
  /** 默认和最多都是20个 */
  size?: Int64;
}

export interface GetQueriesDataResponse {
  data?: Array<queries.QueriesData>;
  page_token?: Int64;
  has_more?: boolean;
  code: Int64;
  msg: string;
}

export interface GetQueriesFieldMetasRequest {
  space_id: string;
  bot_id: string;
}

export interface GetQueriesFieldMetasResponse {
  /** key: field名称，如"channel"，"intention"等，对应 GetQueriesDataRequest.filters 的key */
  field_metas?: Record<string, queries.FieldMeta>;
  code: Int64;
  msg: string;
}

export interface ObData {
  /** 指标的时间点。时间戳格式，精确到毫秒 */
  metrics_time: Int64;
  /** 指标的值 */
  value: string;
  /** 指标的维度 */
  dim: Dim;
  /** 数据标签名称 */
  name?: Array<string>;
  /** 数据标签名称附加信息 */
  tag?: string;
  /** 数据标签展示名称（可被前端直接展示） */
  label_name?: string;
  /** 数据标签的id，唯一标识标签，用于作为跳转页面时的筛选条件 */
  label_id?: string;
}

export interface ObQueryData {
  list: Array<ObData>;
}

export interface Point {
  /** 指标的时间点。时间戳格式，精确到毫秒 */
  metrics_time: Int64;
  /** 指标的值 */
  value: string;
}

export interface PreCheckAddBotAuthReq {
  /** Bot机器人ID */
  bot_id: string;
  /** 授权类型 */
  auth_type: BotAuthType;
  /** AGW loader通用参数( */
  agw_common_param?: agw_common.AgwCommonParam;
}

export interface PreCheckAddBotAuthResp {
  data?: BotAuthData;
  code: number;
  msg: string;
}

export interface RankValue {
  /** 本周期排名 */
  value?: string;
  /** 上周期排名 */
  last_period_value?: string;
  /** 类目名称 */
  category_name?: string;
}

export interface TimeRange {
  /** 开始时间。时间戳，精确到毫秒 */
  start: Int64;
  /** 结束时间。时间戳，精确到毫秒 */
  end: Int64;
}

export interface UserIntentSatisfactionRanking {
  starling_key: string;
  frequency: Int64;
  score: number;
  contribution: number;
  label_id: string;
  parent_starling_key: string;
}

export interface UserIntentSatisfactionRankingRequest {
  space_id: string;
  bot_id: string;
  /** 时间范围。 */
  time_range: TimeRange;
  label_depth: string;
  /** 指定的渠道列表 */
  connector_ids?: Array<string>;
}

export interface UserIntentSatisfactionRankingResponse {
  data: UserIntentSatisfactionRankings;
  /** 给API接口使用。RPC接口不要使用该字段 */
  code: Int64;
  /** 给API接口使用。RPC接口不要使用该字段 */
  msg: string;
}

export interface UserIntentSatisfactionRankings {
  data: Array<UserIntentSatisfactionRanking>;
}

export interface Value {
  /** 本周期值 */
  value?: string;
  /** 上周期值 */
  last_period_value?: string;
}

export interface ViewBotAuthReq {
  /** Bot机器人ID */
  bot_id: string;
}

export interface ViewBotAuthResp {
  data_list?: Array<BotAuthData>;
  code: number;
  msg: string;
}
/* eslint-enable */
