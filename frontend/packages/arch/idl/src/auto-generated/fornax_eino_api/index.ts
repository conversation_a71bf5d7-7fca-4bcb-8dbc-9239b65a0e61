/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_eino_app from './namespaces/flow_devops_eino_app';
import * as flow_devops_eino_app_canvas from './namespaces/flow_devops_eino_app_canvas';
import * as flow_devops_eino_app_debug from './namespaces/flow_devops_eino_app_debug';

export {
  flow_devops_eino_app,
  flow_devops_eino_app_canvas,
  flow_devops_eino_app_debug,
};
export * from './namespaces/flow_devops_eino_app';
export * from './namespaces/flow_devops_eino_app_canvas';
export * from './namespaces/flow_devops_eino_app_debug';

export type Int64 = string | number;

export default class FornaxEinoApiService<T> {
  private request: any = () => {
    throw new Error('FornaxEinoApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /eino/devops/ping */
  Ping(
    req?: flow_devops_eino_app.PingReq,
    options?: T,
  ): Promise<flow_devops_eino_app.PingResp> {
    const url = this.genBaseURL('/eino/devops/ping');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /eino/devops/nclc/v1/gen_code */
  GenCode(
    req?: flow_devops_eino_app.GraphGenCodeReq,
    options?: T,
  ): Promise<flow_devops_eino_app.GraphGenCodeResp> {
    const _req = req || {};
    const url = this.genBaseURL('/eino/devops/nclc/v1/gen_code');
    const method = 'POST';
    const data = {
      gen_path: _req['gen_path'],
      overwrite: _req['overwrite'],
      fdl_schema: _req['fdl_schema'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /eino/devops/display/v1/display */
  Display(
    req?: flow_devops_eino_app.DisplayReq,
    options?: T,
  ): Promise<flow_devops_eino_app.DisplayResp> {
    const _req = req || {};
    const url = this.genBaseURL('/eino/devops/display/v1/display');
    const method = 'GET';
    const params = {
      drange: _req['drange'],
      var_name: _req['var_name'],
      func_name: _req['func_name'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /eino/devops/debug/v1/graphs */
  ListGraph(
    req?: flow_devops_eino_app.ListGraphReq,
    options?: T,
  ): Promise<flow_devops_eino_app.ListGraphResp> {
    const url = this.genBaseURL('/eino/devops/debug/v1/graphs');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /eino/devops/stream_log */
  StreamLog(
    req?: flow_devops_eino_app.StreamLogReq,
    options?: T,
  ): Promise<string> {
    const url = this.genBaseURL('/eino/devops/stream_log');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /eino/devops/debug/v1/graphs/:graph_id/canvas */
  GetCanvasInfo(
    req: flow_devops_eino_app.GetCanvasInfoReq,
    options?: T,
  ): Promise<flow_devops_eino_app.GetCanvasInfoResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/eino/devops/debug/v1/graphs/${_req['graph_id']}/canvas`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /eino/devops/debug/v1/graphs/:graph_id/threads */
  CreateDebugThread(
    req: flow_devops_eino_app.CreateDebugThreadReq,
    options?: T,
  ): Promise<flow_devops_eino_app.CreateDebugThreadResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/eino/devops/debug/v1/graphs/${_req['graph_id']}/threads`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /eino/devops/debug/v1/graphs/:graph_id/threads/:thread_id/stream */
  StreamDebugRun(
    req: flow_devops_eino_app.StreamDebugRunReq,
    options?: T,
  ): Promise<flow_devops_eino_app.StreamDebugRunResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/eino/devops/debug/v1/graphs/${_req['graph_id']}/threads/${_req['thread_id']}/stream`,
    );
    const method = 'POST';
    const data = {
      from_node: _req['from_node'],
      input: _req['input'],
      log_id: _req['log_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /eino/devops/nclc/v1/components
   *
   * simulate eino cli result not api
   */
  ListComponents(
    req?: flow_devops_eino_app.ListComponentsRequest,
    options?: T,
  ): Promise<flow_devops_eino_app.ListComponentsResp> {
    const url = this.genBaseURL('/eino/devops/nclc/v1/components');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /eino/devops/nclc/v1/get_gen_code_path */
  GetGenCodePath(
    req?: flow_devops_eino_app.GetGenCodePathReq,
    options?: T,
  ): Promise<flow_devops_eino_app.GetGenCodePathResp> {
    const url = this.genBaseURL('/eino/devops/nclc/v1/get_gen_code_path');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** GET /eino/devops/debug/v1/input_types */
  ListInputTypes(
    req?: flow_devops_eino_app.ListInputTypesReq,
    options?: T,
  ): Promise<flow_devops_eino_app.ListInputTypesResp> {
    const url = this.genBaseURL('/eino/devops/debug/v1/input_types');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /eino/devops/version */
  Version(
    req?: flow_devops_eino_app.VersionReq,
    options?: T,
  ): Promise<flow_devops_eino_app.VersionResp> {
    const url = this.genBaseURL('/eino/devops/version');
    const method = 'GET';
    return this.request({ url, method }, options);
  }
}
/* eslint-enable */
