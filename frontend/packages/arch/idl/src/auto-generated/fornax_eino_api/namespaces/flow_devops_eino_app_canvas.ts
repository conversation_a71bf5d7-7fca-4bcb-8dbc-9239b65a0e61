/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface Branch {
  id: string;
  condition?: Condition;
  source_node_key: string;
  target_node_keys?: Array<string>;
}

export interface CanvasInfo {
  /** Graph 名称, Debug 时使用 */
  name?: string;
  /** 渲染展示的版本, Debug 时使用 */
  version?: string;
  /** Canvas的Graph类型，目前有两种(Graph,StateGraph), Debug 时使用 */
  component: string;
  nodes?: Array<Node>;
  edges?: Array<Edge>;
  branches?: Array<Branch>;
  /** Graph 触发模式, Debug 暂时不需关注 */
  node_trigger_mode?: string;
  /** 当Graph类型为StateGraph 时, 对应的方法名称, Debug 暂时不需关注 */
  gen_local_state_method?: string;
}

export interface ComponentSchema {
  component?: string;
  /** 编排时使用 组件类型 枚举值 official、custom */
  component_source?: string;
  /** 组件实现的标识，可视化编排需要保证唯一 */
  identifier?: string;
  /** 编排时使用，生成组件的方法，全局唯一 */
  method?: string;
  /** 编排时,展示其title. debug时依据此Schema规范填入内容 */
  input_type?: JsonSchema;
  /** 编排时只做展示,展示其title. */
  output_type?: JsonSchema;
  /** 编排时使用, 组件额外的配置，按照Schema规范填入内容, Lambda 中的 interaction_type 是枚举: invoke,stream,transform,collect */
  component_extra_schema?: JsonSchema;
  /** 编排时使用, 组件额外的配置 json string 提交给执行侧 */
  component_extra?: string;
  /** 编排时使用, 组件的插槽配置，必定是一个 component */
  slots?: Array<Slot>;
  /** 编排时使用, 组件的 config 配置，按照Schema规范填入内容 */
  config?: ConfigSchema;
  /** 编排时使用, 组件额外的配置，按照Schema规范填入内容, Lambda 中的 interaction_type 是枚举: invoke,stream,transform,collect */
  extra_property?: ExtraPropertySchema;
  /** 编排时使用, 组件具体实现在 UI 上展示的名称 */
  name?: string;
  /** 编排时使用, 组件额外的配置，表述输入输出的type是否可修改 */
  is_io_type_mutable?: boolean;
  /** 编排时使用, 组件额外的配置，表述schema的版本 */
  version?: string;
}

export interface Condition {
  method: string;
  is_stream?: boolean;
  input_type?: JsonSchema;
}

export interface ConfigSchema {
  schema?: JsonSchema;
  config_input?: string;
  description?: string;
}

export interface Edge {
  id: string;
  name: string;
  source_node_key: string;
  target_node_key: string;
}

export interface ExtraPropertySchema {
  schema?: JsonSchema;
  extra_property_input?: string;
}

export interface GenLocalState {
  is_set?: boolean;
  output_type?: JsonSchema;
}

export interface GoDefinition {
  libraryRef?: Library;
  typeName: string;
  kind: string;
  isPtr: boolean;
}

export interface GraphSchema {
  name: string;
  component: string;
  nodes?: Array<Node>;
  edges?: Array<Edge>;
  branches?: Array<Branch>;
  /** Graph 触发模式, 编排必填 枚举值; AnyPredecessor、AllPredecessor */
  node_trigger_mode?: string;
  /** 当Graph类型为StateGraph 时, 编排时当Graph为GraphState类型，此参数必填 */
  gen_local_state?: GenLocalState;
  input_type?: JsonSchema;
  output_type?: JsonSchema;
  /** GraphID，Debug 时使用 */
  id?: string;
}

export interface JsonSchema {
  type: string;
  title?: string;
  description?: string;
  items?: JsonSchema;
  properties?: Record<string, JsonSchema>;
  anyOf?: Array<JsonSchema>;
  additionalProperties?: JsonSchema;
  required?: Array<string>;
  /** custom field */
  propertyOrder?: Array<string>;
  enum?: Array<string>;
  default?: string;
  goDefinition?: GoDefinition;
}

export interface Library {
  module: string;
  version: string;
  pkgPath: string;
}

export interface Node {
  /** 编排时需要用户必填，同一个Graph 唯一， Debug 时做展示使用 */
  key: string;
  /** 编排时需要用户必填, Debug 暂没有使用 */
  name: string;
  /** Debug 使用，表示Node类型 eg: 虚拟branch 节点或者 Component组件 */
  type: string;
  /** 具体组件实现 */
  component_schema?: ComponentSchema;
  /** 具体子Graph实现 */
  graph_schema?: GraphSchema;
  /** 编排时使用, Node Option参数 */
  node_option?: NodeOption;
  /** Debug 使用 对start 节点的入参的推断 */
  infer_input?: JsonSchema;
  /** Debug 使用 该节点是否可操作 */
  allow_operate?: boolean;
}

export interface NodeOption {
  /** 非必填 */
  input_key?: string;
  /** 非必填 */
  output_key?: string;
  /** 非必填, Node是否需要该hander 作为Option 传参 */
  used_state_pre_handler?: boolean;
  /** 非必填, Node是否需要该hander 作为Option 传参 */
  used_state_post_handler?: boolean;
}

export interface Slot {
  component?: string;
  field_loc_path?: string;
  multiple?: boolean;
  required?: boolean;
  component_items?: Array<ComponentSchema>;
  go_definition?: GoDefinition;
}
/* eslint-enable */
