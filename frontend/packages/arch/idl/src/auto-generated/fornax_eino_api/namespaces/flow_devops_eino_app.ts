/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_eino_app_canvas from './flow_devops_eino_app_canvas';
import * as flow_devops_eino_app_debug from './flow_devops_eino_app_debug';

export type Int64 = string | number;

/** Code Doc  */
export enum ErrCode {
  InvalidConfig = 100,
  DownloadEinoTool = 110,
  CreateToolWindows = 120,
  ComandExecute = 130,
  DebugTestRun = 140,
  DebugTestAbort = 150,
}

export enum SubErrCode {
  CommonErrOfInvalidConfig = 100001,
  NotFoundPropertiesErrOfInvalidConfig = 100002,
  CommonErrOfDownloadEinoTool = 110001,
  HTTPRequestTimeoutErrOfDownloadEinoTool = 110002,
  HTTPRequestFailedErrOfDownloadEinoTool = 110003,
  NotFoundMD5SignatureErrOfDownloadEinoTool = 110004,
  MD5CheckFailedErrOfDownloadEinoTool = 110005,
  WriteFileIOExceptionErrOfDownloadEinoTool = 110006,
  AddPermissionExceptionErrOfDownloadEinoTool = 110007,
  EinoToolDownloadEventErrOfDownloadEinoTool = 110008,
  CommonErrOfCreateToolWindows = 120001,
  OpenLinkFailedErrOfCreateToolWindows = 120002,
  GetPageDataFailedErrOfCreateToolWindows = 120003,
  InvaildEntryURLErrOfCreateToolWindows = 120004,
  CommonErrOfComandExecute = 130001,
  CommandVersionErrOfComandExecute = 130002,
  CommandGetGenPathErrOfComandExecute = 130003,
  CommandGenCodeErrOfComandExecute = 130004,
  CommonErrOfDebugTestRun = 140001,
  HTTPRequestTimeoutErrOfDebugTestRun = 140002,
  HTTPRequestFailedErrOfDebugTestRun = 140003,
  CommonErrOfDebugTestAbort = 150001,
  HTTPRequestTimeoutErrOfDebugTestAbort = 150002,
  HTTPRequestFailedErrOfDebugTestAbort = 150003,
}

export interface ComponentItem {
  /** 列表项名称 */
  name?: string;
  /** 如果该项是一个组件，则为非空 */
  component_schema?: flow_devops_eino_app_canvas.ComponentSchema;
  /** 嵌套组件列表 */
  children?: Array<ComponentItem>;
  can_orchestrate?: boolean;
}

export interface CreateDebugThreadData {
  thread_id?: string;
}

export interface CreateDebugThreadReq {
  graph_id: string;
}

export interface CreateDebugThreadResp {
  code: Int64;
  msg: string;
  data?: CreateDebugThreadData;
}

export interface DisplayReq {
  drange?: string;
  var_name?: string;
  func_name?: string;
}

export interface DisplayResp {
  canvas_info?: flow_devops_eino_app_canvas.CanvasInfo;
}

export interface GetCanvasInfoData {
  canvas_info?: flow_devops_eino_app_canvas.CanvasInfo;
}

export interface GetCanvasInfoReq {
  graph_id: string;
}

export interface GetCanvasInfoResp {
  code: Int64;
  msg: string;
  data?: GetCanvasInfoData;
}

export interface GetGenCodePathReq {}

export interface GetGenCodePathResp {
  selected_path?: string;
}

export interface GraphGenCodeReq {
  gen_path?: string;
  overwrite?: boolean;
  fdl_schema?: string;
}

export interface GraphGenCodeResp {
  gen_path?: string;
  fdl_schema?: string;
}

export interface GraphMeta {
  id?: string;
  name?: string;
}

export interface ListComponentsRequest {}

export interface ListComponentsResp {
  /** 官方组件 */
  official_components?: Array<ComponentItem>;
  /** 自定义组件 */
  custom_components?: Array<ComponentItem>;
}

export interface ListGraphData {
  graphs?: Array<GraphMeta>;
}

export interface ListGraphReq {}

export interface ListGraphResp {
  data?: ListGraphData;
  code: Int64;
  msg: string;
}

export interface ListInputTypesData {
  types?: Array<flow_devops_eino_app_canvas.JsonSchema>;
}

export interface ListInputTypesReq {}

export interface ListInputTypesResp {
  code?: Int64;
  msg?: string;
  data?: ListInputTypesData;
}

export interface PingReq {}

export interface PingResp {
  data: string;
  code: Int64;
  msg: string;
}

export interface StreamDebugRunReq {
  graph_id: string;
  thread_id: string;
  from_node?: string;
  input?: string;
  log_id?: string;
}

export interface StreamDebugRunResp {
  type: string;
  debug_id: string;
  error?: string;
  content?: flow_devops_eino_app_debug.NodeDebugState;
}

export interface StreamLogReq {}

export interface VersionReq {}

export interface VersionResp {
  data: string;
  code: Int64;
  msg: string;
}
/* eslint-enable */
