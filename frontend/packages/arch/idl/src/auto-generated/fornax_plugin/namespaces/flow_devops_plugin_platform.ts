/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as tool from './tool';
import * as release from './release';

export type Int64 = string | number;

export interface BatchDeleteToolRequest {}

export interface BatchDeleteToolResponse {}

export interface DeletePluginRequest {}

export interface DeletePluginResponse {}

export interface ListPluginRequest {
  space_id?: Int64;
  page?: number;
  page_size?: number;
  base?: base.Base;
}

export interface ListPluginResponse {
  /** map key为plugin id, value为该plugin的具体的tool的元信息列表 */
  tools?: Record<Int64, Array<tool.Tool>>;
  plugins?: Array<tool.Plugin>;
  base_resp?: base.BaseResp;
}

export interface ObjectReleaseRequest {
  object_id: Int64;
  object_type: release.ObjectType;
  version: string;
  space_id: Int64;
  region?: release.Region;
  env?: string;
  release_type?: release.ReleaseType;
  base?: base.Base;
}

export interface ObjectReleaseResponse {
  object?: release.ObjectRelease;
  base_resp?: base.BaseResp;
}

export interface PublishPluginRequest {}

export interface PublishPluginResponse {}

export interface SubmitPluginRequest {
  space_id: Int64;
  plugin: tool.Plugin;
  version: string;
  tools?: Array<tool.Tool>;
  base?: base.Base;
}

export interface SubmitPluginResponse {
  base_resp?: base.BaseResp;
}
/* eslint-enable */
