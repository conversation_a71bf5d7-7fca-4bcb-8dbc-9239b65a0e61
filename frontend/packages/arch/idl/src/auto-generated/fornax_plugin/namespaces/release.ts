/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ObjectType {
  Prompt = 1,
  Plugin = 2,
}

export enum Region {
  Unknown = 0,
  BOE = 1,
  BOEi18n = 2,
  CN = 3,
  I18N = 4,
}

export enum ReleaseType {
  Normal = 1,
  Gray = 2,
}

export interface ObjectRelease {
  object_id: Int64;
  object_type: ObjectType;
  version: string;
  space_id: Int64;
  region?: Region;
  env?: string;
  release_type?: ReleaseType;
  snapshot?: string;
}
/* eslint-enable */
