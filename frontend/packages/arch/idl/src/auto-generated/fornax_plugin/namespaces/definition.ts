/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface Definition {
  /** Type specifies the data type of the schema. */
  type: string;
  /** Description is the description of the schema. */
  description?: string;
  /** Enum is used to restrict a value to a fixed set of values. It must be an array with at least one element, where each element is unique. You will probably only use this with strings. */
  enum?: Array<string>;
  /** Properties describes the properties of an object, if the schema type is Object. */
  properties?: Record<string, Definition>;
  /** Required specifies which properties are required, if the schema type is Object. */
  required?: Array<string>;
  /** Items specifies which data type an array contains, if the schema type is Array. */
  items?: Definition;
}
/* eslint-enable */
