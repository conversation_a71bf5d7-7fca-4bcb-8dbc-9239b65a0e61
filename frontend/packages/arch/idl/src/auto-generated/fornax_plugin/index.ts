/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as definition from './namespaces/definition';
import * as flow_devops_plugin_ping from './namespaces/flow_devops_plugin_ping';
import * as flow_devops_plugin_platform from './namespaces/flow_devops_plugin_platform';
import * as release from './namespaces/release';
import * as tool from './namespaces/tool';

export {
  base,
  definition,
  flow_devops_plugin_ping,
  flow_devops_plugin_platform,
  release,
  tool,
};
export * from './namespaces/base';
export * from './namespaces/definition';
export * from './namespaces/flow_devops_plugin_ping';
export * from './namespaces/flow_devops_plugin_platform';
export * from './namespaces/release';
export * from './namespaces/tool';

export type Int64 = string | number;

export default class FornaxPluginService<T> {
  private request: any = () => {
    throw new Error('FornaxPluginService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/devops/plugin/ping
   *
   * KitexThrift
   */
  Ping(
    req: flow_devops_plugin_ping.PingReq,
    options?: T,
  ): Promise<flow_devops_plugin_ping.PingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/plugin/ping');
    const method = 'POST';
    const data = { ping_message: _req['ping_message'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/plugin/platform/list_space_plugins */
  ListPlugin(
    req?: flow_devops_plugin_platform.ListPluginRequest,
    options?: T,
  ): Promise<flow_devops_plugin_platform.ListPluginResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/plugin/platform/list_space_plugins',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/plugin/platform/release_plugin
   *
   * 发布Plugin版本
   */
  ObjectRelease(
    req: flow_devops_plugin_platform.ObjectReleaseRequest,
    options?: T,
  ): Promise<flow_devops_plugin_platform.ObjectReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/plugin/platform/release_plugin');
    const method = 'POST';
    const data = {
      object_id: _req['object_id'],
      object_type: _req['object_type'],
      version: _req['version'],
      space_id: _req['space_id'],
      region: _req['region'],
      env: _req['env'],
      release_type: _req['release_type'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/plugin/platform/publish_plugin
   *
   * 上架Plugin到Hub
   */
  PublishPlugin(
    req?: flow_devops_plugin_platform.PublishPluginRequest,
    options?: T,
  ): Promise<flow_devops_plugin_platform.PublishPluginResponse> {
    const url = this.genBaseURL('/api/devops/plugin/platform/publish_plugin');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/devops/plugin/platform/submit_plugin
   *
   * 提交Plugin版本记录
   */
  SubmitPlugin(
    req: flow_devops_plugin_platform.SubmitPluginRequest,
    options?: T,
  ): Promise<flow_devops_plugin_platform.SubmitPluginResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/plugin/platform/submit_plugin');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      plugin: _req['plugin'],
      version: _req['version'],
      tools: _req['tools'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/plugin/platform/delete_plugin */
  DeletePlugin(
    req?: flow_devops_plugin_platform.DeletePluginRequest,
    options?: T,
  ): Promise<flow_devops_plugin_platform.DeletePluginResponse> {
    const url = this.genBaseURL('/api/devops/plugin/platform/delete_plugin');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/devops/plugin/platform/mdelete_tool */
  BatchDeleteTool(
    req?: flow_devops_plugin_platform.BatchDeleteToolRequest,
    options?: T,
  ): Promise<flow_devops_plugin_platform.BatchDeleteToolResponse> {
    const url = this.genBaseURL('/api/devops/plugin/platform/mdelete_tool');
    const method = 'POST';
    return this.request({ url, method }, options);
  }
}
/* eslint-enable */
