/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as common from './namespaces/common';
import * as entity from './namespaces/entity';

export { base, common, entity };
export * from './namespaces/base';
export * from './namespaces/common';
export * from './namespaces/entity';

export type Int64 = string | number;

export default class MagicEnglishService<T> {
  private request: any = () => {
    throw new Error('MagicEnglishService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /api/magic/tts_token */
  GetTTSToken(
    req?: entity.GetTTSTokenReq,
    options?: T,
  ): Promise<entity.GetTTSTokenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/tts_token');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/magic/chat_llm */
  ChatWithLLM(
    req?: entity.ChatWithLLMReq,
    options?: T,
  ): Promise<entity.ChatWithLLMResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/chat_llm');
    const method = 'POST';
    const data = {
      text: _req['text'],
      duration: _req['duration'],
      audio_id: _req['audio_id'],
      conversation_id: _req['conversation_id'],
      message_id: _req['message_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/magic/start_chat */
  StartChat(
    req: entity.StartChatReq,
    options?: T,
  ): Promise<entity.StartChatResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/start_chat');
    const method = 'POST';
    const data = {
      exercise_id: _req['exercise_id'],
      conversation_id: _req['conversation_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/magic/get_learning_path */
  GetLearningPath(
    req?: entity.GetLearningPathReq,
    options?: T,
  ): Promise<entity.GetLearningPathResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/get_learning_path');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/magic/unlock_topic */
  UnlockTopic(
    req: entity.UnlockTopicReq,
    options?: T,
  ): Promise<entity.UnlockTopicResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/unlock_topic');
    const method = 'POST';
    const data = { topic_id: _req['topic_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/magic/get_exercise_info */
  GetExerciseInfo(
    req: entity.GetExerciseInfoReq,
    options?: T,
  ): Promise<entity.GetExerciseInfoResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/get_exercise_info');
    const method = 'GET';
    const params = { exercise_id: _req['exercise_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/magic/get_message_tip
   *
   * 消息提示
   */
  GetMessageTip(
    req?: entity.GetMessageTipReq,
    options?: T,
  ): Promise<entity.GetMessageTipResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/get_message_tip');
    const method = 'GET';
    const params = { message_id: _req['message_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/magic/message_eval
   *
   * 用户消息评测
   */
  MessageEval(
    req?: entity.MessageEvalReq,
    options?: T,
  ): Promise<entity.MessageEvalResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/message_eval');
    const method = 'POST';
    const data = { message_id: _req['message_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/magic/list_phase */
  ListPhase(
    req?: entity.ListPhaseReq,
    options?: T,
  ): Promise<entity.ListPhaseResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/list_phase');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/magic/select_phase */
  SelectPhase(
    req: entity.SelectPhaseReq,
    options?: T,
  ): Promise<entity.SelectPhaseResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/select_phase');
    const method = 'POST';
    const data = { phase_id: _req['phase_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/magic/get_user_level */
  GetUserLevel(
    req?: entity.GetUserLevelReq,
    options?: T,
  ): Promise<entity.GetUserLevelResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/get_user_level');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/magic/translate_message
   *
   * 消息翻译
   */
  TranslateMessage(
    req: entity.TranslateMessageReq,
    options?: T,
  ): Promise<entity.TranslateMessageResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/translate_message');
    const method = 'POST';
    const data = { message_id: _req['message_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/magic/resume_conversation
   *
   * 恢复对话
   */
  ResumeConversation(
    req: entity.ResumeConversationReq,
    options?: T,
  ): Promise<entity.ResumeConversationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/resume_conversation');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/magic/close_conversation
   *
   * 结束对话
   */
  CloseConversation(
    req: entity.CloseConversationReq,
    options?: T,
  ): Promise<entity.CloseConversationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/close_conversation');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/magic/get_last_conversation
   *
   * 获取节点上次对话
   */
  GetLastConversation(
    req: entity.GetLastConversationReq,
    options?: T,
  ): Promise<entity.GetLastConversationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/get_last_conversation');
    const method = 'GET';
    const params = { exercise_id: _req['exercise_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/magic/conversation/record
   *
   * 获取对话练习记录
   */
  ListConversationRecord(
    req?: entity.ListConversationRecordReq,
    options?: T,
  ): Promise<entity.ListConversationRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/conversation/record');
    const method = 'GET';
    const params = {
      exercise_id: _req['exercise_id'],
      status: _req['status'],
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/magic/conversation/detail
   *
   * 获取对话详情
   */
  GetConversationDetail(
    req: entity.GetConversationDetailReq,
    options?: T,
  ): Promise<entity.GetConversationDetailResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/conversation/detail');
    const method = 'GET';
    const params = { id: _req['id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/magic/report
   *
   * 生成对话报告
   */
  CreateConversationReport(
    req: entity.CreateConversationReportReq,
    options?: T,
  ): Promise<entity.CreateConversationReportResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/report');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/magic/get_js_sdk_config
   *
   * 获取JSSDK配置
   */
  GetJSSDKConfig(
    req: entity.GetJSSDKConfigReq,
    options?: T,
  ): Promise<entity.GetJSSDKConfigResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/get_js_sdk_config');
    const method = 'GET';
    const params = { url: _req['url'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/magic/calendar/duration
   *
   * 更新使用时长
   */
  UpdateStudyDuration(
    req: entity.UpdateStudyDurationReq,
    options?: T,
  ): Promise<entity.UpdateStudyDurationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/calendar/duration');
    const method = 'POST';
    const data = {
      duration: _req['duration'],
      module_type: _req['module_type'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/magic/calendar/duration/today
   *
   * 获取今日使用时长
   */
  GetTodayDuration(
    req?: entity.GetTodayDurationReq,
    options?: T,
  ): Promise<entity.GetTodayDurationResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/calendar/duration/today');
    const method = 'GET';
    const params = { module_type: _req['module_type'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/magic/calendar/duration/total
   *
   * 获取累计使用时长
   */
  GetTotalDuration(
    req?: entity.GetTotalDurationReq,
    options?: T,
  ): Promise<entity.GetTotalDurationResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/magic/calendar/duration/total');
    const method = 'GET';
    const params = { module_type: _req['module_type'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/meeting/start
   *
   * 开始学习会议
   */
  StartMeeting(
    req: entity.StartMeetingReq,
    options?: T,
  ): Promise<entity.StartMeetingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/start');
    const method = 'POST';
    const data = { meeting_id: _req['meeting_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/meeting/continue
   *
   * 继续下一题
   */
  ContinueMeeting(
    req: entity.ContinueMeetingReq,
    options?: T,
  ): Promise<entity.ContinueMeetingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/continue');
    const method = 'POST';
    const data = {
      learning_id: _req['learning_id'],
      exercise_id: _req['exercise_id'],
      exercise_seq: _req['exercise_seq'],
      user_answer: _req['user_answer'],
      user_audio_id: _req['user_audio_id'],
      original_content: _req['original_content'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/meeting/complete
   *
   * 完成学习
   */
  CompleteMeeting(
    req: entity.CompleteMeetingReq,
    options?: T,
  ): Promise<entity.CompleteMeetingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/complete');
    const method = 'POST';
    const data = { learning_id: _req['learning_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/meeting/slide
   *
   * -- 场景会议 -- //
   *
   * 首页轮播
   */
  GetMeetingSlide(
    req?: entity.GetMeetingSlideReq,
    options?: T,
  ): Promise<entity.GetMeetingSlideResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/meeting/slide');
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/meeting/list
   *
   * 列表接口
   */
  GetMeetingList(
    req: entity.GetMeetingListReq,
    options?: T,
  ): Promise<entity.GetMeetingListResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/list');
    const method = 'POST';
    const data = {
      meeting_type: _req['meeting_type'],
      category_id: _req['category_id'],
      only_not_learn: _req['only_not_learn'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/meeting/category
   *
   * 分类信息
   */
  GetMeetingCategory(
    req: entity.GetMeetingCategoryReq,
    options?: T,
  ): Promise<entity.GetMeetingCategoryResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/category');
    const method = 'POST';
    const data = {
      study_module_type: _req['study_module_type'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/meeting/chat_room/detail
   *
   * 获取ai chat详情 （学完后重新进入）
   */
  GetMeetingAIChatDetail(
    req: entity.GetMeetingAIChatDetailReq,
    options?: T,
  ): Promise<entity.GetMeetingAIChatDetailResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/chat_room/detail');
    const method = 'POST';
    const data = { learning_id: _req['learning_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/magic/meeting/ai/role_message
   *
   * 生成角色消息
   */
  GenerateRoleMessage(
    req: entity.GenerateRoleMessageReq,
    options?: T,
  ): Promise<entity.GenerateRoleMessageResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/meeting/ai/role_message');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      user_response: _req['user_response'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/magic/meeting/ai/suggestion
   *
   * 获取 AI 对话回复建议
   */
  GetAISuggestion(
    req: entity.GetAISuggestionReq,
    options?: T,
  ): Promise<entity.GetAISuggestionResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/meeting/ai/suggestion');
    const method = 'GET';
    const params = {
      conversation_id: _req['conversation_id'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/meeting/check_user_audio
   *
   * 保存音频，语音转文本,检查准确度
   */
  CheckUserAudio(
    req: entity.CheckUserAudioReq,
    options?: T,
  ): Promise<entity.CheckUserAudioResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/check_user_audio');
    const method = 'POST';
    const data = {
      body: _req['body'],
      audio_body: _req['audio_body'],
      original_content: _req['original_content'],
      need_check_accuracy: _req['need_check_accuracy'],
      base: _req['base'],
    };
    const headers = { 'Content-Type': _req['Content-Type'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/magic/meeting/ai/leave
   *
   * 离开 AI 对话
   */
  LeaveAiChatRoom(
    req: entity.LeaveAiChatRoomReq,
    options?: T,
  ): Promise<entity.LeaveAiChatRoomResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/meeting/ai/leave');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/meeting/check_text_accuracy
   *
   * 检查文本的准确度
   */
  CheckTextAccuracy(
    req: entity.CheckTextAccuracyReq,
    options?: T,
  ): Promise<entity.CheckTextAccuracyResp> {
    const _req = req;
    const url = this.genBaseURL('/api/meeting/check_text_accuracy');
    const method = 'POST';
    const data = {
      text: _req['text'],
      original_text: _req['original_text'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/magic/translate
   *
   * -- 刷题 -- //
   *
   * 翻译
   */
  TranslateMessageV2(
    req: entity.TranslateMessageV2Req,
    options?: T,
  ): Promise<entity.TranslateMessageV2Resp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/translate');
    const method = 'POST';
    const data = {
      content: _req['content'],
      history_message_list: _req['history_message_list'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/magic/oral_score
   *
   * 口语评测
   */
  GetOralScore(
    req: entity.GetOralScoreReq,
    options?: T,
  ): Promise<entity.GetOralScoreResp> {
    const _req = req;
    const url = this.genBaseURL('/api/magic/oral_score');
    const method = 'POST';
    const data = { user_response: _req['user_response'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
