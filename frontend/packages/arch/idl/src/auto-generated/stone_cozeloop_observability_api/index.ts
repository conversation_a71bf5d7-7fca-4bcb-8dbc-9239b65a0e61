/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as annotation from './namespaces/annotation';
import * as authz from './namespaces/authz';
import * as common from './namespaces/common';
import * as coze_loop_observability_openapi from './namespaces/coze_loop_observability_openapi';
import * as coze_loop_observability_saas from './namespaces/coze_loop_observability_saas';
import * as coze_loop_observability_trace from './namespaces/coze_loop_observability_trace';
import * as filter from './namespaces/filter';
import * as span from './namespaces/span';
import * as view from './namespaces/view';

export {
  annotation,
  authz,
  common,
  coze_loop_observability_openapi,
  coze_loop_observability_saas,
  coze_loop_observability_trace,
  filter,
  span,
  view,
};
export * from './namespaces/annotation';
export * from './namespaces/authz';
export * from './namespaces/common';
export * from './namespaces/coze_loop_observability_openapi';
export * from './namespaces/coze_loop_observability_saas';
export * from './namespaces/coze_loop_observability_trace';
export * from './namespaces/filter';
export * from './namespaces/span';
export * from './namespaces/view';

export type Int64 = string | number;

export default class StoneCozeloopObservabilityApiService<T> {
  private request: any = () => {
    throw new Error(
      'StoneCozeloopObservabilityApiService.request is undefined',
    );
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/observability/v1/views/list */
  ListViews(
    req: coze_loop_observability_trace.ListViewsRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.ListViewsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observability/v1/views/list');
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      workspace_id: _req['workspace_id'],
      view_name: _req['view_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/observability/v1/traces/meta_info */
  GetTracesMetaInfo(
    req?: coze_loop_observability_trace.GetTracesMetaInfoRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.GetTracesMetaInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/observability/v1/traces/meta_info');
    const method = 'GET';
    const params = {
      platform_type: _req['platform_type'],
      span_list_type: _req['span_list_type'],
      workspace_id: _req['workspace_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/observability/v1/views */
  CreateView(
    req: coze_loop_observability_trace.CreateViewRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.CreateViewResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observability/v1/views');
    const method = 'POST';
    const data = {
      enterprise_id: _req['enterprise_id'],
      workspace_id: _req['workspace_id'],
      view_name: _req['view_name'],
      platform_type: _req['platform_type'],
      span_list_type: _req['span_list_type'],
      filters: _req['filters'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/observability/v1/traces/:trace_id */
  GetTrace(
    req: coze_loop_observability_trace.GetTraceRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.GetTraceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/observability/v1/traces/${_req['trace_id']}`,
    );
    const method = 'GET';
    const params = {
      workspace_id: _req['workspace_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      platform_type: _req['platform_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /** PUT /api/observability/v1/views/:view_id */
  UpdateView(
    req: coze_loop_observability_trace.UpdateViewRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.UpdateViewResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/observability/v1/views/${_req['view_id']}`,
    );
    const method = 'PUT';
    const data = {
      workspace_id: _req['workspace_id'],
      view_name: _req['view_name'],
      platform_type: _req['platform_type'],
      span_list_type: _req['span_list_type'],
      filters: _req['filters'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/observability/v1/traces/batch_get_advance_info */
  BatchGetTracesAdvanceInfo(
    req: coze_loop_observability_trace.BatchGetTracesAdvanceInfoRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.BatchGetTracesAdvanceInfoResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/observability/v1/traces/batch_get_advance_info',
    );
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      traces: _req['traces'],
      platform_type: _req['platform_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v1/loop/traces/ingest */
  IngestTraces(
    req?: coze_loop_observability_openapi.IngestTracesRequest,
    options?: T,
  ): Promise<coze_loop_observability_openapi.IngestTracesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/loop/traces/ingest');
    const method = 'POST';
    const data = { spans: _req['spans'] };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /api/observability/v1/views/:view_id */
  DeleteView(
    req: coze_loop_observability_trace.DeleteViewRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.DeleteViewResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/observability/v1/views/${_req['view_id']}`,
    );
    const method = 'DELETE';
    const params = { workspace_id: _req['workspace_id'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/observability/v1/spans/list */
  ListSpans(
    req: coze_loop_observability_trace.ListSpansRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.ListSpansResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observability/v1/spans/list');
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      start_time: _req['start_time'],
      end_time: _req['end_time'],
      filters: _req['filters'],
      page_size: _req['page_size'],
      order_bys: _req['order_bys'],
      page_token: _req['page_token'],
      platform_type: _req['platform_type'],
      span_list_type: _req['span_list_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/observability/v1/intelligences/list */
  ListIntelligences(
    req: coze_loop_observability_saas.ListIntelligencesRequest,
    options?: T,
  ): Promise<coze_loop_observability_saas.ListIntelligencesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observability/v1/intelligences/list');
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      intelligence_type: _req['intelligence_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** DELETE /api/observability/v1/annotations/:annotation_id */
  DeleteManualAnnotation(
    req: coze_loop_observability_trace.DeleteManualAnnotationRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.DeleteManualAnnotationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/observability/v1/annotations/${_req['annotation_id']}`,
    );
    const method = 'DELETE';
    const params = {
      workspace_id: _req['workspace_id'],
      trace_id: _req['trace_id'],
      span_id: _req['span_id'],
      start_time: _req['start_time'],
      annotation_key: _req['annotation_key'],
      platform_type: _req['platform_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/observability/v1/annotations */
  CreateManualAnnotation(
    req: coze_loop_observability_trace.CreateManualAnnotationRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.CreateManualAnnotationResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observability/v1/annotations');
    const method = 'POST';
    const data = {
      annotation: _req['annotation'],
      platform_type: _req['platform_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** PUT /api/observability/v1/annotations/:annotation_id */
  UpdateManualAnnotation(
    req: coze_loop_observability_trace.UpdateManualAnnotationRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.UpdateManualAnnotationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/observability/v1/annotations/${_req['annotation_id']}`,
    );
    const method = 'PUT';
    const data = {
      annotation: _req['annotation'],
      platform_type: _req['platform_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/observability/v1/annotations/list */
  ListAnnotations(
    req: coze_loop_observability_trace.ListAnnotationsRequest,
    options?: T,
  ): Promise<coze_loop_observability_trace.ListAnnotationsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/observability/v1/annotations/list');
    const method = 'POST';
    const data = {
      workspace_id: _req['workspace_id'],
      span_id: _req['span_id'],
      trace_id: _req['trace_id'],
      start_time: _req['start_time'],
      platform_type: _req['platform_type'],
      desc_by_updated_at: _req['desc_by_updated_at'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
