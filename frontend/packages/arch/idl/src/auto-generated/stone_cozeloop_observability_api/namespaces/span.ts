/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as annotation from './annotation';

export type Int64 = string | number;

export interface AttrTos {
  input_data_url?: string;
  output_data_url?: string;
  multimodal_data?: Record<string, string>;
}

export interface InputSpan {
  started_at_micros: string;
  span_id: string;
  parent_id: string;
  trace_id: string;
  duration: string;
  call_type?: string;
  workspace_id: string;
  span_name: string;
  span_type: string;
  method: string;
  status_code: number;
  input: string;
  output: string;
  object_storage?: string;
  system_tags_string?: Record<string, string>;
  system_tags_long?: Record<string, Int64>;
  system_tags_double?: Record<string, number>;
  tags_string?: Record<string, string>;
  tags_long?: Record<string, Int64>;
  tags_double?: Record<string, number>;
  tags_bool?: Record<string, boolean>;
  tags_bytes?: Record<string, string>;
  duration_micros?: string;
}

export interface OutputSpan {
  trace_id: string;
  span_id: string;
  parent_id: string;
  span_name: string;
  span_type: string;
  type: string;
  started_at: string;
  duration: string;
  status: string;
  status_code: number;
  input: string;
  output: string;
  logic_delete_date?: string;
  custom_tags?: Record<string, string>;
  attr_tos?: AttrTos;
  system_tags?: Record<string, string>;
  annotations?: Array<annotation.Annotation>;
}
/* eslint-enable */
