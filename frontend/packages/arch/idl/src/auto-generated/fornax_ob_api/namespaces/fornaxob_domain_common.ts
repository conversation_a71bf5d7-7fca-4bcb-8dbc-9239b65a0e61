/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface BaseInfo {
  created_by?: UserInfo;
  updated_by?: UserInfo;
  /** 创建时间,ms timestamp */
  created_at?: Int64;
  /** 更新时间,ms timestamp */
  updated_at?: Int64;
}

export interface UserInfo {
  /** 姓名 */
  name?: string;
  /** 英文名称 */
  en_name?: string;
  /** 用户头像url */
  avatar_url?: string;
  /** 72 * 72 头像 */
  avatar_thumb?: string;
  /** 用户应用内唯一标识 */
  open_id?: string;
  /** 用户应用开发商内唯一标识 */
  union_id?: string;
  /** 企业标识 */
  tenant_key?: string;
  /** 用户在租户内的唯一标识 */
  user_id?: string;
  /** 用户邮箱 */
  email?: string;
}
/* eslint-enable */
