/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as annotation from './annotation';

export type Int64 = string | number;

export enum SpanStatus {
  Unknown = 1,
  Success = 2,
  Error = 3,
  Broken = 4,
}

export enum SpanType {
  Unknown = 1,
  Prompt = 2,
  Model = 3,
  Parser = 4,
  Embedding = 5,
  Memory = 6,
  Plugin = 7,
  Function = 8,
  Graph = 9,
  Remote = 10,
  Loader = 11,
  Transformer = 12,
  VectorStore = 13,
  VectorRetriever = 14,
  Agent = 15,
  CozeBot = 16,
  LLMCall = 17,
}

export interface AttrAgent {
  bot_id: string;
  bot_version: string;
  bot_space_id: string;
  bot_env: string;
  bot_name: string;
  channel: string;
  connector_id: string;
  connector_uid: string;
  latency_first_resp: string;
  section_id?: string;
  conversation_id?: string;
  message_id?: string;
  message_type?: string;
}

export interface AttrBase {
  /** 触发事件的用户ID */
  user_id: string;
  /** 触发事件的用户ID */
  device_id: string;
  /** 事件发生的space */
  space_id: string;
  /** 事件发生的psm */
  psm: string;
  /** 泳道 */
  psm_env: string;
  /** 本节点的输入 */
  input: string;
  /** 本节点的输出 */
  output: string;
  /** 本节点的报错 */
  err_msg: string;
  status_code?: string;
  tokens?: Int64;
  input_tokens?: Int64;
  output_tokens?: Int64;
  span_type?: string;
  prompt_key?: string;
  call_type?: string;
  debug_id?: string;
  /** 事件发生的space */
  fornax_space_id?: string;
  /** 是否是第一跳 */
  fornax_psm_first_span?: string;
}

export interface AttrCozeBot {
  bot_id: string;
  bot_version: string;
  bot_space_id: string;
  bot_env: string;
  bot_name: string;
  channel: string;
  connector_id: string;
  connector_uid: string;
  latency_first_resp: string;
  os_version: string;
  os: string;
  query_input_method: string;
  section_id?: string;
  conversation_id?: string;
  message_id?: string;
  message_type?: string;
}

export interface AttrModel {
  /** model类型: gpt3.5/gpt4 ... */
  model_name: string;
}

export interface AttrTos {
  input_data_url?: string;
  output_data_url?: string;
  multimodal_data?: Record<string, string>;
}

/** A Span represents a single operation performed by a single component of the system. */
export interface Span {
  /** A unique identifier for a trace. All spans from the same trace share the same `trace_id`.
This field is required. */
  trace_id: string;
  /** A unique identifier for a span within a trace, assigned when the span
is created.
This field is required. */
  id: string;
  /** The `span id` of this span's parent span. If this is a root span, then the value of this
field must be empty.
❌ "parent_id": null
✅ "parent_id": "" */
  parent_id: string;
  /** A description of the span's operation.

For example, the name can be a qualified method name or a file name
and a line number where the operation is called. A best practice is to use
the same display name at the same call point in an application.
This makes it easier to correlate spans in different traces.

This field is semantically required to be set to non-empty string.
Empty value is equivalent to an unknown span name.

This field is required. */
  name: string;
  /** Type is the type of span. */
  type: SpanType;
  /** StartTime(ms) is the start time of the span. On the client side, this is the time
kept by the local machine where the span execution starts. On the server side,
this is the time when the server's application handler starts running.
Value is UNIX Epoch time in nanoseconds since 00:00:00 UTC on 1 January 1970. */
  start_time: string;
  /** Latency(ms) */
  latency: string;
  /** An final status for this span. */
  status: SpanStatus;
  /** argos日志ID */
  log_id: string;
  /** fornax base attributes */
  attr_base: AttrBase;
  attr_model?: AttrModel;
  extra?: Record<string, string>;
  /** 废弃，不返回任何值 */
  custom_span_type?: string;
  attr_agent?: AttrAgent;
  attr_coze_bot?: AttrCozeBot;
  service_meta?: Record<string, string>;
  /** 是否是加密数据 */
  is_encryption_data?: boolean;
  attr_tos?: AttrTos;
  system_tags?: Record<string, string>;
  logic_delete_date?: string;
  annotations?: Array<annotation.Annotation>;
}
/* eslint-enable */
