/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum BuiltinTemplateType {
  Prompt = 1,
  Code = 2,
}

export enum EvaluatorRunStatus {
  /** 运行状态, 异步下状态流转, 同步下只有 Success / Fail */
  Unknown = 0,
  Success = 1,
  Fail = 2,
}

export enum EvaluatorType {
  Prompt = 1,
  Code = 2,
}

export enum LanguageType {
  Python = 1,
  JS = 2,
}

export enum PromptSourceType {
  BuiltinTemplate = 1,
  FornaxPrompt = 2,
  Custom = 3,
}

export enum ToolType {
  Function = 1,
  /** for gemini native tool */
  GoogleSearch = 2,
}
/* eslint-enable */
