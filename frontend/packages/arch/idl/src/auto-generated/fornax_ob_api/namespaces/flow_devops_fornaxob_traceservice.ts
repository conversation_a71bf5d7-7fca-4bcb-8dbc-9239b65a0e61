/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_fornaxob_common from './flow_devops_fornaxob_common';
import * as annotation from './annotation';
import * as base from './base';
import * as eval_set from './eval_set';
import * as task from './task';
import * as datasetv2 from './datasetv2';
import * as flow_devops_fornaxob_fieldfilter from './flow_devops_fornaxob_fieldfilter';
import * as flow_devops_fornaxob_fieldfilterv2 from './flow_devops_fornaxob_fieldfilterv2';
import * as span from './span';

export type Int64 = string | number;

export enum ExportType {
  AppendData = 1,
  OverwriteData = 2,
}

export enum ItemStatus {
  Success = 0,
  Error = 1,
}

export enum SearchTraceType {
  LogID = 1,
  TraceID = 2,
  MessageID = 3,
  UID = 4,
}

export enum SearchTraceV3Type {
  LogID = 1,
  TraceID = 2,
}

export interface AnnotationEvaluator {
  evaluator_version_id: Int64;
  evaluator_name: string;
  evaluator_version: string;
}

export interface BatchGetTracesAdvanceInfoData {
  traces_advance_info: Array<TraceAdvanceInfo>;
}

export interface BatchGetTracesAdvanceInfoRequest {
  space_id: string;
  traces: Array<TraceQueryParams>;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
}

export interface BatchGetTracesAdvanceInfoResponse {
  data: BatchGetTracesAdvanceInfoData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface ChangeEvaluatorScoreRequest {
  workspace_id: Int64;
  evaluator_record_id: Int64;
  span_id: string;
  start_time: Int64;
  correction: annotation.Correction;
  Base?: base.Base;
}

export interface ChangeEvaluatorScoreResponse {
  annotation: annotation.Annotation;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface EvaluationSetConfig {
  /** 是否是新增评测集 */
  is_new_evaluation_set: boolean;
  /** 评测集id，新增评测集时可为空 */
  evaluation_set_id?: Int64;
  /** 评测集名称，选择已有评测集时可为空 */
  evaluation_set_name?: string;
  /** 评测集列数据schema */
  evaluation_set_schema?: eval_set.EvaluationSetSchema;
}

export interface ExportTracesToEvaluationRequest {
  space_id: string;
  spans: Array<string>;
  datasets: Array<string>;
  start_time?: string;
  end_time?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  /** 平台类型，不填默认是fornax */
  span_list_type?: flow_devops_fornaxob_common.SpanListType;
}

export interface ExportTracesToEvaluationResponse {
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface ExportTracesToEvaluationSetRequest {
  workspace_id: Int64;
  span_ids: Array<string>;
  evaluation_set: EvaluationSetConfig;
  start_time?: Int64;
  end_time?: Int64;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  /** 导入方式，不填默认为追加 */
  export_type?: ExportType;
  field_mappings?: Array<task.FieldMapping>;
}

export interface ExportTracesToEvaluationSetResponse {
  /** 成功导入的数量 */
  success_count?: number;
  /** 错误信息 */
  errors?: Array<datasetv2.ItemErrorGroup>;
  /** 评测集id */
  evaluation_set_id?: Int64;
  /** 评测集名称 */
  evaluation_set_name?: string;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

/** 后续需要迁移到 threadservice.FieldMeta */
export interface FieldMeta {
  /** 字段类型 */
  value_type: flow_devops_fornaxob_common.ValueType;
  /** 支持的操作类型 */
  filter_types: Array<flow_devops_fornaxob_fieldfilter.FieldFilterType>;
  /** 支持的可选项 */
  field_options?: flow_devops_fornaxob_fieldfilter.FieldOptions;
  /** 支持自定义填写 */
  support_customizable_option?: boolean;
  /** 支持的操作类型 v2 */
  filter_types_v2?: Array<flow_devops_fornaxob_fieldfilterv2.FieldFilterType>;
}

export interface GetSpanInfoRequest {
  /** space id */
  space_id: string;
  trace_id: string;
  span_id: Array<string>;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
}

export interface GetSpanInfoResponse {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface GetTraceByLogIDRequest {
  /** space id */
  space_id: string;
  log_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
}

export interface GetTraceByLogIDResponse {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface GetTraceData {
  spans: Array<span.Span>;
  traces_advance_info?: TraceAdvanceInfo;
  /** 是否是完整的tree数据，false：仅包含tree结构信息, true：包含tree结构信息和span的详细信息 */
  is_complete_trace?: boolean;
}

export interface GetTraceRequest {
  /** space id */
  space_id: string;
  trace_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  log_id?: string;
}

export interface GetTraceResponse {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface GetTracesMetaInfoData {
  /** 字段元信息 */
  field_metas: Record<string, FieldMeta>;
}

export interface GetTracesMetaInfoRequest {
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  /** 查询的 span 标签页类型，不填默认是 root span */
  span_list_type?: flow_devops_fornaxob_common.SpanListType;
  space_id?: Int64;
}

export interface GetTracesMetaInfoResponse {
  data?: GetTracesMetaInfoData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface IngestTracesRequest {
  /** span列表 */
  spans?: Array<Span>;
  Base?: base.Base;
}

export interface IngestTracesResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface Item {
  status: ItemStatus;
  fields?: Record<string, string>;
  /** 错误信息 */
  errors?: Array<ItemError>;
}

export interface ItemError {
  type?: datasetv2.ItemErrorType;
  /** 有错误的字段名，非必填 */
  field_names?: Array<string>;
}

export interface ListAllSpansRequest {
  /** space id */
  space_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
  /** 4: optional list<string> AppID (agw.js_conv="str" api.body = "app_id" go.tag="json:\"app_id\"")           // app id 字段作废 */
  filters?: Record<string, flow_devops_fornaxob_fieldfilter.FieldFilter>;
  /** Full-Text search 全文本搜索，会搜input、output、bot_version, os_version这四个字段。可以传入多个关键词，每个关键词之间是and关系 */
  full_text_search?: Array<string>;
  /** default 1000 */
  limit?: number;
  /** The ORDER BY is used to sort the records, in ascending or descending order.

        ORDER BY with multiple field: The system will sort the results by field1(order_by[0]), then by field2(order_by[1])

        ⚠️  WARNING: only support start_time

         Example:
        [
            {
                "field_name": "start_time",
                "order_type": "desc"
            }
        ] */
  order_by?: Array<flow_devops_fornaxob_common.OrderBy>;
  /** The page token is generated after the first query and passed in the subsequent queries
     to determine the starting point for the next page of results. */
  page_token?: string;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  /** 是否仅获取 root span */
  is_root_span_only?: boolean;
  query_and_or?: flow_devops_fornaxob_fieldfilterv2.Relation;
}

export interface ListAllSpansResponse {
  data: ListTracesData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface ListAnnotationEvaluatorsRequest {
  workspace_id: Int64;
  name?: string;
}

export interface ListAnnotationEvaluatorsResponse {
  evaluators: Array<AnnotationEvaluator>;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface ListLLMSpansRequest {
  /** space id */
  space_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
  /** 4: optional list<string> AppID (agw.js_conv="str" api.body = "app_id" go.tag="json:\"app_id\"")           // app id 字段作废 */
  filters?: Record<string, flow_devops_fornaxob_fieldfilter.FieldFilter>;
  /** Full-Text search 全文本搜索，会搜input、output、bot_version, os_version这四个字段。可以传入多个关键词，每个关键词之间是and关系 */
  full_text_search?: Array<string>;
  /** default 1000 */
  limit?: number;
  /** The ORDER BY is used to sort the records, in ascending or descending order.

        ORDER BY with multiple field: The system will sort the results by field1(order_by[0]), then by field2(order_by[1])

        ⚠️  WARNING: only support start_time

         Example:
        [
            {
                "field_name": "start_time",
                "order_type": "desc"
            }
        ] */
  order_by?: Array<flow_devops_fornaxob_common.OrderBy>;
  /** The page token is generated after the first query and passed in the subsequent queries
     to determine the starting point for the next page of results. */
  page_token?: string;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  query_and_or?: flow_devops_fornaxob_fieldfilterv2.Relation;
}

export interface ListLLMSpansResponse {
  data: ListTracesData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface ListTracesData {
  spans: Array<span.Span>;
  /** 下一页的分页token，前端拉取下一页数据时回传。 */
  next_page_token: string;
  /** 是否有更多数据 */
  has_more: boolean;
  /** Span的后处理信息 */
  spans_extra?: Record<string, SpanExtra>;
}

export interface ListTracesRequest {
  /** space id */
  space_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
  /** 4: optional list<string> AppID (agw.js_conv="str" api.body = "app_id" go.tag="json:\"app_id\"")           // app id 字段作废 */
  filters?: Record<string, flow_devops_fornaxob_fieldfilter.FieldFilter>;
  /** Full-Text search 全文本搜索，会搜input、output、bot_version, os_version这四个字段。可以传入多个关键词，每个关键词之间是and关系 */
  full_text_search?: Array<string>;
  /** default 1000 */
  limit?: number;
  /** The ORDER BY is used to sort the records, in ascending or descending order.

        ORDER BY with multiple field: The system will sort the results by field1(order_by[0]), then by field2(order_by[1])

        ⚠️  WARNING: only support start_time

         Example:
        [
            {
                "field_name": "start_time",
                "order_type": "desc"
            }
        ] */
  order_by?: Array<flow_devops_fornaxob_common.OrderBy>;
  /** The page token is generated after the first query and passed in the subsequent queries
     to determine the starting point for the next page of results. */
  page_token?: string;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
  /** 平台类型，不填默认是psm */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  query_and_or?: flow_devops_fornaxob_fieldfilterv2.Relation;
}

export interface ListTracesResponse {
  data: ListTracesData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface OtelIngestTracesRequest {
  /** pb/json bytes数据 */
  body: Blob;
  /** 数据类型,可选application/x-protobuf或application/json */
  ContentType: string;
  /** 数据编码类型,可选gzip */
  ContentEncoding: string;
  /** 空间ID */
  SpaceID: string;
  Base?: base.Base;
}

export interface OtelIngestTracesResponse {
  /** pb bytes数据 */
  body?: Blob;
  BaseResp?: base.BaseResp;
}

export interface PreviewExportTracesToEvaluationRequest {
  workspace_id: Int64;
  span_ids: Array<string>;
  evaluation_set: EvaluationSetConfig;
  start_time?: Int64;
  end_time?: Int64;
  /** 平台类型 */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  /** 导入方式，不填默认为追加 */
  export_type?: ExportType;
  field_mappings?: Array<task.FieldMapping>;
}

export interface PreviewExportTracesToEvaluationResponse {
  /** 预览数据 */
  items?: Array<Item>;
  /** 概要错误信息 */
  errors?: Array<datasetv2.ItemErrorGroup>;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface SearchTraceV3Request {
  /** space id */
  space_id: string;
  /** 当前支持logid/traceid */
  id: string;
  search_type: SearchTraceV3Type;
  target_env?: flow_devops_fornaxob_common.EnvType;
  /** 访问boe数据时指定泳道，非业务使用，其它接口的相同字段作用一样 */
  'x-boe-env'?: string;
  transferred?: boolean;
  /** SearchTraceV3Type为LogID时生效，可选往后查询多久，不传默认往后查一小时
hour */
  scan_span_in_hour?: string;
}

export interface SearchTraceV3Response {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface Span {
  /** Span 开始时间 */
  started_at_micros: string;
  /** LogID */
  logid?: string;
  /** SpanID */
  span_id: string;
  /** 父SpanID */
  parent_id: string;
  /** TraceID */
  trace_id: string;
  /** Span 耗时（已废弃，使用DurationMicros） */
  duration: number;
  /** PSM */
  psm?: string;
  /** CallType */
  call_type?: string;
  /** SpaceID */
  workspace_id: string;
  /** Span名称 */
  span_name: string;
  /** Span类型 */
  span_type: string;
  /** 方法名 */
  method: string;
  /** 状态码 */
  status_code: number;
  /** 输入 */
  input: string;
  /** 输出 */
  output: string;
  /** 对象存储信息 */
  object_storage?: string;
  /** string类型系统tag */
  system_tags_string?: Record<string, string>;
  /** long类型系统tag */
  system_tags_long?: Record<string, Int64>;
  /** double类型系统tag */
  system_tags_double?: Record<string, number>;
  /** string类型自定义tag */
  tags_string?: Record<string, string>;
  /** long类型自定义tag */
  tags_long?: Record<string, Int64>;
  /** double类型自定义tag */
  tags_double?: Record<string, number>;
  /** bool类型自定义tag(兼容老结构用) */
  tags_bool?: Record<string, boolean>;
  /** byte类型自定义tag(兼容老结构用) */
  tags_bytes?: Record<string, string>;
  /** Span 耗时(微秒) */
  duration_micros?: string;
}

export interface SpanExtra {
  datasets?: Array<string>;
}

export interface TokenCost {
  /** 输入消耗token数 */
  input: number;
  /** 输出消耗token数 */
  output: number;
}

export interface TraceAdvanceInfo {
  trace_id: string;
  tokens: TokenCost;
}

/** Trace查询参数 */
export interface TraceQueryParams {
  trace_id: string;
  start_time: string;
  end_time: string;
}
/* eslint-enable */
