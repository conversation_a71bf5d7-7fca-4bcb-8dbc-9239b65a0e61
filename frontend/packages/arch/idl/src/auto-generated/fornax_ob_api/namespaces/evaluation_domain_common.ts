/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 模型系列，特别指不同模型接入商 */
export enum ModelClass {
  Undefined = 0,
  /** gpt */
  GPT = 1,
  /** 字节 */
  SEED = 2,
  /** google */
  Gemini = 3,
  /** 亚马逊 */
  Claude = 4,
  /** 文心一言 */
  Ernie = 5,
  /** 百川 */
  Baichuan = 6,
  /** 阿里 */
  Qwen = 7,
  /** 智谱 */
  GML = 8,
  /** 深度求索 */
  DeepSeek = 9,
}

export enum Provider {
  /** GPT OpenAPI平台 */
  GPTOpenAPI = 1,
  /** 火山方舟 */
  Maas = 2,
  /** 暂时特指seed从bot_engine接入 */
  BotEngine = 3,
  /** merlin平台 */
  Merlin = 4,
  /** merlin-seed平台 */
  MerlinSeed = 5,
}

export enum Role {
  System = 1,
  User = 2,
  Assistant = 3,
  Tool = 4,
}

export enum TenantType {
  /** 字节 */
  ByteDance = 0,
  /** 懂车帝 */
  Dcar = 1,
}

export interface BaseInfo {
  created_by?: UserInfo;
  updated_by?: UserInfo;
  created_at?: Int64;
  updated_at?: Int64;
  deleted_at?: Int64;
}

export interface UserInfo {
  /** 姓名 */
  name?: string;
  /** 英文名称 */
  en_name?: string;
  /** 用户头像url */
  avatar_url?: string;
  /** 72 * 72 头像 */
  avatar_thumb?: string;
  /** 用户应用内唯一标识 */
  open_id?: string;
  /** 用户应用开发商内唯一标识 */
  union_id?: string;
  /** 企业标识 */
  tenant_key?: string;
  /** 用户在租户内的唯一标识 */
  user_id?: string;
  /** 用户邮箱 */
  email?: string;
  /** 租户 */
  tenant?: TenantType;
}
/* eslint-enable */
