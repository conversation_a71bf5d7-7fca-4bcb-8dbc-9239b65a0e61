/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_fornaxob_common from './flow_devops_fornaxob_common';
import * as flow_devops_fornaxob_fieldfilterv2 from './flow_devops_fornaxob_fieldfilterv2';
import * as query from './query';

export type Int64 = string | number;

export interface GetThreadMetaRequest {
  /** space id */
  space_id: string;
  thread_id: string;
  /** ms */
  start_time?: string;
  /** ms, end_at >= start_at */
  end_time?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
}

export interface GetThreadMetaResponse {
  data: ThreadMeta;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface GetThreadRequest {
  /** space id */
  space_id: string;
  thread_id: string;
  /** ms */
  start_time?: string;
  /** ms, end_at >= start_at */
  end_time?: string;
  /** default 20 max 200 */
  limit?: number;
  order_type?: flow_devops_fornaxob_common.OrderType;
  /** The page token is generated after the first query and passed in the subsequent queries
         to determine the starting point for the next page of results. */
  page_token?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
}

export interface GetThreadResponse {
  data: Thread;
  /** 下一页的分页token，前端拉取下一页数据时回传。 */
  next_page_token?: string;
  /** 是否有更多数据 */
  has_more?: boolean;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface GetThreadsConfigData {
  /** 字段元信息 */
  field_metas: Record<string, ThreadFieldMeta>;
}

export interface GetThreadsConfigRequest {
  /** space id */
  space_id: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
}

export interface GetThreadsConfigResponse {
  data?: GetThreadsConfigData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface ListThreadsRequest {
  /** space id */
  space_id: string;
  /** ms */
  start_time?: string;
  /** ms, end_at >= start_at */
  end_time?: string;
  filters?: flow_devops_fornaxob_fieldfilterv2.Filters;
  /** Full-Text search 全文本搜索，会搜input、output这2个字段 */
  full_text_search?: string;
  /** default 20 max 200 */
  limit?: number;
  /** The page token is generated after the first query and passed in the subsequent queries
     to determine the starting point for the next page of results. */
  page_token?: string;
  /** 平台类型，不填默认是fornax */
  platform_type?: flow_devops_fornaxob_common.PlatformType;
  target_env?: flow_devops_fornaxob_common.EnvType;
  /** 后端接口转发标志位，非业务或agw使用，其它接口的相同字段作用一样 */
  transferred?: boolean;
  /** 访问boe数据时指定泳道，非业务使用，其它接口的相同字段作用一样 */
  'x-boe-env'?: string;
}

export interface ListThreadsResponse {
  /** 这里仅返回Thread维度信息，不返回Query维度信息 */
  threads?: Array<Thread>;
  /** 下一页的分页token，前端拉取下一页数据时回传。 */
  next_page_token?: string;
  /** 是否有更多数据 */
  has_more?: boolean;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface Thread {
  queries?: Array<query.Query>;
  /** 第一条query开始时间 */
  start_time_min?: Int64;
  /** 最后一条query开始时间 */
  start_time_max?: Int64;
  /** 总耗时 */
  duration_sum?: Int64;
  /** 总token */
  tokens_sum?: Int64;
  /** 最后一条query的output */
  last_message?: string;
  /** 总query条数 */
  query_count?: Int64;
  /** 会话元信息 */
  thread_meta?: ThreadMeta;
  /** 聚合后的conversation_id */
  conversation_id?: string;
  /** 聚合后的thread_id */
  thread_id?: string;
  /** 初始化消息 */
  init_message?: string;
  /** 最后一条query的input */
  last_input?: string;
  /** 最后一条query的结束时间 */
  end_time_max?: Int64;
}

/** trace 中的 field 迁移和此处一样的结构后，需要下掉 FilterTypesV2 */
export interface ThreadFieldMeta {
  /** 字段类型 */
  value_type: flow_devops_fornaxob_common.ValueType;
  /** 支持的操作类型 */
  filter_types: Array<flow_devops_fornaxob_fieldfilterv2.FieldFilterType>;
  /** 支持的可选项 */
  field_options?: flow_devops_fornaxob_fieldfilterv2.FieldOptions;
  /** 支持自定义填写 */
  support_customizable_option?: boolean;
  /** 支持的操作类型 v2 */
  filter_types_v2?: Array<flow_devops_fornaxob_fieldfilterv2.FieldFilterType>;
}

export interface ThreadMeta {
  user_id?: string;
  /** 设备ID */
  device_id?: string;
  /** 设备平台 */
  device_platform?: string;
  /** 渠道 */
  channel?: string;
  /** 渠道 ID */
  connector_id?: string;
}
/* eslint-enable */
