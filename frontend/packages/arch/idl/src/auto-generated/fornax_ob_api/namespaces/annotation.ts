/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as fornaxob_domain_common from './fornaxob_domain_common';

export type Int64 = string | number;

export interface Annotation {
  id: string;
  annotation_type: string;
  key: string;
  value_type: string;
  value: string;
  status: string;
  auto_evaluate?: AutoEvaluate;
  /** 基础信息 */
  base_info?: fornaxob_domain_common.BaseInfo;
}

export interface AutoEvaluate {
  evaluator_version_id: string;
  evaluator_name: string;
  evaluator_version: string;
  evaluator_result?: EvaluatorResult;
  record_id: string;
  task_id: string;
}

export interface Correction {
  /** 人工校准得分 */
  score?: number;
  /** 人工校准理由 */
  explain?: string;
  /** 基础信息 */
  base_info?: fornaxob_domain_common.BaseInfo;
}

export interface EvaluatorResult {
  /** 打分 */
  score?: number;
  /** 校准打分 */
  correction?: Correction;
  /** 推理过程 */
  reasoning?: string;
}
/* eslint-enable */
