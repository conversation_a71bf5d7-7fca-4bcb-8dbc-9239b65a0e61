/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum FieldFilterType {
  /** Equals (=) */
  Eq = 1,
  /** != */
  NotEq = 2,
  /** > */
  Gt = 3,
  /** < */
  Lt = 4,
  /** Greater Than or Equal (>=) */
  Gte = 5,
  /** Less Than or Equal (<=) */
  Lte = 6,
  /** Like */
  Like = 7,
  /** In */
  In = 8,
  /** Not In */
  NotIn = 9,
  /** Is Null */
  IsNull = 10,
  /** Not Null */
  NotNull = 11,
  /** 在list中存在 */
  InList = 12,
  /** Not Like */
  NotLike = 13,
}

export enum FieldGroupSpecial {
  /** last_message_latency + last_message_start_timestamp - first_message_start_timestamp */
  Duration = 1,
  /** 最后一条消息的Output */
  LastMessage = 2,
}

export enum OrderType {
  Unknown = 1,
  Desc = 2,
}

export enum Relation {
  And = 1,
  Or = 2,
}

export interface FieldFilter {
  /** 支持下钻，例如 llm_info.InputTokens */
  field_name: string;
  field_filter_type: FieldFilterType;
  filter_value: FieldValue;
}

export interface FieldOptions {
  i32?: Array<number>;
  i64?: Array<string>;
  f64?: Array<number>;
  string?: Array<string>;
}

export interface FieldValue {
  v_bool?: boolean;
  v_i32?: number;
  v_i64?: string;
  v_f64?: number;
  v_string?: string;
  v_i32_list?: Array<number>;
  v_i64_list?: Array<string>;
  v_f64_list?: Array<number>;
  v_string_list?: Array<string>;
}

export interface Filters {
  field_filters?: Array<FieldFilter>;
  /** FieldFilters之间的关系：and/or，默认and */
  relation?: Relation;
  /** 子过滤器 */
  sub_filter?: Array<Filters>;
}
/* eslint-enable */
