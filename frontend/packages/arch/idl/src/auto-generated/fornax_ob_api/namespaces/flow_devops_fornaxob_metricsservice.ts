/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as metrics from './metrics';
import * as flow_devops_fornaxob_common from './flow_devops_fornaxob_common';

export type Int64 = string | number;

/** 运维视图的类型 */
export enum MetricsType {
  Unknown = 1,
  /** 每秒收到的查询次数，反映了查询负载情况 */
  GraphQueryQPS = 2,
  /** 查询成功率，反映了在当前负载下的稳定性 */
  GraphQuerySuccessRate = 3,
  /** 从收到查询到返回第一条Token的时延，反映了查询方可感知到的响应速度 */
  GraphFirstTokenLatency = 4,
  /** 从收到查询到返回全部Tokens的时延，反映了的整体性能 */
  GraphQueryLatency = 5,
  /** 每秒大模型的调用次数，反映了大模型的调用负载 */
  ModelQueryQPS = 6,
  /** 大模型的调用成功率，反映了大模型在当前负载下的稳定性 */
  ModelQuerySuccessRate = 7,
  /** 大模型的响应时长，反映了大模型的整体性能 */
  ModelQueryLatency = 8,
  /** 从收到查询到返回第一条Token的时延，反映了可感知到的响应速度 */
  ModelFirstTokenLatency = 9,
  /** 大模型每秒返回的Token数量 */
  ModelTokensCount = 10,
}

export interface Curve {
  tag_kvs: Record<string, string>;
  points: Array<Point>;
}

export interface GetMetricsData {
  curves: Array<Curve>;
  /** 降采样间隔 */
  interval: metrics.DownsampleInterval;
}

export interface GetMetricsRequest {
  space_id: string;
  /** s */
  start_time: string;
  /** s, end_at >= start_at */
  end_time: string;
  metrics_type: MetricsType;
  aggregate_type?: metrics.AggregateType;
  /** tag */
  tag_kvs?: Record<string, Array<string>>;
  /** 过滤项（除 client_name 以外其他的过滤项） */
  filters?: Record<string, Array<string>>;
  top_k?: number;
  /** client_name 会用于组成 metrics_name，所以不放在 tags/fileters 中，单独传 */
  client_names?: Array<string>;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  'x-boe-env'?: string;
}

export interface GetMetricsResponse {
  data: GetMetricsData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface GetTagsOptionsData {
  /** psm: graph_list */
  clients: Record<string, Array<TagValueGraph>>;
  /** 模型名称列表 */
  models: Array<string>;
}

export interface GetTagsOptionsRequest {
  space_id: string;
  /** s */
  start_time: string;
  /** s, end_at >= start_at */
  end_time: string;
  target_env?: flow_devops_fornaxob_common.EnvType;
  transferred?: boolean;
  /** 废弃 */
  client_names?: Array<string>;
  client_name?: string;
  app_id?: string;
  'x-boe-env'?: string;
}

export interface GetTagsOptionsResponse {
  data: GetTagsOptionsData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface Point {
  x: Int64;
  x_alias: string;
  y: number;
}

export interface TagValueGraph {
  id: Int64;
  name: string;
}
/* eslint-enable */
