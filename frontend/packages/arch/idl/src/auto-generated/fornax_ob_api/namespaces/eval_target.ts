/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum CozeBotInfoType {
  /** 草稿 bot */
  DraftBot = 1,
  /** 商店 bot */
  ProductBot = 2,
}

export enum EvalTargetRunStatus {
  Unknown = 0,
  Success = 1,
  Fail = 2,
}

export enum EvalTargetType {
  /** CozeBot */
  CozeBot = 1,
  /** Prompt */
  FornaxPrompt = 2,
}

export enum ModelPlatform {
  Unknown = 0,
  GPTOpenAPI = 1,
  MAAS = 2,
}

export enum SubmitStatus {
  Undefined = 0,
  /** 未提交 */
  UnSubmit = 1,
  /** 已提交 */
  Submitted = 2,
}
/* eslint-enable */
