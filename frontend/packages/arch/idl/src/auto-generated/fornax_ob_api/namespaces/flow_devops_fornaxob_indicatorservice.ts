/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AggregationType {
  /** 时间聚合类型 */
  Minute = 1,
  Hour = 2,
  Day = 3,
  Week = 4,
}

export enum AppType {
  /** 不区分应用类型 */
  All = 0,
  PSM = 1,
  CozeBot = 2,
  PTaaS = 3,
  Model = 4,
  FornaxSaas = 5,
  FornaxPrompt = 6,
}

/** 指标选项类型 */
export enum IndicatorOptionType {
  Undefined = 0,
  /** model唯一标识 */
  ModelIdentification = 1,
  /** prompt key */
  PromptKey = 2,
}

export enum InsightIndicatorType {
  /** 指标类型，持续补充 */
  InsightIndicatorsToken = 1,
  InsightIndicatorsInputToken = 2,
  InsightIndicatorsOutputToken = 3,
  InsightIndicatorsUserCount = 4,
  InsightIndicatorsTraceCount = 5,
  InsightIndicatorsMessageCount = 6,
  InsightIndicatorsQPS = 7,
  InsightIndicatorsQPSSuccess = 8,
  InsightIndicatorsQPSFail = 9,
  InsightIndicatorsSpanCount = 10,
  InsightIndicatorsRootDurationP50 = 11,
  InsightIndicatorsRootDurationP90 = 12,
  InsightIndicatorsRootDurationP99 = 13,
  InsightIndicatorsRootDurationAvg = 14,
  InsightIndicatorsRootDurationMax = 15,
  InsightIndicatorsRootDurationMin = 16,
  InsightIndicatorsLLMDurationP50 = 17,
  InsightIndicatorsLLMDurationP90 = 18,
  InsightIndicatorsLLMDurationP99 = 19,
  InsightIndicatorsLLMDurationAvg = 20,
  InsightIndicatorsLLMDurationMax = 21,
  InsightIndicatorsLLMDurationMin = 22,
  InsightIndicatorsLatencyFirstRespP50 = 23,
  InsightIndicatorsLatencyFirstRespP90 = 24,
  InsightIndicatorsLatencyFirstRespP99 = 25,
  InsightIndicatorsLatencyFirstRespAvg = 26,
  InsightIndicatorsLatencyFirstRespMax = 27,
  InsightIndicatorsLatencyFirstRespMin = 28,
  InsightIndicatorsSuccessRate = 29,
  InsightIndicatorsModelQPS = 30,
  InsightIndicatorsModelQPSSuccess = 31,
  InsightIndicatorsModelQPSFail = 32,
  InsightIndicatorsModelSuccessRate = 33,
  InsightIndicatorsModelTokenRateP50 = 34,
  InsightIndicatorsModelTokenRateP90 = 35,
  InsightIndicatorsModelTokenRateP99 = 36,
  InsightIndicatorsModelTokenRateAvg = 37,
  InsightIndicatorsModelTokenRateMax = 38,
  InsightIndicatorsModelTokenRateMin = 39,
  InsightIndicatorsPromptHubToken = 40,
  InsightIndicatorsPromptHubInputToken = 41,
  InsightIndicatorsPromptHubOutputToken = 42,
  InsightIndicatorsModelCount = 43,
}

export enum OverviewIndicatorType {
  /** 总览指标类型，持续补充 */
  OverviewIndicatorsUsegeCount = 1,
  OverviewIndicatorsModelErrorRate = 2,
  OverviewIndicatorsErrorRate = 3,
  OverviewIndicatorsModelDurationAvg = 4,
  OverviewIndicatorsModelToken = 5,
}

export interface GetIndicatorOptionsRequest {
  /** 需要返回的options，比如model_identification */
  options: Array<IndicatorOptionType>;
  /** 以下条件是或的关系
fornax space id */
  space_id: string;
}

export interface GetIndicatorOptionsResponse {
  /** 指标选项，map[option]option的分类 */
  indicator_options: Partial<Record<IndicatorOptionType, Array<string>>>;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface Indicator {
  /** 指标点，用于展示成折线图，废弃 */
  indicator_points: Array<IndicatorPoint>;
  /** 指标累加值，总数 */
  totals: string;
  /** 多个观测对象的指标点，用于展示成多行折线图 */
  multi_obs_objs_indicator_points?: Record<string, Array<IndicatorPoint>>;
}

export interface IndicatorPoint {
  /** 指标名称 */
  indicator_type: InsightIndicatorType;
  /** 指标的值，整数或小数 */
  indicator_value: string;
  /** 指标的时间戳，毫秒 */
  timestamp?: string;
}

export interface InsightIndicatorFilter {
  /** 应用类型 */
  app_type: AppType;
  /** psm列表 */
  psm?: Array<string>;
  /** coze bot id列表 */
  coze_bot_id?: Array<string>;
  /** prompt key+version列表，version为空代表不过滤version */
  prompt_key_version?: Array<PromptKeyVersion>;
  /** 是不是评测流量，false:不是评测流量，true:是评测流量，不填:不区分评测流量 */
  is_evaluation?: boolean;
  /** model唯一标识 列表，来自于QueryIndicatorOptions接口 */
  model_identification?: Array<string>;
}

export interface ObsObjMeta {
  /** 展示名称，比如bot_id对应的展示名称是bot_name，prompt和psm的展示名称是自己 */
  show_name?: string;
}

export interface OverviewIndicator {
  /** 总览指标名称 */
  overview_indicator_type: OverviewIndicatorType;
  /** 总览指标的值，整数或小数 */
  overview_indicator_value?: string;
  /** 上一周期的总览指标的值，总数或小数 */
  overview_indicator_last_value?: string;
}

export interface PromptKeyVersion {
  prompt_key: string;
  prompt_version?: Array<string>;
}

export interface QueryInsightIndicatorByOptionRequest {
  /** fornax space id */
  space_id: string;
  /** 指标类型 */
  indicator_type: InsightIndicatorType;
  /** 开始时间，当天0:00。时间戳，毫秒 */
  start_time: Int64;
  /** 结束时间，当天23:59。时间戳，毫秒 */
  end_time: Int64;
  /** 需要返回的options，比如prompt_key */
  options: IndicatorOptionType;
  /** 聚合类型，默认为天 */
  aggregation_type?: AggregationType;
  /** 应用类型 */
  app_type: AppType;
}

export interface QueryInsightIndicatorByOptionResponse {
  /** 多个观测对象的指标 */
  indicator_options_with_value?: Record<string, string>;
  /** 指标累加值，总数 */
  totals: string;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface QueryInsightIndicatorsRequest {
  /** fornax space id */
  space_id: string;
  /** 指标类型 */
  indicator_type: Array<InsightIndicatorType>;
  /** 开始时间，当天0:00。时间戳，毫秒 */
  start_time: Int64;
  /** 结束时间，当天23:59。时间戳，毫秒 */
  end_time: Int64;
  /** 条件过滤 */
  filter?: InsightIndicatorFilter;
  /** 聚合类型，默认为天 */
  aggregation_type?: AggregationType;
  /** 总览指标类型 */
  overview_indicator_type?: Array<OverviewIndicatorType>;
}

export interface QueryInsightIndicatorsResponse {
  /** 指标结果 */
  indicators: Partial<Record<InsightIndicatorType, Indicator>>;
  /** 观测对象meta信息，key对应MultiObsObjsIndicatorPoints的key */
  obs_objs_metas?: Record<string, ObsObjMeta>;
  /** 总览指标结果 */
  overview_indicators?: Partial<
    Record<OverviewIndicatorType, OverviewIndicator>
  >;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}
/* eslint-enable */
