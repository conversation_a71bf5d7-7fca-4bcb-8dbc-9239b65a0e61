/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';
import * as base from './base';

export type Int64 = string | number;

export interface AgentExecuteMeta {
  from_region?: string;
  callee?: string;
  cluster?: string;
  timeout?: Int64;
  method?: string;
}

export interface AgentExecuteProxyContent {
  agent_execute_meta?: AgentExecuteMeta;
  call_type?: flow_devops_evaluation_entity.CallbackType;
  /** use base64 encode and decode */
  payload?: string;
}

export interface AgentExecuteProxyReq {
  agent_execute_proxy_content?: AgentExecuteProxyContent;
  extra?: Record<string, string>;
  Base?: base.Base;
}

export interface AgentExecuteProxyRes {
  result_payload?: string;
  occour_error?: boolean;
  error_message?: string;
}

export interface AgentExecuteProxyResp {
  agent_execute_proxy_result?: AgentExecuteProxyRes;
  extra?: Record<string, string>;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
