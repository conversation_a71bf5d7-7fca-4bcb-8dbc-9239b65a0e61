/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ApplyStatus {
  Pending = 1,
  Approved = 2,
  Rejected = 3,
  Canceled = 4,
}

export enum BuiltinSpanFilterType {
  Undefined = 0,
  /** 业务含义(区别于 Trace Root)上的根节点 */
  BizRootSpan = 1,
  /** 模型 span */
  LLMSpan = 2,
  /** 非内置过滤 */
  CustomSpan = 3,
}

/** Schedule */
export enum ExecutePeriodic {
  Undefined = 0,
  Everyday = 1,
  Workdays = 2,
  Weekends = 3,
  Monday = 4,
  Tuesday = 5,
  Wednesday = 6,
  Thursday = 7,
  Friday = 8,
  Saturday = 9,
  Sunday = 10,
}

/** 比较算子 */
export enum FilterCmpOp {
  Undefined = 0,
  /** greater than */
  GT = 1,
  /** greater than or equal */
  GTE = 2,
  /** less than */
  LT = 3,
  /** less than or equal */
  LTE = 4,
  In = 5,
  NotIn = 6,
  /** equal */
  Eq = 7,
  /** not equal */
  NEq = 8,
  Like = 9,
  NotLike = 10,
  /** 有该 tag */
  Exists = 11,
  /** 没有该 tag */
  NotExists = 12,
}

/** 逻辑算子 */
export enum FilterLogicOp {
  Undefined = 0,
  Nop = 1,
  And = 2,
  Or = 3,
  Not = 4,
}

export enum ObjectType {
  Undefined = 0,
  CozeBot = 1,
  FornaxPSM = 2,
  Prompt = 3,
  Doubao = 4,
  FornaxAgent = 5,
}

export enum SecurityLevel {
  L4 = 4,
}

export enum Span2ColumnSourceType {
  PromptVariable = 1,
  SpanMeta = 2,
  SpanTag = 3,
}

export enum TaskStatusType {
  Undefined = 0,
  Unstarted = 1,
  Running = 2,
  Succeeded = 3,
  Failed = 4,
  Pending = 5,
  Stopped = 6,
}

/** Task */
export enum TaskType {
  Undefined = 0,
  /** Span 导入数据集 */
  SpanToDataset = 1,
  /** 在线评测 Span */
  SpanEval = 2,
  /** 批量执行自动化用例 */
  BatchExecAutoUseCase = 3,
  /** Deprecated: 统一使用 SpanToDataset
数据回流用于模型精调 */
  SpanToDatasetForModelSFT = 4,
}

export enum ValueKind {
  Undefined = 0,
  Bool = 1,
  Integer = 2,
  Double = 3,
  String = 4,
}

export interface AIObject {
  objectType?: ObjectType;
  cozeBot?: CozeBotObject;
  fornaxPSM?: FornaxPSMObject;
  doubao?: DoubaoObject;
}

/** AI Object */
export interface CozeBotObject {
  id?: string;
  name?: string;
  env?: string;
  version?: string;
  spaceID?: string;
  channel?: string;
  connectorID?: string;
}

export interface DoubaoObject {
  tenant?: string;
}

export interface FlowSpan {
  /** 原始的 BytedTrace 的 Span 信息 */
  raw?: RawSpan;
  spanType?: string;
  /** 由 fornax_sdk 提供的运行时元信息 */
  runtime?: RuntimeTags;
  /** 流量信息 */
  traffic?: TrafficTags;
  /** 其他非标 tag */
  metadata?: Record<string, string>;
  input?: string;
  output?: string;
  aiObject?: AIObject;
}

export interface FornaxPSMObject {
  spaceID?: string;
}

/** notice: 仅包含部分信息 */
export interface RawSpan {
  spanID?: string;
  spanName?: string;
  spanType?: string;
  traceID?: string;
  parentID?: string;
  logID?: string;
  method?: string;
  duration?: Int64;
  startTime?: Int64;
  statusCode?: number;
  serverEnv?: ServerInSpan;
}

export interface RuntimeTags {
  sdk?: string;
  language?: string;
}

export interface ServerInSpan {
  psm?: string;
  cluster?: string;
  dc?: string;
  env?: string;
  podName?: string;
  stage?: string;
  Region?: string;
}

/** Traffic */
export interface TrafficTags {
  userID?: string;
  deviceID?: string;
}
/* eslint-enable */
