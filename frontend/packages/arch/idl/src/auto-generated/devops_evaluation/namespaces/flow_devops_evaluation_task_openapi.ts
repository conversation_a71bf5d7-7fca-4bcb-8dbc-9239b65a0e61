/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_evaluation_openapi_common from './flow_devops_evaluation_openapi_common';
import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';

export type Int64 = string | number;

export interface CancelTaskData {}

export interface CancelTasksRequest {
  case_id: string;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  task_ids?: Array<string>;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
}

export interface CancelTasksResponse {
  code: number;
  msg: string;
  data?: CancelTaskData;
  BaseResp?: base.BaseResp;
}

export interface CreateCustomMetricsDataPointsData {}

export interface CreateCustomMetricsDataPointsRequest {
  case_id: string;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  task_id: string;
  data_points?: Array<MetricsDataPoint>;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  Base?: base.Base;
}

export interface CreateCustomMetricsDataPointsResponse {
  code: number;
  msg: string;
  data?: CreateCustomMetricsDataPointsData;
  BaseResp?: base.BaseResp;
}

export interface CreateEvalTaskData {
  task_id: string;
}

export interface CreateEvalTaskRequest {
  case_id: string;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  Base?: base.Base;
}

export interface CreateEvalTaskResponse {
  code: number;
  msg: string;
  data?: CreateEvalTaskData;
  BaseResp?: base.BaseResp;
}

export interface DataPointAttributes {
  name: string;
  granularity: string;
  data_type: string;
  value_type?: string;
}

export interface DataPointValue {
  value: string;
  row_id?: string;
  row_group_id?: string;
}

export interface GetAccountInfoData {
  app_id: string;
  space_id: string;
  account_id: string;
}

export interface GetAccountInfoRequest {
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  Base?: base.Base;
}

export interface GetAccountInfoResponse {
  code: number;
  msg: string;
  data?: GetAccountInfoData;
  BaseResp?: base.BaseResp;
}

export interface ListTaskRowGroupsData {
  has_more: boolean;
  page_token: string;
  dataset_ready: boolean;
  row_groups?: Array<flow_devops_evaluation_openapi_common.RowGroup>;
}

export interface ListTaskRowGroupsRequest {
  case_id: string;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  task_id: string;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  page_token?: string;
  page_size?: Int64;
  Base?: base.Base;
}

export interface ListTaskRowGroupsResponse {
  code: number;
  msg: string;
  data?: ListTaskRowGroupsData;
  BaseResp?: base.BaseResp;
}

export interface MetricsDataPoint {
  attributes?: DataPointAttributes;
  data?: DataPointValue;
}

export interface RowGroupResult {
  row_group_id: string;
  eval_start_time?: Int64;
  eval_end_time?: Int64;
  row_results?: Array<RowResult>;
}

export interface RowResult {
  row_id: string;
  eval_start_time?: Int64;
  eval_end_time?: Int64;
  usage?: Usage;
  /** deprecated */
  agent_output?: flow_devops_evaluation_callback_common.Content;
  output?: flow_devops_evaluation_openapi_common.OpenContent;
}

export interface UpdateAgentOutputData {}

export interface UpdateAgentOutputRequest {
  case_id: string;
  /** 请求方的应用ID */
  'FlowDevops-Agw-OpenAPI-AppId': string;
  /** 请求方的空间ID */
  'FlowDevops-Agw-OpenAPI-SpaceId': string;
  task_id: string;
  /** 请求方的服务账号ID */
  'FlowDevops-Agw-OpenAPI-AccountId': string;
  row_group_results?: Array<RowGroupResult>;
  Base?: base.Base;
}

export interface UpdateAgentOutputResponse {
  code: number;
  msg: string;
  data?: UpdateAgentOutputData;
  BaseResp?: base.BaseResp;
}

export interface Usage {
  input_token_usage?: Int64;
  ouput_token_usage?: Int64;
}
/* eslint-enable */
