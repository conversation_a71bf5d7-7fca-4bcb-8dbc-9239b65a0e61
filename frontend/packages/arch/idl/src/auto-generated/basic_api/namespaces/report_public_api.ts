/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as report_common from './report_common';

export type Int64 = string | number;

export interface GetReportMetaRequest {}

export interface GetReportMetaResponse {
  code: number;
  message: string;
  data?: ReportMetaData;
}

export interface ReportDetail {
  description?: string;
  /** uri */
  images?: Array<string>;
  reason_codes?: Array<number>;
}

export interface ReportMetaData {
  report_reasons?: Array<ReportReason>;
}

export interface ReportReason {
  reason_code: number;
  starling_key: string;
}

export interface ReportSubmitRequest {
  object_type?: report_common.ReportObjectType;
  object_id?: string;
  detail?: ReportDetail;
  Cookie?: string;
}

export interface ReportSubmitResponse {
  code: number;
  message: string;
}
/* eslint-enable */
