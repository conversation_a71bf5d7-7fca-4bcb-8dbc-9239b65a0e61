/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as user_common from './user_common';

export type Int64 = string | number;

export interface GetUserProfileData {
  /** 名字（唯一） */
  user_name?: string;
  /** 昵称 */
  name?: string;
  avatar?: string;
  user_id?: string;
  /** 签名 */
  signature?: string;
  /** 是否有正在审核的字段 */
  audit_status?: user_common.PassportAuditStatus;
  share_id?: string;
  /** 审核的细节 */
  audit_detail?: user_common.AuditDetail;
  /** 用户标签 */
  user_label?: user_common.UserLabel;
}

export interface GetUserProfileRequest {
  user_id?: string;
  bid?: string;
  Cookie?: string;
}

export interface GetUserProfileResponse {
  code: number;
  message: string;
  data?: GetUserProfileData;
}

export interface UpdateUserProfileCheckRequest {
  user_unique_name?: string;
}

export interface UpdateUserProfileCheckResponse {
  code: number;
  message: string;
}

export interface UpdateUserProfileRequest {
  user_unique_name?: string;
  name?: string;
  avatar?: string;
  signature?: string;
  Cookie?: string;
}

export interface UpdateUserProfileResponse {
  code: number;
  message: string;
}
/* eslint-enable */
