/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as shortcut_command from './shortcut_command';

export type Int64 = string | number;

export enum ActionKey {
  /** 复制 */
  Copy = 1,
  /** 删除 */
  Delete = 2,
  /** 启用/禁用 */
  EnableSwitch = 3,
  /** 编辑 */
  Edit = 4,
  /** 切换成funcflow */
  SwitchToFuncflow = 8,
  /** 切换成chatflow */
  SwitchToChatflow = 9,
  /** 跨空间复制 */
  CrossSpaceCopy = 10,
}

export enum AgentBizUniversalMethod {
  Unknown = 0,
  Get = 1,
  Post = 2,
}

export enum AgentTriggerAPIType {
  Unknown = 0,
  Workflow = 1,
  Chat = 2,
  ChatV3 = 3,
}

export enum AIGenerateCardStatus {
  /** AI Schema 生成且格式符合预期 */
  Init = 0,
  /** AI Schema 转 Card 成功 */
  Success = 1,
  /** 格式与预期不符 */
  Invalid = 2,
  /** 格式符合预期但转卡片失败 */
  Failed = 3,
  /** 手动终止，部分成功 */
  Interrupt = 4,
}

export enum AISchemaTransferStatus {
  Success = 1,
  Failed = 2,
  Interrupt = 3,
}

export enum AuditStatus {
  /** 链接内容不通过 */
  LinkRejected = -60,
  /** 视频内容不通过 */
  VideoRejected = -50,
  /** 音频内容不通过 */
  AudioRejected = -40,
  /** 图片内容不通过 */
  ImageRejected = -30,
  /** 文本内容不通过 */
  TextRejected = -20,
  /** 审核不通过(原因见详情) */
  Rejected = -1,
  /** 审核通过 */
  Approved = 1,
  /** 待审核 */
  Pending = 100,
}

export enum CardCategory {
  /** 模版卡片 */
  Template = 1,
  /** 自定义卡片 */
  Custom = 2,
}

export enum CardDisplayType {
  /** 基础 */
  Basic = 1,
  /** 列表 */
  List = 2,
  /** 自定义卡片 */
  Custom = 3,
  /** 横向列表 */
  Slide = 4,
}

export enum CardStatus {
  Draft = 0,
  Published = 1,
  UnPublish = 2,
}

export enum CardThumbnailStatus {
  /** 未定义 */
  Default = 0,
  /** 空卡片 */
  Empty = 1,
  /** 待生成 */
  NeedGenerate = 2,
  /** 生成中 */
  Generating = 3,
  /** 生成失败 */
  Failed = 4,
  /** 生成成功 */
  Success = 5,
  /** 已发布卡片编辑过待重新发布 */
  NeedPublish = 6,
}

export enum ChannelType {
  Default = 0,
  Doubao = 100,
  CiCi = 101,
  Feishu = 200,
  WhatsApp = 300,
  Discord = 301,
  Twitter = 302,
  Telegram = 303,
  Messenger = 304,
  LINE = 305,
  Instagram = 306,
  Slack = 307,
  Reddit = 308,
}

export enum CopyStatus {
  Succeed = 0,
  Fail = 1,
}

export enum DocumentType {
  /** 上传中 */
  Processing = 0,
  /** 生效 */
  Enable = 1,
  /** 失效 */
  Disable = 2,
  /** 删除 */
  Deleted = 3,
  /** 失败 */
  Failed = 9,
}

export enum PublishStatus {
  /** 未发布 */
  UnPublished = 1,
  /** 已发布 */
  Published = 2,
}

export enum Scene {
  /** 卡片，实际对应文件类型是图片 */
  Card = 0,
  /** agent，实际对应文件类型是PDF */
  Agent = 1,
  /** 音频 */
  Audio = 2,
  /** 缩略图 */
  Thumbnail = 3,
  /** 视频 */
  Video = 4,
}

export enum SliceStatus {
  /** 未向量化 */
  PendingVectoring = 0,
  /** 已向量化 */
  FinishVectoring = 1,
  /** 禁用 */
  Deactive = 9,
}

export enum SortField {
  Default = 0,
  /** 更新时间 */
  UpdateTime = 1,
  /** 发布时间 */
  PublishTime = 2,
}

export enum SortOrderType {
  /** 降序 */
  Desc = 0,
  /** 升序 */
  Asc = 1,
}

export enum Stage {
  Copy = 1,
  Replace = 2,
  RollBack = 3,
}

export enum TccServiceID {
  Card = 0,
  CardBuilder = 1,
}

/** -------------- 运营平台接口 end -------------- */
export enum ToolType {
  Plugin = 1,
  Workflow = 2,
  Widget = 3,
}

export interface AgentBizUniversalData {
  body?: string;
}

export interface AgentBizUniversalRequest {
  /** API 路径，需要包含路径参数 */
  api_path: string;
  /** 请求方式 */
  method: AgentBizUniversalMethod;
  /** query 参数，格式 "a=1&b=2" */
  query?: string;
  /** body 参数 */
  body?: string;
  'Content-Type': string;
  Base?: base.Base;
}

export interface AgentBizUniversalResponse {
  data?: AgentBizUniversalData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface AgentConfig {
  /** 快捷指令绑定配置 */
  command_relation_map?: Record<string, string>;
}

export interface AgentInfo {
  /** agentID */
  agent_id: string;
  /** 对应平台的botID */
  bot_id?: string;
  /** 名称 */
  name?: string;
  /** 额外信息 */
  description?: string;
  /** 创建时间戳 */
  create_time?: Int64;
  /** 更新时间戳 */
  update_time?: Int64;
  /** Bot版本号 */
  bot_version?: string;
  /** 快捷指令配置列表 */
  shortcut_command_list?: Array<shortcut_command.ShortcutCommand>;
  /** agent通用配置 */
  agent_config?: AgentConfig;
}

export interface AgentInstanceInfo {
  /** agentID */
  agent_id: string;
  /** 对应平台的botID */
  bot_id?: string;
  /** 实例ID */
  instance_id: string;
  /** 会话ID */
  session_id?: string;
  /** 名称 */
  name?: string;
  /** 创建者ID */
  creator_id?: string;
  /** 内容（论文tos地址 or 调研报告内容） */
  content?: string;
  /** 额外信息 */
  init_param_info?: Record<string, string>;
  /** 创建时间戳 */
  create_time?: Int64;
  /** 更新时间戳 */
  update_time?: Int64;
}

export interface AgentTriggerCozeAPIData {
  content?: string;
}

export interface AgentTriggerCozeAPIRequest {
  /** 场景，用以获取对应的coze token */
  scene: string;
  /** 类型，需要调用哪个流式的api  // 目前只支持 workflow */
  api_type: AgentTriggerAPIType;
  /** 鉴权token */
  token: string;
  /** api body, json string */
  body?: string;
  Base?: base.Base;
}

export interface AgentTriggerCozeAPIResponse {
  data?: AgentTriggerCozeAPIData;
  BaseResp?: base.BaseResp;
}

export interface AgentTriggerOpenAPIStreamRequest {
  /** 场景，用以获取对应的coze token */
  scene: string;
  /** 类型，需要调用哪个流式的api */
  api_type: AgentTriggerAPIType;
  /** 鉴权token */
  token: string;
  /** api body, json string */
  body?: string;
  Base?: base.Base;
}

export interface AgentTriggerOpenAPIStreamResponse {
  /** 具体内容 */
  message?: string;
  finished?: boolean;
  BaseResp?: base.BaseResp;
}

export interface AgentUploadFileData {
  /** URI */
  name?: string;
  /** 尺寸 */
  size?: Int64;
  /** 文件类型 */
  content_type?: string;
  /** URI */
  uri?: string;
  /** 缩略图 URI */
  thumbnail_uri?: string;
  /** 上传到 Coze 侧的 URI */
  coze_uri?: string;
  tiny_uri?: string;
}

export interface AgentUploadFileRequest {
  /** multipart/form-data 格式，二进制中只包含一个文件，且其 name="resource" */
  data?: Blob;
  'Content-Type'?: string;
  Base?: base.Base;
}

export interface AgentUploadFileResponse {
  data?: AgentUploadFileData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface AnalyseAgentReferenceRequest {
  /** 参考资料 */
  references?: Array<ReferenceData>;
  /** 快速模式：加并发且设置超时时间，提高返回速度 */
  fast_mode?: boolean;
  Base?: base.Base;
}

export interface AnalyseAgentReferenceResponse {
  /** 解析过滤后剩余的数据 */
  data?: Array<ReferenceData>;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface AnalyzingPaperRequest {
  /** 需要和平台确定，是否 agent 中还需要SpaceID 的概念。 */
  'space-id'?: string;
  uri: string;
  name: string;
  base?: base.Base;
}

export interface AnalyzingPaperResponse {
  /** 论文ID ，agent 侧的唯一ID */
  paper_id: string;
  /** 数据集ID ， 用于轮训解析进度 */
  dataset_id: string;
  /** 文档ID ，  用于轮训解析进度 */
  document_id: string;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface AsyncGenThumbnailRequest {
  messages?: Array<GenThumbnailMessage>;
  'space-id'?: string;
  Base?: base.Base;
}

export interface AsyncGenThumbnailResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface CardBasicInfo {
  card_id?: string;
  /** 草稿ID */
  draft_card_id?: string;
  /** 卡片名称 */
  name?: string;
  /** 作者ID */
  creator_id?: string;
  /** 卡片类型 */
  card_category?: CardCategory;
  /** 卡片展示类型 */
  card_display_type?: CardDisplayType;
  /** 卡片版本号 */
  version_num?: Int64;
  /** 卡片版本名称 */
  version_name?: string;
  /** 发布描述 */
  description?: string;
  /** 主缩略图 */
  thumbnail?: string;
  /** 毫秒级时间戳 */
  update_time?: Int64;
  /** 属性信息 */
  properties?: string;
  properties_hash?: string;
  /** 模版卡片才会有 */
  basic_card_id?: string;
  /** 模版卡片才会有 */
  list_card_id?: string;
  /** 发布状态 */
  card_status?: CardStatus;
  /** 发布时间 */
  publish_time?: Int64;
  /** 卡片缩略图生成状态 */
  thumb_status?: CardThumbnailStatus;
  /** 卡片创建者名称 */
  creator_name?: string;
  /** 卡片创建者头像' */
  creator_avatar?: string;
  /** 审核状态 */
  audit_status?: AuditStatus;
  /** 审核不通过详情 */
  audit_failure_details?: Array<number>;
  /** 最新的审核通过的字符串版本号 */
  latest_approved_version_name?: string;
  /** 最新的审核通过的数字版本号 */
  latest_approved_version_code?: string;
  /** 空间 ID */
  space_id?: string;
}

export interface CardChannelInfo {
  channel?: ChannelType;
  thumbnail?: string;
}

export interface CardChannelsThumbnail {
  imgUrl?: string;
  imgUri?: string;
  /** 线上兼容，用string */
  channel?: string;
  status?: number;
}

export interface CardCondition {
  id?: string;
  space_id?: string;
  name?: string;
  creator_id?: string;
  page?: number;
  size?: number;
  status?: CardStatus;
  create_time_range?: TimeRange;
  channel?: ChannelType;
}

export interface CardCopyResult {
  /** 复制结果状态 0 成功 1 失败 */
  CopyStatus?: CopyStatus;
  /** 失败原因 */
  FailReason?: string;
  /** 复制后卡片ID */
  CardID?: Int64;
}

export interface CardGenerateRecordInfo {
  id?: string;
  card_id?: string;
  /** 保存时间 */
  create_time?: string;
  /** 用户输入 */
  user_description?: string;
  /** AI 输出信息 */
  ai_schema?: string;
  /** 各渠道卡片信息 */
  channel_info?: Array<CardChannelInfo>;
}

export interface CardHistoryData {
  total: Int64;
  history_infos: Array<PublishHistoryInfo>;
}

export interface CardInfoData {
  card_id?: string;
  /** 草稿ID */
  draft_card_id?: string;
  /** 卡片名称 */
  name?: string;
  creator_id?: string;
  /** 卡片类型 */
  card_category?: CardCategory;
  /** 卡片展示类型 */
  card_display_type?: CardDisplayType;
  /** 卡片版本号 */
  version_num?: string;
  /** 卡片版本名称 */
  version_name?: string;
  /** 发布描述 */
  description?: string;
  /** 毫秒级时间戳 */
  update_time?: string;
  properties_hash?: string;
  card_meta_info?: Array<CardMetaInfo>;
  card_status?: CardStatus;
  properties?: string;
  /** 卡片缩略图生成状态 */
  thumb_status?: CardThumbnailStatus;
  /** 审核状态 */
  audit_status?: AuditStatus;
  /** 审核不通过详情 */
  audit_failure_details?: Array<number>;
  card_basic_info?: CardBasicInfo;
}

export interface CardInfoForOP {
  id?: string;
  name?: string;
  space_id?: string;
  create_time?: Int64;
  update_time?: Int64;
  thumbnail?: string;
  dsl_map?: Record<number, string>;
  status?: number;
  creator_id?: string;
}

export interface CardInstanceInfo {
  /** 卡片实例ID */
  InstanceID?: Int64;
  CardBody?: string;
}

export interface CardMetaInfo {
  dsl_content?: string;
  lynx_url?: string;
  thumbnail?: string;
  channel_type?: ChannelType;
  version_num?: string;
  /** 审核状态 */
  audit_status?: AuditStatus;
  /** 审核不通过详情 */
  audit_failure_details?: Array<number>;
}

export interface CardMetaPublishData {
  card_id: string;
  version_num: string;
  /** 审核状态 */
  audit_status: number;
  /** 审核不通过详情 */
  audit_failure_details?: Array<number>;
}

export interface CardMetaPublishRequest {
  /** 作者 */
  creator_id: Int64;
  /** 草稿卡片ID */
  draft_card_id: string;
  /** 卡片ID */
  card_id: string;
  description?: string;
  version_name: string;
  thumbnail_info?: Array<ThumbnailInfo>;
  'use-builder-psm'?: string;
  base?: base.Base;
}

export interface CardMetaPublishResponse {
  data?: CardMetaPublishData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CardSuggestionData {
  business_id?: string;
  unique_id?: string;
  card_id?: string;
  version_num?: string;
  dsl_content?: string;
  image_theme?: string;
  image_desc?: string;
  image_weight?: string;
}

export interface CardTemplateInfo {
  /** 模版ID */
  template_id: string;
  /** 创建者ID */
  creator_id: string;
  /** 模版名称 */
  name: string;
  /** 渠道 */
  channel_type: ChannelType;
  /** 缩略图uri */
  thumbnail: string;
  /** dsl */
  dsl_content: string;
  category: CardCategory;
  create_time: Int64;
}

export interface CardThumbnailInfo {
  CardID?: Int64;
  ThumbStatus?: CardThumbnailStatus;
  ThumbnailUri?: string;
}

export interface CardUpdateInfo {
  CardID?: Int64;
  LatestVersion?: Int64;
  NeedUpdate?: boolean;
}

export interface CardUploadFileRequest {
  /** 文件名 */
  file_name?: string;
  /** 文件数据 */
  data?: Blob;
  /** MIME type */
  'Content-Type'?: string;
  /** 场景 */
  scene?: Scene;
  base?: base.Base;
}

export interface CardUploadFileResponse {
  data?: UploadFileData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CardVersion {
  CardID?: Int64;
  VersionNum?: Int64;
}

export interface CheckCardLengthRequest {
  /** 草稿id */
  DraftID: string;
  'use-builder-psm'?: string;
  Base?: base.Base;
}

export interface CheckCardLengthResponse {
  is_over_length?: Partial<Record<ChannelType, boolean>>;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface CreateAgentInstanceRequest {
  /** agentID, 对应平台botID */
  agent_id: string;
  /** 名称 (不一定有，可能要自动生成一个名称) */
  name: string;
  /** 各个agent特有参数，如论文agent需要的datasetID */
  init_param?: Record<string, string>;
  /** agent内容，如论文大师是一个tos的uri */
  content?: string;
  /** 幂等键（如果传了则会幂等处理） */
  idem_key?: string;
  base?: base.Base;
}

export interface CreateAgentInstanceResponse {
  /** 实例ID ，agent 自己生成 */
  instance_id: string;
  /** 会话ID ，关联会话ID */
  session_id: string;
  /** 名称 */
  name: string;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface DeleteAgentInstanceRequest {
  /** agentID */
  agent_id: string;
  /** 实例ID */
  instance_id: string;
  base?: base.Base;
}

export interface DeleteAgentInstanceResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface DeleteCardTemplateData {
  success: boolean;
}

export interface DeleteCardTemplateRequest {
  /** 创建者ID */
  creator_id: string;
  /** 模版ID */
  template_id: string;
  /** 空间ID */
  'space-id': string;
  base?: base.Base;
}

export interface DeleteCardTemplateResponse {
  data?: DeleteCardTemplateData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

/** 展示用，实现方提供展示信息 */
export interface DisplayResourceInfo {
  /** 资源id */
  ResID?: Int64;
  /** 资源描述 */
  Desc?: string;
  /** 资源Icon，完整url */
  Icon?: string;
  /** 资源状态，各类型资源自身定义 */
  BizResStatus?: number;
  /** 是否开启多人编辑 */
  CollaborationEnable?: boolean;
  /** 业务携带的扩展信息，以res_type区分，每个res_type定义的schema和含义不一样，使用前需要判断res_type */
  BizExtend?: Record<string, string>;
  /** 不同类型的不同操作按钮，由资源实现方和前端约定。返回则展示，要隐藏某个按钮，则不要返回； */
  Actions?: Array<ResourceAction>;
  /** 是否禁止进详情页 */
  DetailDisable?: boolean;
  /** 资源名称 */
  Name?: string;
  /** 资源发布状态，1-未发布，2-已发布 */
  PublishStatus?: PublishStatus;
  /** 最近编辑时间, unix秒级时间戳 */
  EditTime?: Int64;
}

/** 上线兼容，不打tag */
export interface GenCardChannelsThumbnailData {
  Thumbnails?: Array<CardChannelsThumbnail>;
}

export interface GenCardChannelsThumbnailRequest {
  DraftID?: string;
  Channels?: Array<ChannelType>;
  CardID?: string;
  'use-builder-psm'?: string;
  Base?: base.Base;
}

export interface GenCardChannelsThumbnailResponse {
  data?: GenCardChannelsThumbnailData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GeneralGetTccConfigData {
  /** 配置值，一个json-string */
  config?: string;
}

export interface GeneralGetTccConfigRequest {
  /** tcc key */
  key: string;
  /** tcc 空间名 */
  space: string;
  /** tcc 服务id */
  tcc_service_id: TccServiceID;
  /** 值处理选项 */
  value_option?: ValueOption;
  Base?: base.Base;
}

export interface GeneralGetTccConfigResponse {
  data?: GeneralGetTccConfigData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GenerateCardData {
  record_id: string;
  card_id: string;
  ai_schema?: string;
}

export interface GenerateCardRequest {
  'space-id': string;
  card_id: string;
  description: string;
  UserID: Int64;
  language?: string;
  base?: base.Base;
}

export interface GenerateCardResponse {
  data?: GenerateCardData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GenerateCardsThumbnailRequest {
  'space-id': string;
  card_ids?: Array<string>;
  'use-builder-psm'?: string;
  base?: base.Base;
}

export interface GenerateCardsThumbnailResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GenerateCardStreamRequest {
  'space-id': string;
  card_id: string;
  description: string;
  UserID: Int64;
  language?: string;
  user_data?: string;
  /** prompt版本，对应一个task，二期需求加入，2为二期，以此类推 */
  prompt_version?: string;
  reference?: string;
  base?: base.Base;
}

export interface GenerateCardStreamResponse {
  message?: string;
  finished?: boolean;
  record_id?: string;
  BaseResp?: base.BaseResp;
}

export interface GenerateCardSuggestionData {
  card_suggestion_data_list?: Array<CardSuggestionData>;
}

export interface GenerateCardSuggestionRequest {
  description: string;
  candidates_number: Int64;
  base?: base.Base;
}

export interface GenerateCardSuggestionResponse {
  data?: GenerateCardSuggestionData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GenerateCardVariablesData {
  card_variables?: string;
}

export interface GenerateCardVariablesRequest {
  'space-id': string;
  card_id: string;
  language: string;
  desc: string;
  /** 渠道类型 */
  channel_type: ChannelType;
  base?: base.Base;
}

export interface GenerateCardVariablesResponse {
  data?: GenerateCardVariablesData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GenThumbnailMessage {
  card_id?: string;
  channel?: ChannelType;
}

export interface GetAgentAccompaniedImageData {
  image_urls?: Array<string>;
}

export interface GetAgentAccompaniedImageForPluginData {
  image_url?: string;
}

export interface GetAgentAccompaniedImageForPluginRequest {
  Authorization: string;
  origin_url?: string;
  Base?: base.Base;
}

export interface GetAgentAccompaniedImageForPluginResponse {
  data?: GetAgentAccompaniedImageForPluginData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetAgentAccompaniedImageRequest {
  /** 调用场景 */
  scene?: string;
  /** 调用workflow需要传的参数（jsonString) */
  param?: string;
  Base?: base.Base;
}

export interface GetAgentAccompaniedImageResponse {
  data?: GetAgentAccompaniedImageData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetAgentDetailRequest {
  /** agentID */
  agent_id: string;
  base?: base.Base;
}

export interface GetAgentDetailResponse {
  /** 实例信息 */
  agent?: AgentInfo;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetAgentInstanceDetailRequest {
  /** agentID */
  agent_id: string;
  /** 实例ID */
  instance_id: string;
  base?: base.Base;
}

export interface GetAgentInstanceDetailResponse {
  /** 实例信息 */
  instance?: AgentInstanceInfo;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetAgentReferenceRequest {
  reference_url?: Array<string>;
  Base?: base.Base;
}

export interface GetAgentReferenceResponse {
  data?: Array<ReferenceData>;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetAgentUserSettingsData {
  setting_map?: Record<string, UserSetting>;
}

export interface GetAgentUserSettingsRequest {
  /** agentID */
  agent_id: string;
  /** 用户配置key */
  setting_keys?: Array<string>;
  Base?: base.Base;
}

export interface GetAgentUserSettingsResponse {
  data?: GetAgentUserSettingsData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetCardInfoRequest {
  card_id: string;
  /** 不指定渠道，就返回所有渠道 */
  channel_type?: ChannelType;
  /** 如果不指定版本，那么就返回最新的版本 */
  version_num?: string;
  creator_id?: string;
  /** 空间ID */
  'space-id'?: string;
  base?: base.Base;
}

export interface GetCardInfoResponse {
  data?: CardInfoData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetCardPluginRelationData {
  plugin_ids?: Array<Int64>;
}

export interface GetCardTccInfoRequest {
  Base?: base.Base;
}

export interface GetCardTccInfoResponse {
  card_builder_package_detail: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetCardUserSettingsData {
  user_settings?: Array<UserSetting>;
}

export interface GetCardUserSettingsRequest {
  /** 用户id */
  creator_id?: string;
  /** 用户配置key */
  setting_keys?: Array<string>;
  Base?: base.Base;
}

export interface GetCardUserSettingsResponse {
  /** 用户id */
  data?: GetCardUserSettingsData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface GetDefaultPromptData {
  title: string;
  desc: string;
  prompt: string;
  success: boolean;
  prompts?: Array<string>;
}

export interface GetDefaultPromptRequest {
  'space-id': string;
  UserID: Int64;
  tool_type: ToolType;
  plugin_id?: string;
  api_id?: string;
  workflow_id?: string;
  language?: string;
  base?: base.Base;
}

export interface GetDefaultPromptResponse {
  data?: GetDefaultPromptData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetFileInfoData {
  url?: string;
  data?: string;
}

export interface GetFileInfoRequest {
  file_name?: string;
  /** 优先使用url，如果url失败，在看fileName */
  file_url?: string;
  need_data?: boolean;
  'x-jwt-token'?: string;
  Base?: base.Base;
}

export interface GetFileInfoResponse {
  data?: GetFileInfoData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetImageInfoData {
  url?: string;
  data?: string;
  content_type?: string;
}

export interface GetImageInfoRequest {
  file_name: string;
  need_data: boolean;
  'x-jwt-token'?: string;
  Base?: base.Base;
}

export interface GetImageInfoResponse {
  data?: GetImageInfoData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetJwtTokenData {
  token?: string;
}

export interface GetJwtTokenForPluginData {
  token?: string;
}

export interface GetJwtTokenForPluginRequest {
  Authorization: string;
  /** 需要额外存入JWT鉴权参数 对应的配置Key, 不传取默认配置 */
  scene?: string;
  Base?: base.Base;
}

export interface GetJwtTokenForPluginResponse {
  data?: GetJwtTokenForPluginData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetJwtTokenRequest {
  expire_time?: number;
  Base?: base.Base;
}

export interface GetJwtTokenResponse {
  data?: GetJwtTokenData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetPaperTranslateResultRequest {
  agent_id: string;
  /** 论文对应InstanceID */
  instance_id: string;
  base?: base.Base;
}

export interface GetPaperTranslateResultResponse {
  data?: TranslateResultData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetTaskProgressRequest {
  'space-id'?: string;
  dataset_id: string;
  document_ids: Array<string>;
  base?: base.Base;
}

export interface GetTaskProgressResponse {
  data_list: Array<TaskProgressData>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface InstanceUpdateParam {
  /** 名称 */
  name?: string;
  /** 内容 */
  content?: string;
  /** 各个agent特有参数，如论文agent需要的datasetID */
  init_param?: Record<string, string>;
}

export interface ListAgentInstanceRequest {
  /** agentID */
  agent_id: string;
  /** 分页大小 */
  size?: number;
  /** 页数 */
  page?: number;
  /** 排序字段 */
  sort_cond?: SortCondition;
  base?: base.Base;
}

export interface ListAgentInstanceResponse {
  /** 实例列表 */
  instance_list: Array<AgentInstanceInfo>;
  /** 总量 */
  total: Int64;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface ListCardGenerateHistoryData {
  total: Int64;
  history_infos: Array<CardGenerateRecordInfo>;
}

export interface ListCardGenerateHistoryRequest {
  'space-id': string;
  UserID: Int64;
  card_id: string;
  page: number;
  size: number;
  base?: base.Base;
}

export interface ListCardGenerateHistoryResponse {
  data?: ListCardGenerateHistoryData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface ListSliceRequest {
  /** agentID */
  agent_id: string;
  /** 实例ID */
  InstanceID: string;
  dataset_id: string;
  document_id: string;
  page?: number;
  size?: number;
  base?: base.Base;
}

export interface ListSliceResponse {
  slices?: Array<SliceInfo>;
  total?: string;
  hasmore?: boolean;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface MGetCardInfoRequest {
  'space-id': string;
  card_ids?: Array<string>;
  base?: base.Base;
}

export interface MGetCardInfoResponse {
  card_basic_infos: Array<CardBasicInfo>;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface MGetCardTemplateData {
  total: Int64;
  card_templates: Array<CardTemplateInfo>;
}

export interface MGetCardTemplateRequest {
  /** 用户ID */
  creator_id?: string;
  /** 分页大小 */
  size?: Int64;
  /** 分页 */
  page?: Int64;
  /** 渠道 */
  channel_type: ChannelType;
  category: CardCategory;
  'space-id'?: string;
  base?: base.Base;
}

export interface MGetCardTemplateResponse {
  data?: MGetCardTemplateData;
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface PreviewCardData {
  /** {渠道：DSL} */
  Content: string;
  /** 预览SDK地址 */
  SDKUrl?: string;
}

export interface PreviewCardRequest {
  /** 草稿id */
  DraftID?: string;
  /** 渠道id */
  ChannelType?: ChannelType;
  'use-builder-psm'?: string;
  Base?: base.Base;
}

export interface PreviewCardResponse {
  data?: PreviewCardData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface PublishHistoryInfo {
  card_id?: string;
  /** 卡片草稿 id */
  draft_id?: string;
  /** 卡片名称 */
  name?: string;
  /** 卡片版本号 */
  version_num?: string;
  /** 发布时间 */
  publish_time?: string;
  /** 卡片版本名称 */
  version_name?: string;
  /** 发布描述 */
  description?: string;
  /** 审核状态 */
  audit_status: number;
  /** 审核不通过详情 */
  audit_failure_details?: Array<number>;
}

export interface QueryCardHistoryRequest {
  'space-id': string;
  card_id: string;
  user_id: string;
  page: number;
  size: number;
  base?: base.Base;
}

export interface QueryCardHistoryResponse {
  data?: CardHistoryData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface QueryCardListCondition {
  /** 只展示自己创建的 */
  only_myself?: boolean;
  /** 状态 */
  status?: CardStatus;
  /** 搜索关键词 */
  keyword?: string;
  /** 排序条件 */
  sort_condition?: SortCondition;
}

export interface QueryCardListRequest {
  /** 空间ID */
  'space-id'?: Int64;
  page?: number;
  size?: number;
  /** 额外查询条件 */
  condition?: QueryCardListCondition;
  base?: base.Base;
}

export interface QueryCardListResponse {
  card_info_list?: Array<CardBasicInfo>;
  total?: number;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface QueryCardsData {
  cards?: Array<CardInfoForOP>;
  count?: number;
}

export interface QueryCardsRequest {
  card_condition?: CardCondition;
  base?: base.Base;
}

export interface QueryCardsResponse {
  data?: QueryCardsData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface ReferenceData {
  /** Url */
  url?: string;
  /** 内容 */
  content?: string;
  /** icon */
  icon?: string;
  /** 站点名称 */
  website_name?: string;
  /** 标题 */
  title?: string;
  /** 是否是用户手动添加的 */
  is_user_add?: boolean;
}

export interface ResourceAction {
  /** 一个操作对应一个唯一的key，key由资源侧约束 */
  key: ActionKey;
  /** ture=可以操作该Action，false=置灰 */
  enable: boolean;
}

export interface SaveAgentUserSettingsRequest {
  /** agentID */
  agent_id: string;
  /** 用户配置 */
  setting?: Array<UserSetting>;
  Base?: base.Base;
}

export interface SaveAgentUserSettingsResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface SaveCardData {
  template_id: string;
}

export interface SaveCardTemplateRequest {
  /** 创建者ID */
  creator_id: string;
  /** 渠道类型 */
  channel_type: ChannelType;
  /** 缩略图uri */
  thumbnail: string;
  /** 模版名称 */
  name: string;
  /** dsl */
  dsl_content: string;
  /** 空间ID */
  'space-id': string;
  base?: base.Base;
}

export interface SaveCardTemplateResponse {
  data?: SaveCardData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface SaveCardUserSettingsRequest {
  /** 用户id */
  creator_id?: string;
  /** 用户配置 */
  user_settings?: Array<UserSetting>;
  Base?: base.Base;
}

export interface SaveCardUserSettingsResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface SaveGenerateHistoryRequest {
  record_id: string;
  card_id: string;
  'space-id': string;
  UserID: Int64;
  /** AI schema to card UIDL 转换结果 */
  transfer_status?: AISchemaTransferStatus;
  /** 各渠道卡片信息 */
  channel_info?: Array<CardChannelInfo>;
  /** AI schema */
  ai_schema?: string;
  base?: base.Base;
}

export interface SaveGenerateHistoryResponse {
  BaseResp?: base.BaseResp;
}

export interface SliceInfo {
  slice_id?: string;
  content?: string;
  Status?: SliceStatus;
  /** 序号 */
  sequence?: string;
  /** 分片相关的元信息, 透传 slice 表里的 extra->chunk_info 字段 (json) */
  chunk_meta_info?: string;
}

export interface SortCondition {
  /** 排序字段 */
  field?: SortField;
  /** 排序类型 */
  order_type?: SortOrderType;
}

export interface TaskProgressData {
  document_id?: string;
  /** 进度，0-100 */
  progress?: number;
  status?: DocumentType;
  /** 状态的详细描述；如果切片失败，返回失败信息 */
  status_descript?: string;
}

export interface ThumbnailInfo {
  channel_type?: ChannelType;
  /** 缩略图 */
  thumbnail?: string;
  /** 是否是主图 */
  main_image?: boolean;
}

/** -------------- 运营平台接口 start -------------- */
export interface TimeRange {
  start_time: string;
  end_time: string;
}

export interface TranslatePaperRequest {
  agent_id: string;
  /** 论文对应InstanceID */
  instance_id: string;
  base?: base.Base;
}

export interface TranslatePaperResponse {
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface TranslateResult {
  /** 图片地址 */
  image_url?: string;
  /** 解析后文本信息tos地址 */
  text_blocks_url?: string;
  /** 原始图片地址 */
  ori_image_url?: string;
  /** 页码 0开始 */
  page_no?: number;
  /** 该页处理状态(0 未开始 1 成功 2 失败) */
  status?: number;
}

export interface TranslateResultData {
  /** 任务状态（0 未开始 1 进行中 2 完成 3 失败） */
  task_status?: number;
  /** 返回结果 */
  result?: Array<TranslateResult>;
}

export interface TranslateTextData {
  /** 翻译结果 */
  trans_result?: Record<string, string>;
}

export interface TranslateTextRequest {
  agent_id?: string;
  instance_id?: string;
  text?: Array<string>;
  base?: base.Base;
}

export interface TranslateTextResponse {
  data?: TranslateTextData;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface UpdateAgentInstanceInfoRequest {
  /** agentID */
  agent_id: string;
  /** 实例ID */
  instance_id: string;
  /** 更新参数 */
  update_param?: InstanceUpdateParam;
  base?: base.Base;
}

export interface UpdateAgentInstanceInfoResponse {
  code?: Int64;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface UpdateCardInfoRequest {
  card_id: string;
  name?: string;
  card_status?: CardStatus;
  creator_id?: string;
  /** 是否编辑了卡片 */
  edit_card?: boolean;
  base?: base.Base;
}

export interface UpdateCardInfoResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UpdateTemplateRequest {
  ids?: Array<string>;
  operator?: string;
  stage?: Stage;
  Base?: base.Base;
}

export interface UpdateTemplateResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface UploadFileData {
  /** 文件url */
  upload_url?: string;
  /** 文件uri，提交使用这个 */
  upload_uri?: string;
}

export interface UserSetting {
  setting_key?: string;
  setting_value?: string;
}

export interface ValueOption {
  /** 移除换行符 */
  remove_newline?: boolean;
  /** 移除空格 */
  remove_space?: boolean;
}
/* eslint-enable */
