/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** ======================= 枚举 ======================= */
export enum DeleteStatus {
  /** 正常 */
  Normal = 0,
  /** 删除 */
  Deleted = 1,
}

export enum GradeLevel {
  /** 未知 */
  Unknown = 0,
  /** A1.1 */
  A11 = 1,
  /** A1.2 */
  A12 = 2,
  /** A2.1 */
  A21 = 3,
  /** A2.2 */
  A22 = 4,
  /** B1.1 */
  B11 = 5,
  /** B1.2 */
  B12 = 6,
  /** B2.1 */
  B21 = 7,
  /** B2.2 */
  B22 = 8,
  /** C1.1 */
  C11 = 9,
  /** C1.2 */
  C12 = 10,
  /** C2 */
  C2 = 11,
}

export enum PaperExamStatus {
  /** 待开始 */
  NotStart = 0,
  /** 考试中 */
  InProgress = 1,
  /** 考试结束 */
  Finished = 2,
  /** 强制退出 */
  Exited = 3,
  /** 到时间未提交，系统自动结束 */
  Suspend = 4,
}

export enum PaperLevel {
  /** 未知 */
  Unknown = 0,
  /** A1 */
  A1 = 1,
  /** A2 */
  A2 = 2,
  /** B1 */
  B1 = 3,
  /** B2 */
  B2 = 4,
  /** C1 */
  C1 = 5,
}

export enum PaperStatus {
  /** 正常 */
  Normal = 0,
  /** 禁用 */
  Disbled = 1,
}

/** 题目选项正确状态 */
export enum QuestionOptionCorrectStatus {
  /** 错误 */
  Incorrect = 0,
  /** 正确 */
  Correct = 1,
}

export enum RemindType {
  /** 未知 */
  Unknown = 0,
  /** 测试通知 */
  Test = 1,
  /** 正式通知 */
  Formal = 2,
}

export enum TeamExamCategory {
  /** 测试 */
  Test = 0,
  /** 团队考试 */
  TeamExam = 1,
}

/** 团队考试批改类型 */
export enum TeamExamCheckType {
  /** 机器批改 */
  DeprecatedMachine = 0,
  /** 人工批改 */
  DeprecatedManual = 1,
  /** 人工AI */
  Manual = 2,
  /** 机器AI */
  Machine = 3,
  /** 统一考试（人工批改） */
  UnifiedManual = 4,
  /** 统一考试（机器批改） */
  UnifiedMachine = 5,
}

/** 团队考试状态 */
export enum TeamExamStatus {
  /** 进行中 */
  InProgress = 0,
  /** 未开始 */
  Pending = 1,
  /** 已完成 */
  Finished = 2,
}

export interface CreatePaperExamParams {
  /** 录入用户名 */
  username?: string;
  /** 考生邮箱 */
  lark_email: string;
  /** 试卷等级 */
  paper_level: PaperLevel;
}

export interface CreatePaperQuestionOptionParams {
  /** 题目ID */
  paper_question_id?: Int64;
  /** 选项内容 */
  content?: string;
  /** 选项正确性 */
  is_correct?: QuestionOptionCorrectStatus;
}

/** ======================= 请求模型 ======================= */
export interface CreatePaperQuestionParams {
  /** 题目组ID */
  paper_question_group_id?: Int64;
  /** 题干 */
  content?: string;
  /** 选项列表 */
  options?: Array<CreatePaperQuestionOptionParams>;
}

/** ======================= 响应模型 ======================= */
export interface Paper {
  /** 试卷ID */
  id?: Int64;
  /** 试卷名称 */
  name?: string;
  /** 试卷等级 */
  level?: PaperLevel;
  /** 试卷状态 */
  status?: PaperStatus;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
  /** 题目数 */
  question_count?: number;
}

export interface PaperExam {
  /** 考试ID */
  id?: Int64;
  /** 用户ID */
  user_id?: Int64;
  /** 用户名 */
  username?: string;
  /** 用户邮箱 */
  lark_email?: string;
  /** 试卷等级 */
  paper_level?: PaperLevel;
  /** 试卷ID */
  paper_id?: Int64;
  /** 答题情况 */
  answer_situation?: string;
  /** 答题等级 */
  answer_level?: GradeLevel;
  /** 视频列表 */
  video_urls?: Array<string>;
  /** 考试状态 */
  status?: PaperExamStatus;
  /** 作答列表 */
  answers?: Array<PaperExamAnswer>;
  /** 试卷信息 */
  paper?: Paper;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
}

export interface PaperExamAnswer {
  /** ID */
  id?: Int64;
  /** 考试ID */
  paper_exam_id?: Int64;
  /** 题目ID */
  paper_question_id?: Int64;
  /** 选项ID，多选时逗号分隔选项id列表 */
  answer?: string;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
}

export interface PaperExamParseResult {
  /** 用户名 */
  username?: string;
  /** 用户邮箱 */
  lark_email?: string;
  /** 试卷等级 */
  paper_level?: PaperLevel;
  /** 是否可导入 */
  is_valid?: boolean;
  /** 备注 */
  remark?: string;
}

export interface PaperQuestion {
  /** 题目ID */
  id?: Int64;
  /** 题目组ID */
  paper_question_group_id?: Int64;
  /** 题干 */
  content?: string;
  /** 选项列表 */
  options?: Array<PaperQuestionOption>;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
}

export interface PaperQuestionGroup {
  /** 题目组ID */
  id?: Int64;
  /** 试卷ID */
  paper_id?: Int64;
  /** 题干 */
  content?: string;
  /** 关联音频 */
  audio_id?: string;
  /** 关联图片 */
  picture?: string;
  /** 题目列表 */
  questions?: Array<PaperQuestion>;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
}

export interface PaperQuestionOption {
  /** 选项ID */
  id?: Int64;
  /** 题目ID */
  paper_question_id?: Int64;
  /** 选项内容 */
  content?: string;
  /** 选项正确性 */
  is_correct?: QuestionOptionCorrectStatus;
  /** 更新时间 */
  updated_at?: Int64;
  /** 创建时间 */
  created_at?: Int64;
}

export interface PlayInfo {
  /** status 视频状态 */
  status?: Int64;
  /** 同上 */
  message?: string;
  /** provider name */
  account_name?: string;
  /** 媒体类型 audio video */
  media_type?: string;
  /** 原视频长度 */
  duration?: number;
  /** 封面截图 */
  poster_url?: string;
  /** 转码视频信息,包括视频播放地址，视频元信息 */
  video_infos?: Array<VideoInfo>;
  /** 封面截图uri */
  poster_uri?: string;
  /** 热度值 */
  popularity_level?: Int64;
  /** 帮助信息地址 */
  help_info_u_r_l?: string;
  /** 视频架构下维护的状态 */
  user_action?: string;
}

export interface UpdatePaperQuestionOptionParams {
  /** 选项ID，如果为0则为创建 */
  id?: Int64;
  /** 题目ID */
  paper_question_id?: Int64;
  /** 选项内容 */
  content?: string;
  /** 选项正确性 */
  is_correct?: QuestionOptionCorrectStatus;
  /** 标记为删除的选项 */
  mark_as_delete?: boolean;
}

export interface UpdatePaperQuestionParams {
  /** 题目ID，如果为0则为创建 */
  id?: Int64;
  /** 题目组ID */
  paper_question_group_id?: Int64;
  /** 题干 */
  content?: string;
  /** 选项列表 */
  options?: Array<UpdatePaperQuestionOptionParams>;
  /** 标记为删除的题目 */
  mark_as_delete?: boolean;
}

export interface VideoInfo {
  /** 主播放地址 */
  main_url?: string;
  /** 备播放地址 */
  backup_url?: string;
  /** 视频元信息 */
  video_meta?: VideoMeta;
  /** 用于告诉客户端，当前返回的url过期时间（未来时间） */
  url_expire?: Int64;
  /** 视频File id，用于在p2p播放时的唯一标识 */
  file_id?: string;
}

export interface VideoMeta {
  /** 视频长度 */
  height?: Int64;
  /** 视频宽度 */
  width?: Int64;
  /** 格式(mp4) */
  format?: string;
  /** 视频长度 */
  duration?: number;
  /** 视频大小 */
  size?: Int64;
  /** 视频比特率 */
  bitrate?: Int64;
  /** 分辨率 */
  definition?: string;
  /** logo名称 */
  logo_type?: string;
  /** 编码格式 */
  codec_type?: string;
  /** 视频类型 */
  encoded_type?: string;
}
/* eslint-enable */
