/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as uniexam from './uniexam';
import * as base from './base';

export type Int64 = string | number;

/** 题目批改状态 */
export enum ExternalUserStatus {
  /** 禁用 */
  Disabled = 0,
  /** 启用 */
  Enabled = 1,
}

export interface ConfirmUniExamRecordForExternalReq {
  /** 外部考试记录ID */
  external_id?: string;
  /** 听力试卷等级 */
  listening_paper_level?: uniexam.GeneralLevel;
  /** 听力得分 */
  listening_score?: number;
  /** 听力等级 */
  listening_level?: uniexam.DetailLevel;
  /** 口语得分 */
  oral_score?: number;
  /** 口语等级 */
  oral_level?: uniexam.DetailLevel;
  /** 最终定级(不传最终定级仅保存分数，状态不变，传最终定级会确定提交批改，状态变为已完成) */
  final_level?: uniexam.DetailLevel;
  /** 确认类型(0:未知 1:确认 2:保存) */
  confirm_type?: uniexam.ConfirmUniExamType;
  base?: base.Base;
}

export interface ConfirmUniExamRecordForExternalResp {
  uni_exam?: uniexam.UniExam;
  code?: number;
  message?: string;
  base_resp?: base.BaseResp;
}

export interface GetUniExamRecordForExternalReq {
  /** 外部考试记录ID */
  id?: string;
  base?: base.Base;
}

export interface GetUniExamRecordForExternalResp {
  uni_exam?: uniexam.UniExam;
  code?: number;
  message?: string;
  base_resp?: base.BaseResp;
}

export interface ListUniExamRecordForExternalReq {
  /** 页码 */
  page?: number;
  /** 每页数量 */
  page_size?: number;
  /** 外部考试ID */
  exam_id?: string;
  base?: base.Base;
}

export interface ListUniExamRecordForExternalResp {
  uni_exams?: Array<uniexam.UniExam>;
  total?: number;
  page?: number;
  page_size?: number;
  code?: number;
  message?: string;
  base_resp?: base.BaseResp;
}

export interface LoginByBasicReq {
  /** 用户名 */
  email?: string;
  /** 密码 */
  password?: string;
  base?: base.Base;
}

export interface LoginByBasicResp {
  base_resp?: base.BaseResp;
}

export interface LogoutByBasicReq {
  base?: base.Base;
}

export interface LogoutByBasicResp {
  base_resp?: base.BaseResp;
}
/* eslint-enable */
