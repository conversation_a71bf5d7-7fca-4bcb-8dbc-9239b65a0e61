/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as common from './namespaces/common';
import * as entity from './namespaces/entity';
import * as external from './namespaces/external';
import * as origin from './namespaces/origin';
import * as uniexam from './namespaces/uniexam';

export { base, common, entity, external, origin, uniexam };
export * from './namespaces/base';
export * from './namespaces/common';
export * from './namespaces/entity';
export * from './namespaces/external';
export * from './namespaces/origin';
export * from './namespaces/uniexam';

export type Int64 = string | number;

export default class EnglishExaminationService<T> {
  private request: any = () => {
    throw new Error('EnglishExaminationService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * GET /api/examination/login
   *
   * 用户登录
   */
  Login(req?: entity.LoginReq, options?: T): Promise<entity.LoginResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/login');
    const method = 'GET';
    const params = {
      code: _req['code'],
      state: _req['state'],
      session_id: _req['session_id'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/paper
   *
   * 创建试卷
   */
  CreatePaper(
    req: entity.CreatePaperReq,
    options?: T,
  ): Promise<entity.CreatePaperResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/paper');
    const method = 'POST';
    const data = {
      name: _req['name'],
      level: _req['level'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/paper
   *
   * 更新试卷
   */
  UpdatePaper(
    req?: entity.UpdatePaperReq,
    options?: T,
  ): Promise<entity.UpdatePaperResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/paper');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      name: _req['name'],
      level: _req['level'],
      status: _req['status'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/user_info
   *
   * 获取用户信息
   */
  GetSessionUserInfo(
    req?: entity.GetSessionUserInfoReq,
    options?: T,
  ): Promise<entity.GetSessionUserInfoResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/user_info');
    const method = 'GET';
    const params = { base: _req['base'] };
    const headers = { 'x-innovation-token': _req['x-innovation-token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/examination/paper
   *
   * 获取试卷列表
   */
  ListPaper(
    req?: entity.ListPaperReq,
    options?: T,
  ): Promise<entity.ListPaperResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/paper');
    const method = 'GET';
    const params = {
      page: _req['page'],
      page_size: _req['page_size'],
      status: _req['status'],
      level: _req['level'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/examination/paper/:id
   *
   * 删除试卷
   */
  DeletePaper(
    req: entity.DeletePaperReq,
    options?: T,
  ): Promise<entity.DeletePaperResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/examination/paper/${_req['id']}`);
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/loginByJwt
   *
   * JWT登录
   */
  LoginByJwt(
    req: entity.LoginByJwtReq,
    options?: T,
  ): Promise<entity.LoginByJwtResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/loginByJwt');
    const method = 'GET';
    const params = { base: _req['base'] };
    const headers = { authorization: _req['authorization'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/examination/question_group
   *
   * 创建题目组
   */
  CreateQuestionGroup(
    req: entity.CreateQuestionGroupReq,
    options?: T,
  ): Promise<entity.CreateQuestionGroupResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/question_group');
    const method = 'POST';
    const data = {
      paper_id: _req['paper_id'],
      content: _req['content'],
      audio_id: _req['audio_id'],
      picture: _req['picture'],
      questions: _req['questions'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/question_option
   *
   * 创建选项
   */
  CreateQuestionOption(
    req: entity.CreateQuestionOptionReq,
    options?: T,
  ): Promise<entity.CreateQuestionOptionResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/question_option');
    const method = 'POST';
    const data = {
      paper_question_id: _req['paper_question_id'],
      content: _req['content'],
      is_correct: _req['is_correct'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/question
   *
   * 创建题目
   */
  CreateQuestion(
    req: entity.CreateQuestionReq,
    options?: T,
  ): Promise<entity.CreateQuestionResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/question');
    const method = 'POST';
    const data = {
      paper_question_group_id: _req['paper_question_group_id'],
      content: _req['content'],
      question_options: _req['question_options'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/question_group
   *
   * 更新题目组
   */
  UpdateQuestionGroup(
    req: entity.UpdateQuestionGroupReq,
    options?: T,
  ): Promise<entity.UpdateQuestionGroupResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/question_group');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      content: _req['content'],
      audio_id: _req['audio_id'],
      picture: _req['picture'],
      questions: _req['questions'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/question_group
   *
   * 获取试卷的题目组列表
   */
  ListQuestionGroup(
    req: entity.ListQuestionGroupReq,
    options?: T,
  ): Promise<entity.ListQuestionGroupResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/question_group');
    const method = 'GET';
    const params = { paper_id: _req['paper_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/examination/question_group/:id
   *
   * 删除题目组
   */
  DeleteQuestionGroup(
    req: entity.DeleteQuestionGroupReq,
    options?: T,
  ): Promise<entity.DeleteQuestionGroupResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/examination/question_group/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/paper_exam
   *
   * 获取测试名单
   */
  ListUserPaperExamination(
    req?: entity.ListUserPaperExaminationReq,
    options?: T,
  ): Promise<entity.ListUserPaperExaminationResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/paper_exam');
    const method = 'GET';
    const params = {
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/paper_exam
   *
   * 录入测试名单
   */
  BatchCreateUserPaperExamination(
    req: entity.BatchCreateUserPaperExaminationReq,
    options?: T,
  ): Promise<entity.BatchCreateUserPaperExaminationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/paper_exam');
    const method = 'POST';
    const data = { paper_exams: _req['paper_exams'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/paper_exam
   *
   * 更新测试名单
   */
  UpdateUserPaperExamination(
    req: entity.UpdateUserPaperExaminationReq,
    options?: T,
  ): Promise<entity.UpdateUserPaperExaminationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/paper_exam');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      level: _req['level'],
      paper_id: _req['paper_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/paper_exam/export
   *
   * 导出测试记录到 Excel 文件
   */
  ExportUserPaperExamination(
    req?: entity.ExportUserPaperExaminationReq,
    options?: T,
  ): Promise<entity.ExportUserPaperExaminationResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/paper_exam/export');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/examination/paper_exam/:id
   *
   * 删除测试名单
   */
  DeleteUserPaperExamination(
    req: entity.DeleteUserPaperExaminationReq,
    options?: T,
  ): Promise<entity.DeleteUserPaperExaminationResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/examination/paper_exam/${_req['id']}`);
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/paper_for_exam
   *
   * 获取可选的试卷列表
   */
  ListPaperForExam(
    req: entity.ListPaperForExamReq,
    options?: T,
  ): Promise<entity.ListPaperForExamResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/paper_for_exam');
    const method = 'GET';
    const params = { paper_exam_id: _req['paper_exam_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/paper_exam/parse
   *
   * 解析测试名单 Excel 表格
   */
  ParseUserPaperExamination(
    req: entity.ParseUserPaperExaminationReq,
    options?: T,
  ): Promise<entity.ParseUserPaperExaminationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/paper_exam/parse');
    const method = 'POST';
    const data = { excel_id: _req['excel_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/video/token
   *
   * 下发上传视频的 token
   */
  GetUploadVideoToken(
    req?: entity.GetUploadVideoTokenReq,
    options?: T,
  ): Promise<entity.GetUploadVideoTokenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/video/token');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/video/play
   *
   * 播放视频
   */
  GetPlayVideoInfo(
    req: entity.GetPlayVideoInfoReq,
    options?: T,
  ): Promise<entity.GetPlayVideoInfoResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/video/play');
    const method = 'POST';
    const data = { vids: _req['vids'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/myexam/list
   *
   * 获取我的考试资格
   */
  GetMyPaperExam(
    req?: entity.GetMyPaperExamReq,
    options?: T,
  ): Promise<entity.GetMyPaperExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/myexam/list');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/myexam/answer
   *
   * 提交作答
   */
  AnswerMyPaperExamQuestion(
    req?: entity.AnswerMyPaperExamQuestionReq,
    options?: T,
  ): Promise<entity.AnswerMyPaperExamQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/myexam/answer');
    const method = 'POST';
    const data = {
      paper_exam_id: _req['paper_exam_id'],
      question_answers: _req['question_answers'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/myexam/start
   *
   * 开始考试，获取考题
   */
  StartMyPaperExam(
    req?: entity.StartMyPaperExamReq,
    options?: T,
  ): Promise<entity.StartMyPaperExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/myexam/start');
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/myexam/submit
   *
   * 提交试卷
   */
  SubmitMyPaperExam(
    req?: entity.SubmitMyPaperExamReq,
    options?: T,
  ): Promise<entity.SubmitMyPaperExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/myexam/submit');
    const method = 'POST';
    const data = {
      paper_exam_id: _req['paper_exam_id'],
      video_id: _req['video_id'],
      force_submit: _req['force_submit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/myexam/leave
   *
   * 切屏上报
   */
  ReportLeaveScreen(
    req?: entity.ReportLeaveScreenReq,
    options?: T,
  ): Promise<entity.ReportLeaveScreenResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/myexam/leave');
    const method = 'POST';
    const data = { paper_exam_id: _req['paper_exam_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/check/admin
   *
   * 检查管理员权限
   */
  CheckAdminPermission(
    req?: entity.CheckAdminPermissionReq,
    options?: T,
  ): Promise<entity.CheckAdminPermissionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/check/admin');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/check/alpha
   *
   * 检查内测权限
   */
  CheckAlphaTestPermission(
    req?: entity.CheckAlphaTestPermissionReq,
    options?: T,
  ): Promise<entity.CheckAlphaTestPermissionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/check/alpha');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/origin_exam/report/leave
   *
   * 切屏上报
   */
  ReportOriginExamLeave(
    req?: entity.ReportOriginExamLeaveReq,
    options?: T,
  ): Promise<entity.ReportOriginExamLeaveResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/report/leave');
    const method = 'POST';
    const data = { grade_exam_id: _req['grade_exam_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/origin_exam/cert
   *
   * 查询原味考试资格
   */
  GetOriginExamCert(
    req?: entity.GetOriginExamCertReq,
    options?: T,
  ): Promise<entity.GetOriginExamCertResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/cert');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/origin_exam/start
   *
   * 开始考试，返回第一轮出题
   */
  StartOriginExam(
    req?: entity.StartOriginExamReq,
    options?: T,
  ): Promise<entity.StartOriginExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/start');
    const method = 'POST';
    const data = { team_exam_id: _req['team_exam_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/origin_exam/finish
   *
   * 提交试卷
   */
  SubmitOriginExam(
    req?: entity.SubmitOriginExamReq,
    options?: T,
  ): Promise<entity.SubmitOriginExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/finish');
    const method = 'POST';
    const data = {
      grade_exam_id: _req['grade_exam_id'],
      video_id: _req['video_id'],
      force_submit: _req['force_submit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/origin_exam/next_round
   *
   * 获取下轮出题
   */
  GetOriginExamNextRound(
    req?: entity.GetOriginExamNextRoundReq,
    options?: T,
  ): Promise<entity.GetOriginExamNextRoundResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/next_round');
    const method = 'POST';
    const data = {
      grade_exam_id: _req['grade_exam_id'],
      team_exam_id: _req['team_exam_id'],
      current_round_id: _req['current_round_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/origin_exam/answer
   *
   * 提交作答
   */
  SubmitOriginExamAnswer(
    req?: entity.SubmitOriginExamAnswerReq,
    options?: T,
  ): Promise<entity.SubmitOriginExamAnswerResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/answer');
    const method = 'POST';
    const data = {
      grade_exam_id: _req['grade_exam_id'],
      library_question_id: _req['library_question_id'],
      user_answer: _req['user_answer'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/origin_exam/result
   *
   * 获取测试报告列表
   */
  ListOriginExamResult(
    req?: entity.ListOriginExamResultReq,
    options?: T,
  ): Promise<entity.ListOriginExamResultResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/result');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/origin_exam/libq
   *
   * 题库列表
   */
  ListOriginLibraryQuestion(
    req?: entity.ListOriginLibraryQuestionReq,
    options?: T,
  ): Promise<entity.ListOriginLibraryQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/libq');
    const method = 'GET';
    const params = {
      page: _req['page'],
      page_size: _req['page_size'],
      keyword: _req['keyword'],
      level: _req['level'],
      type: _req['type'],
      status: _req['status'],
      part: _req['part'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/origin_exam/libq
   *
   * 题库创建
   */
  MCreateOriginLibraryQuestion(
    req?: entity.MCreateOriginLibraryQuestionReq,
    options?: T,
  ): Promise<entity.MCreateOriginLibraryQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/libq');
    const method = 'POST';
    const data = { questions: _req['questions'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/origin_exam/libq
   *
   * 题库更新
   */
  UpdateOriginLibraryQuestion(
    req?: entity.UpdateOriginLibraryQuestionReq,
    options?: T,
  ): Promise<entity.UpdateOriginLibraryQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/origin_exam/libq');
    const method = 'PUT';
    const data = { question: _req['question'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/examination/origin_exam/libq/:id
   *
   * 题库删除
   */
  DeleteOriginLibraryQuestion(
    req: entity.DeleteOriginLibraryQuestionReq,
    options?: T,
  ): Promise<entity.DeleteOriginLibraryQuestionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/examination/origin_exam/libq/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/common/tts
   *
   * 文本转语音
   */
  TextToSpeech(
    req?: entity.TextToSpeechReq,
    options?: T,
  ): Promise<entity.TextToSpeechResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/common/tts');
    const method = 'GET';
    const params = { text: _req['text'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/examination/uni_exam/question_group
   *
   * 更新题目组
   */
  UpdateUniExamQuestionGroup(
    req?: entity.UpdateUniExamQuestionGroupReq,
    options?: T,
  ): Promise<entity.UpdateUniExamQuestionGroupResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question_group');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      content: _req['content'],
      audio_uri: _req['audio_uri'],
      image_uri: _req['image_uri'],
      questions: _req['questions'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/question
   *
   * 创建题目
   */
  CreateUniExamQuestion(
    req?: entity.CreateUniExamQuestionReq,
    options?: T,
  ): Promise<entity.CreateUniExamQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question');
    const method = 'POST';
    const data = {
      question_group_id: _req['question_group_id'],
      content: _req['content'],
      audio_uri: _req['audio_uri'],
      image_uri: _req['image_uri'],
      oral_part: _req['oral_part'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/uni_exam/question
   *
   * 更新口语题
   */
  UpdateUniExamQuestion(
    req?: entity.UpdateUniExamQuestionReq,
    options?: T,
  ): Promise<entity.UpdateUniExamQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      content: _req['content'],
      audio_uri: _req['audio_uri'],
      image_uri: _req['image_uri'],
      status: _req['status'],
      is_simulated: _req['is_simulated'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/uni_exam/record/:id
   *
   * 考试记录详情
   */
  GetUniExamRecord(
    req?: entity.GetUniExamRecordReq,
    options?: T,
  ): Promise<entity.GetUniExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/record/${_req['id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/push
   *
   * 推送考试(已废弃)
   */
  PushUniExamRecord(
    req?: entity.PushUniExamRecordReq,
    options?: T,
  ): Promise<entity.PushUniExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team_exam/push');
    const method = 'POST';
    const data = {
      id: _req['id'],
      check_type: _req['check_type'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/uni_exam/result
   *
   * 考试结果列表
   */
  ListUniExamResult(
    req?: entity.ListUniExamResultReq,
    options?: T,
  ): Promise<entity.ListUniExamResultResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/result');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/paper
   *
   * 创建听力试卷
   */
  CreateUniExamPaper(
    req: entity.CreateUniExamPaperReq,
    options?: T,
  ): Promise<entity.CreateUniExamPaperResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/uni_exam/paper');
    const method = 'POST';
    const data = {
      name: _req['name'],
      level: _req['level'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/uni_exam/question_group
   *
   * 获取听力试卷的题目组列表
   */
  ListUniExamQuestionGroup(
    req?: entity.ListUniExamQuestionGroupReq,
    options?: T,
  ): Promise<entity.ListUniExamQuestionGroupResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question_group');
    const method = 'GET';
    const params = { paper_id: _req['paper_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/uni_exam/question
   *
   * 题目列表
   */
  ListUniExamQuestion(
    req?: entity.ListUniExamQuestionReq,
    options?: T,
  ): Promise<entity.ListUniExamQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question');
    const method = 'GET';
    const params = {
      part: _req['part'],
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/uni_exam/team_exam/remind
   *
   * 团队考试提醒记录
   */
  ListTeamExamRemind(
    req?: entity.ListTeamExamRemindReq,
    options?: T,
  ): Promise<entity.ListTeamExamRemindResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team_exam/remind');
    const method = 'GET';
    const params = { team_exam_id: _req['team_exam_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/answer
   *
   * 提交作答
   */
  SubmitUniExamAnswer(
    req?: entity.SubmitUniExamAnswerReq,
    options?: T,
  ): Promise<entity.SubmitUniExamAnswerResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/answer');
    const method = 'POST';
    const data = {
      uni_exam_id: _req['uni_exam_id'],
      uni_exam_answers: _req['uni_exam_answers'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/report/leave
   *
   * 切屏上报
   */
  ReportUniExamLeave(
    req?: entity.ReportUniExamLeaveReq,
    options?: T,
  ): Promise<entity.ReportUniExamLeaveResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/report/leave');
    const method = 'POST';
    const data = { uni_exam_id: _req['uni_exam_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/examination/uni_exam/question_group/:id
   *
   * 删除题目组
   */
  DeleteUniExamQuestionGroup(
    req?: entity.DeleteUniExamQuestionGroupReq,
    options?: T,
  ): Promise<entity.DeleteUniExamQuestionGroupResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/question_group/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/common/user/search
   *
   * 搜索用户
   */
  SearchUser(
    req?: entity.SearchUserReq,
    options?: T,
  ): Promise<entity.SearchUserResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/common/user/search');
    const method = 'GET';
    const params = { keyword: _req['keyword'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/record/confirm
   *
   * 考试人工批改确认
   */
  ConfirmUniExamRecord(
    req?: entity.ConfirmUniExamRecordReq,
    options?: T,
  ): Promise<entity.ConfirmUniExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/record/confirm');
    const method = 'POST';
    const data = {
      id: _req['id'],
      listening_paper_level: _req['listening_paper_level'],
      listening_score: _req['listening_score'],
      listening_level: _req['listening_level'],
      oral_score: _req['oral_score'],
      oral_level: _req['oral_level'],
      final_level: _req['final_level'],
      confirm_type: _req['confirm_type'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/uni_exam/paper
   *
   * 获取听力试卷列表
   */
  ListUniExamPaper(
    req?: entity.ListUniExamPaperReq,
    options?: T,
  ): Promise<entity.ListUniExamPaperResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/paper');
    const method = 'GET';
    const params = {
      page: _req['page'],
      page_size: _req['page_size'],
      status: _req['status'],
      level: _req['level'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/uni_exam/team
   *
   * 团队列表
   */
  ListTeam(
    req?: entity.ListTeamReq,
    options?: T,
  ): Promise<entity.ListTeamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team');
    const method = 'GET';
    const params = {
      page: _req['page'],
      page_size: _req['page_size'],
      name: _req['name'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/question_group
   *
   * 创建题目组
   */
  CreateUniExamQuestionGroup(
    req?: entity.CreateUniExamQuestionGroupReq,
    options?: T,
  ): Promise<entity.CreateUniExamQuestionGroupResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question_group');
    const method = 'POST';
    const data = {
      paper_id: _req['paper_id'],
      content: _req['content'],
      audio_uri: _req['audio_uri'],
      image_uri: _req['image_uri'],
      questions: _req['questions'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/examination/uni_exam/team_exam/:id
   *
   * 团队考试记录删除
   */
  DeleteTeamExamRecord(
    req?: entity.DeleteTeamExamRecordReq,
    options?: T,
  ): Promise<entity.DeleteTeamExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/team_exam/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/finish
   *
   * 提交试卷
   */
  SubmitUniExam(
    req?: entity.SubmitUniExamReq,
    options?: T,
  ): Promise<entity.SubmitUniExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/finish');
    const method = 'POST';
    const data = {
      uni_exam_id: _req['uni_exam_id'],
      video_uri: _req['video_uri'],
      force_submit: _req['force_submit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/uni_exam/paper
   *
   * 更新听力试卷
   */
  UpdateUniExamPaper(
    req?: entity.UpdateUniExamPaperReq,
    options?: T,
  ): Promise<entity.UpdateUniExamPaperResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/paper');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      name: _req['name'],
      status: _req['status'],
      is_simulated: _req['is_simulated'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/examination/uni_exam/question/:id
   *
   * 删除题目
   */
  DeleteUniExamQuestion(
    req?: entity.DeleteUniExamQuestionReq,
    options?: T,
  ): Promise<entity.DeleteUniExamQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/question/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * DELETE /api/examination/uni_exam/paper/:id
   *
   * 删除听力试卷
   */
  DeleteUniExamPaper(
    req: entity.DeleteUniExamPaperReq,
    options?: T,
  ): Promise<entity.DeleteUniExamPaperResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/examination/uni_exam/paper/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/remind
   *
   * 团队考试提醒
   */
  RemindTeamExam(
    req?: entity.RemindTeamExamReq,
    options?: T,
  ): Promise<entity.RemindTeamExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team_exam/remind');
    const method = 'POST';
    const data = {
      id: _req['id'],
      content: _req['content'],
      timestamp: _req['timestamp'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/start
   *
   * 开始考试
   */
  StartUniExam(
    req?: entity.StartUniExamReq,
    options?: T,
  ): Promise<entity.StartUniExamResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/start');
    const method = 'POST';
    const data = {
      team_exam_id: _req['team_exam_id'],
      category: _req['category'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/uni_exam/answer
   *
   * 修改作答
   */
  UpdateUniExamAnswer(
    req?: entity.UpdateUniExamAnswerReq,
    options?: T,
  ): Promise<entity.UpdateUniExamAnswerResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/answer');
    const method = 'PUT';
    const data = {
      uni_exam_id: _req['uni_exam_id'],
      uni_exam_question_id: _req['uni_exam_question_id'],
      user_answer: _req['user_answer'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/examination/uni_exam/record/:id
   *
   * 考试记录删除
   */
  DeleteUniExamRecord(
    req?: entity.DeleteUniExamRecordReq,
    options?: T,
  ): Promise<entity.DeleteUniExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/record/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/uni_exam/team_exam/:id
   *
   * 团队考试记录详情
   */
  GetTeamExamRecord(
    req?: entity.GetTeamExamRecordReq,
    options?: T,
  ): Promise<entity.GetTeamExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/team_exam/${_req['id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/uni_exam/cert
   *
   * 查询考试资格
   */
  GetUniExamCert(
    req?: entity.GetUniExamCertReq,
    options?: T,
  ): Promise<entity.GetUniExamCertResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/cert');
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/finish
   *
   * 结束考试
   */
  FinishUniExamRecord(
    req?: entity.FinishUniExamRecordReq,
    options?: T,
  ): Promise<entity.FinishUniExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team_exam/finish');
    const method = 'POST';
    const data = { id: _req['id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/examination/uni_exam/team_exam/remind/:id
   *
   * 删除团队考试提醒
   */
  DeleteTeamExamRemind(
    req?: entity.DeleteTeamExamRemindReq,
    options?: T,
  ): Promise<entity.DeleteTeamExamRemindResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/team_exam/remind/${_req['id']}`,
    );
    const method = 'DELETE';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/examination/uni_exam/team_exam/export/:id
   *
   * 团队考试记录导出
   */
  ExportTeamExamRecord(
    req?: entity.ExportTeamExamRecordReq,
    options?: T,
  ): Promise<entity.ExportTeamExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/uni_exam/team_exam/export/${_req['id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/record
   *
   * 考试记录列表
   */
  ListUniExamRecord(
    req?: entity.ListUniExamRecordReq,
    options?: T,
  ): Promise<entity.ListUniExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/record');
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      filter: _req['filter'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/answer/check
   *
   * 语音批改
   */
  GetOralAnswerCheck(
    req: entity.GetOralAnswerCheckReq,
    options?: T,
  ): Promise<entity.GetOralAnswerCheckResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/uni_exam/answer/check');
    const method = 'POST';
    const data = { audio_uri: _req['audio_uri'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/uni_exam/answer/check/task
   *
   * 语音批改结果列表
   */
  GetOralAnswerCheckTaskResult(
    req: entity.GetOralAnswerCheckTaskResultReq,
    options?: T,
  ): Promise<entity.GetOralAnswerCheckTaskResultResp> {
    const _req = req;
    const url = this.genBaseURL('/api/examination/uni_exam/answer/check/task');
    const method = 'GET';
    const params = { task_id: _req['task_id'], base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/external/uni_exam/record/confirm
   *
   * 考试人工批改确认(外部)
   */
  ConfirmUniExamRecordForExternal(
    req?: external.ConfirmUniExamRecordForExternalReq,
    options?: T,
  ): Promise<external.ConfirmUniExamRecordForExternalResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/examination/external/uni_exam/record/confirm',
    );
    const method = 'POST';
    const data = {
      external_id: _req['external_id'],
      listening_paper_level: _req['listening_paper_level'],
      listening_score: _req['listening_score'],
      listening_level: _req['listening_level'],
      oral_score: _req['oral_score'],
      oral_level: _req['oral_level'],
      final_level: _req['final_level'],
      confirm_type: _req['confirm_type'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/external/uni_exam/record/:id
   *
   * 考试记录详情(外部)
   */
  GetUniExamRecordForExternal(
    req?: external.GetUniExamRecordForExternalReq,
    options?: T,
  ): Promise<external.GetUniExamRecordForExternalResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/examination/external/uni_exam/record/${_req['id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/external/auth/loginByBasic
   *
   * 用户登录(外部)
   */
  LoginByBasic(
    req?: external.LoginByBasicReq,
    options?: T,
  ): Promise<external.LoginByBasicResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/external/auth/loginByBasic');
    const method = 'POST';
    const data = {
      email: _req['email'],
      password: _req['password'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/external/auth/logoutByBasic
   *
   * 用户注销(外部)
   */
  LogoutByBasic(
    req?: external.LogoutByBasicReq,
    options?: T,
  ): Promise<external.LogoutByBasicResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/external/auth/logoutByBasic');
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/external/uni_exam/record
   *
   * 考试记录列表(外部)
   */
  ListUniExamRecordForExternal(
    req?: external.ListUniExamRecordForExternalReq,
    options?: T,
  ): Promise<external.ListUniExamRecordForExternalResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/external/uni_exam/record');
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      exam_id: _req['exam_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/uni_exam/team_exam/test
   *
   * 更新测试
   */
  UpdateTeamExamTest(
    req?: entity.UpdateTeamExamTestReq,
    options?: T,
  ): Promise<entity.UpdateTeamExamTestResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team_exam/test');
    const method = 'PUT';
    const data = {
      id: _req['id'],
      email: _req['email'],
      operation: _req['operation'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/test
   *
   * 创建测试
   */
  CreateTeamExamTest(
    req?: entity.CreateTeamExamTestReq,
    options?: T,
  ): Promise<entity.CreateTeamExamTestResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team_exam/test');
    const method = 'POST';
    const data = {
      name: _req['name'],
      check_type: _req['check_type'],
      start_at: _req['start_at'],
      end_at: _req['end_at'],
      filter: _req['filter'],
      email: _req['email'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/test/check
   *
   * 检查更新测试
   */
  UpdateTeamExamTestCheck(
    req?: entity.UpdateTeamExamTestReq,
    options?: T,
  ): Promise<entity.UpdateTeamExamTestResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/examination/uni_exam/team_exam/test/check',
    );
    const method = 'POST';
    const data = {
      id: _req['id'],
      email: _req['email'],
      operation: _req['operation'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/record/export
   *
   * 导出考试结果
   */
  ExportUniExamRecord(
    req?: entity.ExportUniExamRecordReq,
    options?: T,
  ): Promise<entity.ExportUniExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/record/export');
    const method = 'POST';
    const data = { filter: _req['filter'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/examination/uni_exam/record/filter/option
   *
   * 获取考试记录列表筛选项
   */
  ListUniExamRecordFilterOptions(
    req?: entity.ListUniExamRecordFilterOptionsReq,
    options?: T,
  ): Promise<entity.ListUniExamRecordFilterOptionsResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/examination/uni_exam/record/filter/option',
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/examination/uni_exam/save_video_chunk
   *
   * 保存视频切片
   */
  SaveVideoChunk(
    req?: entity.SaveVideoChunkReq,
    options?: T,
  ): Promise<entity.SaveVideoChunkResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/save_video_chunk');
    const method = 'POST';
    const data = {
      uni_exam_id: _req['uni_exam_id'],
      video_uri: _req['video_uri'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/test/option
   *
   * 获取创建测试选项
   */
  ListCreateTeamExamTestOptions(
    req?: entity.ListCreateTeamExamTestOptionsReq,
    options?: T,
  ): Promise<entity.ListCreateTeamExamTestOptionsResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/examination/uni_exam/team_exam/test/option',
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/test/conflict_users/export
   *
   * 导出冲突用户记录
   */
  ExportConflictUsers(
    req?: entity.ExportConflictUsersReq,
    options?: T,
  ): Promise<entity.ExportConflictUsersResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/examination/uni_exam/team_exam/test/conflict_users/export',
    );
    const method = 'POST';
    const data = {
      team_exam_id: _req['team_exam_id'],
      filter: _req['filter'],
      email: _req['email'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/uni_exam/team_exam/test/user
   *
   * 更新用户考试关联记录
   */
  UpdateTeamExamUser(
    req?: entity.UpdateTeamExamUserReq,
    options?: T,
  ): Promise<entity.UpdateTeamExamUserResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/examination/uni_exam/team_exam/test/user',
    );
    const method = 'PUT';
    const data = {
      team_exam_id: _req['team_exam_id'],
      test_conflict_users: _req['test_conflict_users'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/record
   *
   * 团队考试记录列表
   */
  ListTeamExamRecord(
    req?: entity.ListTeamExamRecordReq,
    options?: T,
  ): Promise<entity.ListTeamExamRecordResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/team_exam/record');
    const method = 'POST';
    const data = {
      page: _req['page'],
      page_size: _req['page_size'],
      name: _req['name'],
      user_email: _req['user_email'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/magic/check
   *
   * 获取口语打分(调试)
   */
  MagicCheck(
    req?: entity.MagicCheckReq,
    options?: T,
  ): Promise<entity.MagicCheckResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/magic/check');
    const method = 'POST';
    const data = { content: _req['content'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/examination/uni_exam/question_group/nfn
   *
   * 更新n选n题目组
   */
  UpdateUniExamQuestionGroupNFN(
    req?: entity.CreateUniExamQuestionGroupNFNReq,
    options?: T,
  ): Promise<entity.CreateUniExamQuestionGroupNFNResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question_group/nfn');
    const method = 'PUT';
    const data = {
      paper_id: _req['paper_id'],
      question_group_id: _req['question_group_id'],
      content: _req['content'],
      audio_uri: _req['audio_uri'],
      image_uri: _req['image_uri'],
      option_contents: _req['option_contents'],
      questions: _req['questions'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/score/history
   *
   * 获取考试分数历史记录列表
   */
  ListUniexamScoreHistory(
    req?: entity.ListUniexamScoreHistoryReq,
    options?: T,
  ): Promise<entity.ListUniexamScoreHistoryResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/score/history');
    const method = 'POST';
    const data = { exam_id: _req['exam_id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/question_group/nfn
   *
   * 创建n选n题目组
   */
  CreateUniExamQuestionGroupNFN(
    req?: entity.CreateUniExamQuestionGroupNFNReq,
    options?: T,
  ): Promise<entity.CreateUniExamQuestionGroupNFNResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/examination/uni_exam/question_group/nfn');
    const method = 'POST';
    const data = {
      paper_id: _req['paper_id'],
      question_group_id: _req['question_group_id'],
      content: _req['content'],
      audio_uri: _req['audio_uri'],
      image_uri: _req['image_uri'],
      option_contents: _req['option_contents'],
      questions: _req['questions'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/examination/uni_exam/team_exam/remind/test
   *
   * 测试考试提醒
   */
  RemindTest(
    req?: entity.RemindTestReq,
    options?: T,
  ): Promise<entity.RemindTestResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/examination/uni_exam/team_exam/remind/test',
    );
    const method = 'POST';
    const data = { id: _req['id'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
