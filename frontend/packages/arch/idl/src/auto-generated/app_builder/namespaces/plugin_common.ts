/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum Auth {
  NO = 0,
  SERVICE = 1,
  USER = 2,
  OAUTH = 3,
}

export enum ConditionType {
  Equal = 1,
  NotEqual = 2,
  LengthGt = 3,
  LengthGtEqual = 4,
  LengthLt = 5,
  LengthLtEqual = 6,
  Contains = 7,
  NotContains = 8,
  Null = 9,
  NotNull = 10,
  True = 11,
  False = 12,
  Gt = 13,
  GtEqual = 14,
  Lt = 15,
  LtEqual = 16,
}

export enum IfConditionRelation {
  And = 1,
  Or = 2,
}

export enum InputType {
  String = 1,
  Integer = 2,
  Boolean = 3,
  Number = 4,
  Array = 5,
  Object = 6,
}

export enum InstallStatus {
  USING = 1,
  REMOVE = 2,
  OFFLINE = 3,
  /** 查询不传，展示用 */
  NOTINSTALL = 4,
}

export enum ModelType {
  GPT3dot5Turbo = 1,
  GPT4 = 2,
  Seed = 3,
  SeedMultiturn = 4,
  StableDiffusion = 5,
  ByteArtist = 6,
  Claude = 7,
  BingChat = 8,
  Bard = 9,
  Assistant = 10,
  DallE = 11,
  Midjourney = 12,
  TTSearch = 13,
  GPTTask = 14,
  GPT4Browsing = 15,
  GPT4WithPlugins = 16,
  GPT3dot5WithPlugins = 17,
  SeedSystem = 18,
  Plugin = 20,
  GPT4Plugin = 21,
  WebGPT = 22,
  GPT3dot5WithFunction = 23,
  AutoPlugin = 24,
  MiniMax = 25,
  GPT3dot5WithTako = 26,
  GPT4WithTako = 27,
  GPT4WithTakoTikTok = 28,
  GPT4WithTakoGoogle = 29,
  SeedBeta = 30,
  ByteArtistAnime = 31,
  GenMusic = 32,
  GPT48k = 33,
  SeedStrongCharacterForTest = 34,
  SeedForTest = 35,
  SeedOmniFake = 36,
  SeedOmniBrowse = 37,
  SeedOmniSota = 38,
  SeedOmniSotaBrowse = 39,
  SeedOmniBrowseWithCard = 40,
  CiciSearchMore = 41,
  CiciSearchLess = 42,
  CiciSearchBing = 43,
  SeedOmniPlugin = 44,
  CiciSearchBing_GPT35 = 45,
  GPT40613 = 46,
  SeedForMap = 47,
  SeedForMusic = 48,
  XiaoningWithMem = 49,
  /** 功能性bot的模型从100开始，旧版bot的模型在100以下  <EMAIL> */
  GPT_35Turbo = 100,
  GPT_35Turbo0301 = 101,
  GPT_4 = 102,
  GPT_40314 = 103,
  GPT_432k = 104,
  GPT_432k0314 = 105,
  /** seed 主模型 */
  Seed_Beta = 106,
  Seed_Music = 107,
  GPT4_32k0613 = 108,
  Seed_Img2Text = 109,
  /** seed主模型 + browsing */
  Seed_WebGPT = 110,
  /** seed主模型 + browsing + (plugin暂为开放) */
  Seed_WithPlugins = 111,
  GPT_35Turbo0613 = 112,
  GPT_35Turbo16K = 113,
  /** sota */
  Seed_Sota = 114,
  /** sota + browsing */
  Seed_Sota_Browsing = 115,
  /** strong character */
  Seed_WithSystem = 116,
  /** 三合一, 主模型 + plugin + browsing, 但plugin是固定的 */
  Seed_Omni = 118,
  /** seed function calling */
  SeedFunctionCall = 119,
  /** seed 主模型， 带"是的你说的对" */
  Seed_Main = 120,
  /** 联网策略实验 */
  SeedBrowsingExperiment = 121,
  /** browsing单模型 */
  SeedBrowsingOnly = 122,
  /** 虚假的 llm，function call 协议，一直调用第一个 function */
  Echo = 123,
  GPT_40613 = 124,
  /** for seed test */
  Seed_WithSystemForTest = 125,
  /** 地图 */
  Seed_Map = 126,
  SeedSuggest = 127,
  Seed_ComplexInstructionTest = 128,
  Seed_ComplexInstruction = 129,
  Edu = 130,
  Tako_Intent = 131,
  Seed_Text2Img = 132,
  GPT_4Preview_128k1106 = 133,
  SeedForDouyin = 134,
  SeedFunctionCallSp = 135,
  SeedSuggestOffline = 136,
  SkylarkChat = 137,
  SeedStrongCharacterNearlinePortrait = 138,
}

export enum NodeType {
  Start = 1,
  End = 2,
  LLM = 3,
  Api = 4,
  Code = 5,
  Dataset = 6,
  Input = 7,
  If = 8,
}

export enum OrderBy {
  CreateTime = 0,
  UpdateTime = 1,
  Hot = 3,
}

export enum ParameterLocation {
  Path = 1,
  Query = 2,
  Body = 3,
  Header = 4,
}

export enum ParamRequirementType {
  CanNotDelete = 1,
  CanNotChangeName = 2,
  CanChange = 3,
  CanNotChangeAnything = 4,
}

export enum PluginStatus {
  SUBMITTED = 1,
  REVIEWING = 2,
  PREPARED = 3,
  PUBLISHED = 4,
  OFFLINE = 5,
}

export enum PluginType {
  PLUGIN = 1,
  APP = 2,
  FUNC = 3,
  WORKFLOW = 4,
}

export enum SchemaType {
  DAG = 0,
  FDL = 1,
}

export enum ScopeType {
  /** 所有 */
  All = 0,
  /** 自己 */
  Self = 1,
}

export enum Tag {
  All = 1,
  Hot = 2,
  Information = 3,
  Music = 4,
  Picture = 5,
  UtilityTool = 6,
  Life = 7,
  Traval = 8,
  Network = 9,
  System = 10,
  Movie = 11,
  Office = 12,
  Shopping = 13,
  Education = 14,
  Health = 15,
  Social = 16,
  Entertainment = 17,
  Finance = 18,
  Hidden = 100,
}

export enum TerminatePlanType {
  USELLM = 1,
  USESETTING = 2,
}

/** 状态，1不可发布 2可发布  3已发布 4删除 */
export enum WorkFlowStatus {
  CanNotPublish = 1,
  CanPublish = 2,
  HadPublished = 3,
  Deleted = 4,
}
/* eslint-enable */
