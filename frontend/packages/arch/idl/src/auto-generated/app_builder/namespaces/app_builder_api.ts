/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as user from './user';
import * as filebox from './filebox';
import * as starry from './starry';

export type Int64 = string | number;

/** 场景 */
export enum AIGenScene {
  /** 对应prompt平台任务：AgentAppBuilderWorflowGen */
  Workflow = 0,
  /** 对应prompt平台任务：AgentAppBuilderPageGen */
  Page = 1,
  /** 对应prompt平台任务：AgentAppBuilderSystemGen */
  SystemPrompt = 2,
  Requirement = 3,
  Database = 4,
}

export enum FieldItemType {
  /** 文本（对应 mysql 的 varchar） */
  Text = 1,
  /** 数字 */
  Number = 2,
  /** 时间 */
  Date = 3,
  /** float */
  Float = 4,
  /** bool */
  Boolean = 5,
  /** 文本（对应 mysql 的 text） */
  FullText = 6,
}

export enum MessageType {
  System = 1,
  User = 2,
  Assistant = 3,
  Placeholder = 4,
}

export interface AddDatabaseData {
  table_ids?: Array<string>;
}

export interface AddDatabaseRequest {
  /** bot_id 绑定的bot */
  bot_id?: string;
  /** 创建者id */
  user_id?: string;
  /** bot空间id */
  'space-id'?: string;
  /** 表信息 */
  table_list?: Array<TableInfo>;
  Base?: base.Base;
}

export interface AddDatabaseResponse {
  data?: AddDatabaseData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface AgentAppBuilderProxyRequest {
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface AgentAppBuilderProxyResponse {
  data?: string;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface AgentAppInfoData {
  /** agent app id */
  agent_app_id?: string;
  agent_app_name?: string;
  icon?: string;
  type_source_app_id?: string;
  /** space id */
  space_id?: string;
  /** bot id */
  bot_id?: string;
  /** 星夜 app id */
  app_id?: string;
  /** 星夜 sandbox id */
  sandbox_id?: string;
  /** 星夜 snapshot id */
  snapshot_id?: string;
  /** 创建人 ID */
  creator_id?: string;
  /** 创建时间戳秒 */
  created_at?: string;
  /** 修改时间戳秒 */
  updated_at?: string;
  /** version_name xxx.xxx.xxx */
  version_name?: string;
  /** 已发布的最大版本号 xxx.xxx.xxx */
  max_publish_version_name?: string;
  /** local plugin id */
  plugin_id?: string;
}

export interface AgentAppPublishInfo {
  /** id 主键 */
  publish_id?: string;
  /** agent_app_id */
  agent_app_id?: string;
  /** space id */
  space_id?: string;
  /** bot id */
  bot_id?: string;
  /** 星夜 app id */
  app_id?: string;
  /** 快照 ID */
  snapshot_id?: string;
  /** version name xxx.xxx.xxx */
  version_name?: string;
  /** version code */
  version_code?: Int64;
  /** 描述 */
  description?: string;
  /** content url */
  content_url?: string;
  /** 发布者信息 */
  creator?: user.UserBasicInfo;
  /** 创建时间 */
  created_at?: Int64;
  /** 修改时间 */
  updated_at?: Int64;
  /** 已发布的最大版本号 xxx.xxx.xxx */
  max_publish_version_name?: string;
  /** 发布状态 */
  status?: number;
  /** 流水线 ID */
  bytecycle_id?: string;
  /** 流水线 build_no */
  build_no?: number;
}

export interface AIGenerateContentData {
  /** 生成的内容，不同的场景内容不同，但最终都是一段string */
  content?: string;
}

export interface AIGenerateContentRequest {
  /** 场景参数，对应prompt平台任务 */
  scene?: AIGenScene;
  /** 空间id */
  'space-id'?: string;
  /** user_id 配合空间id鉴权 */
  user_id?: string;
  /** prompt平台有3种变量：【文本消息】【消息列表】【struct】
文本消息，key = 变量名 value = 变量值
如： language = english */
  string_variables?: Record<string, string>;
  /** 消息列表，key = 变量名 value = 消息list */
  message_variables?: Record<string, Array<Message>>;
  Base?: base.Base;
}

export interface AIGenerateContentResponse {
  data?: AIGenerateContentData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface BatchGetPackageListRequest {
  /** 查询结构 */
  query?: Array<GetPackageQuery>;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface BatchGetPackageListResponse {
  /** package_list */
  data?: GetPackageListData;
  code: Int64;
  msg: string;
  /** 基本响应结果 */
  BaseResp?: base.BaseResp;
}

export interface CreateAgentAppData {
  /** agent app id */
  agent_app_id?: string;
}

export interface CreateAgentAppRequest {
  agent_app_name?: string;
  icon?: string;
  type_source_app_id?: string;
  /** space id */
  space_id?: string;
  /** bot id */
  bot_id?: string;
  /** 星夜 App ID, 不接受传参 */
  AppID?: string;
  /** 星夜 SandBox ID, 不接受传参 */
  SandboxID?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface CreateAgentAppResponse {
  data?: CreateAgentAppData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CreateLocalPluginData {
  /** plugin id */
  plugin_id?: string;
}

export interface CreateLocalPluginRequest {
  agent_app_id?: string;
  /** ai_plugin (填写 json) */
  ai_plugin?: string;
  /** openapi (填写 yaml) */
  openapi?: string;
  'X-Space-Id': string;
  Cookie?: string;
  Base?: base.Base;
}

export interface CreateLocalPluginResponse {
  data?: CreateLocalPluginData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CreatePackageData {
  package_id?: string;
}

export interface CreatePackageRequest {
  /** 1:            i64       PackageID   (agw.key = 'package_id',agw.js_conv = 'str'), */
  package_name: string;
  space_id?: string;
  bot_id?: string;
  version_name: string;
  meta_title?: string;
  meta_desc?: string;
  meta_contains?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface CreatePackageResponse {
  /** agent app info */
  data?: CreatePackageData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DeletePackageRequest {
  /** 包名 */
  package_name?: string;
  /** 版本号 */
  version_name?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface DeletePackageResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DoActionRequest {
  /** 透传给 plugin */
  plugin_id: string;
  /** 透传给 plugin */
  api_name: string;
  /** bot 的 space id，不知道就不填。 */
  user_id?: string;
  /** 透传给 plugin */
  parameters?: string;
  /** 目前的消息ID */
  message_id?: string;
  plugin_name?: string;
  device_id?: string;
  /** 额外信息 */
  ext?: Record<string, string>;
  /** 输出token限制 */
  output_token_limit?: string;
  section_id?: string;
  Base?: base.Base;
}

export interface DoActionResponse {
  data?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface FieldDefault {
  default_text?: string;
  default_number?: NumberDefault;
  /** 时间，timestamp */
  default_date?: string;
  default_float?: number;
  default_bool?: boolean;
}

export interface FieldItem {
  name?: string;
  desc?: string;
  type?: FieldItemType;
  must_required?: boolean;
  primary_key?: boolean;
  is_visible?: boolean;
  default_value?: FieldDefault;
  alter_id?: string;
}

export interface GenAgentAppData {
  /** 创建的agent_id */
  agent_id?: string;
}

export interface GenAgentAppRequest {
  /** bot_id 绑定的bot */
  bot_id?: string;
  /** 星夜sandbox数据 */
  sandbox?: string;
  /** agent_name 目前bot名称相同 */
  name?: string;
  /** 创建者id */
  user_id?: string;
  /** bot空间id */
  'space-id'?: string;
  /** app 类型 */
  agent_type?: number;
  Base?: base.Base;
}

export interface GenAgentAppResponse {
  data?: GenAgentAppData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetAgentAppPublishListData {
  /** publish info list */
  publish_list?: Array<AgentAppPublishInfo>;
  /** 总数 */
  total?: Int64;
}

export interface GetAgentAppPublishListRequest {
  /** publish id */
  publish_id?: Int64;
  /** agent app id */
  agent_app_id?: Int64;
  /** agent app name */
  agent_app_name?: string;
  /** bot id */
  bot_id?: Int64;
  /** space id */
  space_id?: Int64;
  /** 当前页码，默认为 1 */
  page?: number;
  /** 每页显示数量，默认为 20 */
  size?: number;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface GetAgentAppPublishListResponse {
  data?: GetAgentAppPublishListData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetAgentAppPublishRequest {
  /** publish id */
  publish_id?: Int64;
  /** app id */
  app_id?: string;
  /** bot id */
  bot_id?: Int64;
  /** with_max_version */
  with_max_version?: boolean;
  /** agent app id */
  agent_app_id?: Int64;
  /** space id */
  space_id?: Int64;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface GetAgentAppPublishResponse {
  data?: AgentAppPublishInfo;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetAgentAppRequest {
  /** agent app id */
  agent_app_id?: Int64;
  /** space id */
  space_id?: Int64;
  /** bot id */
  bot_id?: Int64;
  /** version name ：eg ：xxx.xxx.xxx */
  version_name?: string;
  /** 星夜 app id */
  app_id?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface GetAgentAppResponse {
  /** agent app info */
  data?: AgentAppInfoData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetFileListRequest {
  /** 资源所属botId - 必传 */
  bot_id: string;
  /** 2:  required string     ConnectorID (agw.source = "query",  agw.key = "connector_id",   agw.js_conv = 'str'),   // 资源所属connectorId - 必传
3:  required i64        UserID      (agw.source = "query",  agw.key = "user_id",        agw.js_conv = 'str'),   // 用户id - 必传
文件名, 模糊搜索 */
  file_name?: string;
  /** 文件大类, 精确匹配 */
  md_type?: number;
  /** 文件格式, 模糊搜索 */
  file_format?: string;
  /** 查询起始时间戳, 单位秒, 与EndTime同时为0时表示不启用该查询条件 */
  begin_time?: string;
  /** 查询结束时间戳, 单位秒, 与BeginTime同时为0时表示不启用该查询条件 */
  end_time?: string;
  /** 页数, 从0开始 */
  page?: number;
  /** 每页大小, 默认10 */
  size?: number;
  Base?: base.Base;
}

export interface GetFileListResponse {
  /** 符合查询条件的总数 */
  total?: number;
  /** 查询结果 */
  data?: Array<filebox.FileListRPCMeta>;
  code: Int64;
  msg: string;
  /** 错误信息 */
  BaseResp?: base.BaseResp;
}

export interface GetFileMetaInfoRequest {
  /** 1:  required i64    UserID      (agw.source = 'query', agw.key = 'user_id',     agw.js_conv = 'str'),   // 用户userId */
  bot_id: string;
  /** 3:  optional string ConnectorID (agw.source = "query", agw.key = "connector_id",agw.js_conv = 'str'), */
  file_id?: string;
  file_uri?: string;
  file_name?: string;
  biz_type?: string;
  Base?: base.Base;
}

export interface GetFileMetaInfoResponse {
  data?: filebox.FileMetaInfo;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetFileURLRequest {
  /** 资源 URI 必传 */
  uri: string;
  Base?: base.Base;
}

export interface GetFileURLResponse {
  /** 查询结果 */
  data: filebox.FileURLData;
  code: Int64;
  msg: string;
  /** 错误信息 */
  BaseResp?: base.BaseResp;
}

export interface GetListFileChunkRequest {
  /** 1:  required i64        UserID          (agw.source = "query", agw.key = "user_id",     agw.js_conv = 'str'),   // 用户userId */
  bot_id: string;
  /** 3:  optional string     ConnectorID     (agw.source = "query", agw.key = "connector_id",agw.js_conv = 'str'), */
  biz_type?: string;
  file_id?: string;
  file_uri?: string;
  /** 包括该seq_id, 从0开始 */
  start_chunk_seq_id?: number;
  /** 包括该seq_id, 一次最多取50个chunk。end_chunk_seq_id - start_chunk_seq_id + 1 <= 50 */
  end_chunk_seq_id?: number;
  Base?: base.Base;
}

export interface GetListFileChunkResponse {
  data?: Array<filebox.FileChunk>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetPackageData {
  /** id 主键 */
  id?: string;
  space_id?: Int64;
  package_name?: string;
  version_name?: string;
  version_code?: string;
  is_latest_version?: number;
  meta_title?: string;
  meta_desc?: string;
  meta_contains?: string;
  /** 发布者信息 */
  creator?: user.UserBasicInfo;
  /** 创建时间 */
  created_at?: Int64;
  /** 修改时间 */
  updated_at?: Int64;
}

export interface GetPackageListData {
  /** package_list */
  package_list?: Array<GetPackageData>;
  /** total */
  total?: Int64;
}

export interface GetPackageListRequest {
  /** 1:   string         PackageID   (agw.source = 'query',  agw.key = 'package_id')                     ,   // package_id
包名 */
  package_name?: string;
  /** 版本号 */
  version_name?: string;
  /** 当前页码，默认为 1 */
  page?: number;
  /** 每页显示数量，默认为 10 */
  size?: number;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface GetPackageListResponse {
  /** package_list */
  data?: GetPackageListData;
  code: Int64;
  msg: string;
  /** 基本响应结果 */
  BaseResp?: base.BaseResp;
}

export interface GetPackageQuery {
  /** 包名 */
  package_name?: string;
  /** 版本号 */
  version_name?: string;
}

export interface GetPackageRequest {
  /** 包名 */
  package_name?: string;
  /** 版本号 */
  version_name?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface GetPackageResponse {
  /** agent app info */
  data?: GetPackageData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetPackageVersionListData {
  /** version list */
  version_list?: Array<string>;
  /** total */
  total?: Int64;
}

export interface GetPackageVersionListRequest {
  /** 1:   string         PackageID   (agw.source = 'query',  agw.key = 'package_id')                     ,   // package_id
包名 */
  package_name?: string;
  /** 当前页码，默认为 1 */
  page?: number;
  /** 每页显示数量，默认为 20 */
  size?: number;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface GetPackageVersionListResponse {
  /** version list */
  data?: GetPackageVersionListData;
  code: Int64;
  msg: string;
  /** 基本响应结果 */
  BaseResp?: base.BaseResp;
}

export interface Message {
  type?: MessageType;
  content?: string;
}

export interface NumberDefault {
  auto_increment?: boolean;
  value?: string;
}

export interface PreviewAgentAppData {
  Content: string;
}

export interface PreviewAgentAppRequest {
  agent_app_id?: string;
  'use-builder-psm'?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface PreviewAgentAppResponse {
  data?: PreviewAgentAppData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface PublishAgentAppData {
  /** publish id */
  publish_id?: string;
  /** 发布状态 */
  status?: number;
}

export interface PublishAgentAppRequest {
  /** agent app id */
  agent_app_id?: string;
  /** agent app name */
  agent_app_name?: string;
  /** 发布描述 */
  remark?: string;
  /** version_name xxx.xxx.xxx */
  version_name?: string;
  /** page id 数组 */
  page_ids?: Array<string>;
  /** 是否发布到豆包 */
  to_doubao?: boolean;
  'X-Space-Id'?: string;
  Cookie?: string;
  Base?: base.Base;
}

export interface PublishAgentAppResponse {
  data?: PublishAgentAppData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface SearchFileRequest {
  /** 1:  required i64            UserID      (agw.source = "query",  agw.key = "user_id",     agw.js_conv = 'str')   ,  // 用户userId */
  bot_id: string;
  /** 3:  optional string         ConnectorID (agw.source = "query",  agw.key = "connector_id",agw.js_conv = 'str')   , */
  query?: string;
  file_uri_list?: Array<string>;
  file_name_list?: Array<string>;
  Base?: base.Base;
}

export interface SearchFileResponse {
  data?: Array<filebox.FileSearchItem>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface StarryClonePageRequest {
  sandbox_id: string;
  app_id?: string;
  page_id: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryClonePageResponse {
  Data?: Array<starry.IPage>;
  BaseResp?: base.BaseResp;
}

export interface StarryCreatePageRequest {
  sandbox_id?: string;
  app_id?: string;
  page_id?: string;
  data?: string;
  exts?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryCreatePageResponse {
  Data?: starry.IPage;
  BaseResp?: base.BaseResp;
}

export interface StarryCreateSnapshotRequest {
  sandbox_id?: string;
  desc?: string;
  page_ids?: Array<string>;
  previous_snapshot_id?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryCreateSnapshotResponse {
  snapshotId?: string;
  version?: string;
  BaseResp?: base.BaseResp;
}

export interface StarryDeletePageRequest {
  sandbox_id?: string;
  page_id?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryDeletePageResponse {
  deletedCount?: number;
  BaseResp?: base.BaseResp;
}

export interface StarryGetCompSetsRequest {
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetCompSetsResponse {
  data?: string;
  BaseResp?: base.BaseResp;
}

export interface StarryGetPageRequest {
  sandbox_id?: string;
  page_id?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetPageResponse {
  Data?: starry.IPage;
  BaseResp?: base.BaseResp;
}

export interface StarryGetPreviewDataRequest {
  sandbox_id?: string;
  page_id?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetPreviewDataResponse {
  Sandbox?: starry.ISandbox;
  Page?: starry.IPage;
  BaseResp?: base.BaseResp;
}

export interface StarryGetSandboxRequest {
  sandbox_id?: string;
  with_app?: boolean;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetSandboxResponse {
  Sandbox?: starry.ISandbox;
  App?: starry.IApp;
  BaseResp?: base.BaseResp;
}

export interface StarryGetSandboxSnapshotRequest {
  sandbox_id?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetSandboxSnapshotResponse {
  Data?: string;
  BaseResp?: base.BaseResp;
}

export interface StarryGetSnapshotRequest {
  snapshot_id?: string;
  sandbox_id?: string;
  version?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetSnapshotResponse {
  Data?: string;
  BaseResp?: base.BaseResp;
}

export interface StarryRestoreSnapshotRequest {
  snapshot_id: string;
  sandbox_id: string;
  action?: string;
  version?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryRestoreSnapshotResponse {
  sandbox?: starry.ISandbox;
  pages?: Array<string>;
  BaseResp?: base.BaseResp;
}

export interface StarryUpdatePageRequest {
  sandbox_id?: string;
  page_id?: string;
  crdt_history?: string;
  action?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryUpdatePageResponse {
  actionId?: string;
  page?: string;
  BaseResp?: base.BaseResp;
}

export interface StarryUpdateSandboxRequest {
  sandbox_id?: string;
  crdt_history?: string;
  action?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface StarryUpdateSandboxResponse {
  actionId?: string;
  sandbox?: string;
  BaseResp?: base.BaseResp;
}

export interface SummaryFileRequest {
  bot_id?: string;
  /** 文件Key */
  file_key?: string;
  base?: base.Base;
}

export interface SummaryFileResponse {
  SummaryInfo?: SummaryInfo;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface SummaryInfo {
  file_name: string;
  /** 总结内容 */
  content: string;
}

export interface TableInfo {
  /** database名称 */
  name?: string;
  /** database描述 */
  desc?: string;
  /** 字段配置 */
  field_list?: Array<FieldItem>;
  /** 模式 */
  rw_mode?: number;
}

export interface UpdateAgentAppData {
  /** agent app id */
  agent_app_id?: string;
}

export interface UpdateAgentAppRequest {
  /** agent app id */
  agent_app_id?: string;
  agent_app_name?: string;
  icon?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface UpdateAgentAppResponse {
  data?: UpdateAgentAppData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UpdateAgentBotData {
  workflow_list?: Array<WorkflowData>;
}

export interface UpdateAgentBotRequest {
  /** bot_id 更新bot参数 */
  bot_id?: string;
  /** bot workflow schema，关联bot的workflow列表 */
  bot_workflow_list?: Array<WorkflowData>;
  /** bot的prompt */
  system_prompt?: string;
  /** user_id 更新bot参数 */
  user_id?: string;
  /** space_id 更新bot参数 */
  'space-id'?: string;
  /** agent workflow shcema，只关联agent的workflow列表 */
  agent_workflow_list?: Array<WorkflowData>;
  /** table ids 表的id列表 */
  table_ids?: Array<string>;
  Base?: base.Base;
}

export interface UpdateAgentBotResponse {
  data?: UpdateAgentBotData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UpdateLocalPluginData {}

export interface UpdateLocalPluginRequest {
  agent_app_id?: string;
  /** ai_plugin (填写 json) */
  ai_plugin?: string;
  /** openapi (填写 yaml) */
  openapi?: string;
  client_id?: string;
  client_secret?: string;
  service_token?: string;
  'X-Space-Id': string;
  Cookie?: string;
  Base?: base.Base;
}

export interface UpdateLocalPluginResponse {
  data?: UpdateLocalPluginData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UpdatePackageRequest {
  package_id?: string;
  package_name: string;
  /** 3:            i64       SpaceID     (agw.source = 'body',   agw.key = 'space_id',  agw.js_conv = 'str') ,
4:            i64       BotID       (agw.source = 'body',   agw.key = 'bot_id',    agw.js_conv = 'str') , */
  version_name: string;
  meta_title?: string;
  meta_desc?: string;
  meta_contains?: string;
  'X-Space-Id'?: string;
  Base?: base.Base;
}

export interface UpdatePackageResponse {
  /** agent app info */
  data?: CreatePackageData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UploadFileRequest {
  /** 文件名 */
  file_name?: string;
  /** 资源URI */
  source_uri?: string;
  /** botid */
  bot_id?: string;
  base?: base.Base;
}

export interface UploadFileResponse {
  data?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface WorkflowData {
  /** workflow_id */
  id?: string;
  /** workflow名称 */
  name?: string;
  /** workflow描述 */
  desc?: string;
  /** workflow schema */
  schema?: string;
  /** workflw图标uri */
  icon_uri?: string;
  /** workflow关联的plugin_id */
  plugin_id?: string;
  /** workflow入参列表，一个参数包含: name,desc,required,type,sub_parameters,sub_type */
  param?: string;
  /** workflowc出参列表 */
  outputs?: string;
}
/* eslint-enable */
