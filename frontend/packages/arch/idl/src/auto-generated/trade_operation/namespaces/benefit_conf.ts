/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface PublicCommSetConfBenefitRequest {
  /** 不传仅返回用户信息 */
  benefit_type?: common.BenefitType;
  /** 必填。这里指的是Coze的AccountID */
  coze_account_id?: string;
  coze_account_type?: common.CozeAccountType;
  enterprise_id?: string;
  counter?: common.CommonCounter;
}

export interface PublicCommSetConfBenefitResponse {
  code: number;
  message: string;
}
/* eslint-enable */
