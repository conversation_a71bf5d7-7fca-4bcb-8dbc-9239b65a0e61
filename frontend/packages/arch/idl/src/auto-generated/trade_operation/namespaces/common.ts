/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AccountStatus {
  Available = 1,
  Unavailable = 2,
}

/** 协议状态 */
export enum AgreementStatus {
  /** 停用 */
  Closed = 0,
  /** 有效 */
  InEffect = 1,
}

export enum AmountType {
  /** 日后可以拓展为满减、sku 数量折扣等 */
  Unknown = 0,
  ChargeAmount = 1,
}

/** 权益类型。同 benefit_common.thrift 对齐。此处用于业务层使用。 */
export enum BenefitType {
  /** 海外 */
  SubsMessageCredit = 1,
  UserFreeChat = 2,
  TopUpMessageCredit = 3,
  BonusMessageCredit = 4,
  /** 40 -59 免费次数 */
  Freetimes = 40,
  /** 评测免费次数 */
  EvaluateFree = 41,
  /** Workflow 测试运行免费次数 */
  WorkflowTestRunFree = 42,
  /** App 测试运行免费次数 */
  AppTestRunFree = 43,
  /** Plugin 测试运行免费次数 */
  PluginRunFree = 44,
  /** API 运行免费次数 */
  APIRunFree = 45,
  /** SDK 运行免费次数 */
  SDKRunFree = 46,
  /** 60 - 99 限流
模型 RPM 限流 */
  RateLimitModelRPM = 60,
  /** 模型 Input TPM 限流 */
  RateLimitModelInputTPM = 61,
  /** 模型 Output TPM 限流 */
  RateLimitModelOutputTPM = 62,
  /** 基础模型 Input TPM 限流 */
  RateLimitModelInputTPMBasic = 63,
  /** 基础模型 Output TPM 限流 */
  RateLimitModelOutputTPMBasic = 64,
  /** Plugin 运行 QPS 限流 */
  PluginRunQPS = 65,
  /** Plugin 运行并发度限流 */
  PluginRunParallel = 66,
  /** 图像节点
Workflow 运行 QPS 限流 */
  WorkflowRunQPS = 67,
  /** Workflow 运行并发度限流 */
  WorkflowRunParallel = 68,
  /** API 运行 QPS 限流 */
  APIRunQPS = 70,
  /** 语音 QPS 限流 */
  VoiceQPS = 71,
  /** 语音并发度限流 */
  VoiceParallel = 72,
  /** 100-109 资源点
资源点总量 */
  ResourcePoint = 100,
  /** 免费资源点，废弃 */
  FreeResourcePoint = 101,
  /** 火山购买的资源点 */
  VolcProResourcePoint = 102,
  /** 周期性资源点 */
  PeriodicResourcePoint = 103,
  /** 渠道递减资源点 */
  ChannelResourcePoint = 104,
  /** 试算资源点 */
  CutAndTryResourcePoint = 109,
  /** 110-129 Fornax
Trace 用量 */
  TraceAmount = 111,
  /** Trace 存储时长 */
  TraceStorageDuration = 112,
  /** 130-149 WorkSpace
Space 总量 */
  SpaceAmount = 131,
  /** Space 人数 */
  SpacePeopleNumber = 132,
  /** Space 下协作者人数 */
  SpaceCollaboratorNumber = 133,
  /** Space 下协作实体数量 */
  SpaceCollabEntityNumber = 134,
  /** 150-169 运维
日志存储时长 */
  LogStorageDuration = 151,
  /** 日志导出 */
  LogExport = 152,
  /** 170-179 知识库
知识库容量 */
  Capacity = 170,
  /** 180-199 语音
音色克隆总数 */
  VoiceCloneNumber = 180,
  /** 音色克隆基础数量 */
  VoiceCloneNumberBasic = 181,
  /** 200-219 租户相关
席位数上限 */
  SeatNumberLimit = 200,
  /** 基础席位数 */
  SeatNumberBasic = 201,
  /** 移除水印 */
  RemoveWatermark = 220,
  /** 240-269 配置
安心用 */
  ConfidenceUsing = 240,
}

export enum BenefitUseMode {
  /** 按额度使用 */
  ByQuota = 1,
  /** 无限使用 */
  Unlimited = 2,
  /** 不可用 */
  UnAvailable = 10,
}

export enum ChargeableEntityType {
  Unknown = 0,
  Agent = 1,
  Model = 10,
  ModelTPM = 11,
  Plugin = 20,
  Imageflow = 21,
  Voice = 30,
  RealTime = 40,
  Knowledge = 50,
  Seat = 60,
}

/** 支付侧拒付状态 */
export enum ChargebackPayStatus {
  /** 未知 */
  Unknown = 0,
  /** 发生拒付 */
  Chargeback = 1,
  /** 已拒付返还 */
  ChargebackReverse = 2,
}

/** 拒付状态 */
export enum ChargebackStatus {
  /** 未知 */
  Unknown = 0,
  /** 发生拒付 */
  Chargeback = 1,
  /** 已拒付返还 */
  ChargebackReverse = 2,
}

export enum ChargeScene {
  Unknown = 0,
  Token = 1,
  Credit = 2,
}

export enum CozeAccountType {
  /** 未知 */
  Unknown = 0,
  /** 组织账号 */
  Organization = 1,
  /** 个人账号 */
  Personal = 2,
}

export enum CreditChangeType {
  TopUp = 1,
  Bonus = 2,
  /** 智能体 */
  BotConsume = 3,
  WorkflowConsume = 4,
  EvaluateConsume = 5,
  /** 应用 */
  ProjectConsume = 6,
  /** 扣子罗盘 */
  FornaxConsume = 7,
  /** 智能语音 */
  IntelligentVoiceConsume = 8,
  Expired = 20,
}

export enum CreditType {
  /** 海外 */
  Premium = 1,
  TopUp = 2,
  Bonus = 3,
  /** 国内
免费资源点 */
  FreePoint = 100,
  /** 充值资源点 */
  TopUpPonit = 101,
}

export enum DiscountCalculationType {
  Unknown = 0,
  Multiplication = 1,
  Subtraction = 2,
}

export enum InstanceLimitStatus {
  /** 未受限 */
  UnLimited = 1,
  /** 受限中（欠费） */
  Limited = 2,
}

export enum InstanceStatus {
  /** 创建中, 理论上不会返回该状态 */
  InstanceStatusCreating = 0,
  /** 运行中 */
  Running = 1,
  /** 创建失败, 理论上不会返回该状态 */
  InstanceStatusFailed = 2,
  /** 退订回收 */
  UnsubsRecycled = 3,
  /** 到期关停 */
  ExpiredClosed = 4,
  /** 到期回收 */
  ExpiredRecycled = 5,
  /** 欠费关停 */
  InstanceStatusOverdueShutdown = 6,
  /** 欠费回收 */
  InstanceStatusOverdueRecycled = 7,
  /** 退订关停 */
  InstanceStatusTerminatedShutdown = 8,
}

/** 订单逆向状态 */
export enum OrderReverseStatus {
  /** 未知 */
  Unknown = 0,
  /** 退款中 */
  RefundProcessing = 1,
  /** 退款成功 */
  RefundSuccess = 2,
  /** 退款失败 */
  RefundFailed = 3,
  /** 发生拒付 */
  Chargeback = 11,
  /** 已拒付返还 */
  ChargebackReverse = 12,
}

/** 订单状态 */
export enum OrderStatus {
  /** 初始化 */
  Init = 0,
  /** 成功 */
  Success = 1,
  /** 失败 */
  Failed = 2,
  /** 关闭 */
  Closed = 3,
}

/** 订单类型 */
export enum OrderType {
  /** 未知 */
  Unknown = 0,
  /** Token充值 */
  TokenCharge = 1,
  /** Token自动充值 */
  TokenAutoCharge = 2,
  /** MessageCredit订阅 */
  SubMessageCredit = 11,
  /** MessageCredit充值 */
  MessageCredit = 12,
  /** 模板购买 */
  PurchaseTemplate = 21,
}

/** 支付状态 */
export enum PayStatus {
  /** 初始状态，未完成支付 */
  Init = 0,
  /** 支付成功，这是最终状态 */
  Success = 1,
  /** 支付失败或关闭，这是最终状态 */
  Closed = 2,
}

/** 支付侧退款状态 */
export enum RefundByType {
  /** 业务平台发起 */
  ByBizPlatform = 0,
  /** 用户发起 */
  ByUser = 1,
}

/** 支付侧退款状态 */
export enum RefundPayStatus {
  /** 退款中 */
  Processing = 0,
  /** 退款成功 */
  Success = 1,
  /** 退款失败 */
  Failed = 2,
}

/** 退款状态 */
export enum RefundStatus {
  /** 退款中 */
  Processing = 0,
  /** 退款成功 */
  Success = 1,
  /** 退款失败 */
  Failed = 2,
}

export enum ResourcePackageType {
  /** 模型TPM */
  ModelTPM = 1,
  /** 音色克隆 */
  VoiceClone = 21,
}

export enum ResourceUsageStrategy {
  /** 无限制 */
  UnLimit = 1,
  /** 限制 */
  Forbidden = 2,
  /** 通过额度校验 */
  ByQuota = 3,
}

/** 业务场景 */
export enum Scene {
  /** 未知 */
  Unknown = 0,
  /** Token充值 */
  TokenCharge = 1,
  /** Token自动充值 */
  TokenAutoCharge = 2,
  /** MessageCredit订阅 */
  SubMessageCredit = 11,
  /** MessageCredit充值 */
  MessageCredit = 12,
  /** 模板购买 */
  PurchaseTemplate = 21,
  /** 音色克隆 */
  VoiceClone = 100,
  /** 音色克隆存储 */
  VoiceCloneStorage = 101,
}

export enum SubscriptionRenewalType {
  Unknown = 0,
  /** 手动续费 */
  ManualRenewal = 1,
  /** 自动续费 */
  AutoRenewal = 2,
  /** 到期不续费续费 */
  DontRenewal = 3,
}

export enum SubscriptionStatus {
  /** 初始化 */
  Init = 0,
  /** 待支付 */
  Pending = 1,
  /** 订阅中 */
  SUBSCRIBED = 2,
  /** 已取消 */
  CANCELED = 3,
  /** 已终止 */
  REVOKE = 4,
  /** 已关闭 */
  Closed = 5,
  /** 已过期 */
  Expired = 6,
  /** 欠费 */
  InDebt = 7,
}

export enum SubscriptionType {
  MessageCredit = 0,
}

/** 订阅 SKU 级别 */
export enum SubsMsgCreditLevel {
  Free = 0,
  PremiumLite = 10,
  Premium = 15,
  PremiumPlus = 20,
}

export enum SubsSKUType {
  /** 自动续费 */
  AutoRenew = 0,
  /** 一次性订阅 */
  OneOff = 1,
}

export enum TradeSourceType {
  /** 未知 */
  Unknown = 0,
  Bot = 1,
}

export enum UserLevel {
  /** 免费版。 */
  Free = 0,
  /** 海外
PremiumLite */
  PremiumLite = 10,
  /** Premium */
  Premium = 15,
  PremiumPlus = 20,
  /** 国内
V1火山专业版 */
  V1ProInstance = 100,
  /** 个人旗舰版 */
  ProPersonal = 110,
  /** 团队版 */
  Team = 120,
  /** 企业版 */
  Enterprise = 130,
}

export enum VolcanoUserType {
  Unknown = 0,
  RootUser = 1,
  BasicUser = 2,
}

export enum VolcInstanceType {
  /** 正常版本 */
  Normal = 1,
  /** 渠道版本 */
  Channel = 2,
}

export interface ChannelInfo {
  /** 渠道ID */
  channel_id: string;
  /** 渠道名称 */
  channel_name: string;
  /** 渠道 icon */
  icon_url: string;
}

export interface CommonCounter {
  /** 当 Strategy == ByQuota 时, 表示已使用量, 若权益无相关用量数据则返回 0 */
  used?: number;
  /** 当 Strategy == ByQuota 时, 表示用量上限 */
  total?: number;
  /** 资源使用策略 */
  strategy?: ResourceUsageStrategy;
  /** 开始时间，单位秒 */
  start_at?: Int64;
  /** 结束时间，单位秒 */
  end_at?: Int64;
}
/* eslint-enable */
