/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum CallbackEvent {
  Unknown = 0,
  UpdateKnowledgeDocumentStatus = 1,
  DeleteDirtyKnowledgeDocumentIndex = 2,
  HandleTimeoutKnowledgeDocument = 3,
  RenewKnowledgeScheduleTasks = 4,
}

/** 暴露给用户的渠道 */
export enum Channel {
  Unknown = 0,
  Feishu = 1,
  Lark = 2,
  /** 后续删除 */
  TOS = 3,
}

export enum ChunkType {
  Unknown = 0,
  Auto = 1,
  Custom = 2,
}

export enum EmbeddingStatus {
  Unknown = 0,
  Success = 1,
  Processing = 2,
  Failure = 3,
}

export enum FeishuObjectType {
  Unknown = 0,
  Doc = 1,
  Docx = 2,
  Sheet = 3,
  Bitable = 4,
  Slides = 5,
  Mindnote = 6,
  File = 7,
  Wiki = 8,
}

export enum IDPChunkingStrategy {
  /** 基于plain text切分 */
  PLAIN_TEXT = 0,
  /** 基于[paragraph/段落]的切分 */
  PARAGRAPH = 1,
}

export enum KnowledgeShelfDocumentType {
  Unknown = 0,
  Table = 1,
}

/** 暴露给用户的资源类型 */
export enum ResourceType {
  Unknown = 0,
  FeishuDoc = 1,
  LarkDoc = 2,
  FeishuWikiNode = 3,
  LocalExcelFile = 100,
  LocalCSVFile = 101,
  OpenAPI = 200,
  Internal = 1000,
}

export enum TableColumnType {
  Unknown = 0,
  String = 1,
  Integer = 2,
  Float = 3,
  Boolean = 4,
}

export interface ChunkInfo {
  /** 多模态情况？ */
  seq_id?: number;
  content?: string;
  chunk_size?: Int64;
}

export interface ChunkRule {
  chunk_type?: ChunkType;
  idp_chunk_rule?: IDPChunkRule;
  custom_chunk_rule?: CustomChunkRule;
}

export interface CustomChunkRule {
  chunk_size?: Int64;
  chunk_overlap?: Int64;
  separator?: string;
  remove_urls_emails?: boolean;
  remove_extra_spaces?: boolean;
}

export interface IDPChunkRule {
  chunk_size?: Int64;
  strategy?: IDPChunkingStrategy;
}

export interface IndexInfo {
  chunk_num?: number;
}

export interface Knowledge {
  id?: Int64;
  space_id?: Int64;
  knowledge_key?: string;
  knowledge_name?: string;
  description?: string;
  knowledge_doc_num?: number;
  creator_id?: Int64;
  updator_id?: Int64;
  create_time?: Int64;
  update_time?: Int64;
}

export interface KnowledgeDocument {
  space_id?: Int64;
  knowledge_id?: Int64;
  knowledge_document_id?: Int64;
  /** 知识库展示名称，本期与飞书文档同名 */
  knowledge_document_name?: string;
  /** deprecated */
  channel?: Channel;
  resource_type?: ResourceType;
  embedding_status?: EmbeddingStatus;
  embedding_time?: Int64;
  chunk_rule?: ChunkRule;
  status_info?: StatusInfo;
  index_info?: IndexInfo;
  knowledge_shelf_document_id?: Int64;
  file_url?: string;
  creator_id?: Int64;
  updator_id?: Int64;
  create_time?: Int64;
  update_time?: Int64;
}

export interface KnowledgeDocumentEntry {
  id?: Int64;
  sequence_id?: string;
  space_id?: Int64;
  knowledge_id?: Int64;
  knowledge_shelf_document_id?: Int64;
  knowledge_document_id?: Int64;
  detail?: KnowledgeDocumentEntryDetail;
  creator_id?: Int64;
  updator_id?: Int64;
  create_time?: Int64;
  update_time?: Int64;
}

export interface KnowledgeDocumentEntryDetail {
  resource_type?: ResourceType;
  table?: TableEntry;
}

export interface KnowledgeDocumentFile {
  url?: string;
  resource_type?: ResourceType;
}

export interface KnowledgeShelfDocument {
  id?: Int64;
  space_id?: Int64;
  knowledge_id?: Int64;
  type?: KnowledgeShelfDocumentType;
  schema?: KnowledgeShelfDocumentSchema;
  name?: string;
  description?: string;
  creator_id?: Int64;
  updator_id?: Int64;
  create_time?: Int64;
  update_time?: Int64;
}

export interface KnowledgeShelfDocumentSchema {
  table_schema?: Array<TableColumnSchema>;
}

export interface StatusInfo {
  message?: string;
  log_id?: string;
}

export interface TableColumnIndexMeta {
  is_semantic?: boolean;
}

export interface TableColumnSchema {
  sequence?: number;
  name?: string;
  column_type?: TableColumnType;
  index_meta?: TableColumnIndexMeta;
}

export interface TableEntry {
  cells?: Record<string, string>;
}

export interface UserInfo {
  name?: string;
  en_name?: string;
  /** 用户头像url */
  avatar_url?: string;
  /** 72 * 72 头像 */
  avatar_thumb?: string;
  /** 用户应用内唯一标识 */
  open_id?: string;
  /** 用户应用开发商内唯一标识 */
  union_id?: string;
  /** 企业标识 */
  tenant_key?: string;
  /** 用户在租户内的唯一标识 */
  user_id?: string;
  /** 用户邮箱 */
  email?: string;
}

export interface WikiNode {
  object_type?: FeishuObjectType;
  space_id?: string;
  url?: string;
  title?: string;
  owner_user_info?: UserInfo;
  has_child_nodes?: boolean;
  child_nodes?: Array<WikiNode>;
  sec_label_name?: string;
}
/* eslint-enable */
