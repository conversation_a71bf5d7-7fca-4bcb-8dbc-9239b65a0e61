/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_knowledge_common from './flow_devops_knowledge_common';
import * as base from './base';

export type Int64 = string | number;

export interface Channel {
  field?: string;
  top_k?: number;
  min_score?: number;
}

export interface Item {
  doc_id?: string;
  score?: number;
  /** chunk数据 */
  slice?: string;
  slice_meta?: string;
  knowledge_meta?: KnowledgeRetrieveMeta;
  channels?: Array<RetrieveChannel>;
}

export interface KnowledgeRetrieveMeta {
  file_url?: string;
  title?: string;
  resource_type?: flow_devops_knowledge_common.ResourceType;
  knowledge_key?: string;
  chunk_size?: Int64;
}

export interface Ranker {
  type?: string;
  params?: RerankParams;
}

export interface RecallData {
  items?: Array<Item>;
}

export interface RerankParams {
  min_score?: number;
}

export interface RetrieveChannel {
  source?: string;
  score?: number;
}

export interface RetrieveOptions {
  parse_table_values?: boolean;
}

export interface RetrieveReq {
  Authorization?: string;
  knowledge_keys?: Array<string>;
  query?: string;
  channels?: Array<Channel>;
  top_k?: number;
  rank?: Ranker;
  options?: RetrieveOptions;
  base?: base.Base;
}

export interface RetrieveResp {
  code?: number;
  msg?: string;
  data?: RecallData;
  base_resp?: base.BaseResp;
}
/* eslint-enable */
