/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as flow_devops_knowledge_common from './namespaces/flow_devops_knowledge_common';
import * as flow_devops_knowledge_platform from './namespaces/flow_devops_knowledge_platform';
import * as flow_devops_knowledge_retrive from './namespaces/flow_devops_knowledge_retrive';
import * as flow_devops_knowledge_runtime from './namespaces/flow_devops_knowledge_runtime';

export {
  base,
  flow_devops_knowledge_common,
  flow_devops_knowledge_platform,
  flow_devops_knowledge_retrive,
  flow_devops_knowledge_runtime,
};
export * from './namespaces/base';
export * from './namespaces/flow_devops_knowledge_common';
export * from './namespaces/flow_devops_knowledge_platform';
export * from './namespaces/flow_devops_knowledge_retrive';
export * from './namespaces/flow_devops_knowledge_runtime';

export type Int64 = string | number;

export default class FornaxKnowledgeService<T> {
  private request: any = () => {
    throw new Error('FornaxKnowledgeService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc/mget */
  MGetKnowledgeDocument(
    req?: flow_devops_knowledge_platform.MGetKnowledgeDocumentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.MGetKnowledgeDocumentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc/mget',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      search_content: _req['search_content'],
      knowledge_id: _req['knowledge_id'],
      knowledge_shelf_document_id: _req['knowledge_shelf_document_id'],
      resource_types: _req['resource_types'],
      offset: _req['offset'],
      limit: _req['limit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/knowledge_platform/v1/knowledge/upsert
   *
   * ============== 平台接口 ==============
   */
  UpsertKnowledge(
    req?: flow_devops_knowledge_platform.UpsertKnowledgeReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.UpsertKnowledgeResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge/upsert',
    );
    const method = 'POST';
    const data = { knowledge: _req['knowledge'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge/mget */
  MGetKnowledge(
    req?: flow_devops_knowledge_platform.MGetKnowledgeReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.MGetKnowledgeResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge/mget',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      search_content: _req['search_content'],
      offset: _req['offset'],
      limit: _req['limit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * @deprecated
   *
   * POST /api/devops/knowledge_platform/v1/authorization/check_auth_status
   *
   * deprecated
   */
  CheckAuthStatus(
    req?: flow_devops_knowledge_platform.CheckAuthStatusReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.CheckAuthStatusResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/authorization/check_auth_status',
    );
    const method = 'POST';
    const data = {
      channel: _req['channel'],
      resource_type: _req['resource_type'],
      user_id: _req['user_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc/batch_delete */
  BatchDeleteKnowledgeDocument(
    req?: flow_devops_knowledge_platform.BatchDeleteKnowledgeDocumentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.BatchDeleteKnowledgeDocumentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc/batch_delete',
    );
    const method = 'POST';
    const data = {
      knowledge_document_ids: _req['knowledge_document_ids'],
      knowledge_id: _req['knowledge_id'],
      operator_id: _req['operator_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc/batch_add */
  BatchAddKnowledgeDocument(
    req?: flow_devops_knowledge_platform.BatchAddKnowledgeDocumentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.BatchAddKnowledgeDocumentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc/batch_add',
    );
    const method = 'POST';
    const data = {
      knowledge_documents: _req['knowledge_documents'],
      files: _req['files'],
      creator_id: _req['creator_id'],
      updator_id: _req['updator_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc/embedding */
  StartEmbedding(
    req?: flow_devops_knowledge_platform.StartEmbeddingReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.StartEmbeddingResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc/embedding',
    );
    const method = 'POST';
    const data = {
      knowledge_document_id: _req['knowledge_document_id'],
      chunk_rule: _req['chunk_rule'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/knowledge_platform/v1/retrieve
   *
   * ============== 召回接口 ==============
   */
  Retrieve(
    req?: flow_devops_knowledge_retrive.RetrieveReq,
    options?: T,
  ): Promise<flow_devops_knowledge_retrive.RetrieveResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/knowledge_platform/v1/retrieve');
    const method = 'POST';
    const data = {
      knowledge_keys: _req['knowledge_keys'],
      query: _req['query'],
      channels: _req['channels'],
      top_k: _req['top_k'],
      rank: _req['rank'],
      options: _req['options'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge/get_by_id */
  GetKnowledgeByID(
    req?: flow_devops_knowledge_platform.GetKnowledgeByIDReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.GetKnowledgeByIDResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge/get_by_id',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      knowledge_id: _req['knowledge_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc/get_feishu_file_meta */
  GetFeishuFileMeta(
    req?: flow_devops_knowledge_platform.GetFeishuFileMetaReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.GetFeishuFileMetaResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc/get_feishu_file_meta',
    );
    const method = 'POST';
    const data = {
      resource_type: _req['resource_type'],
      file: _req['file'],
      operator_id: _req['operator_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc/get_feishu_wiki_node */
  GetFeishuWikiNode(
    req?: flow_devops_knowledge_platform.GetFeishuWikiNodeReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.GetFeishuWikiNodeResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc/get_feishu_wiki_node',
    );
    const method = 'POST';
    const data = {
      url: _req['url'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/get_chunk_list */
  GetKnowledgeChunkList(
    req?: flow_devops_knowledge_platform.GetKnowledgeChunkListReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.GetKnowledgeChunkListResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/get_chunk_list',
    );
    const method = 'POST';
    const data = {
      knowledge_document: _req['knowledge_document'],
      last_seq_id: _req['last_seq_id'],
      limit: _req['limit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/retrieve_test */
  RetrieveTest(
    req?: flow_devops_knowledge_platform.RetrieveTestReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.RetrieveTestResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/retrieve_test',
    );
    const method = 'POST';
    const data = {
      knowledge_keys: _req['knowledge_keys'],
      query: _req['query'],
      channels: _req['channels'],
      top_k: _req['top_k'],
      rank: _req['rank'],
      space_id: _req['space_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/knowledge_platform/v1/knowledge_event/callback
   *
   * ============== 回调接口 ==============
   */
  EventCallback(
    req?: flow_devops_knowledge_platform.EventCallbackReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.EventCallbackResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_event/callback',
    );
    const method = 'POST';
    const data = { event: _req['event'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/get_preview_chunk_list */
  GetPreviewChunkList(
    req?: flow_devops_knowledge_platform.GetPreviewChunkListReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.GetPreviewChunkListResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/get_preview_chunk_list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      knowledge_document_file: _req['knowledge_document_file'],
      chunk_rule: _req['chunk_rule'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc/mget_by_parent */
  MGetKnowledgeDocumentByParent(
    req?: flow_devops_knowledge_platform.MGetKnowledgeDocumentByParentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.MGetKnowledgeDocumentByParentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc/mget_by_parent',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      parent_knowledge_doc_id: _req['parent_knowledge_doc_id'],
      knowledge_id: _req['knowledge_id'],
      offset: _req['offset'],
      limit: _req['limit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_event/embedding */
  EmbeddingCallback(
    req?: flow_devops_knowledge_platform.EmbeddingCallbackReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.EmbeddingCallbackResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_event/embedding',
    );
    const method = 'POST';
    const data = { texts: _req['texts'], base: _req['base'] };
    const params = { model_id: _req['model_id'] };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_shelf_doc/delete */
  DeleteKnowledgeShelfDocument(
    req?: flow_devops_knowledge_platform.DeleteKnowledgeShelfDocumentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.DeleteKnowledgeShelfDocumentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_shelf_doc/delete',
    );
    const method = 'POST';
    const data = {
      knowledge_shelf_document_id: _req['knowledge_shelf_document_id'],
      space_id: _req['space_id'],
      knowledge_id: _req['knowledge_id'],
      operator_id: _req['operator_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge/delete */
  DeleteKnowledge(
    req?: flow_devops_knowledge_platform.DeleteKnowledgeReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.DeleteKnowledgeResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge/delete',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      knowledge_id: _req['knowledge_id'],
      operator_id: _req['operator_id'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_shelf_doc/create */
  CreateKnowledgeShelfDocument(
    req?: flow_devops_knowledge_platform.CreateKnowledgeShelfDocumentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.CreateKnowledgeShelfDocumentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_shelf_doc/create',
    );
    const method = 'POST';
    const data = {
      knowledge_shelf_document: _req['knowledge_shelf_document'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open-apis/knowledge/v1/data/import_table_entry
   *
   * ============== OpenAPI接口 ==============
   */
  ImportKnowledgeTableData(
    req?: flow_devops_knowledge_runtime.ImportKnowledgeTableDataReq,
    options?: T,
  ): Promise<flow_devops_knowledge_runtime.ImportKnowledgeTableDataResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/open-apis/knowledge/v1/data/import_table_entry',
    );
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      entries: _req['entries'],
      unique_entries: _req['unique_entries'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_shelf_doc/mget */
  MGetKnowledgeShelfDocument(
    req?: flow_devops_knowledge_platform.MGetKnowledgeShelfDocumentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.MGetKnowledgeShelfDocumentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_shelf_doc/mget',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      knowledge_id: _req['knowledge_id'],
      knowledge_shelf_document_id: _req['knowledge_shelf_document_id'],
      type: _req['type'],
      search_content: _req['search_content'],
      offset: _req['offset'],
      limit: _req['limit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/knowledge_doc_entry/mget */
  MGetKnowledgeDocumentEntry(
    req?: flow_devops_knowledge_platform.MGetKnowledgeDocumentEntryReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.MGetKnowledgeDocumentEntryResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/knowledge_doc_entry/mget',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      knowledge_id: _req['knowledge_id'],
      knowledge_shelf_document_id: _req['knowledge_shelf_document_id'],
      resource_type: _req['resource_type'],
      offset: _req['offset'],
      limit: _req['limit'],
      base: _req['base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/knowledge_platform/v1/generate_id */
  GenerateID(
    req?: flow_devops_knowledge_platform.GenerateIDReq,
    options?: T,
  ): Promise<flow_devops_knowledge_platform.GenerateIDResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/knowledge_platform/v1/generate_id',
    );
    const method = 'POST';
    const data = { number: _req['number'], base: _req['base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /open-apis/knowledge/v1/embedding */
  OApiStartEmbedding(
    req?: flow_devops_knowledge_runtime.StartEmbeddingReq,
    options?: T,
  ): Promise<flow_devops_knowledge_runtime.StartEmbeddingResp> {
    const _req = req || {};
    const url = this.genBaseURL('/open-apis/knowledge/v1/embedding');
    const method = 'POST';
    const data = {
      knowledge_document_id: _req['knowledge_document_id'],
      chunk_rule: _req['chunk_rule'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /open-apis/knowledge/v1/knowledge_shelf_doc/mget */
  OApiMGetKnowledgeShelfDocument(
    req?: flow_devops_knowledge_runtime.MGetKnowledgeShelfDocumentReq,
    options?: T,
  ): Promise<flow_devops_knowledge_runtime.MGetKnowledgeShelfDocumentResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/open-apis/knowledge/v1/knowledge_shelf_doc/mget',
    );
    const method = 'POST';
    const data = {
      knowledge_id: _req['knowledge_id'],
      knowledge_shelf_document_id: _req['knowledge_shelf_document_id'],
      type: _req['type'],
      offset: _req['offset'],
      limit: _req['limit'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /open-apis/knowledge/v1/knowledge_doc_entry/mget */
  OApiMGetKnowledgeDocumentEntry(
    req?: flow_devops_knowledge_runtime.MGetKnowledgeDocumentEntryReq,
    options?: T,
  ): Promise<flow_devops_knowledge_runtime.MGetKnowledgeDocumentEntryResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/open-apis/knowledge/v1/knowledge_doc_entry/mget',
    );
    const method = 'POST';
    const data = {
      knowledge_shelf_document_id: _req['knowledge_shelf_document_id'],
      knowledge_document_id: _req['knowledge_document_id'],
      resource_type: _req['resource_type'],
      offset: _req['offset'],
      limit: _req['limit'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /open-apis/knowledge/v1/knowledge_doc_entry/batch_delete */
  OApiBatchDeleteKnowledgeDocumentEntry(
    req?: flow_devops_knowledge_runtime.BatchDeleteKnowledgeDocumentEntryReq,
    options?: T,
  ): Promise<flow_devops_knowledge_runtime.BatchDeleteKnowledgeDocumentEntryResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/open-apis/knowledge/v1/knowledge_doc_entry/batch_delete',
    );
    const method = 'POST';
    const data = {
      knowledge_document_id: _req['knowledge_document_id'],
      knowledge_document_entry_ids: _req['knowledge_document_entry_ids'],
      base: _req['base'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }
}
/* eslint-enable */
