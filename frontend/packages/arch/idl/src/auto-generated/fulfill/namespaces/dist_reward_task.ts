/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bot_operation_common from './bot_operation_common';
import * as common from './common';
import * as fulfill_common from './fulfill_common';

export type Int64 = string | number;

/** 发放奖励任务的状态 */
export enum DistRewardTaskStatus {
  Unknown = 0,
  /** 审核中 */
  InReview = 1,
  /** 已拒绝 */
  Rejected = 2,
  /** 已通过 */
  Approved = 3,
  /** 已撤回 */
  Canceled = 4,
}

export enum DistRewardType {
  Unknonw = 0,
  /** 需传递 AddMessageCreditsRewardStruct 结构体 */
  MessageCredits = 1,
  /** 暂时不支持，后续迁移过来 */
  CozeToken = 2,
  /** 退款申请，复用审批任务 */
  Refund = 3,
}

export interface AddCozeTokenStruct {
  amount: Int64;
}

export interface AddMessageCreditsRewardStruct {
  amount_per_day: Int64;
  active_days: Int64;
}

export interface AdminCancelDistRewardTaskRequest {
  /** 主键 */
  id: string;
}

export interface AdminCancelDistRewardTaskResponse {
  code?: number;
  message?: string;
}

export interface AdminCreateDistRewardTaskRequest {
  /** 发放给谁 */
  to_uid_list: Array<string>;
  /** 发放奖励的类型 */
  dist_reward_type: DistRewardType;
  /** 发放奖励具体内容 */
  reward_detail: RewardDetail;
  /** 原因 */
  reason: string;
}

export interface AdminCreateDistRewardTaskResponse {
  code?: number;
  message?: string;
  data?: AdminCreateDistRewardTaskResponseData;
}

export interface AdminCreateDistRewardTaskResponseData {
  /** 任务主键 id */
  id?: string;
}

export interface AdminListDistRewardTaskFulfillmentRequest {
  /** 任务主键 id */
  task_id: string;
}

export interface AdminListDistRewardTaskFulfillmentResponse {
  code?: number;
  message?: string;
  data?: AdminListDistRewardTaskFulfillmentResponseData;
}

export interface AdminListDistRewardTaskFulfillmentResponseData {
  /** 履约单列表 */
  fulfillment_list?: Array<Fulfillment>;
}

export interface AdminListDistRewardTaskRequest {
  /** 页码, >=1 */
  page: Int64;
  /** 每页数量, 1-200 */
  count: Int64;
  /** 状态 */
  status_in?: Array<DistRewardTaskStatus>;
  /** 创建时间开始，秒时间戳 */
  created_at_begin?: Int64;
  /** 发给哪个 uid 了 */
  uid?: string;
  /** 是否禁用环境隔离 */
  disable_env?: boolean;
  dist_reward_type?: DistRewardType;
}

export interface AdminListDistRewardTaskResponse {
  code?: number;
  message?: string;
  data?: AdminListDistRewardTaskResponseData;
}

export interface AdminListDistRewardTaskResponseData {
  /** 任务列表 */
  task_list?: Array<DistRewardTask>;
  /** 总数 */
  total?: Int64;
}

/** dist Reward task 的实体 */
export interface DistRewardTask {
  /** 主键 */
  id?: string;
  /** 谁创建的 */
  op_user?: bot_operation_common.ByteDanceUser;
  /** 谁审批的 */
  approval_user?: bot_operation_common.ByteDanceUser;
  /** 发放奖励的类型 */
  dist_reward_type?: DistRewardType;
  /** 发放奖励具体内容 */
  reward_detail?: RewardDetail;
  /** 一共给多少人 */
  user_count?: Int64;
  /** 状态 */
  status?: DistRewardTaskStatus;
  /** 原因 */
  reason?: string;
  /** 创建时间，秒时间戳 */
  created_at?: Int64;
  /** 审批实例 code */
  approval_instance_code?: string;
  /** 数据所属环境，为空表示基准环境，否则为多环境 */
  env?: string;
  /** uid 的 列表 */
  uid_list?: Array<string>;
}

export interface Fulfillment {
  /** 主键 */
  id?: string;
  /** 实体类型 */
  entity_type?: common.EntityType;
  /** 状态 */
  status?: fulfill_common.FulfillmentStatus;
  /** 创建时间，秒时间戳 */
  created_at?: Int64;
  /** 发送给谁的 */
  to_user?: bot_operation_common.PassportUser;
}

export interface RefundStruct {
  OrderID?: string;
  RefundAmount?: Int64;
  Currency?: string;
  Reason?: string;
  GoodsTypeDisplay?: string;
  OrderType?: bot_operation_common.OrderType;
  RefundSubsExpiryTime?: string;
  IsSkipCheck?: boolean;
}

export interface RewardDetail {
  /** DistRewardType 为 MessageCredits 需要传该结构体 */
  add_message_credits_reward_struct?: AddMessageCreditsRewardStruct;
  /** DistRewardType 为 CozeToken 需要传该结构体 */
  add_coze_token_struct?: AddCozeTokenStruct;
  RefundStruct?: RefundStruct;
}
/* eslint-enable */
