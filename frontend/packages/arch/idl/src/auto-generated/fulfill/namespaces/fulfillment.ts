/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as fulfill_common from './fulfill_common';

export type Int64 = string | number;

export enum RecipientType {
  Unknown = 0,
  CozeUser = 1,
}

/** 用于增加实体调用数量的结构定义。
 如增加 coze token */
export interface AddEntityAmountContent {
  /** 实体类型 */
  EntityType?: common.EntityType;
  /** 实体 id */
  EntityID?: Int64;
  /** 如 EntityType 为 coze token，则添加的数量为 coze token 的添加数量 */
  Amount?: Int64;
}

export interface AddMessageCreditContent {
  AmountPerDay?: Int64;
  ActiveDays?: Int64;
}

export interface AutoChargeCallbackContent {
  /** 支付 id */
  TradeOrderID?: string;
  /** 是否支付成功 */
  IsSuccess?: boolean;
}

export interface AutoChargePaymentContent {
  /** 自动充值任务 id */
  AutoChargeTaskID?: Int64;
}

export interface ContentJSON {
  /** 履约类型：如加数量 */
  FulfillmentType?: fulfill_common.FulfillmentType;
  /** fulfillmentType 为 AddEntityAmount 需要传该结构体 */
  AddEntityAmountContent?: AddEntityAmountContent;
  /** fulfillmentType 为 AutoChargePayment 需要传该结构体 */
  AutoChargePaymentContent?: AutoChargePaymentContent;
  /** fulfillmentType 为 AutoChargeCallback 需要传该结构体 */
  AutoChargeCallbackContent?: AutoChargeCallbackContent;
  /** fulfillmentType 为 AutoMessageCredit 需要传该结构体 */
  AddMessageCreditContent?: AddMessageCreditContent;
}

export interface CreateFulfillmentItem {
  /** 幂等 key */
  UniqueKey?: string;
  /** 履约单收益人 id，暂时支持 user_id */
  RecipientID?: string;
  /** 履约单接受者类型 */
  RecipientType?: RecipientType;
  /** 来源：运营平台，交易平台 */
  SourceType?: fulfill_common.SourceType;
  /** 源 ID，运营平台则为任务 id，交易则为订单 id */
  SourceID?: string;
  /** source 为运营平台，需要传递，结构为见 ContentJson */
  ContentJSON?: ContentJSON;
}

export interface Fulfillment {
  /** 履约单 id */
  ID?: Int64;
  /** 幂等 key */
  UniqueKey?: string;
  /** 履约单收益人 id，暂时支持 user_id */
  RecipientID?: string;
  /** 履约单接受者类型 */
  RecipientType?: RecipientType;
  /** 履约类型 */
  Type?: fulfill_common.FulfillmentType;
  /** 履约状态 */
  Status?: fulfill_common.FulfillmentStatus;
  /** 履约描述详情 */
  ContentJSON?: ContentJSON;
  /** 数据所属环境，为空表示基准环境，否则为多环境 */
  Env?: string;
  /** 创建时间戳, 秒 */
  CreatedAt?: Int64;
  /** 创建时间戳, 秒 */
  UpdatedAt?: Int64;
}
/* eslint-enable */
