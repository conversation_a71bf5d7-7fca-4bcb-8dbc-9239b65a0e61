/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bot_operation_common from './bot_operation_common';
import * as common from './common';
import * as fulfill_common from './fulfill_common';

export type Int64 = string | number;

/** coze token 任务的状态 */
export enum CozeTokenTaskStatus {
  Unknown = 0,
  /** 审核中 */
  InReview = 1,
  /** 已拒绝 */
  Rejected = 2,
  /** 已通过 */
  Approved = 3,
  /** 已撤回 */
  Canceled = 4,
}

export interface BotOpCancelCozeTokenTaskRequest {
  /** 主键 */
  id?: string;
}

export interface BotOpCancelCozeTokenTaskResponse {
  code?: number;
  message?: string;
}

export interface BotOpCreateCozeTokenTaskRequest {
  /** 发放给谁 */
  to_uid_list?: Array<string>;
  /** 给每个人多少 token */
  single_user_token?: Int64;
  /** 原因 */
  reason?: string;
}

export interface BotOpCreateCozeTokenTaskResponse {
  code?: number;
  message?: string;
  data?: BotOpCreateCozeTokenTaskResponseData;
}

export interface BotOpCreateCozeTokenTaskResponseData {
  /** 任务主键 id */
  id?: string;
}

export interface BotOpListCozeTokenTaskFulfillmentRequest {
  /** 任务主键 id */
  task_id?: string;
}

export interface BotOpListCozeTokenTaskFulfillmentResponse {
  code?: number;
  message?: string;
  data?: BotOpListCozeTokenTaskFulfillmentResponseData;
}

export interface BotOpListCozeTokenTaskFulfillmentResponseData {
  /** 履约单列表 */
  fulfillment_list?: Array<Fulfillment>;
}

export interface BotOpListCozeTokenTaskRequest {
  /** 页码, >=1 */
  page?: Int64;
  /** 每页数量, 1-200 */
  count?: Int64;
  /** 状态 */
  status_in?: Array<CozeTokenTaskStatus>;
  /** 创建时间开始，秒时间戳 */
  created_at_begin?: Int64;
  /** 发给哪个 uid 了 */
  uid?: string;
  /** 是否禁用环境隔离 */
  disable_env?: boolean;
}

export interface BotOpListCozeTokenTaskResponse {
  code?: number;
  message?: string;
  data?: BotOpListCozeTokenTaskResponseData;
}

export interface BotOpListCozeTokenTaskResponseData {
  /** 任务列表 */
  task_list?: Array<CozeTokenTask>;
  /** 总数 */
  total?: Int64;
}

/** coze token 的实体 */
export interface CozeTokenTask {
  /** 主键 */
  id?: string;
  /** 谁创建的 */
  op_user?: bot_operation_common.ByteDanceUser;
  /** 谁审批的 */
  approval_user?: bot_operation_common.ByteDanceUser;
  /** 给每个人多少 token */
  single_user_token?: Int64;
  /** 一共给多少人 */
  user_count?: Int64;
  /** 状态 */
  status?: CozeTokenTaskStatus;
  /** 原因 */
  reason?: string;
  /** 创建时间，秒时间戳 */
  created_at?: Int64;
  /** 审批实例 code */
  approval_instance_code?: string;
  /** uid 的 列表 */
  uid_list?: Array<string>;
  /** 数据所属环境，为空表示基准环境，否则为多环境 */
  env?: string;
}

export interface Fulfillment {
  /** 主键 */
  id?: string;
  /** 实体类型 */
  entity_type?: common.EntityType;
  /** 数量 */
  amount?: Int64;
  /** 状态 */
  status?: fulfill_common.FulfillmentStatus;
  /** 创建时间，秒时间戳 */
  created_at?: Int64;
  /** 发送给谁的 */
  to_user?: bot_operation_common.PassportUser;
}
/* eslint-enable */
