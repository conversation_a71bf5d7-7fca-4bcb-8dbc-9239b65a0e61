/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 订单状态 */
export enum OrderStatus {
  /** 初始化 */
  Init = 0,
  /** 成功 */
  Success = 1,
  /** 失败 */
  Failed = 2,
  /** 关闭 */
  Closed = 3,
  /** 退款中 */
  Refunding = 4,
  /** 已退款 */
  Refunded = 5,
  /** 退款失败 */
  RefundFaield = 6,
  /** 已拒付 */
  ChargeBack = 7,
  /** 拒付抗辩成功 */
  ChargeBackReverse = 8,
}

/** 订单类型 */
export enum OrderType {
  /** 未知 */
  Unknown = 0,
  /** Token充值 */
  TokenCharge = 1,
  /** Token自动充值 */
  TokenAutoCharge = 2,
  /** MessageCredit订阅 */
  SubMessageCredit = 11,
  /** MessageCredit充值 */
  MessageCredit = 12,
}

export enum RefundType {
  Unknown = 0,
  /** 全额退款 */
  Full = 1,
  /** 按使用量退款 */
  ActualUse = 2,
}

export interface ByteDanceUser {
  /** 邮箱 */
  email?: string;
  /** 头像 */
  avatar_url?: string;
  /** 中文名 */
  name?: string;
  /** 英文 */
  en_name?: string;
}

export interface PassportUser {
  /** 用户 uid */
  uid?: string;
  /** 昵称 */
  name?: string;
  /** 头像 */
  avatar_url?: string;
}
/* eslint-enable */
