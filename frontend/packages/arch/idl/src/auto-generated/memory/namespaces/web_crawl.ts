/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

/** typedef string SubLinkDiscoveryTaskStatus
const SubLinkDiscoveryTaskStatus SUB_LINK_DISCOVERY_TASK_STATUS_RUNNING = "running"
const SubLinkDiscoveryTaskStatus SUB_LINK_DISCOVERY_TASK_STATUS_SUCCESS = "finished"
const SubLinkDiscoveryTaskStatus SUB_LINK_DISCOVERY_TASK_STATUS_ABORTED = "aborted" */
export enum SubLinkDiscoveryTaskStatus {
  SUB_LINK_DISCOVERY_TASK_STATUS_UNKNOWN = 0,
  SUB_LINK_DISCOVERY_TASK_STATUS_RUNNING = 1,
  SUB_LINK_DISCOVERY_TASK_STATUS_SUCCESS = 2,
  SUB_LINK_DISCOVERY_TASK_STATUS_ABORTED = 3,
  SUB_LINK_DISCOVERY_TASK_STATUS_FINISHED_WITH_ERROR = 4,
}

export interface AbortSubLinkDiscoveryTaskRequest {
  task_id?: string;
  Base?: base.Base;
}

export interface AbortSubLinkDiscoveryTaskResponse {
  BaseResp?: base.BaseResp;
}

export interface CreateSubLinkDiscoveryTaskRequest {
  url?: string;
  creator_id?: Int64;
  Base?: base.Base;
}

export interface CreateSubLinkDiscoveryTaskResponse {
  task_id?: string;
  BaseResp?: base.BaseResp;
}

export interface GetSubLinkDiscoveryTaskRequest {
  task_id?: string;
  Base?: base.Base;
}

export interface GetSubLinkDiscoveryTaskResponse {
  urls?: Array<string>;
  status?: SubLinkDiscoveryTaskStatus;
  BaseResp?: base.BaseResp;
}

export interface ParseSiteMapRequest {
  sitemap_url?: string;
  creator_id?: Int64;
  Base?: base.Base;
}

export interface ParseSiteMapResponse {
  urls?: Array<string>;
  BaseResp?: base.BaseResp;
}

export interface SubmitBatchCrawlTaskRequest {
  web_urls?: Array<string>;
  creator_id?: Int64;
  Base?: base.Base;
}

export interface SubmitBatchCrawlTaskResponse {
  web_ids?: Array<string>;
  BaseResp: base.BaseResp;
}
/* eslint-enable */
