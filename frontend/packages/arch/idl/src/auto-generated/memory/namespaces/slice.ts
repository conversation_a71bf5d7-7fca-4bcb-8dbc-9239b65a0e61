/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as common from './common';

export type Int64 = string | number;

export enum SliceSource {
  bot = 1,
  hit_test = 2,
}

export interface BatchDeleteSliceRequest {
  slice_id_list: Array<string>;
  document_id: string;
  Base?: base.Base;
}

export interface BatchDeleteSliceResponse {
  code: Int64;
  msg: string;
}

export interface ChangeSliceStatusReq {
  slice_id: string;
  creator_id: string;
  status: common.SliceStatus;
  Base?: base.Base;
}

export interface ChangeSliceStatusResp {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CreateSliceReq {
  document_id: string;
  creator_id?: string;
  content: string;
  Base?: base.Base;
}

export interface CreateSliceResp {
  slice_id?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DelSliceReq {
  slice_id: string;
  creator_id?: string;
  Base?: base.Base;
}

export interface DelSliceResp {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetSliceListReq {
  doc_id?: string;
  /** 序号 */
  sequence?: string;
  /** 查询关键字 */
  key_word?: string;
  creator_id?: string;
  /** 从1开始 */
  page_no?: string;
  page_size?: string;
  sort_field?: string;
  is_asc?: boolean;
  Base?: base.Base;
}

export interface GetSliceListResp {
  slice_list?: Array<common.SliceInfo>;
  total?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UpdateSliceContentReq {
  slice_id: string;
  creator_id?: string;
  /** 限制2000字 */
  content: string;
  Base?: base.Base;
}

export interface UpdateSliceContentResp {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
