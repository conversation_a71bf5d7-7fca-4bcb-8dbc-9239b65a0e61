/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';
import * as base from './base';
import * as connector_common from './connector_common';

export type Int64 = string | number;

export enum SegmentAppendType {
  /** 占位 */
  SegmentAppendType_None = 0,
  /** 尾部追加 */
  SegmentAppendType_Tail = 1,
  /** 提前定义，本次不用
头部 */
  SegmentAppendType_Head = 2,
  /** 中间 */
  SegmentAppendType_Mid = 3,
}

export enum TableDataType {
  /** schema sheets 和 preview data */
  AllData = 0,
  /** 只需要 schema 结构 & Sheets */
  OnlySchema = 1,
  /** 只需要 preview data */
  OnlyPreview = 2,
}

export interface AddSegmentRequest {
  space_id: string;
  document_id: string;
  source_type: common.DocumentSourceType;
  source_file: common.SourceFileInfo;
  user_table_info: DocumentTableTaskInfo;
  append_type: SegmentAppendType;
  /** 提前定义，本次不用 */
  append_sequence?: Int64;
  Base?: base.Base;
}

export interface AddSegmentResponse {
  document_id: string;
  /** 如果失败会返回错误码 */
  code: Int64;
  msg: string;
}

export interface CreateDocumentRequest {
  creator_id?: string;
  dataset_id?: string;
  document?: common.DocumentInfo;
  space_id?: string;
  Base?: base.Base;
}

export interface CreateDocumentResponse {
  id?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteUserDataSourceRequest {
  data_source_id?: Int64;
  /** 需要删除已经添加的文件和向量块 */
  need_delete_document?: boolean;
  auth_id?: string;
  connector_id?: connector_common.ConnectorID;
  Base?: base.Base;
}

export interface DeleteUserDataSourceResponse {
  code: Int64;
  msg: string;
}

export interface DelWebDataRequest {
  web_id?: string;
  creator_id?: string;
  Base?: base.Base;
}

export interface DelWebDataResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DocumentFileInfo {
  name?: string;
  uri?: string;
  document_id?: string;
}

export interface DocumentTableTaskInfo {
  /** 用户选择的 sheet id */
  sheet_id: string;
  /** 用户选择的表头行数，从 0 开始编号 */
  header_line_idx: string;
  /** 用户选择的起始行号，从 0 开始编号 */
  start_line_idx: string;
}

export interface DocumentTaskInfo {
  name?: string;
  uri?: string;
  /** 用于重新切片 */
  document_id?: string;
  /** 格式类型 */
  format_type?: common.FormatType;
  /** 表格元数据 */
  doc_table_meta?: Array<common.DocTableColumn>;
  /** 表格解析信息 */
  doc_table_info?: DocumentTableTaskInfo;
  source_file_id?: string;
  document_source_type?: common.DocumentSourceType;
}

export interface GetDocumentTableInfoRequest {
  /** 如果为第一次 url 上传的表格，传递该值 */
  submit_web_id?: string;
  /** 如果为第一次本地文件上传的表格，传递该值 */
  tos_uri?: string;
  /** 如果为已有 document 的表格，传递该值 */
  document_id?: string;
  /** 创建人[http接口不需要传递] */
  creator_id?: Int64;
  source_file_id?: string;
  Base?: base.Base;
}

export interface GetDocumentTableInfoResponse {
  code?: number;
  msg?: string;
  sheet_list?: Array<common.DocTableSheet>;
  /** key: sheet_id -> list<common.DocTableColumn> */
  table_meta?: Record<Int64, Array<common.DocTableColumn>>;
  /** key: sheet_id -> list_preview_data */
  preview_data?: Record<Int64, Array<Record<Int64, string>>>;
}

export interface GetTableSchemaInfoRequest {
  /** 本地文件上传的 tos 地址，传递该值(等前端上线完迁移到SourceFile) */
  tos_uri?: string;
  /** 表格解析信息, 默认初始值0,0,1 */
  doc_table_info?: DocumentTableTaskInfo;
  /** 不传默认返回所有数据 */
  table_data_type?: TableDataType;
  /** 如果需要拉取的是当前 document 的 schema 时传递该值 */
  document_id?: string;
  /** source file 的信息，新增 segment / 之前逻辑迁移到这里 */
  source_file?: common.SourceFileInfo;
  /** 表格预览前端需要传递原始的数据表结构 */
  origin_table_meta?: Array<common.DocTableColumn>;
  /** 表格预览前端需要传递用户编辑之后的数据表结构 */
  preview_table_meta?: Array<common.DocTableColumn>;
  Base?: base.Base;
}

export interface GetTableSchemaInfoResponse {
  code?: number;
  msg?: string;
  sheet_list?: Array<common.DocTableSheet>;
  /** 选中的 sheet 的 schema, 不选择默认返回第一个 sheet */
  table_meta?: Array<common.DocTableColumn>;
  /** knowledge table 场景中会返回 */
  preview_data?: Array<Record<Int64, string>>;
}

export interface GetTaskProgressV2Request {
  document_id?: Array<string>;
  creator_id?: string;
  Base?: base.Base;
}

export interface GetTaskProgressV2Response {
  data?: Array<TaskProgressDataV2>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListDocumentRequest {
  dataset_id?: string;
  /** http接口不传递 */
  creator_id?: Int64;
  document_id?: string;
  page?: number;
  size?: number;
  Base?: base.Base;
}

export interface ListDocumentResponse {
  documentsInfo?: Array<common.DocumentInfo>;
  total?: number;
  code?: Int64;
  msg?: string;
}

export interface ProcessDocumentsTaskRequest {
  /** http接口不需要传递 */
  creator_id?: Int64;
  dataset_id?: string;
  document_infos?: Array<DocumentTaskInfo>;
  rule?: string;
  Base?: base.Base;
}

export interface ProcessDocumentsTaskResponse {
  document_infos?: Array<DocumentFileInfo>;
  code?: Int64;
  msg?: string;
}

export interface ProcessWebDocumentsTaskRequest {
  creator_id?: string;
  dataset_id?: string;
  document_info?: Array<WebDocumentInfo>;
  formatType?: common.FormatType;
  Base?: base.Base;
}

export interface ProcessWebDocumentsTaskResponse {
  document_infos?: Array<DocumentFileInfo>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface QueryWebInfoRequest {
  web_ids?: Array<string>;
  /** 是否包含内容 */
  include_content?: boolean;
  creator_id?: string;
  Base?: base.Base;
}

export interface QueryWebInfoResponse {
  data?: Record<Int64, WebInfoData>;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ResegmentRequest {
  dataset_id: string;
  document_id: string;
  rule?: string;
  /** 格式类型 */
  format_type?: common.FormatType;
  creator_id?: string;
  Base?: base.Base;
}

export interface ResegmentResponse {
  code?: Int64;
  msg?: string;
  document_infos?: DocumentFileInfo;
  BaseResp?: base.BaseResp;
}

export interface SubmitCrawlDataRequest {
  /** web_document */
  web_documents?: Array<WebDocument>;
  /** 隶属的datasetID */
  dataset_id?: string;
  Base?: base.Base;
}

export interface SubmitCrawlDataResponse {
  document_infos?: Array<DocumentFileInfo>;
  code?: Int64;
  msg?: string;
}

export interface SubmitWebContentV2Request {
  web_id?: string;
  content?: string;
  Base?: base.Base;
}

export interface SubmitWebContentV2Response {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface SubmitWebUrlRequest {
  web_url?: string;
  /** 0 不包换子页面。 */
  subpages_count?: number;
  creator_id?: string;
  /** 文件格式类型 */
  format_type?: common.FormatType;
  /** 网页标题 url 类型必传 */
  title?: string;
  Base?: base.Base;
}

export interface SubmitWebUrlResponse {
  web_id?: string;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface TableSchemaValidRequest {
  space_id: string;
  document_id: string;
  /** source file 的信息 */
  source_file: common.SourceFileInfo;
  user_table_info: DocumentTableTaskInfo;
  Base?: base.Base;
}

export interface TableSchemaValidResponse {
  column_valid_result?: Record<string, string>;
  /** 如果失败会返回错误码 */
  code: Int64;
  msg: string;
}

export interface TaskProgressData {
  document_id?: string;
  progress?: number;
  status?: common.DocumentStatus;
  /** 状态的详细描述；如果切片失败，返回失败信息 */
  status_descript?: string;
}

export interface TaskProgressDataV2 {
  document_id?: string;
  progress?: number;
  status?: common.DocumentStatus;
  /** 状态的详细描述；如果切片失败，返回失败信息 */
  status_descript?: string;
  document_name?: string;
}

export interface UpdateDocumentV2Request {
  creator_id?: string;
  document_id?: string;
  status?: common.DocumentStatus;
  document_name?: string;
  table_meta?: Array<common.DocTableColumn>;
  Base?: base.Base;
}

export interface UpdateDocumentV2Response {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UpdateWebRuleRequest {
  document_id?: string;
  update_type?: common.DocumentUpdateType;
  update_interval?: number;
  creator_id?: string;
  Base?: base.Base;
}

export interface UpdateWebRuleResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ValidateUnitNameRequest {
  space_id: string;
  dataset_id: string;
  unit_name: string;
  format_type: common.FormatType;
  document_id?: string;
  Base?: base.Base;
}

export interface ValidateUnitNameResponse {
  /** 如果失败会返回错误码 */
  code: Int64;
  msg: string;
}

export interface WebDocument {
  /** 标题(表名) */
  title?: string;
  /** 表头 */
  headers?: Array<string>;
  /** 抓取到的完整信息 */
  content?: Array<Record<string, string>>;
  /** 类型(文档/表格) */
  format_type?: common.FormatType;
  /** 抓取页面的URL */
  url?: string;
  /** 抓取信息的XPATH */
  marks?: Record<string, string>;
}

export interface WebDocumentInfo {
  web_id?: string;
  update_type?: common.DocumentUpdateType;
  /** 更新间隔天数 0表示不更新 */
  update_interval?: number;
  sub_web_ids?: Array<string>;
  /** 如果是已有更新 */
  document_id?: string;
  /** 表格类型元数据 */
  table_meta?: Array<common.DocTableColumn>;
  /** 原有的表格类型元数据 deprecated 服务代码中没用 */
  orig_table_meta?: Array<common.DocTableColumn>;
  name?: string;
}

export interface WebInfo {
  id?: string;
  url?: string;
  content?: string;
  title?: string;
  subpages?: Array<WebInfo>;
  subpages_count?: number;
  status?: common.WebInfoStatus;
}

export interface WebInfoData {
  progress?: number;
  web_info?: WebInfo;
  status?: common.WebInfoStatus;
  status_descript?: string;
}
/* eslint-enable */
