/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as connector_common from './connector_common';
import * as base from './base';
import * as data_connector_common from './data_connector_common';

export type Int64 = string | number;

export enum TimeFilterEnum {
  TimeUnlimited = 0,
  WithinAWeek = 1,
  WithinAMonth = 2,
  WithinAYear = 3,
}

export interface AuthInfo {
  auth_id?: string;
  connector_id?: connector_common.ConnectorID;
  name?: string;
  icon?: string;
}

export interface CancelConnectionTaskRequest {
  instance_id_list: Array<string>;
  Base?: base.Base;
}

export interface CancelConnectionTaskResponse {
  code: Int64;
  msg: string;
}

export interface ConnectionFileNode {
  auth_id: string;
  file_node_type: connector_common.FileNodeType;
  file_node_list: Array<FileNode>;
}

export interface ConnectionInfo {
  connection_id?: string;
  connection_name?: string;
  connection_icon?: string;
  status?: connector_common.ConnectionStatus;
  source_id?: Int64;
  destination_id?: Int64;
  instance_id?: string;
}

export interface ConnectionTask {
  connection_id?: string;
  status?: string;
  entity_task_list?: Array<EntityTask>;
  instance_id?: string;
}

export interface ConnectorPreCheckRequest {
  source_connector_id?: connector_common.ConnectorID;
  source_connector_param?: string;
  dest_connector_id?: connector_common.ConnectorID;
  dest_connector_param?: string;
  Base?: base.Base;
}

export interface ConnectorPreCheckResponse {
  SourcePrecheckResult?: string;
  DestPrecheckResult?: string;
  code: Int64;
  msg: string;
}

export interface DataSourceOAuthCompleteRequest {
  code: string;
  state: string;
  auth_code?: string;
}

export interface DataSourceOAuthCompleteResponse {
  code: Int64;
  msg: string;
}

export interface DataSourceOAuthConsentURLRequest {
  connector_id: connector_common.ConnectorID;
  redirect_url: string;
  Base?: base.Base;
}

export interface DataSourceOAuthConsentURLResponse {
  consent_url?: string;
  code: Int64;
  msg: string;
}

export interface EntityTask {
  entity_id?: string;
  connection_id?: string;
  file_id?: string;
  file_name?: string;
  status?: string;
  file_node_type?: data_connector_common.FileNodeType;
  tos_key?: string;
  file_url?: string;
  error_msg?: string;
  file_size?: string;
  instance_id?: string;
  record_id?: string;
  error_code?: Int64;
}

export interface FileNode {
  file_id?: string;
  file_node_type?: data_connector_common.FileNodeType;
  file_name?: string;
  has_children_nodes?: boolean;
  children_nodes?: Array<FileNode>;
  icon?: string;
  file_type?: string;
  file_url?: string;
  /** wiki, 知识空间id */
  space_id?: string;
  /** wiki, 表示知识空间类型（团队空间 或 个人空间） */
  space_type?: string;
  /** wiki, 对应文档类型的token，可根据 obj_type 判断属于哪种文档类型 */
  obj_token?: string;
  /** wiki, 文档类型，对于快捷方式，该字段是对应的实体的obj_type */
  obj_type?: string;
}

export interface GetConnectionEntityRequest {
  entity_id: string;
  Base?: base.Base;
}

export interface GetConnectionEntityResponse {
  connection_id?: string;
  auth_id?: string;
  /** 三方数据平台文件列表 */
  file_node?: FileNode;
  is_exist: boolean;
  is_authorized: boolean;
  /** 该字段暂时不返回 */
  auth_info_list?: Array<AuthInfo>;
  code: Int64;
  msg: string;
}

export interface GetConnectorIDListRequest {
  Base?: base.Base;
}

export interface GetConnectorIDListResponse {
  connector_id_list?: Array<connector_common.ConnectorID>;
  code: Int64;
  msg: string;
}

export interface GetDataSourceFileTreeRequest {
  auth_id: string;
  file_type_list: Array<data_connector_common.FileNodeType>;
  folder_id?: string;
  query_all?: boolean;
  Base?: base.Base;
}

export interface GetDataSourceFileTreeResponse {
  /** 三方数据平台文件列表 */
  data_source_file_tree?: Array<FileNode>;
  code: Int64;
  msg: string;
}

export interface MGetAuthInfoRequest {
  connector_id_list: Array<connector_common.ConnectorID>;
  Base?: base.Base;
}

export interface MGetAuthInfoResponse {
  auth_info_map?: Partial<
    Record<connector_common.ConnectorID, Array<AuthInfo>>
  >;
  code: Int64;
  msg: string;
}

export interface PollConnectionTaskRequest {
  instance_id_list: Array<string>;
  file_node_type: connector_common.FileNodeType;
  Base?: base.Base;
}

export interface PollConnectionTaskResponse {
  connection_task?: Array<ConnectionTask>;
  code: Int64;
  msg: string;
}

export interface RetryEntityTaskRequest {
  entity_id: string;
  instance_id?: string;
  Base?: base.Base;
}

export interface RetryEntityTaskResponse {
  connection_info?: ConnectionInfo;
  code: Int64;
  msg: string;
}

export interface SubmitConnectionTaskRequest {
  connection_file_node_list: Array<ConnectionFileNode>;
  Base?: base.Base;
}

export interface SubmitConnectionTaskResponse {
  connection_info_list?: Array<ConnectionInfo>;
  code: Int64;
  msg: string;
}
/* eslint-enable */
