/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as volcano_database from './volcano_database';
import * as base from './base';

export type Int64 = string | number;

export enum ActionKey {
  /** 复制 */
  Copy = 1,
  /** 删除 */
  Delete = 2,
  /** 启用/禁用 */
  EnableSwitch = 3,
  /** 编辑 */
  Edit = 4,
  /** 跨空间复制 */
  CrossSpaceCopy = 10,
}

export enum BotTableRWMode {
  /** 单用户模式 */
  LimitedReadWrite = 1,
  /** 只读模式 */
  ReadOnly = 2,
  /** 多用户模式 */
  UnlimitedReadWrite = 3,
  /** Max 边界值 */
  RWModeMax = 4,
}

export enum BotTableStatus {
  /** 已上线 */
  Online = 1,
  /** 删除 */
  Delete = 2,
}

export enum ColumnType {
  Unknown = 0,
  /** 文本 */
  Text = 1,
  /** 数字 */
  Number = 2,
  /** 时间 */
  Date = 3,
  /** float */
  Float = 4,
  /** bool */
  Boolean = 5,
  /** 图片 */
  Image = 6,
}

/** *****  bot_table start   ******* */
export enum DatabaseType {
  All = -1,
  /** Coze上创建的数据库 */
  Coze = 0,
  /** 火山引擎数据库 */
  Volcano = 1,
}

export enum FieldFunction {
  /** 当前时间戳 */
  CURRENT_TIMESTAMP = 1,
  /** 当前日期 */
  CURRENT_DATE = 2,
  /** 当前时间 */
  CURRENT_TIME = 3,
  /** 当前日期和时间（等同于 CURRENT_TIMESTAMP） */
  NOW = 4,
  /** 当前 UTC 时间戳 */
  UTC_TIMESTAMP = 5,
  /** 当前 UTC 日期 */
  UTC_DATE = 6,
  /** 当前 UTC 时间 */
  UTC_TIME = 7,
  /** 生成 UUID */
  UUID = 8,
  /** 生成随机数 */
  RAND = 9,
  /** 当前用户 */
  USER = 10,
  /** 当前数据库 */
  DATABASE = 11,
  /** MySQL 版本 */
  VERSION = 12,
  /** 最后插入的 ID */
  LAST_INSERT_ID = 13,
}

export enum FieldItemType {
  Unknown = 0,
  /** 文本 */
  Text = 1,
  /** 数字 */
  Number = 2,
  /** 时间 */
  Date = 3,
  /** float */
  Float = 4,
  /** bool */
  Boolean = 5,
  /** 二进制 */
  Binary = 6,
}

export enum OperateType {
  Insert = 1,
  Update = 2,
  Delete = 3,
  Select = 4,
}

export enum Operation {
  /** "=" */
  EQUAL = 1,
  /** "<>" 或 "!=" */
  NOT_EQUAL = 2,
  /** ">" */
  GREATER_THAN = 3,
  /** "<" */
  LESS_THAN = 4,
  /** ">=" */
  GREATER_EQUAL = 5,
  /** "<=" */
  LESS_EQUAL = 6,
  /** "IN" */
  IN = 7,
  /** "NOT IN" */
  NOT_IN = 8,
  /** "IS NULL" */
  IS_NULL = 9,
  /** "IS NOT NULL" */
  IS_NOT_NULL = 10,
  /** "LIKE" 模糊匹配字符串 */
  LIKE = 11,
  /** "NOT LIKE" 反向模糊匹配 */
  NOT_LIKE = 12,
}

export enum PublishStatus {
  /** 未发布 */
  UnPublished = 1,
  /** 已发布 */
  Published = 2,
}

export enum RefType {
  NoRef = 0,
  Bot = 1,
  ChatGroup = 2,
}

export enum SceneType {
  /** bot 个性描述 */
  BotPersona = 1,
  /** 开发者给的模型文本描述 */
  ModelDesc = 2,
}

export enum ScopeType {
  Draft = 1,
  Online = 2,
  All = 3,
}

export enum SortDirection {
  ASC = 1,
  Desc = 2,
}

export enum TableDataType {
  /** schema sheets 和 preview data */
  AllData = 0,
  /** 只需要 schema 结构 & Sheets */
  OnlySchema = 1,
  /** 只需要 preview data */
  OnlyPreview = 2,
}

export enum TableRWMode {
  ReadWrite = 1,
  ReadOnly = 2,
  DisableDelete = 3,
  RWModeMax = 4,
}

export enum TableType {
  /** 草稿 */
  DraftTable = 1,
  /** 线上 */
  OnlineTable = 2,
}

export interface AddDatabaseRequest {
  /** 创建者id */
  creator_id?: string;
  /** 空间的id */
  space_id?: string;
  /** project id */
  project_id?: string;
  /** 头像url */
  icon_uri?: string;
  /** 表名 */
  table_name?: string;
  /** 表描述 */
  table_desc?: string;
  /** 字段信息 */
  field_list?: Array<FieldItem>;
  /** 读写模式，单用户模式/多用户模式 */
  rw_mode?: BotTableRWMode;
  /** 是否支持prompt调用 */
  prompt_disabled?: boolean;
  /** 扩展信息 */
  extra_info?: Record<string, string>;
  database_type?: DatabaseType;
  volcano_database_bind_info?: volcano_database.VolcanoDatabaseBindInfo;
  /** 火山region id */
  region_id?: string;
  Base?: base.Base;
}

export interface AlterBotTableRequest {
  /** 修改表信息 */
  bot_table?: BotTable;
  Base?: base.Base;
}

export interface AlterBotTableResponse {
  /** table id */
  table_id?: string;
  BaseResp: base.BaseResp;
}

export interface BinaryDefault {
  value?: Blob;
}

export interface BindDatabaseToBotRequest {
  /** 草稿态数据database表主键id，注意是草稿态哈 */
  database_id?: string;
  /** bot_id */
  bot_id?: string;
  Base?: base.Base;
}

export interface BindDatabaseToBotResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface BoolDefault {
  value?: boolean;
}

export interface BotTable {
  /** 自增id，table id */
  id?: string;
  /** 相关id. bot_id */
  bot_id?: string;
  /** table_id */
  table_id?: string;
  /** 表名 */
  table_name?: string;
  /** 表描述 */
  table_desc?: string;
  /** 状态 */
  status?: BotTableStatus;
  /** 创建着id */
  creator_id?: Int64;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
  /** 字段信息 */
  field_list?: Array<FieldItem>;
  /** 数据表实际名称 */
  actual_table_name?: string;
  /** 读写模式 */
  rw_mode?: BotTableRWMode;
  /** 扩展信息 */
  extra_info?: Record<string, string>;
}

export interface ComplexCondition {
  conditions?: Array<Condition>;
  /** 为了拓展，先不用 */
  nestedConditions?: ComplexCondition;
  /** "AND" 或 "OR" */
  logic: string;
}

export interface Condition {
  /** 左值填字段名 */
  left: string;
  operation: Operation;
  /** 右值 */
  right: string;
}

export interface ConnectorInfo {
  ConnectorID?: Int64;
  ConnectorName?: string;
  ConnectorIDStr?: string;
}

export interface Criterion {
  conditions?: Array<Condition>;
  logic_expression?: string;
}

export interface DatabaseFileProgressData {
  file_name?: string;
  progress?: number;
  /** 描述信息，如果有代表文件处理失败 */
  status_descript?: string;
}

export interface DatabaseInfo {
  /** online_database_info的主键id */
  id?: string;
  /** 空间的id */
  space_id?: string;
  /** project id */
  project_id?: string;
  /** datamodel侧的表id */
  datamodel_table_id?: string;
  /** 头像url */
  icon_url?: string;
  /** 头像url */
  icon_uri?: string;
  /** 表名 */
  table_name?: string;
  /** 表描述 */
  table_desc?: string;
  /** 状态 */
  status?: BotTableStatus;
  /** 创建者id */
  creator_id?: string;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
  /** 字段信息 */
  field_list?: Array<FieldItem>;
  /** 数据表实际名称 */
  actual_table_name?: string;
  /** 读写模式 */
  rw_mode?: BotTableRWMode;
  /** 是否支持prompt调用 */
  prompt_disabled?: boolean;
  /** 是否可见 */
  is_visible?: boolean;
  /** 对应草稿态的id */
  draft_id?: string;
  /** 相关id. bot_id，老的有，新的没有 */
  bot_id?: string;
  /** 扩展信息 */
  extra_info?: Record<string, string>;
  /** 是否已经添加到bot中 */
  is_added_to_bot?: boolean;
  /** 0=coze知识库 1=火山知识库 */
  database_type?: DatabaseType;
  volcano_storage_config?: volcano_database.VolcanoStorageConfig;
}

export interface DateDefault {
  /** 动态函数（如 CURRENT_TIMESTAMP） */
  func?: FieldFunction;
  /** 更新时的动态函数 */
  on_update?: FieldFunction;
  /** 静态值（格式：YYYY-MM-DD） */
  value?: string;
}

export interface DateTimeDefault {
  /** 动态函数（如 CURRENT_TIMESTAMP） */
  func?: FieldFunction;
  /** 更新时的动态函数 */
  on_update?: FieldFunction;
  /** 静态值（格式：YYYY-MM-DD HH:MM:SS） */
  value?: string;
}

export interface DecimalDefault {
  value?: string;
}

export interface DeleteBotTableRequest {
  related_id: string;
  table_id: string;
  user_id?: Int64;
  Base?: base.Base;
}

export interface DeleteBotTableResponse {
  /** table id */
  table_id?: string;
  BaseResp: base.BaseResp;
}

export interface DeleteDatabaseRequest {
  /** database_info的主键id */
  id?: string;
  Base?: base.Base;
}

export interface DeleteDatabaseResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

/** 展示用，实现方提供展示信息 */
export interface DisplayResourceInfo {
  /** 资源id */
  ResID?: Int64;
  /** 资源描述 */
  Desc?: string;
  /** 资源Icon，完整url */
  Icon?: string;
  /** 资源状态，各类型资源自身定义 */
  BizResStatus?: number;
  /** 是否开启多人编辑 */
  CollaborationEnable?: boolean;
  /** 业务携带的扩展信息，以res_type区分，每个res_type定义的schema和含义不一样，使用前需要判断res_type */
  BizExtend?: Record<string, string>;
  /** 不同类型的不同操作按钮，由资源实现方和前端约定。返回则展示，要隐藏某个按钮，则不要返回； */
  Actions?: Array<ResourceAction>;
  /** 是否禁止进详情页 */
  DetailDisable?: boolean;
  /** 资源名称 */
  Name?: string;
  /** 资源发布状态，1-未发布，2-已发布 */
  PublishStatus?: PublishStatus;
  /** 最近编辑时间, unix秒级时间戳 */
  EditTime?: Int64;
  ResSubType?: number;
}

export interface EnumDefault {
  value?: string;
}

/** struct VolcanoDBFieldItem {
    1: string        name
    2: string        desc
    3: FieldItemType type
    4: bool          must_required
    5: i64           id                 // 该字段只用来判断是否发布，不为 0 就是已发布的，前端对已发布的字段不能修改字段类型
    6: i64           alterId            // 修改字段时（alter、publish）用来判断增删改，0 表示新增，非 0 表示修改或删除
    7: bool          is_primary_key     // 是否是主键
    8: FieldDefault default_value       // 默认值
    9: map<string,string> map_ext_meta      // 业务自定义扩展field元数据
} */
export interface FieldDefault {
  /** CHAR、VARCHAR、TEXT */
  default_text?: TextDefault;
  /** INT、TINYINT、SMALLINT、MEDIUMINT、BIGINT */
  default_number?: NumberDefault;
  /** FLOAT、DOUBLE */
  default_float?: FloatDefault;
  /** DECIMAL */
  default_decimal?: DecimalDefault;
  /** BOOL、BOOLEAN */
  default_bool?: BoolDefault;
  /** DATE */
  default_date?: DateDefault;
  /** DATETIME */
  default_datetime?: DateTimeDefault;
  /** TIMESTAMP */
  default_timestamp?: TimestampDefault;
  /** TIME */
  default_time?: TimeDefault;
  /** ENUM */
  default_enum?: EnumDefault;
  /** SET */
  default_set?: SetDefault;
  /** JSON */
  default_json?: JsonDefault;
  /** BLOB、BINARY、VARBINARY */
  default_binary?: BinaryDefault;
}

export interface FieldItem {
  name?: string;
  desc?: string;
  type?: FieldItemType;
  must_required?: boolean;
  /** 该字段只用来判断是否发布，不为 0 就是已发布的，前端对已发布的字段不能修改字段类型 */
  id?: Int64;
  /** 修改字段时（alter、publish）用来判断增删改，0 表示新增，非 0 表示修改或删除 */
  alterId?: Int64;
  /** 是否是系统字段 */
  is_system_field?: boolean;
  /** 是否是主键 */
  is_primary_key?: boolean;
  /** 默认值 */
  default_value?: FieldDefault;
  /** 默认值 */
  default_value_str?: string;
  /** 业务自定义扩展field元数据 */
  map_ext_meta?: Record<string, string>;
  /** 原始的数据类型（目前火山数据库会返回这个字段） */
  origin_type?: string;
}

export interface FloatDefault {
  value?: number;
}

export interface GetBotTableRequest {
  creator_id?: Int64;
  bot_id?: string;
  table_ids?: Array<string>;
  table_type: TableType;
  Base?: base.Base;
}

export interface GetBotTableResponse {
  BotTableList?: Array<BotTable>;
  BaseResp: base.BaseResp;
}

export interface GetDatabaseFileProgressRequest {
  database_id?: string;
  /** table类型 */
  table_type: TableType;
  Base?: base.Base;
}

export interface GetDatabaseFileProgressResponse {
  data?: DatabaseFileProgressData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetDatabaseTemplateRequest {
  database_id?: string;
  /** table类型 */
  table_type: TableType;
  Base?: base.Base;
}

export interface GetDatabaseTemplateResponse {
  /** 下载地址 */
  TosUrl?: string;
  BaseResp?: base.BaseResp;
}

export interface GetModeConfigRequest {
  /** bot id */
  bot_id: Int64;
  /** 业务线id */
  connector_id?: Int64;
  /** 业务线用户id */
  connector_uid?: string;
  Base?: base.Base;
}

export interface GetModeConfigResponse {
  code?: number;
  msg?: string;
  mode?: string;
  bot_id?: Int64;
  max_table_num?: Int64;
  max_column_num?: Int64;
  max_capacity_kb?: Int64;
  max_row_num?: Int64;
  BaseResp?: base.BaseResp;
}

export interface GetNL2SQLRequest {
  /** 数据库请求的自然语言描述 */
  text: string;
  /** bot id */
  bot_id: Int64;
  /** 业务线id */
  connector_id?: Int64;
  /** 业务线用户id */
  connector_uid?: string;
  /** table类型，分 draft 和 online 两种 */
  table_type: TableType;
  /** 数据库id */
  database_id?: string;
  Base?: base.Base;
}

export interface GetNL2SQLResponse {
  code?: number;
  msg?: string;
  sql: string;
  extraMap?: Record<string, string>;
  BaseResp?: base.BaseResp;
}

export interface GetOnlineDatabaseIdRequest {
  /** draft 的database_id */
  id: string;
  Base?: base.Base;
}

export interface GetOnlineDatabaseIdResponse {
  /** 根据草稿的id查询线上的id */
  id?: string;
  BaseResp: base.BaseResp;
}

export interface GetSpaceConnectorListRequest {
  SpaceId: string;
  /** release inhouse */
  Version?: string;
  ConnectorID?: Int64;
  ListAll?: boolean;
  Base?: base.Base;
}

export interface GetSpaceConnectorListResponse {
  ConnectorList?: Array<ConnectorInfo>;
  BaseResp?: base.BaseResp;
}

export interface GetTableSchemaRequest {
  /** 表格解析信息, 默认初始值0,0,1 */
  table_sheet?: TableSheet;
  /** 不传默认返回所有数据 */
  table_data_type?: TableDataType;
  /** 兼容重构前的版本：如果需要拉取的是当前 document 的 schema 时传递该值 */
  database_id?: string;
  /** source file 的信息，新增 segment / 之前逻辑迁移到这里 */
  source_file?: SourceInfo;
  Base?: base.Base;
}

export interface InsertBotTableRequest {
  /** 保存表信息 */
  bot_table?: BotTable;
  Base?: base.Base;
}

export interface InsertBotTableResponse {
  /** table id */
  table_id?: string;
  BaseResp: base.BaseResp;
}

export interface JsonDefault {
  value?: string;
}

export interface ListDatabaseRecordsRequest {
  /** database_id */
  database_id: string;
  /** bot id，这里是查找bot关联的草稿态数据的时候填这个 */
  bot_id?: string;
  /** workflow_id，，这里是查找wk_flow关联的草稿态表的时候填这个 */
  workflow_id?: string;
  /** 为true不根据user_id进行过滤Records */
  not_filter_by_user_id?: boolean;
  /** 为true不根据ConnectorID进行过滤Records */
  not_filter_by_connector_id?: boolean;
  /** 要查的是草稿态还是线上态 */
  table_type?: TableType;
  /** 别超过100，建议50 */
  limit?: Int64;
  /** 偏移量 */
  offset?: Int64;
  /** 同个project下数据不隔离 */
  project_id?: string;
  /** 筛选条件 */
  filter_criterion?: ComplexCondition;
  /** 排序条件 */
  order_by_list?: Array<OrderBy>;
  Base?: base.Base;
}

export interface ListDatabaseRecordsResponse {
  data: Array<Record<string, string>>;
  HasMore: boolean;
  TotalNum: number;
  /** 字段信息 */
  field_list?: Array<FieldItem>;
  BaseResp: base.BaseResp;
}

export interface ListDatabaseRequest {
  /** 获取创建者为某个用户的的数据库 */
  creator_id?: string;
  /** 获取project下的数据库 */
  project_id?: string;
  /** 获取空间下的可见数据库 */
  space_id?: string;
  /** 对bot_id进行过滤，过滤掉已经添加到bot中的database */
  bot_id?: string;
  /** 表格名称，模糊搜索 */
  table_name?: string;
  /** 查草稿态database */
  table_type: TableType;
  /** 排序 */
  order_by?: Array<OrderBy>;
  offset?: number;
  limit?: number;
  /** 筛选条件 */
  filter_criterion?: Criterion;
  /** 排序条件 */
  order_by_list?: Array<OrderBy>;
  /** 数据库类型 0-coze 1-火山 */
  database_type?: DatabaseType;
  Base?: base.Base;
}

export interface ListDatabaseResponse {
  database_info_list?: Array<DatabaseInfo>;
  has_more?: boolean;
  total_count?: Int64;
  BaseResp: base.BaseResp;
}

export interface MigrateOldDataRequest {
  /** 迁移哪个表 */
  bot_type?: TableType;
  /** 迁移哪个bot */
  bot_id?: string;
  /** 失败重试 */
  table_ids?: Array<string>;
  Base?: base.Base;
}

export interface MigrateOldDataResponse {
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface NumberDefault {
  auto_increment?: boolean;
  value?: Int64;
}

export interface OrderBy {
  field?: string;
  direction?: SortDirection;
}

export interface RecommendDataModelRequest {
  bot_id: string;
  scene_type: SceneType;
  text?: string;
  Base?: base.Base;
}

export interface RecommendDataModelResponse {
  bot_table_list?: Array<BotTable>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface RefInfo {
  /** 引用类型 */
  ref_type?: RefType;
  /** 引用 id */
  ref_id?: string;
}

export interface ResetBotTableRequest {
  creator_id?: string;
  bot_id?: string;
  table_id?: string;
  table_type: TableType;
  /** 业务线id */
  connector_id?: Int64;
  /** 业务线用户id */
  connector_uid?: string;
  /** 工作流id */
  workflow_id?: string;
  /** 用户id */
  database_info_id?: string;
  /** 项目id */
  project_id?: string;
  Base?: base.Base;
}

export interface ResetBotTableResponse {
  code?: Int64;
  msg?: string;
}

/** Library资源操作 */
export interface ResourceAction {
  /** 一个操作对应一个唯一的key，key由资源侧约束 */
  key: ActionKey;
  /** ture=可以操作该Action，false=置灰 */
  enable: boolean;
}

export interface Row {
  values?: Array<UpsertValues>;
}

export interface SearchBotTableInfoRequest {
  /** 搜素词,目前忽略 */
  key_word?: string;
  limit?: Int64;
  offset?: Int64;
  /** 用户id */
  connector_uid?: string;
  connector_id?: Int64;
  /** bot id */
  bot_id?: string;
  /** 目前忽略 */
  table_name?: string;
  table_id?: string;
  /** 引用信息 */
  ref_info?: RefInfo;
  Base?: base.Base;
}

export interface SearchBotTableInfoResponse {
  data: Array<Record<string, string>>;
  HasMore: boolean;
  TotalNum: number;
  BaseResp: base.BaseResp;
}

export interface SelectFieldList {
  FieldID: Array<string>;
  isDistinct: boolean;
}

export interface SetDefault {
  value?: Array<string>;
}

export interface SingleDatabaseRequest {
  /** database_info的主键id */
  id?: string;
  /** 传入的是否是草稿态数据，默认是false */
  is_draft?: boolean;
  /** 是否需要系统字段 */
  need_sys_fields?: boolean;
  /** 版本号，不传默认是最新的 */
  version?: string;
  Base?: base.Base;
}

export interface SingleDatabaseResponse {
  database_info?: DatabaseInfo;
  BaseResp: base.BaseResp;
}

export interface SourceInfo {
  /** 本地文件上传的 tos 地址 */
  tos_uri?: string;
  /** imagex_uri, 和 tos_uri 二选一, imagex_uri 优先，需要通过 imagex 的方法获取数据和签发 url */
  imagex_uri?: string;
}

export interface SqlParamVal {
  value_type: FieldItemType;
  is_null: boolean;
  value?: string;
  name?: string;
  origin_type?: string;
}

export interface SubmitDatabaseInsertRequest {
  database_id?: string;
  file_uri?: string;
  /** table类型，要往草稿表插入还是线上表插入 */
  table_type?: TableType;
  table_sheet?: TableSheet;
  /** 要写入的渠道id */
  connector_id?: string;
  Base?: base.Base;
}

export interface SubmitDatabaseInsertResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface TableInfo {
  /** online_database_info的主键id */
  id?: string;
  /** 头像url */
  icon_url?: string;
  /** 表名 */
  table_name?: string;
  /** 表描述 */
  table_desc?: string;
  /** 创建者id */
  creator_id?: string;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
  /** project id */
  project_id?: string;
  icon_uri?: string;
  /** 数据库类型 */
  database_type?: DatabaseType;
}

export interface TableSheet {
  /** 用户选择的 sheet id */
  sheet_id?: string;
  /** 用户选择的表头行数，从 0 开始编号 */
  header_line_idx?: string;
  /** 用户选择的起始行号，从 0 开始编号 */
  start_line_idx?: string;
}

export interface TextDefault {
  /** CHAR、VARCHAR、TEXT 的默认值 */
  value?: string;
}

export interface TimeDefault {
  /** 动态函数（如 CURRENT_TIMESTAMP） */
  func?: FieldFunction;
  /** 更新时的动态函数 */
  on_update?: FieldFunction;
  /** 静态值（格式：HH:MM:SS） */
  value?: string;
}

export interface TimestampDefault {
  /** 动态函数（如 CURRENT_TIMESTAMP） */
  func?: FieldFunction;
  /** 更新时的动态函数 */
  on_update?: FieldFunction;
  /** 静态值（格式：YYYY-MM-DD HH:MM:SS） */
  value?: string;
}

export interface UpdateDatabaseBotSwitchRequest {
  bot_id: string;
  database_id: string;
  /** 是否禁用prompt */
  prompt_disable: boolean;
  Base?: base.Base;
}

export interface UpdateDatabaseBotSwitchResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface UpdateDatabaseRecordsRequest {
  /** database_id */
  database_id: string;
  /** 新增的 */
  record_data_add?: Array<Record<string, string>>;
  /** 修改的 */
  record_data_alter?: Array<Record<string, string>>;
  /** 删除的 */
  record_data_delete?: Array<Record<string, string>>;
  /** 要更新的的是草稿态还是线上态 */
  table_type?: TableType;
  /** 更新时需穿入connector id */
  ori_connector_id?: string;
  Base?: base.Base;
}

export interface UpdateDatabaseRecordsResponse {
  data: Array<Record<string, string>>;
  BaseResp: base.BaseResp;
}

export interface UpdateDatabaseRequest {
  /** database_info的主键id */
  id?: string;
  /** 头像url */
  icon_uri?: string;
  /** 表名 */
  table_name?: string;
  /** 表描述 */
  table_desc?: string;
  /** 字段信息 */
  field_list?: Array<FieldItem>;
  /** 读写模式，单用户模式/多用户模式 */
  rw_mode?: BotTableRWMode;
  /** 是否支持prompt调用 */
  prompt_disabled?: boolean;
  /** 扩展信息 */
  extra_info?: Record<string, string>;
  Base?: base.Base;
}

export interface UpsertValues {
  field_id?: string;
  field_value?: string;
}

export interface ValidateTableSchemaRequest {
  space_id?: string;
  database_id?: string;
  /** source file 的信息 */
  source_file?: SourceInfo;
  table_sheet?: TableSheet;
  table_type?: TableType;
  Base?: base.Base;
}

export interface ValidateTableSchemaResponse {
  schema_valid_result?: Record<string, string>;
  /** 如果失败会返回错误码 */
  code: Int64;
  msg: string;
}
/* eslint-enable */
