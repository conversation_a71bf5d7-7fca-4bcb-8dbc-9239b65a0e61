/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as bot_common from './namespaces/bot_common';
import * as common from './namespaces/common';
import * as connector from './namespaces/connector';
import * as connector_common from './namespaces/connector_common';
import * as copilot_common from './namespaces/copilot_common';
import * as data_connector from './namespaces/data_connector';
import * as data_connector_common from './namespaces/data_connector_common';
import * as dataset from './namespaces/dataset';
import * as document from './namespaces/document';
import * as kvmemory from './namespaces/kvmemory';
import * as long_term_memory from './namespaces/long_term_memory';
import * as memory from './namespaces/memory';
import * as project_memory from './namespaces/project_memory';
import * as resource_common from './namespaces/resource_common';
import * as retriever from './namespaces/retriever';
import * as slice from './namespaces/slice';
import * as table from './namespaces/table';
import * as team_space from './namespaces/team_space';
import * as volcano_database from './namespaces/volcano_database';
import * as web_crawl from './namespaces/web_crawl';

export {
  base,
  bot_common,
  common,
  connector,
  connector_common,
  copilot_common,
  data_connector,
  data_connector_common,
  dataset,
  document,
  kvmemory,
  long_term_memory,
  memory,
  project_memory,
  resource_common,
  retriever,
  slice,
  table,
  team_space,
  volcano_database,
  web_crawl,
};
export * from './namespaces/base';
export * from './namespaces/bot_common';
export * from './namespaces/common';
export * from './namespaces/connector';
export * from './namespaces/connector_common';
export * from './namespaces/copilot_common';
export * from './namespaces/data_connector';
export * from './namespaces/data_connector_common';
export * from './namespaces/dataset';
export * from './namespaces/document';
export * from './namespaces/kvmemory';
export * from './namespaces/long_term_memory';
export * from './namespaces/memory';
export * from './namespaces/project_memory';
export * from './namespaces/resource_common';
export * from './namespaces/retriever';
export * from './namespaces/slice';
export * from './namespaces/table';
export * from './namespaces/team_space';
export * from './namespaces/volcano_database';
export * from './namespaces/web_crawl';

export type Int64 = string | number;

export default class MemoryService<T> {
  private request: any = () => {
    throw new Error('MemoryService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/memory/get_data_source_list
   *
   * data connector接口
   */
  GetUserDataSourceList(
    req?: data_connector.GetUserDataSourceListRequest,
    options?: T,
  ): Promise<data_connector.GetUserDataSourceListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/get_data_source_list');
    const method = 'POST';
    const data = { redirect_url: _req['redirect_url'], Base: _req['Base'] };
    const headers = { host: _req['host'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/memory/associate_file */
  AssociateFile(
    req?: data_connector.AssociateFileRequest,
    options?: T,
  ): Promise<data_connector.AssociateFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/associate_file');
    const method = 'POST';
    const data = { params: _req['params'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/source_file_check */
  CheckSourceFile(
    req?: data_connector.CheckSourceFileRequest,
    options?: T,
  ): Promise<data_connector.CheckSourceFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/source_file_check');
    const method = 'POST';
    const data = {
      source_file_id: _req['source_file_id'],
      redirect_uri: _req['redirect_uri'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/get_authorization_files */
  GetAuthorizationFileList(
    req: data_connector.GetAuthorizationFileListRequest,
    options?: T,
  ): Promise<data_connector.GetAuthorizationFileListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/get_authorization_files');
    const method = 'POST';
    const data = {
      data_source_id: _req['data_source_id'],
      file_type: _req['file_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/memory/delete_user_authorization
   *
   * 三方数据删除接口
   */
  DeleteUserDataSource(
    req?: document.DeleteUserDataSourceRequest,
    options?: T,
  ): Promise<document.DeleteUserDataSourceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/delete_user_authorization');
    const method = 'DELETE';
    const params = {
      data_source_id: _req['data_source_id'],
      need_delete_document: _req['need_delete_document'],
      auth_id: _req['auth_id'],
      connector_id: _req['connector_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/memory/process_document */
  ProcessDocumentsTask(
    req?: document.ProcessDocumentsTaskRequest,
    options?: T,
  ): Promise<document.ProcessDocumentsTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/process_document');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      dataset_id: _req['dataset_id'],
      document_infos: _req['document_infos'],
      rule: _req['rule'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/memory/doc_table_info */
  GetDocumentTableInfo(
    req?: document.GetDocumentTableInfoRequest,
    options?: T,
  ): Promise<document.GetDocumentTableInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/doc_table_info');
    const method = 'GET';
    const params = {
      submit_web_id: _req['submit_web_id'],
      tos_uri: _req['tos_uri'],
      document_id: _req['document_id'],
      creator_id: _req['creator_id'],
      source_file_id: _req['source_file_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/memory/list_document
   *
   * Document
   */
  ListDocument(
    req?: document.ListDocumentRequest,
    options?: T,
  ): Promise<document.ListDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/list_document');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      creator_id: _req['creator_id'],
      document_id: _req['document_id'],
      page: _req['page'],
      size: _req['size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/table_schema_info */
  GetTableSchemaInfo(
    req?: document.GetTableSchemaInfoRequest,
    options?: T,
  ): Promise<document.GetTableSchemaInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/table_schema_info');
    const method = 'POST';
    const data = {
      tos_uri: _req['tos_uri'],
      doc_table_info: _req['doc_table_info'],
      table_data_type: _req['table_data_type'],
      document_id: _req['document_id'],
      source_file: _req['source_file'],
      origin_table_meta: _req['origin_table_meta'],
      preview_table_meta: _req['preview_table_meta'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/connector_gray
   *
   * DEPRECATED
   */
  GetConnectorGray(
    req?: data_connector.GetConnectorGrayRequest,
    options?: T,
  ): Promise<data_connector.GetConnectorGrayResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/connector_gray');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const headers = { host: _req['host'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/memory/nl2db
   *
   * NL2DB 接口
   */
  RecommendDataModel(
    req: table.RecommendDataModelRequest,
    options?: T,
  ): Promise<table.RecommendDataModelResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/nl2db');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      scene_type: _req['scene_type'],
      text: _req['text'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/batch_delete_slice
   *
   * 批量删除分片
   */
  BatchDeleteSlice(
    req: slice.BatchDeleteSliceRequest,
    options?: T,
  ): Promise<slice.BatchDeleteSliceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/batch_delete_slice');
    const method = 'POST';
    const data = {
      slice_id_list: _req['slice_id_list'],
      document_id: _req['document_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/submit_user_policy_record */
  SubmitUserPolicyRecord(
    req: data_connector.SubmitUserPolicyRecordRequest,
    options?: T,
  ): Promise<data_connector.SubmitUserPolicyRecordResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/memory/knowledge/submit_user_policy_record',
    );
    const method = 'POST';
    const data = {
      policy_type: _req['policy_type'],
      user_policy_action: _req['user_policy_action'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/memory/table_nl2sql */
  GetNL2SQL(
    req: table.GetNL2SQLRequest,
    options?: T,
  ): Promise<table.GetNL2SQLResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/table_nl2sql');
    const method = 'GET';
    const params = {
      text: _req['text'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      connector_uid: _req['connector_uid'],
      table_type: _req['table_type'],
      database_id: _req['database_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/memory/table_mode_config */
  GetModeConfig(
    req: table.GetModeConfigRequest,
    options?: T,
  ): Promise<table.GetModeConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/table_mode_config');
    const method = 'GET';
    const params = {
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      connector_uid: _req['connector_uid'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/memory/knowledge/create_dataset
   *
   * Dataset - 迁移中: 请在 flow_dataengine_dataset.thrift 里加
   */
  CreateDataSet(
    req?: dataset.CreateDataSetRequest,
    options?: T,
  ): Promise<dataset.CreateDataSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/create_dataset');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      name: _req['name'],
      description: _req['description'],
      icon_uri: _req['icon_uri'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/process_web_document */
  ProcessWebDocumentsTask(
    req?: document.ProcessWebDocumentsTaskRequest,
    options?: T,
  ): Promise<document.ProcessWebDocumentsTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/process_web_document');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      dataset_id: _req['dataset_id'],
      document_info: _req['document_info'],
      formatType: _req['formatType'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/get_slice_list
   *
   * 通过docID获取分片列表
   */
  GetSliceList(
    req?: slice.GetSliceListReq,
    options?: T,
  ): Promise<slice.GetSliceListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/get_slice_list');
    const method = 'POST';
    const data = {
      doc_id: _req['doc_id'],
      sequence: _req['sequence'],
      key_word: _req['key_word'],
      creator_id: _req['creator_id'],
      page_no: _req['page_no'],
      page_size: _req['page_size'],
      sort_field: _req['sort_field'],
      is_asc: _req['is_asc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/list_dataset */
  ListDataSetV2(
    req?: dataset.ListDataSetV2Request,
    options?: T,
  ): Promise<dataset.ListDataSetV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/list_dataset');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      query: _req['query'],
      search_type: _req['search_type'],
      page: _req['page'],
      size: _req['size'],
      dataset_ids: _req['dataset_ids'],
      space_id: _req['space_id'],
      scope_type: _req['scope_type'],
      source_type: _req['source_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/update_dataset_meta */
  UpdateDataSetMeta(
    req?: dataset.UpdateDataSetMetaRequest,
    options?: T,
  ): Promise<dataset.UpdateDataSetMetaResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/update_dataset_meta');
    const method = 'POST';
    const data = {
      data_set_id: _req['data_set_id'],
      creator_id: _req['creator_id'],
      name: _req['name'],
      icon_uri: _req['icon_uri'],
      description: _req['description'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/update_document */
  UpdateDocumentV2(
    req?: document.UpdateDocumentV2Request,
    options?: T,
  ): Promise<document.UpdateDocumentV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/update_document');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      document_id: _req['document_id'],
      status: _req['status'],
      document_name: _req['document_name'],
      table_meta: _req['table_meta'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/submit_web_content */
  SubmitWebContentV2(
    req?: document.SubmitWebContentV2Request,
    options?: T,
  ): Promise<document.SubmitWebContentV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/submit_web_content');
    const method = 'POST';
    const data = {
      web_id: _req['web_id'],
      content: _req['content'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/get_task_progress */
  GetTaskProgressV2(
    req?: document.GetTaskProgressV2Request,
    options?: T,
  ): Promise<document.GetTaskProgressV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/get_task_progress');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/update_slice_content
   *
   * 更新分片内容
   */
  UpdateSliceContent(
    req: slice.UpdateSliceContentReq,
    options?: T,
  ): Promise<slice.UpdateSliceContentResp> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/update_slice_content');
    const method = 'POST';
    const data = {
      slice_id: _req['slice_id'],
      creator_id: _req['creator_id'],
      content: _req['content'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/delete_slice
   *
   * 删除分片
   */
  DelSlice(req: slice.DelSliceReq, options?: T): Promise<slice.DelSliceResp> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/delete_slice');
    const method = 'POST';
    const data = {
      slice_id: _req['slice_id'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/change_slice_status
   *
   * 禁用启用分片
   */
  ChangeSliceStatus(
    req: slice.ChangeSliceStatusReq,
    options?: T,
  ): Promise<slice.ChangeSliceStatusResp> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/change_slice_status');
    const method = 'POST';
    const data = {
      slice_id: _req['slice_id'],
      creator_id: _req['creator_id'],
      status: _req['status'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/get_bot_list
   *
   * 通过dataset获取关联bot -- deprecated
   */
  GetBotListByDataset(
    req: dataset.GetBotListByDatasetReq,
    options?: T,
  ): Promise<dataset.GetBotListByDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/get_bot_list');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      page_size: _req['page_size'],
      page_no: _req['page_no'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/delete_dataset */
  DeleteDataSet(
    req?: dataset.DeleteDataSetRequest,
    options?: T,
  ): Promise<dataset.DeleteDataSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/delete_dataset');
    const method = 'POST';
    const data = {
      data_set_id: _req['data_set_id'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/query_web_info */
  QueryWebInfo(
    req?: document.QueryWebInfoRequest,
    options?: T,
  ): Promise<document.QueryWebInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/query_web_info');
    const method = 'POST';
    const data = {
      web_ids: _req['web_ids'],
      include_content: _req['include_content'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/submit_web_url
   *
   * web document
   *
   * 提交单个抓取任务
   */
  SubmitWebUrl(
    req?: document.SubmitWebUrlRequest,
    options?: T,
  ): Promise<document.SubmitWebUrlResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/submit_web_url');
    const method = 'POST';
    const data = {
      web_url: _req['web_url'],
      subpages_count: _req['subpages_count'],
      creator_id: _req['creator_id'],
      format_type: _req['format_type'],
      title: _req['title'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/delete_web_data */
  DelWebData(
    req?: document.DelWebDataRequest,
    options?: T,
  ): Promise<document.DelWebDataResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/delete_web_data');
    const method = 'POST';
    const data = {
      web_id: _req['web_id'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/resegment */
  Resegment(
    req: document.ResegmentRequest,
    options?: T,
  ): Promise<document.ResegmentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/resegment');
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      document_id: _req['document_id'],
      rule: _req['rule'],
      format_type: _req['format_type'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/create_slice
   *
   * slice
   *
   * 创建分片
   */
  CreateSlice(
    req: slice.CreateSliceReq,
    options?: T,
  ): Promise<slice.CreateSliceResp> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/create_slice');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      creator_id: _req['creator_id'],
      content: _req['content'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/update_web_rule */
  UpdateWebRule(
    req?: document.UpdateWebRuleRequest,
    options?: T,
  ): Promise<document.UpdateWebRuleResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/update_web_rule');
    const method = 'POST';
    const data = {
      document_id: _req['document_id'],
      update_type: _req['update_type'],
      update_interval: _req['update_interval'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/create_document */
  CreateDocument(
    req?: document.CreateDocumentRequest,
    options?: T,
  ): Promise<document.CreateDocumentResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/create_document');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      dataset_id: _req['dataset_id'],
      document: _req['document'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/table_info_valid */
  TableSchemaValid(
    req: document.TableSchemaValidRequest,
    options?: T,
  ): Promise<document.TableSchemaValidResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/table_info_valid');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      document_id: _req['document_id'],
      source_file: _req['source_file'],
      user_table_info: _req['user_table_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/add_segment */
  AddSegment(
    req: document.AddSegmentRequest,
    options?: T,
  ): Promise<document.AddSegmentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/add_segment');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      document_id: _req['document_id'],
      source_type: _req['source_type'],
      source_file: _req['source_file'],
      user_table_info: _req['user_table_info'],
      append_type: _req['append_type'],
      append_sequence: _req['append_sequence'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/table/add
   *
   * bot table
   */
  InsertBotTable(
    req?: table.InsertBotTableRequest,
    options?: T,
  ): Promise<table.InsertBotTableResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/table/add');
    const method = 'POST';
    const data = { bot_table: _req['bot_table'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/table_info/query */
  SearchBotTableInfo(
    req?: table.SearchBotTableInfoRequest,
    options?: T,
  ): Promise<table.SearchBotTableInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/table_info/query');
    const method = 'POST';
    const data = {
      key_word: _req['key_word'],
      limit: _req['limit'],
      offset: _req['offset'],
      connector_uid: _req['connector_uid'],
      connector_id: _req['connector_id'],
      bot_id: _req['bot_id'],
      table_name: _req['table_name'],
      table_id: _req['table_id'],
      ref_info: _req['ref_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/table/list */
  GetBotTable(
    req: table.GetBotTableRequest,
    options?: T,
  ): Promise<table.GetBotTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/table/list');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      bot_id: _req['bot_id'],
      table_ids: _req['table_ids'],
      table_type: _req['table_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/variable/upsert
   *
   * 插入一条kv数据
   */
  SetKvMemory(
    req: kvmemory.SetKvMemoryReq,
    options?: T,
  ): Promise<kvmemory.SetKvMemoryResp> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/variable/upsert');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      user_id: _req['user_id'],
      data: _req['data'],
      connector_id: _req['connector_id'],
      ref_info: _req['ref_info'],
      project_id: _req['project_id'],
      ProjectVersion: _req['ProjectVersion'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/variable/get */
  GetPlayGroundMemory(
    req?: kvmemory.GetProfileMemoryRequest,
    options?: T,
  ): Promise<kvmemory.GetProfileMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/variable/get');
    const method = 'POST';
    const data = {
      user_id: _req['user_id'],
      bot_id: _req['bot_id'],
      keywords: _req['keywords'],
      connector_id: _req['connector_id'],
      version: _req['version'],
      ref_info: _req['ref_info'],
      ext: _req['ext'],
      project_id: _req['project_id'],
      ProjectVersion: _req['ProjectVersion'],
      VariableChannel: _req['VariableChannel'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/table/alter */
  AlterBotTable(
    req?: table.AlterBotTableRequest,
    options?: T,
  ): Promise<table.AlterBotTableResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/table/alter');
    const method = 'POST';
    const data = { bot_table: _req['bot_table'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/table/delete */
  DeleteBotTable(
    req: table.DeleteBotTableRequest,
    options?: T,
  ): Promise<table.DeleteBotTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/table/delete');
    const method = 'POST';
    const data = {
      related_id: _req['related_id'],
      table_id: _req['table_id'],
      user_id: _req['user_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/variable/delete */
  DelProfileMemory(
    req?: kvmemory.DelProfileMemoryRequest,
    options?: T,
  ): Promise<kvmemory.DelProfileMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/variable/delete');
    const method = 'POST';
    const data = {
      user_id: _req['user_id'],
      bot_id: _req['bot_id'],
      keywords: _req['keywords'],
      connector_id: _req['connector_id'],
      ref_info: _req['ref_info'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/submit_connection_task
   *
   * 同步数据源下载任务
   */
  SubmitConnectionTask(
    req: connector.SubmitConnectionTaskRequest,
    options?: T,
  ): Promise<connector.SubmitConnectionTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/submit_connection_task');
    const method = 'POST';
    const data = {
      connection_file_node_list: _req['connection_file_node_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/query_auth_info
   *
   * 查询授权列表
   */
  MGetAuthInfo(
    req: connector.MGetAuthInfoRequest,
    options?: T,
  ): Promise<connector.MGetAuthInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/query_auth_info');
    const method = 'POST';
    const data = {
      connector_id_list: _req['connector_id_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/get_connection_entity
   *
   * 更新文档content查询entity接口
   */
  GetConnectionEntity(
    req: connector.GetConnectionEntityRequest,
    options?: T,
  ): Promise<connector.GetConnectionEntityResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/get_connection_entity');
    const method = 'POST';
    const data = { entity_id: _req['entity_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/poll_connection_task
   *
   * 轮询数据源下载任务
   */
  PollConnectionTask(
    req: connector.PollConnectionTaskRequest,
    options?: T,
  ): Promise<connector.PollConnectionTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/poll_connection_task');
    const method = 'POST';
    const data = {
      instance_id_list: _req['instance_id_list'],
      file_node_type: _req['file_node_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/data_source_files
   *
   * 获取pick三方数据源文件列表
   */
  GetDataSourceFileTree(
    req: connector.GetDataSourceFileTreeRequest,
    options?: T,
  ): Promise<connector.GetDataSourceFileTreeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/data_source_files');
    const method = 'POST';
    const data = {
      auth_id: _req['auth_id'],
      file_type_list: _req['file_type_list'],
      folder_id: _req['folder_id'],
      query_all: _req['query_all'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/oauth_consent_url
   *
   * 查询鉴权回调URL接口
   */
  DataSourceOAuthConsentURL(
    req: connector.DataSourceOAuthConsentURLRequest,
    options?: T,
  ): Promise<connector.DataSourceOAuthConsentURLResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/oauth_consent_url');
    const method = 'POST';
    const data = {
      connector_id: _req['connector_id'],
      redirect_url: _req['redirect_url'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/retry_entity_task
   *
   * 重试下载文档接口
   */
  RetryEntityTask(
    req: connector.RetryEntityTaskRequest,
    options?: T,
  ): Promise<connector.RetryEntityTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/retry_entity_task');
    const method = 'POST';
    const data = {
      entity_id: _req['entity_id'],
      instance_id: _req['instance_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/cancel_connetion_task
   *
   * 取消下载文档接口
   */
  CancelConnectionTask(
    req: connector.CancelConnectionTaskRequest,
    options?: T,
  ): Promise<connector.CancelConnectionTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/cancel_connetion_task');
    const method = 'POST';
    const data = {
      instance_id_list: _req['instance_id_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/memory/knowledge/oauth_complete */
  DataSourceOAuthComplete(
    req: connector.DataSourceOAuthCompleteRequest,
    options?: T,
  ): Promise<connector.DataSourceOAuthCompleteResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/oauth_complete');
    const method = 'GET';
    const params = {
      code: _req['code'],
      state: _req['state'],
      auth_code: _req['auth_code'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/memory/submit_crawl_data
   *
   * 前端抓取上传接口
   */
  SubmitCrawlData(
    req?: document.SubmitCrawlDataRequest,
    options?: T,
  ): Promise<document.SubmitCrawlDataResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/submit_crawl_data');
    const method = 'POST';
    const data = {
      web_documents: _req['web_documents'],
      dataset_id: _req['dataset_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/get_connector_id_list
   *
   * 获取用户可见的数据源id
   */
  GetConnectorIDList(
    req?: connector.GetConnectorIDListRequest,
    options?: T,
  ): Promise<connector.GetConnectorIDListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/get_connector_id_list');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/table/reset */
  ResetBotTable(
    req: table.ResetBotTableRequest,
    options?: T,
  ): Promise<table.ResetBotTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/table/reset');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      bot_id: _req['bot_id'],
      table_id: _req['table_id'],
      table_type: _req['table_type'],
      connector_id: _req['connector_id'],
      connector_uid: _req['connector_uid'],
      workflow_id: _req['workflow_id'],
      database_info_id: _req['database_info_id'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/knowledge/validate_unit_name */
  ValidateUnitName(
    req: document.ValidateUnitNameRequest,
    options?: T,
  ): Promise<document.ValidateUnitNameResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/knowledge/validate_unit_name');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      dataset_id: _req['dataset_id'],
      unit_name: _req['unit_name'],
      format_type: _req['format_type'],
      document_id: _req['document_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/connector_pre_check
   *
   * 连接器前置校验
   */
  ConnectorPreCheck(
    req?: connector.ConnectorPreCheckRequest,
    options?: T,
  ): Promise<connector.ConnectorPreCheckResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/connector_pre_check');
    const method = 'POST';
    const data = {
      source_connector_id: _req['source_connector_id'],
      source_connector_param: _req['source_connector_param'],
      dest_connector_id: _req['dest_connector_id'],
      dest_connector_param: _req['dest_connector_param'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/submit_batch_crawl_task
   *
   * 创建批量抓取任务  deprecated: 迁移中: 请在 flow_dataengine_dataset.thrift 里加
   */
  SubmitBatchCrawlTask(
    req?: web_crawl.SubmitBatchCrawlTaskRequest,
    options?: T,
  ): Promise<web_crawl.SubmitBatchCrawlTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/memory/knowledge/submit_batch_crawl_task',
    );
    const method = 'POST';
    const data = {
      web_urls: _req['web_urls'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/create_sub_link_discovery_task
   *
   * 创建子链接发现任务接口
   */
  CreateSubLinkDiscoveryTask(
    req?: web_crawl.CreateSubLinkDiscoveryTaskRequest,
    options?: T,
  ): Promise<web_crawl.CreateSubLinkDiscoveryTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/memory/knowledge/create_sub_link_discovery_task',
    );
    const method = 'POST';
    const data = {
      url: _req['url'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/abort_sub_link_discovery_task
   *
   * 终止子链接发现任务接口
   */
  AbortSubLinkDiscoveryTask(
    req?: web_crawl.AbortSubLinkDiscoveryTaskRequest,
    options?: T,
  ): Promise<web_crawl.AbortSubLinkDiscoveryTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/memory/knowledge/abort_sub_link_discovery_task',
    );
    const method = 'POST';
    const data = { task_id: _req['task_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/knowledge/parse_site_map
   *
   * 解析网站地图接口
   */
  ParseSiteMap(
    req?: web_crawl.ParseSiteMapRequest,
    options?: T,
  ): Promise<web_crawl.ParseSiteMapResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/knowledge/parse_site_map');
    const method = 'POST';
    const data = {
      sitemap_url: _req['sitemap_url'],
      creator_id: _req['creator_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/memory/knowledge/get_sub_link_discovery_task
   *
   * 查询子链接发现任务接口
   */
  GetSubLinkDiscoveryTask(
    req?: web_crawl.GetSubLinkDiscoveryTaskRequest,
    options?: T,
  ): Promise<web_crawl.GetSubLinkDiscoveryTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/memory/knowledge/get_sub_link_discovery_task',
    );
    const method = 'GET';
    const params = { task_id: _req['task_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/memory/sys_variable_conf */
  GetSysVariableConf(
    req?: kvmemory.GetSysVariableConfRequest,
    options?: T,
  ): Promise<kvmemory.GetSysVariableConfResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/sys_variable_conf');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/memory/database/delete
   *
   * 删除一个数据库
   */
  DeleteDatabase(
    req?: table.DeleteDatabaseRequest,
    options?: T,
  ): Promise<table.DeleteDatabaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/delete');
    const method = 'POST';
    const data = { id: _req['id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/bind_to_bot
   *
   * 绑定一个数据库到bot
   */
  BindDatabase(
    req?: table.BindDatabaseToBotRequest,
    options?: T,
  ): Promise<table.BindDatabaseToBotResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/bind_to_bot');
    const method = 'POST';
    const data = {
      database_id: _req['database_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/list_records
   *
   * 获取数据库中的Data内容（用户Records）
   */
  ListDatabaseRecords(
    req: table.ListDatabaseRecordsRequest,
    options?: T,
  ): Promise<table.ListDatabaseRecordsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/list_records');
    const method = 'POST';
    const data = {
      database_id: _req['database_id'],
      bot_id: _req['bot_id'],
      workflow_id: _req['workflow_id'],
      not_filter_by_user_id: _req['not_filter_by_user_id'],
      not_filter_by_connector_id: _req['not_filter_by_connector_id'],
      table_type: _req['table_type'],
      limit: _req['limit'],
      offset: _req['offset'],
      project_id: _req['project_id'],
      filter_criterion: _req['filter_criterion'],
      order_by_list: _req['order_by_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/list
   *
   * 新增「database」节点需求 - 接口列表 - start
   *
   * 根据各种组合条件获取数据库列表
   */
  ListDatabase(
    req: table.ListDatabaseRequest,
    options?: T,
  ): Promise<table.ListDatabaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/list');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      project_id: _req['project_id'],
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      table_name: _req['table_name'],
      table_type: _req['table_type'],
      order_by: _req['order_by'],
      offset: _req['offset'],
      limit: _req['limit'],
      filter_criterion: _req['filter_criterion'],
      order_by_list: _req['order_by_list'],
      database_type: _req['database_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/update
   *
   * 更新一个数据库
   */
  UpdateDatabase(
    req?: table.UpdateDatabaseRequest,
    options?: T,
  ): Promise<table.SingleDatabaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/update');
    const method = 'POST';
    const data = {
      id: _req['id'],
      icon_uri: _req['icon_uri'],
      table_name: _req['table_name'],
      table_desc: _req['table_desc'],
      field_list: _req['field_list'],
      rw_mode: _req['rw_mode'],
      prompt_disabled: _req['prompt_disabled'],
      extra_info: _req['extra_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/unbind_to_bot
   *
   * 解除绑定bot中的一个数据库
   */
  UnBindDatabase(
    req?: table.BindDatabaseToBotRequest,
    options?: T,
  ): Promise<table.BindDatabaseToBotResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/unbind_to_bot');
    const method = 'POST';
    const data = {
      database_id: _req['database_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/update_records
   *
   * 对数据库中的Data内容（用户Records）进行增删改
   */
  UpdateDatabaseRecords(
    req: table.UpdateDatabaseRecordsRequest,
    options?: T,
  ): Promise<table.UpdateDatabaseRecordsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/update_records');
    const method = 'POST';
    const data = {
      database_id: _req['database_id'],
      record_data_add: _req['record_data_add'],
      record_data_alter: _req['record_data_alter'],
      record_data_delete: _req['record_data_delete'],
      table_type: _req['table_type'],
      ori_connector_id: _req['ori_connector_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/get_by_id
   *
   * 根据数据库id获取单个数据库信息
   */
  GetDatabaseByID(
    req?: table.SingleDatabaseRequest,
    options?: T,
  ): Promise<table.SingleDatabaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/get_by_id');
    const method = 'POST';
    const data = {
      id: _req['id'],
      is_draft: _req['is_draft'],
      need_sys_fields: _req['need_sys_fields'],
      version: _req['version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/add
   *
   * 新增一个数据库
   */
  AddDatabase(
    req?: table.AddDatabaseRequest,
    options?: T,
  ): Promise<table.SingleDatabaseResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/add');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      space_id: _req['space_id'],
      project_id: _req['project_id'],
      icon_uri: _req['icon_uri'],
      table_name: _req['table_name'],
      table_desc: _req['table_desc'],
      field_list: _req['field_list'],
      rw_mode: _req['rw_mode'],
      prompt_disabled: _req['prompt_disabled'],
      extra_info: _req['extra_info'],
      database_type: _req['database_type'],
      volcano_database_bind_info: _req['volcano_database_bind_info'],
      region_id: _req['region_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/memory/project/variable/meta_list
   *
   * 变量openAPI end
   *
   * Project memory
   */
  GetProjectVariableList(
    req?: project_memory.GetProjectVariableListReq,
    options?: T,
  ): Promise<project_memory.GetProjectVariableListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/project/variable/meta_list');
    const method = 'GET';
    const params = {
      ProjectID: _req['ProjectID'],
      UserID: _req['UserID'],
      version: _req['version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/memory/database/migrate_old_data */
  MigrateOldData(
    req?: table.MigrateOldDataRequest,
    options?: T,
  ): Promise<table.MigrateOldDataResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/database/migrate_old_data');
    const method = 'POST';
    const data = {
      bot_type: _req['bot_type'],
      bot_id: _req['bot_id'],
      table_ids: _req['table_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/table/list_new
   *
   * 「coze 2.0 新增数据库节点的需求」新的获取bot和database的对应关系
   */
  GetBotDatabase(
    req: table.GetBotTableRequest,
    options?: T,
  ): Promise<table.GetBotTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/table/list_new');
    const method = 'POST';
    const data = {
      creator_id: _req['creator_id'],
      bot_id: _req['bot_id'],
      table_ids: _req['table_ids'],
      table_type: _req['table_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/project/variable/meta_update */
  UpdateProjectVariable(
    req?: project_memory.UpdateProjectVariableReq,
    options?: T,
  ): Promise<project_memory.UpdateProjectVariableResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/project/variable/meta_update');
    const method = 'POST';
    const data = {
      ProjectID: _req['ProjectID'],
      UserID: _req['UserID'],
      VariableList: _req['VariableList'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/database/get_online_database_id
   *
   * 根据draft id查询online id
   */
  GetOnlineDatabaseId(
    req: table.GetOnlineDatabaseIdRequest,
    options?: T,
  ): Promise<table.GetOnlineDatabaseIdResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/get_online_database_id');
    const method = 'POST';
    const data = { id: _req['id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/variable/get_meta */
  GetMemoryVariableMeta(
    req?: project_memory.GetMemoryVariableMetaReq,
    options?: T,
  ): Promise<project_memory.GetMemoryVariableMetaResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/variable/get_meta');
    const method = 'POST';
    const data = {
      ConnectorID: _req['ConnectorID'],
      ConnectorType: _req['ConnectorType'],
      version: _req['version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/table_file/submit */
  SubmitDatabaseInsertTask(
    req?: table.SubmitDatabaseInsertRequest,
    options?: T,
  ): Promise<table.SubmitDatabaseInsertResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/table_file/submit');
    const method = 'POST';
    const data = {
      database_id: _req['database_id'],
      file_uri: _req['file_uri'],
      table_type: _req['table_type'],
      table_sheet: _req['table_sheet'],
      connector_id: _req['connector_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/table_schema/get */
  GetTableSchema(
    req?: table.GetTableSchemaRequest,
    options?: T,
  ): Promise<document.GetTableSchemaInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/table_schema/get');
    const method = 'POST';
    const data = {
      table_sheet: _req['table_sheet'],
      table_data_type: _req['table_data_type'],
      database_id: _req['database_id'],
      source_file: _req['source_file'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/table_file/get_progress */
  DatabaseFileProgressData(
    req: table.GetDatabaseFileProgressRequest,
    options?: T,
  ): Promise<table.GetDatabaseFileProgressResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/table_file/get_progress');
    const method = 'POST';
    const data = {
      database_id: _req['database_id'],
      table_type: _req['table_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/table_schema/validate */
  ValidateTableSchema(
    req?: table.ValidateTableSchemaRequest,
    options?: T,
  ): Promise<table.ValidateTableSchemaResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/table_schema/validate');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      database_id: _req['database_id'],
      source_file: _req['source_file'],
      table_sheet: _req['table_sheet'],
      table_type: _req['table_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/get_connector_name */
  GetConnectorName(
    req: table.GetSpaceConnectorListRequest,
    options?: T,
  ): Promise<table.GetSpaceConnectorListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/get_connector_name');
    const method = 'POST';
    const data = {
      SpaceId: _req['SpaceId'],
      Version: _req['Version'],
      ConnectorID: _req['ConnectorID'],
      ListAll: _req['ListAll'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/get_template */
  GetDatabaseTemplate(
    req: table.GetDatabaseTemplateRequest,
    options?: T,
  ): Promise<table.GetDatabaseTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/get_template');
    const method = 'POST';
    const data = {
      database_id: _req['database_id'],
      table_type: _req['table_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/database/update_bot_switch */
  UpdateDatabaseBotSwitch(
    req: table.UpdateDatabaseBotSwitchRequest,
    options?: T,
  ): Promise<table.UpdateDatabaseBotSwitchResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/database/update_bot_switch');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      database_id: _req['database_id'],
      prompt_disable: _req['prompt_disable'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/wechat/get_ticket
   *
   * 接收微信公众号的ticket
   */
  GetWeChatTicket(
    req?: data_connector.GetWeChatTicketRequest,
    options?: T,
  ): Promise<data_connector.GetWeChatTicketResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/wechat/get_ticket');
    const method = 'POST';
    const data = { Data: _req['Data'], Base: _req['Base'] };
    const params = {
      encrypt_type: _req['encrypt_type'],
      timestamp: _req['timestamp'],
      nonce: _req['nonce'],
      msg_signature: _req['msg_signature'],
      signature: _req['signature'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /** PUT /v1/variables */
  OpenSetPlaygroundVariable(
    req?: kvmemory.OpenSetPlaygroundVariableReq,
    options?: T,
  ): Promise<kvmemory.OpenSetPlaygroundVariableResp> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/variables');
    const method = 'PUT';
    const data = {
      app_id: _req['app_id'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      connector_uid: _req['connector_uid'],
      data: _req['data'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /v1/variables */
  OpenGetPlaygroundVariable(
    req?: kvmemory.OpenGetPlaygroundVariableReq,
    options?: T,
  ): Promise<kvmemory.OpenGetPlaygroundVariableResp> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/variables');
    const method = 'GET';
    const params = {
      app_id: _req['app_id'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      connector_uid: _req['connector_uid'],
      keywords: _req['keywords'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/memory/volcano_database/list_database */
  VolcanoDatabaseListDatabase(
    req: volcano_database.VolcaDatabaseListDatabaseRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseListDatabaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/volcano_database/list_database');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      region_id: _req['region_id'],
      connect_info: _req['connect_info'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/volcano_database/list_table */
  VolcanoDatabaseListTable(
    req: volcano_database.VolcaDatabaseListTableRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseListTableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/volcano_database/list_table');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      region_id: _req['region_id'],
      database_bind_info: _req['database_bind_info'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/memory/volcano_database/list_project
   *
   * 火山数据库
   */
  VolcanoDatabaseListProject(
    req: volcano_database.VolcaDatabaseListProjectRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseListProjectResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/volcano_database/list_project');
    const method = 'POST';
    const data = { space_id: _req['space_id'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/volcano_database/connect_check */
  VolcanoDatabaseConnectCheck(
    req?: volcano_database.VolcaDatabaseConnectCheckRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseConnectCheckResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/volcano_database/connect_check');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      region_id: _req['region_id'],
      connect_info: _req['connect_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/volcano_database/get_table_right */
  VolcanoDatabaseGetTableRight(
    req?: volcano_database.VolcaDatabaseGetTableRightRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseGetTableRightResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/volcano_database/get_table_right');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      database_id: _req['database_id'],
      connect_info: _req['connect_info'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/volcano_database/list_instance */
  VolcanoDatabaseInstance(
    req: volcano_database.VolcaDatabaseListInstanceRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseListInstanceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/memory/volcano_database/list_instance');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      project_name: _req['project_name'],
      region_id: _req['region_id'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/volcano_database/batch_associate */
  VolcanoDatabaseBatchAssociate(
    req?: volcano_database.VolcaDatabaseBatchAssociateRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseBatchAssociateResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/memory/volcano_database/batch_associate');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      region_id: _req['region_id'],
      database_bind_info: _req['database_bind_info'],
      volcano_table_list: _req['volcano_table_list'],
      project_id: _req['project_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/memory/volcano_database/change_table_right */
  VolcanoDatabaseChangeTableRight(
    req?: volcano_database.VolcaDatabaseChangeTableRightRequest,
    options?: T,
  ): Promise<volcano_database.VolcaDatabaseChangeTableRightResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/memory/volcano_database/change_table_right',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      connect_info: _req['connect_info'],
      database_ids: _req['database_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
