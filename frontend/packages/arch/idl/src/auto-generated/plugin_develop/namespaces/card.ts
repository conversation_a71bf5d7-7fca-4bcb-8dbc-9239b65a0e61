/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ActionKey {
  /** 复制 */
  Copy = 1,
  /** 删除 */
  Delete = 2,
  /** 启用/禁用 */
  EnableSwitch = 3,
  /** 编辑 */
  Edit = 4,
  /** 切换成funcflow */
  SwitchToFuncflow = 8,
  /** 切换成chatflow */
  SwitchToChatflow = 9,
  /** 跨空间复制 */
  CrossSpaceCopy = 10,
}

export enum AgentBizUniversalMethod {
  Unknown = 0,
  Get = 1,
  Post = 2,
}

export enum AgentTriggerAPIType {
  Unknown = 0,
  Workflow = 1,
  Chat = 2,
  ChatV3 = 3,
}

export enum AIGenerateCardStatus {
  /** AI Schema 生成且格式符合预期 */
  Init = 0,
  /** AI Schema 转 Card 成功 */
  Success = 1,
  /** 格式与预期不符 */
  Invalid = 2,
  /** 格式符合预期但转卡片失败 */
  Failed = 3,
  /** 手动终止，部分成功 */
  Interrupt = 4,
}

export enum AISchemaTransferStatus {
  Success = 1,
  Failed = 2,
  Interrupt = 3,
}

export enum AuditStatus {
  /** 链接内容不通过 */
  LinkRejected = -60,
  /** 视频内容不通过 */
  VideoRejected = -50,
  /** 音频内容不通过 */
  AudioRejected = -40,
  /** 图片内容不通过 */
  ImageRejected = -30,
  /** 文本内容不通过 */
  TextRejected = -20,
  /** 审核不通过(原因见详情) */
  Rejected = -1,
  /** 审核通过 */
  Approved = 1,
  /** 待审核 */
  Pending = 100,
}

export enum CardCategory {
  /** 模版卡片 */
  Template = 1,
  /** 自定义卡片 */
  Custom = 2,
}

export enum CardDisplayType {
  /** 基础 */
  Basic = 1,
  /** 列表 */
  List = 2,
  /** 自定义卡片 */
  Custom = 3,
  /** 横向列表 */
  Slide = 4,
}

export enum CardStatus {
  Draft = 0,
  Published = 1,
  UnPublish = 2,
}

export enum CardThumbnailStatus {
  /** 未定义 */
  Default = 0,
  /** 空卡片 */
  Empty = 1,
  /** 待生成 */
  NeedGenerate = 2,
  /** 生成中 */
  Generating = 3,
  /** 生成失败 */
  Failed = 4,
  /** 生成成功 */
  Success = 5,
  /** 已发布卡片编辑过待重新发布 */
  NeedPublish = 6,
}

export enum ChannelType {
  Default = 0,
  Doubao = 100,
  CiCi = 101,
  Feishu = 200,
  WhatsApp = 300,
  Discord = 301,
  Twitter = 302,
  Telegram = 303,
  Messenger = 304,
  LINE = 305,
  Instagram = 306,
  Slack = 307,
  Reddit = 308,
}

export enum CopyStatus {
  Succeed = 0,
  Fail = 1,
}

export enum DocumentType {
  /** 上传中 */
  Processing = 0,
  /** 生效 */
  Enable = 1,
  /** 失效 */
  Disable = 2,
  /** 删除 */
  Deleted = 3,
  /** 失败 */
  Failed = 9,
}

export enum PublishStatus {
  /** 未发布 */
  UnPublished = 1,
  /** 已发布 */
  Published = 2,
}

export enum Scene {
  /** 卡片，实际对应文件类型是图片 */
  Card = 0,
  /** agent，实际对应文件类型是PDF */
  Agent = 1,
  /** 音频 */
  Audio = 2,
  /** 缩略图 */
  Thumbnail = 3,
  /** 视频 */
  Video = 4,
}

export enum SliceStatus {
  /** 未向量化 */
  PendingVectoring = 0,
  /** 已向量化 */
  FinishVectoring = 1,
  /** 禁用 */
  Deactive = 9,
}

export enum SortField {
  Default = 0,
  /** 更新时间 */
  UpdateTime = 1,
  /** 发布时间 */
  PublishTime = 2,
}

export enum SortOrderType {
  /** 降序 */
  Desc = 0,
  /** 升序 */
  Asc = 1,
}

export enum Stage {
  Copy = 1,
  Replace = 2,
  RollBack = 3,
}

export enum TccServiceID {
  Card = 0,
  CardBuilder = 1,
}

/** -------------- 运营平台接口 end -------------- */
export enum ToolType {
  Plugin = 1,
  Workflow = 2,
  Widget = 3,
}

export interface CardMetaInfo {
  dsl_content?: string;
  lynx_url?: string;
  thumbnail?: string;
  channel_type?: ChannelType;
  version_num?: string;
  /** 审核状态 */
  audit_status?: AuditStatus;
  /** 审核不通过详情 */
  audit_failure_details?: Array<number>;
}
/* eslint-enable */
