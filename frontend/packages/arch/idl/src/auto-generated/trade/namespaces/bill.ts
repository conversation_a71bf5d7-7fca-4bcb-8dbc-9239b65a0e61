/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export enum BillItemType {
  Model = 1,
  Plugin = 2,
  Voice = 3,
  RTC = 4,
  ASR = 5,
  TTS = 6,
  TTS_SMALL = 7,
}

export enum DeviceDimension {
  Device = 1,
  CustomConsumer = 2,
}

export enum OrderByField {
  ChatTotal = 1,
  ChatCurrent = 2,
  BalanceTotal = 3,
  BalanceCurrent = 4,
}

export enum OrderByType {
  Desc = 1,
  Asc = 2,
}

export enum SummaryPeriod {
  Day_7 = 1,
  Day_30 = 2,
  Day_60 = 3,
  Day_1 = 4,
}

export interface BillConsumeDetail {
  name: string;
  change_amount: string;
  tag: string;
  change_type: common.CreditChangeType;
  icon_url: string;
  change_amount_str?: string;
  usage?: Int64;
}

export interface BillItem {
  change_amount: string;
  changed_at: string;
  name?: string;
  channel_info?: common.ChannelInfo;
  tag?: string;
  /** 筛选项从这里拿 */
  change_type?: common.CreditChangeType;
  /** 废弃 */
  model_consume_detail?: Array<BillConsumeDetail>;
  /** 废弃 */
  plugin_consume_detail?: Array<BillConsumeDetail>;
  entity_consume_detail?: Partial<
    Record<BillItemType, Array<BillConsumeDetail>>
  >;
  bill_item_id?: string;
  change_amount_str?: string;
  icon_url?: string;
  space_info?: BillSpaceInfo;
}

export interface BillSpaceInfo {
  name: string;
  icon_url: string;
  id: string;
}

export interface CreditBillData {
  bills?: Array<BillItem>;
  has_more?: boolean;
  page_token?: string;
}

export interface DeviceBenefitDetail {
  date?: string;
  device_id?: string;
  entity_consume_detail?: Array<BillConsumeDetail>;
}

export interface DeviceSummary {
  date?: string;
  uv?: Int64;
  chat_total?: Int64;
  tts_total?: Int64;
  asr_total?: Int64;
  voice_total?: Int64;
  rtc_total?: Int64;
}

export interface DeviceSummaryDetail {
  date?: string;
  device_id?: string;
  activated_at?: string;
  chat_total?: Int64;
  chat_current?: Int64;
  balance_total?: string;
  balance_current?: string;
}

export interface GetCreditBillRequest {
  start_timestamp: Int64;
  end_timestamp: Int64;
  change_types?: Array<common.CreditChangeType>;
  coze_account_id?: string;
  coze_account_type?: common.CozeAccountType;
  page_size?: number;
  page_token?: string;
  enterpirse_id?: string;
  organization_id?: string;
}

export interface GetCreditBillResponse {
  data?: CreditBillData;
  code: number;
  message: string;
}

export interface GetDeviceBenefitDetailData {
  device_benefit_detail?: Array<DeviceBenefitDetail>;
}

export interface GetDeviceSummaryData {
  device_summary?: Array<DeviceSummary>;
  chat_avg?: number;
  tts_avg?: number;
  asr_avg?: number;
  voice_avg?: number;
  rtc_avg?: number;
}

export interface GetDeviceSummaryDetailData {
  device_summary_detail?: Array<DeviceSummaryDetail>;
}

export interface PublicGetDeviceBenefitDetailRequest {
  start_timestamp: Int64;
  end_timestamp: Int64;
  device_id?: string;
  custom_consumer?: string;
}

export interface PublicGetDeviceBenefitDetailResponse {
  code?: number;
  msg?: string;
  data?: GetDeviceBenefitDetailData;
}

export interface PublicGetDeviceSummaryDetailRequest {
  period?: SummaryPeriod;
  device_id?: string;
  page_num?: number;
  page_size?: number;
  order_by_type?: OrderByType;
  order_by_field?: OrderByField;
  dimension?: DeviceDimension;
  custom_consumer_id?: string;
}

export interface PublicGetDeviceSummaryDetailResponse {
  code?: number;
  msg?: string;
  data?: GetDeviceSummaryDetailData;
}

export interface PublicGetDeviceSummaryRequest {
  period?: SummaryPeriod;
  dimension?: DeviceDimension;
}

export interface PublicGetDeviceSummaryResponse {
  code?: number;
  msg?: string;
  data?: GetDeviceSummaryData;
}
/* eslint-enable */
