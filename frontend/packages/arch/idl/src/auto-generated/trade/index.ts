/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as account from './namespaces/account';
import * as benefit from './namespaces/benefit';
import * as benefit_conf from './namespaces/benefit_conf';
import * as bill from './namespaces/bill';
import * as common from './namespaces/common';
import * as fulfill from './namespaces/fulfill';
import * as marketplace_common from './namespaces/marketplace_common';
import * as order from './namespaces/order';
import * as pipo from './namespaces/pipo';
import * as price_rule from './namespaces/price_rule';
import * as product from './namespaces/product';
import * as product_common from './namespaces/product_common';
import * as security from './namespaces/security';
import * as subscription from './namespaces/subscription';

export {
  account,
  benefit,
  benefit_conf,
  bill,
  common,
  fulfill,
  marketplace_common,
  order,
  pipo,
  price_rule,
  product,
  product_common,
  security,
  subscription,
};
export * from './namespaces/account';
export * from './namespaces/benefit';
export * from './namespaces/benefit_conf';
export * from './namespaces/bill';
export * from './namespaces/common';
export * from './namespaces/fulfill';
export * from './namespaces/marketplace_common';
export * from './namespaces/order';
export * from './namespaces/pipo';
export * from './namespaces/price_rule';
export * from './namespaces/product';
export * from './namespaces/product_common';
export * from './namespaces/security';
export * from './namespaces/subscription';

export type Int64 = string | number;

export default class TradeService<T> {
  private request: any = () => {
    throw new Error('TradeService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/marketplace/trade/create_charge_order
   *
   * 发起充值单（coze token、message credit）
   */
  CreateChargeOrder(
    req?: order.CreateChargeOrderRequest,
    options?: T,
  ): Promise<order.CreateChargeOrderResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/create_charge_order');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      currency_code: _req['currency_code'],
      goods_id: _req['goods_id'],
      quantity: _req['quantity'],
      extra: _req['extra'],
      UserType: _req['UserType'],
    };
    const headers = {
      Cookie: _req['Cookie'],
      'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/marketplace/trade/checkout_callback
   *
   * PIPO Checkout支付回调
   */
  PipoCheckoutCallback(
    req?: pipo.PipoCheckoutCallbackRequest,
    options?: T,
  ): Promise<pipo.PipoCheckoutCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/checkout_callback');
    const method = 'POST';
    const data = {
      event_type: _req['event_type'],
      data: _req['data'],
      Body: _req['Body'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/trade/get_account_balance
   *
   * 获取账户余额（token）
   */
  GetAccountBalance(
    req?: account.GetAccountBalanceRequest,
    options?: T,
  ): Promise<account.GetAccountBalanceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/get_account_balance');
    const method = 'GET';
    const params = {
      account_type: _req['account_type'],
      UserID: _req['UserID'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/trade/get_account_bills
   *
   * 获取账户账单（token）
   */
  GetAccountBills(
    req?: account.GetAccountBillsRequest,
    options?: T,
  ): Promise<account.GetAccountBillsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/get_account_bills');
    const method = 'GET';
    const params = {
      account_type: _req['account_type'],
      start_timestamp_ms: _req['start_timestamp_ms'],
      end_timestamp_ms: _req['end_timestamp_ms'],
      UserID: _req['UserID'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/trade/get_model_cost_rules
   *
   * 获取模型计费规则（token）
   */
  GetModelCostRules(
    req?: account.GetModelCostRulesRequest,
    options?: T,
  ): Promise<account.GetModelCostRulesResponse> {
    const url = this.genBaseURL('/api/marketplace/trade/get_model_cost_rules');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/marketplace/trade/agreement_deduction_callback
   *
   * PIPO 协议支付回调
   */
  PipoAgreementDeductionCallback(
    req?: pipo.PipoAgreementDeductionCallbackRequest,
    options?: T,
  ): Promise<pipo.PipoAgreementDeductionCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/agreement_deduction_callback',
    );
    const method = 'POST';
    const data = {
      event_type: _req['event_type'],
      data: _req['data'],
      Body: _req['Body'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/trade/get_user_payment_methods
   *
   * PIPO 查询用户是否绑定支付方式
   */
  PipoGetUserPaymentMethods(
    req?: pipo.PipoGetUserPaymentMethodsRequest,
    options?: T,
  ): Promise<pipo.PipoGetUserPaymentMethodsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/get_user_payment_methods',
    );
    const method = 'GET';
    const params = {
      UserType: _req['UserType'],
      UserID: _req['UserID'],
      Amount: _req['Amount'],
      Currency: _req['Currency'],
      scene: _req['scene'],
    };
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/marketplace/trade/get_payment_method_management_url
   *
   * PIPO 获取支付方式管理页面
   */
  PipoGetPaymentMethodManagementURL(
    req?: pipo.PipoGetPaymentMethodManagementURLRequest,
    options?: T,
  ): Promise<pipo.PipoGetPaymentMethodManagementURLResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/get_payment_method_management_url',
    );
    const method = 'GET';
    const params = {
      UserType: _req['UserType'],
      UserID: _req['UserID'],
      scene: _req['scene'],
    };
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/marketplace/trade/get_trade_conf
   *
   * 查询交易领域配置
   */
  GetTradeConf(
    req?: security.GetTradeConfRequest,
    options?: T,
  ): Promise<security.GetTradeConfResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/get_trade_conf');
    const method = 'GET';
    const params = { scenes: _req['scenes'] };
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/marketplace/trade/get_trade_order
   *
   * 查询交易订单信息
   */
  GetTradeOrder(
    req: order.GetTradeOrderRequest,
    options?: T,
  ): Promise<order.GetTradeOrderResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/trade/get_trade_order');
    const method = 'GET';
    const params = {
      order_id: _req['order_id'],
      real_time_pay_status: _req['real_time_pay_status'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/trade/subscription
   *
   * 获取用户当前订阅详情
   */
  PublicGetSubscriptionDetail(
    req?: subscription.PublicGetSubscriptionDetailRequest,
    options?: T,
  ): Promise<subscription.PublicGetSubscriptionDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/subscription');
    const method = 'GET';
    const params = { subscribe_type: _req['subscribe_type'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/trade/create_subscription
   *
   * 发起订阅（含订阅升降级）
   */
  PublicCreateSubscription(
    req?: subscription.PublicCreateSubscriptionRequest,
    options?: T,
  ): Promise<subscription.PublicCreateSubscriptionResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/create_subscription');
    const method = 'POST';
    const data = {
      goods_id: _req['goods_id'],
      pre_subscription_id: _req['pre_subscription_id'],
      channel: _req['channel'],
      source_type: _req['source_type'],
      source_id: _req['source_id'],
    };
    const headers = {
      Cookie: _req['Cookie'],
      'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/marketplace/trade/resume_subscription
   *
   * 重新订阅
   */
  PublicReSubscription(
    req?: subscription.PublicReSubscriptionRequest,
    options?: T,
  ): Promise<subscription.PublicReSubscriptionResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/resume_subscription');
    const method = 'POST';
    const data = { subscribe_id: _req['subscribe_id'] };
    const headers = {
      Cookie: _req['Cookie'],
      'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/marketplace/trade/subscription_product
   *
   * 获取订阅套餐列表
   */
  PublicGetSubscriptionProductDetail(
    req?: subscription.PublicGetSubscriptionProductDetailRequest,
    options?: T,
  ): Promise<subscription.PublicGetSubscriptionProductDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/subscription_product');
    const method = 'GET';
    const params = { product_type: _req['product_type'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/trade/cancel_subscription
   *
   * 取消订阅
   */
  PublicCancelSubscription(
    req?: subscription.PublicCancelSubscriptionRequest,
    options?: T,
  ): Promise<subscription.PublicCancelSubscriptionResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/cancel_subscription');
    const method = 'POST';
    const data = { subscribe_id: _req['subscribe_id'] };
    const headers = {
      Cookie: _req['Cookie'],
      'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/marketplace/trade/subscription_notify
   *
   * 订阅回调
   */
  SubscriptionNotify(
    req?: pipo.SubscriptionNotifyRequest,
    options?: T,
  ): Promise<pipo.SubscriptionNotifyResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/subscription_notify');
    const method = 'POST';
    const data = { Body: _req['Body'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/trade/create_charge_order_v2
   *
   * 发起充值单（coze token、message credit）规范化前台接口入参
   */
  PublicCreateChargeOrder(
    req?: order.PublicCreateChargeOrderRequest,
    options?: T,
  ): Promise<order.PublicCreateChargeOrderResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/create_charge_order_v2',
    );
    const method = 'POST';
    const data = {
      currency_code: _req['currency_code'],
      goods_id: _req['goods_id'],
      quantity: _req['quantity'],
      extra: _req['extra'],
      charge_scene: _req['charge_scene'],
    };
    const headers = {
      Cookie: _req['Cookie'],
      'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/marketplace/trade/credit_bill
   *
   * 获取用户账单
   */
  PublicGetCreditBill(
    req: bill.GetCreditBillRequest,
    options?: T,
  ): Promise<bill.GetCreditBillResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/trade/credit_bill');
    const method = 'GET';
    const params = {
      start_timestamp: _req['start_timestamp'],
      end_timestamp: _req['end_timestamp'],
      change_types: _req['change_types'],
      coze_account_id: _req['coze_account_id'],
      coze_account_type: _req['coze_account_type'],
      page_size: _req['page_size'],
      page_token: _req['page_token'],
      enterpirse_id: _req['enterpirse_id'],
      organization_id: _req['organization_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/trade/get_pricing_rules
   *
   * 获取阶梯定价规则
   */
  PublicGetPricingRules(
    req: price_rule.GetPricingRulesRequest,
    options?: T,
  ): Promise<price_rule.GetPricingRulesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/trade/get_pricing_rules');
    const method = 'GET';
    const params = {
      scene: _req['scene'],
      coze_account_id: _req['coze_account_id'],
      coze_account_type: _req['coze_account_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/trade/refund_order_callback
   *
   * 退款回调
   */
  RefundOrderCallback(
    req?: pipo.RefundOrderCallbackRequest,
    options?: T,
  ): Promise<pipo.RefundOrderCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/refund_order_callback');
    const method = 'POST';
    const data = { Body: _req['Body'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/trade/invoice_callback
   *
   * PIPO 开票回调
   */
  PipoInvoiceCallback(
    req?: pipo.PipoInvoiceCallbackRequest,
    options?: T,
  ): Promise<pipo.PipoInvoiceCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/invoice_callback');
    const method = 'POST';
    const data = { Body: _req['Body'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/trade/instant_pay_callback
   *
   * PIPO Gateway 支付回调
   */
  PipoInstantPayCallback(
    req?: pipo.PipoInstantPayCallbackRequest,
    options?: T,
  ): Promise<pipo.PipoInstantPayCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/instant_pay_callback');
    const method = 'POST';
    const data = { Body: _req['Body'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/trade/admin/subscription_product
   *
   * 获取订阅商品信息
   */
  AdminGetSubscriptionProductDetail(
    req: subscription.AdminGetSubscriptionProductDetailRequest,
    options?: T,
  ): Promise<subscription.AdminGetSubscriptionProductDetailResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/trade/admin/subscription_product',
    );
    const method = 'GET';
    const params = {
      product_type: _req['product_type'],
      product_id: _req['product_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/trade/chargeback_callback
   *
   * 拒付回调
   */
  ChargebackCallback(
    req?: pipo.ChargebackCallbackRequest,
    options?: T,
  ): Promise<pipo.ChargebackCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/chargeback_callback');
    const method = 'POST';
    const data = { Body: _req['Body'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/trade/get_subs_rec
   *
   * 查询订阅记录信息
   */
  GetSubscriptionRecord(
    req?: subscription.GetSubscriptionRecordRequest,
    options?: T,
  ): Promise<subscription.GetSubscriptionRecordResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/get_subs_rec');
    const method = 'GET';
    const params = {
      subscribe_id: _req['subscribe_id'],
      real_time_subs_status: _req['real_time_subs_status'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/trade/gw_agreement_deduction_callback
   *
   * PIPO gateway 协议支付回调
   */
  PipoGWAgreementDeductionCallbackResponse(
    req?: pipo.PipoGWAgreementDeductionCallbackRequest,
    options?: T,
  ): Promise<pipo.PipoGWAgreementDeductionCallbackResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/gw_agreement_deduction_callback',
    );
    const method = 'POST';
    const data = { Body: _req['Body'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/trade/place_order
   *
   * 下单接口（购买模版）
   */
  PublicPlaceOrder(
    req?: order.PublicPlaceOrderRequest,
    options?: T,
  ): Promise<order.PublicPlaceOrderResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/place_order');
    const method = 'POST';
    const data = {
      goods_id: _req['goods_id'],
      quantity: _req['quantity'],
      scene: _req['scene'],
      return_url: _req['return_url'],
      product_id: _req['product_id'],
    };
    const headers = {
      Cookie: _req['Cookie'],
      'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'],
    };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/marketplace/trade/template_order/list
   *
   * 查询交易订单信息
   */
  PublicGetTemplatePurchaseOrderList(
    req?: order.PublicGetTemplatePurchaseOrderListRequest,
    options?: T,
  ): Promise<order.PublicGetTemplatePurchaseOrderListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/template_order/list');
    const method = 'GET';
    const params = { index: _req['index'], count: _req['count'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/trade/subscription_v2
   *
   * 获取用户当前订阅详情V2
   */
  PublicGetSubscriptionDetailV2(
    req?: subscription.PublicGetSubscriptionDetailV2Request,
    options?: T,
  ): Promise<subscription.PublicGetSubscriptionDetailV2Response> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/subscription_v2');
    const method = 'GET';
    const params = {
      benefit_types: _req['benefit_types'],
      coze_account_id: _req['coze_account_id'],
      coze_account_type: _req['coze_account_type'],
      with_resource_package: _req['with_resource_package'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/trade/subscription_product_v2
   *
   * 获取订阅套餐列表
   */
  PublicGetSubscriptionProductDetailV2(
    req?: subscription.PublicGetSubscriptionProductDetailV2Request,
    options?: T,
  ): Promise<subscription.PublicGetSubscriptionProductDetailV2Response> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/subscription_product_v2',
    );
    const method = 'GET';
    const params = {
      coze_account_id: _req['coze_account_id'],
      coze_account_type: _req['coze_account_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/trade/device_summary */
  PublicGetDeviceSummary(
    req?: bill.PublicGetDeviceSummaryRequest,
    options?: T,
  ): Promise<bill.PublicGetDeviceSummaryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/device_summary');
    const method = 'GET';
    const params = { period: _req['period'], dimension: _req['dimension'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/trade/device_benefit_detail */
  PublicGetDeviceBenefitDetail(
    req: bill.PublicGetDeviceBenefitDetailRequest,
    options?: T,
  ): Promise<bill.PublicGetDeviceBenefitDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/trade/device_benefit_detail');
    const method = 'GET';
    const params = {
      start_timestamp: _req['start_timestamp'],
      end_timestamp: _req['end_timestamp'],
      device_id: _req['device_id'],
      custom_consumer: _req['custom_consumer'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /api/marketplace/trade/device_summary_detail */
  PublicGetDeviceSummaryDetail(
    req?: bill.PublicGetDeviceSummaryDetailRequest,
    options?: T,
  ): Promise<bill.PublicGetDeviceSummaryDetailResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/device_summary_detail');
    const method = 'GET';
    const params = {
      period: _req['period'],
      device_id: _req['device_id'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      order_by_type: _req['order_by_type'],
      order_by_field: _req['order_by_field'],
      dimension: _req['dimension'],
      custom_consumer_id: _req['custom_consumer_id'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/marketplace/trade/space_benefit
   *
   * 获取空间权益
   */
  PublicGetSpaceBenefit(
    req: subscription.PublicGetSpaceBenefitRequest,
    options?: T,
  ): Promise<subscription.PublicGetSpaceBenefitResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/trade/space_benefit');
    const method = 'GET';
    const params = {
      SpaceID: _req['SpaceID'],
      benefit_types: _req['benefit_types'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/trade/benefit/user/extra/create
   *
   * 扩展包-添加
   */
  PublicCreateUserExtraBenefit(
    req?: benefit.PublicCreateUserExtraBenefitRequest,
    options?: T,
  ): Promise<benefit.PublicCreateUserExtraBenefitResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/benefit/user/extra/create',
    );
    const method = 'POST';
    const data = {
      extra_benefit: _req['extra_benefit'],
      enterprise_id: _req['enterprise_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/trade/benefit/user/list
   *
   * 扩容管理
   */
  PublicGetUserBenefit(
    req?: benefit.PublicGetUserBenefitRequest,
    options?: T,
  ): Promise<benefit.PublicGetUserBenefitResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/trade/benefit/user/list');
    const method = 'GET';
    const params = {
      benefit_types: _req['benefit_types'],
      with_extra_only: _req['with_extra_only'],
      resource_id: _req['resource_id'],
      enterprise_id: _req['enterprise_id'],
      account_type: _req['account_type'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/trade/benefit/user/extra/disable
   *
   * 扩展包-取消
   */
  PublicDisableUserExtraBenefit(
    req?: benefit.PublicDisableUserExtraBenefitRequest,
    options?: T,
  ): Promise<benefit.PublicDisableUserExtraBenefitResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/benefit/user/extra/disable',
    );
    const method = 'POST';
    const data = {
      benefit_id: _req['benefit_id'],
      enterprise_id: _req['enterprise_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/trade/benefit_conf/common_set
   *
   * 通用配置设定（当前支持安心用）
   */
  PublicCommSetConfBenefit(
    req?: benefit_conf.PublicCommSetConfBenefitRequest,
    options?: T,
  ): Promise<benefit_conf.PublicCommSetConfBenefitResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/trade/benefit_conf/common_set',
    );
    const method = 'POST';
    const data = { counter: _req['counter'] };
    const params = {
      benefit_type: _req['benefit_type'],
      coze_account_id: _req['coze_account_id'],
      coze_account_type: _req['coze_account_type'],
      enterprise_id: _req['enterprise_id'],
    };
    return this.request({ url, method, data, params }, options);
  }
}
/* eslint-enable */
