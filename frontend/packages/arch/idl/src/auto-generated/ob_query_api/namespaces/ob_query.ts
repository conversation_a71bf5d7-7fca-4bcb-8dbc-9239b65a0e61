/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum QueryTypeEnum {
  Undefined = 0,
  Match = 1,
  Term = 2,
  Range = 3,
  Exist = 4,
  NotExist = 5,
}

export enum TagType {
  STRING = 0,
  DOUBLE = 1,
  BOOL = 2,
  LONG = 3,
  BYTES = 4,
}

export interface Event {
  type?: string;
  name?: string;
  tags?: Array<Tag>;
  start_time?: Int64;
  data?: Blob;
}

export interface FilterTag {
  data_type?: string;
  tag_key?: string;
  multi_tag_keys?: Array<string>;
  values?: Array<string>;
  query_type?: QueryTypeEnum;
}

export interface ListSpansData {
  spans?: Array<Span>;
  context?: string;
}

export interface ListSpansRequest {
  tenant?: string;
  filter_tags?: Array<FilterTag>;
  start_at: Int64;
  end_at: Int64;
  limit?: number;
  desc_by_start_time?: boolean;
  /** 检索游标 */
  context?: string;
  bot_id?: string;
  workspace_id?: string;
  Base?: base.Base;
}

export interface ListSpansResponse {
  code: number;
  msg: string;
  data: ListSpansData;
  BaseResp?: base.BaseResp;
}

export interface QueryTraceData {
  spans?: Array<Span>;
}

export interface QueryTraceRequest {
  tenant?: string;
  trace_id?: string;
  log_id?: string;
  message_id?: string;
  start_at?: Int64;
  end_at?: Int64;
  bot_id?: string;
  workspace_id?: string;
  Base?: base.Base;
}

export interface QueryTraceResponse {
  code: number;
  msg: string;
  data: QueryTraceData;
  BaseResp?: base.BaseResp;
}

export interface Span {
  trace_id?: string;
  log_id?: string;
  psm?: string;
  dc?: string;
  pod_name?: string;
  method?: string;
  span_id?: string;
  type?: string;
  name?: string;
  parent_id?: string;
  events?: Array<Event>;
  duration?: Int64;
  start_time?: Int64;
  status_code?: number;
  from_service?: string;
  tags?: Array<Tag>;
}

/** Tag */
export interface Tag {
  key?: string;
  tag_type?: TagType;
  value?: Value;
}

export interface Value {
  v_str?: string;
  v_double?: number;
  v_bool?: boolean;
  v_long?: Int64;
  v_bytes?: Blob;
}
/* eslint-enable */
