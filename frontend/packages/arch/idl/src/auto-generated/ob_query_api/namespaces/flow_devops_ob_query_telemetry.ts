/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_ob_query_telemetry_common from './flow_devops_ob_query_telemetry_common';
import * as flow_devops_ob_query_telemetry_field_filter from './flow_devops_ob_query_telemetry_field_filter';
import * as flow_devops_ob_query_telemetry_span from './flow_devops_ob_query_telemetry_span';

export type Int64 = string | number;

export interface BatchGetTracesAdvanceInfoData {
  traces_advance_info: Array<TraceAdvanceInfo>;
}

export interface BatchGetTracesAdvanceInfoRequest {
  space_id: string;
  bot_id: string;
  traces: Array<TraceQueryParams>;
}

export interface BatchGetTracesAdvanceInfoResponse {
  data: BatchGetTracesAdvanceInfoData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface FieldMeta {
  /** 字段类型 */
  value_type: flow_devops_ob_query_telemetry_common.ValueType;
  /** 支持的操作类型 */
  filter_types: Array<flow_devops_ob_query_telemetry_field_filter.FieldFilterType>;
  /** 支持的可选项 */
  field_options?: flow_devops_ob_query_telemetry_field_filter.FieldOptions;
}

export interface GetTraceByLogIDRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  log_id: string;
  /** ms, 如果未传默认向前搜索十分钟 */
  start_time?: string;
  /** ms, end_at >= start_at, 如果未传默认向后搜索十分钟 */
  end_time?: string;
}

export interface GetTraceByLogIDResponse {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetTraceData {
  spans: Array<flow_devops_ob_query_telemetry_span.Span>;
}

export interface GetTraceRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  trace_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
}

export interface GetTraceResponse {
  data: GetTraceData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface GetTracesMetaInfoData {
  /** 字段元信息 */
  field_metas: Record<string, FieldMeta>;
  /** span分类, key是分类，value是span type */
  span_category: Partial<
    Record<
      flow_devops_ob_query_telemetry_span.SpanCategory,
      Array<flow_devops_ob_query_telemetry_span.SpanType>
    >
  >;
}

export interface GetTracesMetaInfoRequest {}

export interface GetTracesMetaInfoResponse {
  data?: GetTracesMetaInfoData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code?: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg?: string;
}

export interface GetTracesStatData {
  /** Trace总数量 */
  total_count: number;
  /** 错误率，例如0.3代表30% */
  error_rate: number;
  /** 总tokens消耗 */
  tokens: MetricsValue;
  /** 总时延 */
  latency: MetricsValue;
  /** 首字符回复时延 */
  latency_first_resp: MetricsValue;
}

export interface GetTracesStatRequest {
  space_id: string;
  bot_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
}

export interface GetTracesStatResponse {
  data: GetTracesStatData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

export interface ListTracesData {
  spans: Array<flow_devops_ob_query_telemetry_span.Span>;
  /** 下一页的分页token，前端拉取下一页数据时回传。 */
  next_page_token: string;
  /** 是否有更多数据 */
  has_more: boolean;
}

export interface ListTracesRequest {
  /** space id */
  space_id: string;
  /** bot id */
  bot_id: string;
  /** ms */
  start_time: string;
  /** ms, end_at >= start_at */
  end_time: string;
  /** Example:

        filter 1000 <= sum(input_tokens) <= 2000
        {
            "filters": {
                "input_tokens": {
                    "i64": {
                        "gte": 1000,
                        "lte": 2000,
                    }
                }
            }
        } */
  filters: Record<
    string,
    flow_devops_ob_query_telemetry_field_filter.FieldFilter
  >;
  /** Full-Text search 全文本搜索，会搜input、output、bot_version, os_version这四个字段。可以传入多个关键词，每个关键词之间是and关系 */
  full_text_search?: Array<string>;
  /** default 1000 */
  limit?: number;
  /** The ORDER BY is used to sort the records, in ascending or descending order.

        ORDER BY with multiple field: The system will sort the results by field1(order_by[0]), then by field2(order_by[1])

        ⚠️  WARNING: only support start_time 

         Example:
        [
            {
                "field_name": "start_time",
                "order_type": "desc"
            }
        ] */
  order_by?: Array<flow_devops_ob_query_telemetry_common.OrderBy>;
  /** The page token is generated after the first query and passed in the subsequent queries 
     to determine the starting point for the next page of results. */
  page_token?: string;
}

export interface ListTracesResponse {
  data: ListTracesData;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  code: number;
  /** 仅供http请求使用; 内部RPC不予使用，统一通过BaseResp获取Code和Msg */
  msg: string;
}

/** 度量百分位数 */
export interface MetricsValue {
  p50?: number;
  p90?: number;
}

export interface TokenCost {
  /** 输入消耗token数 */
  input: number;
  /** 输出消耗token数 */
  output: number;
}

export interface TraceAdvanceInfo {
  trace_id: string;
  tokens: TokenCost;
  status: flow_devops_ob_query_telemetry_span.SpanStatus;
}

/** Trace查询参数 */
export interface TraceQueryParams {
  trace_id: string;
  start_time: string;
  end_time: string;
}
/* eslint-enable */
