/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 回调类型 */
export enum CallbackType {
  CREATE = 1,
  DELETE = 2,
  /** 创建并执行，接入时需选择是否可直接被执行，目前可传递query信息 */
  EXECUTE = 3,
}

export enum OperateType {
  /** 运行中（正在输出消息内容） */
  Running = 1,
  /** 暂停（用户发起的暂停） */
  Pause = 2,
  /** 一轮任务完成（agent完成一轮任务，本轮任务结束，等待新一轮任务执行） */
  TaskFinish = 3,
  /** 初始化（等待发起首轮任务） */
  Init = 4,
  /** 终止（本轮任务结束，无法再被执行） */
  Stop = 5,
  /** 中断（agent发起的中断，等待用户确认） */
  Interrupt = 6,
  /** 存在非法内容（被审核，无法露出内容，本轮任务结束，无法再被执行） */
  IllegalContent = 7,
  /** 异常中断（agent运行异常，本轮任务结束，等待新一轮任务执行） */
  AbnormalInterrupt = 8,
  /** 休眠（包含用户发起终止，本轮任务结束，等待新一轮任务执行） */
  Sleep = 9,
}

export interface TaskResult {
  /** 自然语言描述，是否正常生成了产物，或失败原因 */
  result_status?: string;
  results?: Array<TaskResultItem>;
}

export interface TaskResultItem {
  /** 产物链接 */
  link?: string;
}

export interface UpdateTaskNameRequest {
  agent_id?: Int64;
  sk?: string;
  task_id?: string;
  task_name?: string;
}

export interface UpdateTaskNameResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateTaskStatusRequest {
  agent_id?: Int64;
  sk?: string;
  task_id?: string;
  task_status?: OperateType;
  /** 任务状态变更为本轮任务结束(3,5,7,8,9)时的产物信息，执行失败也需要填充原因，若需要回传产物的任务结束时未填充该字段也会判定任务执行失败 */
  result?: TaskResult;
}

export interface UpdateTaskStatusResponse {
  code?: Int64;
  msg?: string;
}
/* eslint-enable */
