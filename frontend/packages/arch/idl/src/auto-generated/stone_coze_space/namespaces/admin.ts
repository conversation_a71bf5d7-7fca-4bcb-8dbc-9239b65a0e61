/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as task from './task';

export type Int64 = string | number;

export interface CreateTaskExampleData {
  example_info?: TaskExample;
}

export interface CreateTaskExampleRequest {
  task_id: string;
  example_name?: string;
  example_description: string;
  icon_uri?: string;
  /** 强制排序的位次，0 或者不传就是不强排 */
  index?: number;
  category_id: string;
  user_query: string;
  share_id: string;
}

export interface CreateTaskExampleResponse {
  code?: Int64;
  msg?: string;
  data?: CreateTaskExampleData;
}

export interface DeleteTaskExampleRequest {
  example_id: string;
}

export interface DeleteTaskExampleResponse {
  code?: Int64;
  msg?: string;
}

export interface GetTaskExampleCategoryListData {
  example_categories?: Array<TaskExampleCategory>;
}

export interface GetTaskExampleCategoryListRequest {}

export interface GetTaskExampleCategoryListResponse {
  code?: Int64;
  msg?: string;
  data?: GetTaskExampleCategoryListData;
}

export interface ListTaskExampleData {
  example_infos?: Array<TaskExample>;
  cursor?: string;
  has_more?: boolean;
}

export interface ListTaskExampleRequest {
  size: number;
  page?: number;
  task_ids?: Array<string>;
  category_id?: string;
}

export interface ListTaskExampleResponse {
  code?: Int64;
  msg?: string;
  data?: ListTaskExampleData;
}

export interface ParseTaskShareURLData {
  task_id?: string;
  share_id?: string;
  cover_url?: string;
  user_query?: string;
  task_name?: string;
  task_description?: string;
}

export interface ParseTaskShareURLRequest {
  share_url: string;
}

export interface ParseTaskShareURLResponse {
  code?: Int64;
  msg?: string;
  data?: ParseTaskShareURLData;
}

export interface TaskExample {
  example_info?: task.TaskExample;
  index?: number;
  task_id?: string;
}

export interface TaskExampleCategory {
  category_info?: task.TaskExampleCategory;
  index?: number;
}

export interface UpdateTaskExampleData {
  example_info?: TaskExample;
}

export interface UpdateTaskExampleRequest {
  example_id: string;
  example_name?: string;
  example_description?: string;
  icon_uri?: string;
  /** 强制排序的位次，0 或者不传就是不强排 */
  index?: number;
  category_id?: string;
  user_query?: string;
  status?: task.TaskExampleStatus;
}

export interface UpdateTaskExampleResponse {
  code?: Int64;
  msg?: string;
  data?: UpdateTaskExampleData;
}

export interface UploadFileData {
  file_uri?: string;
}

export interface UploadTaskFileRequest {
  file_name?: string;
  file_content?: Blob;
}

export interface UploadTaskFileResponse {
  code?: Int64;
  msg?: string;
  data?: UploadFileData;
}
/* eslint-enable */
