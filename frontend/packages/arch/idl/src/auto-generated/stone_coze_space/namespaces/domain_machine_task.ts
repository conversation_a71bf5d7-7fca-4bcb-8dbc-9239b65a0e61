/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum DomainMachineFileSource {
  UploadByUser = 1,
  FinalOutput = 2,
  IntermidiateOutput = 3,
}

export enum DomainMilvTaskType {
  /** 审查 */
  Review = 1,
  /** 起草 */
  Draft = 2,
  /** 法律问答 */
  LawQA = 3,
}

export enum DomainStockTaskType {
  /** 普通咨询任务 */
  GeneralChat = 1,
  /** 定时任务 */
  Scheduled = 2,
}

export enum DomainTaskStatus {
  Delete = 0,
  Running = 1,
  Paused = 2,
  Finished = 3,
  Init = 4,
  Terminated = 5,
  Interrupted = 6,
  /** 存在非法内容 */
  IllegalContent = 7,
  /** 异常中断 */
  AbnormalInterrupted = 8,
  /** 休眠 */
  Sleep = 9,
}

export enum DomainTaskType {
  General = 1,
  UserResearch = 2,
  Stock = 3,
  AnalyzePublicOpinion = 5,
  PPTAgent = 6,
  Milv = 7,
  TongCheng = 9,
  DevAgent = 10000,
  CustomerService = 10001,
}

export enum MachineTaskFileStatus {
  Using = 1,
  Deleted = 2,
}
/* eslint-enable */
