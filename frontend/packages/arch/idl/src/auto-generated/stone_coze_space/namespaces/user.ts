/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum VolcanoUserType {
  RootUser = 1,
  BasicUser = 2,
}

export interface AddUserAuthorizationRequest {
  task_id?: string;
  url?: string;
  clear_arg?: string;
  /** 添加cookie所要透传给沙箱的参数 */
  add_arg?: string;
}

export interface AddUserAuthorizationResponse {
  code?: Int64;
  msg?: string;
}

export interface CozeSpaceUserInfo {
  /** 是否近期活跃 */
  is_recently_active?: boolean;
  /** 火山用户信息 */
  volcano_user_info?: VolcanoUserInfo;
  /** 用户选择的职业信息 */
  job_info?: UserJobInfo;
}

export interface DeleteUserAuthorizationRequest {
  url?: string;
  clear_arg?: string;
}

export interface DeleteUserAuthorizationResponse {
  code?: Int64;
  msg?: string;
}

export interface GetCozeSpaceUserInfoRequest {
  /** 是否需要火山用户信息 */
  need_volcano_info?: boolean;
  need_user_job_info?: boolean;
}

export interface GetCozeSpaceUserInfoResponse {
  code?: Int64;
  msg?: string;
  data?: CozeSpaceUserInfo;
}

export interface GetTeamInfoRequest {}

export interface GetTeamInfoResponse {
  code?: Int64;
  msg?: string;
  data?: GetTeamInfoResponseData;
}

export interface GetTeamInfoResponseData {
  switch?: boolean;
  disable?: boolean;
}

export interface GetUserAuthorizationListRequest {}

export interface GetUserAuthorizationListResponse {
  code?: Int64;
  msg?: string;
  data?: GetUserAuthorizationListResponseData;
}

export interface GetUserAuthorizationListResponseData {
  user_authorizations?: Array<UserAuthorization>;
}

export interface GetUserJobListData {
  job_infos?: Array<UserJobInfo>;
}

export interface GetUserJobListRequest {}

export interface GetUserJobListResponse {
  code?: Int64;
  msg?: string;
  data?: GetUserJobListData;
}

export interface UpdateCozeSpaceUserInfoRequest {
  job_id?: string;
}

export interface UpdateCozeSpaceUserInfoResponse {
  code?: Int64;
  msg?: string;
}

export interface UpdateTeamInfoRequest {
  switch?: boolean;
}

export interface UpdateTeamInfoResponse {
  code?: Int64;
  msg?: string;
}

export interface UserAuthorization {
  url?: string;
}

export interface UserJobInfo {
  job_id: string;
  job_name: string;
}

export interface VolcanoUserInfo {
  open_id?: string;
  user_id?: string;
  instance_id?: string;
  account_id?: string;
  user_type?: VolcanoUserType;
  instance_name?: string;
}
/* eslint-enable */
