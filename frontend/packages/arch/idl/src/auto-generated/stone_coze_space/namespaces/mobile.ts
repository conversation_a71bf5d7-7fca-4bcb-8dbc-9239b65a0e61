/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface AppAgentData {
  agent_id?: Int64;
  /** 最低可展示的版本号 */
  min_app_version?: string;
}

export interface AppUpdateData {
  /** 最新版本号 */
  last_app_version?: string;
  /** 最低可用的app版本号，低于该版本触发升级弹窗 */
  min_app_version?: string;
  /** 升级弹窗标题 */
  update_title?: string;
  /** 升级弹窗文案 */
  update_desc?: string;
  /** 最新版本链接 */
  android_update_url?: string;
  /** 最新版本链接 */
  ios_update_url?: string;
}

export interface GetAppInfoData {
  app_update?: AppUpdateData;
  /** 允许展示的agent列表 */
  expert_agent?: Array<AppAgentData>;
}

export interface GetAppInfoRequest {}

export interface GetAppInfoResponse {
  code?: Int64;
  msg?: string;
  data?: GetAppInfoData;
}
/* eslint-enable */
