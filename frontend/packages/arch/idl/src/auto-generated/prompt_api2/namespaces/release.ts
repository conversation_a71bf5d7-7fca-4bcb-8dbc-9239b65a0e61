/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum CheckResultType {
  /** fail, 质检任务失败状态 */
  CheckResultFail = 1,
  /** block, 质检检测到严重告警，建议卡点阻塞发布流程 */
  CheckResultBlock = 2,
  /** assertFail, 规则执行检测到异常 */
  CheckResultAssertFail = 3,
  /** runFail, 规则执行内部异常，与变更无关 */
  CheckResultRunFail = 4,
  /** pass, 质检通过 */
  CheckResultPass = 5,
  /** warning, 质检检测到warning告警，建议人工check */
  CheckResultWarning = 6,
  /** error, 创建任务失败，不阻塞流程 */
  CheckResultError = 7,
  /** skipped, 质检任务被人工跳过 */
  CheckResultSkip = 8,
  /** running, 检测中 */
  CheckResultRunning = 9,
  /** init, 检测中 */
  CheckResultInit = 10,
}

/** 检查类型 */
export enum CheckType {
  /** release_time_check */
  ReleaseTimeCheck = 0,
  /** common_guard" */
  CommonGuard = 1,
  /** release_event */
  ReleaseEvent = 2,
  /** rule_engine */
  RuleEngine = 3,
  /** exam_check */
  ExamCheck = 4,
}

export interface ActionConfig {
  /** 操作名称文案，如申请逃逸、查看报告链接 */
  action_name?: string;
  /** 逃逸链接、质检报告链接 */
  action_url?: string;
  /** 操作详情描述，如封禁策略名称 */
  action_detail?: string;
}

export interface CheckResult {
  /** 检测ID */
  check_id?: Int64;
  /** 检测详情 */
  check_detail?: Array<CheckResultDetail>;
}

export interface CheckResultDetail {
  /** ref: CheckType, 检查类型，质检、时间窗口封禁、变更校验等 */
  check_type?: string;
  /** ref: CheckResultType, 检查结果 */
  check_result?: string;
  /** 是否卡点建议 */
  is_block?: boolean;
  /** 操作建议 */
  action?: Array<ActionConfig>;
  /** 检测项描述 */
  desc?: string;
}
/* eslint-enable */
