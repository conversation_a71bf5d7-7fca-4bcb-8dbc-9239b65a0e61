/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AuthSemantics {
  Delegation = 0,
  Impersonation = 1,
}

export enum AuthType {
  /** 用户直接访问 */
  Session = 1,
  /** Personal access token */
  PAT = 2,
  /** App Itself */
  AppItself = 3,
  /** App JWT Flow */
  JWT = 4,
  /** Auth Code Flow */
  AuthCode = 5,
  /** PKCE Flow */
  PKCE = 6,
  /** Device code */
  DeviceCode = 7,
  /** Impersonate */
  Impersonate = 8,
  /** Token Exchange Impersonate */
  TokenExchangeImpersonate = 9,
}

export enum PrincipalType {
  User = 1,
  Service = 2,
}

export interface PrincipalIdentifier {
  /** 主体类型 */
  type: PrincipalType;
  /** 主体Id */
  id: string;
}
/* eslint-enable */
