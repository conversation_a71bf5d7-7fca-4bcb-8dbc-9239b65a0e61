/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string;

export enum AuditStatus {
  /** 默认 */
  Default = 0,
  /** 审核中 */
  Pending = 1,
  /** 审核通过 */
  Approved = 2,
  /** 审核不通过 */
  Rejected = 3,
  /** 已废弃 */
  Abandoned = 4,
  /** 异常 */
  Failed = 99,
}

export enum AuditType {
  ProductDraft = 10,
  /** 用户 */
  UserProfile = 18,
  /** 分享对话 */
  ConversationShared = 19,
  /** 推荐对话 */
  Conversation = 20,
  InterationPost = 21,
  InterationComment = 22,
  ProductWorkFlow = 23,
  ProductBot = 24,
  ProductPlugin = 25,
  ImageFlowUploadImages = 26,
  ImageFlowTestRun = 27,
  ImageFlowPluginOutput = 28,
  DataEngineKnowledge = 29,
  BotAvatar = 30,
  BotPublishSingleBot = 31,
  BotPublishMultiAgent = 32,
  BotPublishPlugin = 33,
  BotPublishWorkflow = 34,
  BotModify = 35,
  BotCreate = 36,
  BotWorkflowAsAgentPublish = 37,
  BotWorkflowAsAgentModify = 38,
  BotSingleAgentModify = 39,
  SocietyCreate = 40,
  SocietyPublish = 41,
  SocietyRoleCreate = 42,
  SocietyPrePublish = 43,
  ProductSociety = 44,
  ProductImageFlow = 46,
  PluginImageGenerateInput = 48,
  PluginImageGenerateOutputImage = 49,
  PluginImageGenerateOutputVideo = 50,
  PluginCreate = 51,
  PluginModify = 52,
  VariableModify = 53,
  CardInfo = 60,
  AgentAppWriter = 80,
  /** 模型广场对战Query */
  ModelArenaQuery = 81,
  /** 消息链路追踪 */
  MessageTrace = 82,
  /** 资源库Prompt */
  ResourcePrompt = 83,
  /** ProjectIDE修改 */
  ProjectIDEModify = 84,
  /** Bot 模板 */
  TemplateBot = 85,
  /** 工作流模板 */
  TemplateWorkflow = 86,
  /** Project 模板 */
  TemplateProject = 87,
  /** Project 商品 */
  ProductProject = 88,
  /** Project 发布 */
  ProjectPublish = 89,
  /** Project UI Builder */
  ProjectUIBuilder = 90,
  /** 工作流运行时输入 */
  WorkflowRuntimeInput = 100,
  /** 语音运行时输入 */
  AudioRuntimeInput = 101,
  /** 语音资源 */
  AudioVoiceResource = 102,
  /** 模型微调输入 */
  ModelFinetuneInput = 103,
  /** OpenAPI图片上传 */
  OpenAPIImageUpload = 104,
  /** App (Project) 音频 */
  AppAudio = 105,
  /** 企业创建/修改 */
  EnterpriseModify = 106,
  CozeLoopPEModify = 107,
  CozeLoopExptModify = 108,
  CozeLoopDatasetModify = 109,
  CozeLoopEvaluatorModify = 110,
  /** CozeSpace */
  CozeSpaceQuery = 111,
  /** CozeSpace 回答 */
  CozeSpaceAnswer = 112,
  /** CozeSpace TraeBuilder Query */
  CozeSpaceTraeBuilderQuery = 113,
  /** CozeSpace TraeBuilder 回答 */
  CozeSpaceTraeBuilderAnswer = 114,
  /** CozeSpace web搜索 Query */
  CozeSpaceSearchWebQuery = 115,
  /** CozeSpace web搜索 Answer */
  CozeSpaceSearchWebAnswer = 116,
  /** CozeSpace Img搜索 Query */
  CozeSpaceSearchImgQuery = 117,
  /** CozeSpace Img搜索 Answer */
  CozeSpaceSearchImgAnswer = 118,
  /** CozeSpace 定时任务 Query */
  CozeSpaceScheduleTaskQuery = 119,
  /** 声纹创建/修改 */
  VoicePrintGroup = 120,
  /** 声纹特征创建/修改 */
  VoicePrintGroupFeature = 121,
  /** 视频生成输入 */
  VideoGenerateInput = 122,
  /** CozeSpace PPT Agent 修改 */
  CozeSpacePPTAgentModify = 123,
}

export enum QueueType {
  /** 默认队列 */
  Default = 0,
  /** 召回队列 */
  Recall = 1,
  /** 举报队列 */
  Report = 2,
  /** 离线召回 */
  Review = 3,
}

export enum RefuseReason {
  /** 其他 */
  Other = 0,
  /** 引流广告 */
  Ad = 1,
  /** 引人不适 */
  Uncomfortable = 2,
  /** 违法违规 */
  Illegal = 3,
  /** 宗教邪教 */
  Cult = 4,
  /** 公序良俗 */
  Morals = 5,
  /** 色情低俗 */
  Porn = 6,
  /** 涉领导人 */
  Leaders = 7,
  /** 党政敏感 */
  SensitiveParty = 8,
  /** 隐私政策包含无效内容（如链接失效、打不开，联系方式无效） */
  InvalidContent = 9,
  /** 违规链接 */
  LinksIllegal = 10,
}

export enum ReviewRefuseModule {
  /** Bot 1XXX */
  BotPublish = 1000,
  BotPublishBotIcon = 1001,
  BotPublishBotName = 1002,
  BotPublishBotDesc = 1003,
  BotPublishBotPrompt = 1004,
  BotPublishBotDatabase = 1005,
  BotPublishBotTask = 1006,
  BotPublishBotOnboardingPrologue = 1007,
  BotPublishBotOnboardingSuggestedQuestions = 1008,
  BotPublishBotSuggestReply = 1009,
  BotPublishBotBackgroundImage = 1010,
  BotPublishBotShortcutCommandList = 1011,
  BotPublishBotQueryCollectConf = 1012,
  BotPublishSimpleAgentName = 1013,
  BotPublishSimpleAgentPrompt = 1014,
  BotPublishSimpleAgentScene = 1015,
  BotPublishSimpleAgentSkills = 1016,
  BotPublishSimpleAgentSuggestReplyInfo = 1017,
  BotPublishBotAgentBotIcon = 1018,
  BotPublishBotAgentBotName = 1019,
  BotPublishBotAgentScene = 1020,
  BotPublishBotAgentSuggestReplyInfo = 1021,
  BotPublishGlobalJumpConditions = 1022,
  BotPublishPlugin = 1200,
  BotPublishWorkflow = 1300,
  /** ProductPlugin 22XX */
  ProductPlugin = 2200,
  ProductPluginProductInfo = 2201,
  ProductPluginPluginInfo = 2202,
  ProductPluginAPIs = 2203,
  /** ProductWorkflow 23XX */
  ProductWorkflow = 2300,
  /** ProductImageFlow 24XX */
  ProductImageFlow = 2400,
  /** InteractionPost 31XX */
  InteractionPost = 3100,
  /** InteractionComment 32XX */
  InteractionComment = 3200,
  /** Society 4XXX */
  Society = 4000,
  SocietyBasicInfoIcon = 4001,
  SocietyBasicInfoName = 4002,
  SocietyBasicInfoDesc = 4003,
  SocietyBasicInfoPrompt = 4004,
  SocietyHostInfoIcon = 4005,
  SocietyHostInfoName = 4006,
  SocietyHostInfoDesc = 4007,
  SocietyRoleInfoIcon = 4008,
  SocietyRoleInfoName = 4009,
  SocietyRoleInfoDesc = 4010,
  SocietyRoleInfoNickName = 4011,
  /** Project 5XXX */
  Project = 5000,
  ProjectBasicInfoName = 5001,
  ProjectBasicInfoDesc = 5002,
  ProjectBasicInfoIcon = 5003,
  ProjectPlugin = 5100,
  ProjectWorkflow = 5200,
  ProjectUI = 5300,
  ProjectData = 5400,
}

export interface AuditExtra {
  AuditContext?: string;
  OperationMap?: Record<string, Array<string>>;
  QueueType?: QueueType;
}

export interface AuditRecord {
  ObjectID: Int64;
  AuditType: AuditType;
  AuditStatus: AuditStatus;
  Extra?: AuditExtra;
  FailedReason?: string;
  ReqID?: string;
}
/* eslint-enable */
