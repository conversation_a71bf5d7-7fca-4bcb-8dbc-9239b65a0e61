/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bot_common from './bot_common';
import * as base from './base';

export type Int64 = string | number;

export enum AgentStatus {
  Deleted = 0,
  Valid = 1,
}

export enum CompressionType {
  Disable = 0,
  /** 折半压缩 */
  Halve = 1,
}

export enum EntityType {
  SocietyMeta = 1,
  Bot = 2,
}

export enum EventTypeCode {
  ON_LINE = 0,
  OFF_LINE = 1,
  TOUCH = 2,
}

export enum MetaStatus {
  Deleted = 0,
  Valid = 1,
}

export enum OperateProcessType {
  Pause = 1,
  Continue = 2,
  Termination = 3,
  Exit = 4,
  Retry = 5,
}

export enum ParticipantSource {
  Coze = 1,
  Store = 2,
}

export enum ProcessMode {
  Product = 1,
  Debug = 2,
}

export enum ProcessStatus {
  Waiting = 1,
  Running = 2,
  Suspending = 3,
  Suspend = 4,
  Finished = 5,
  Exit = 6,
}

export enum ProductPublishMode {
  OpenSource = 1,
  ClosedSource = 2,
}

export enum PublishFilter {
  UnPublished = 0,
  Published = 1,
}

export enum PublishStatus {
  Accepted = 1,
  Publishing = 2,
  Auditing = 3,
  AuditNotPass = 4,
  AuditPass = 5,
  Success = 6,
  Fail = 7,
  ExternalAuditing = 8,
}

export enum QuerySource {
  Teamspace = 1,
  SubmitStore = 2,
  Publishing = 3,
  StoreDetail = 4,
}

export enum RoleAgentType {
  Bot = 1,
  User = 2,
  Host = 3,
}

export enum RoleStatus {
  Deleted = 0,
  Valid = 1,
}

export enum RoleType {
  Host = 1,
  PresetBot = 2,
  Custom = 3,
}

export enum SocietyMetaEventType {
  Delete = 1,
}

export enum UserAgentType {
  Player = 1,
  Audience = 2,
  Developer = 3,
}

export interface Agent {
  id: string;
  agent_type: RoleAgentType;
  process_id: string;
  meta_id: string;
  role_id: string;
  participant_id: string;
  nickname: string;
  description: string;
  status: AgentStatus;
  create_time: string;
  update_time: string;
  icon_uri: string;
  icon_url: string;
  role_name: string;
  visibility: bot_common.SocietyVisibility;
  participant_url: string;
  participant_source: ParticipantSource;
}

export interface AgentItem {
  agent_type: RoleAgentType;
  role_id: string;
  participant_id: string;
  nickname: string;
  description: string;
  /** 区分是哪个渠道的bot，store还是teamspace */
  participant_source?: ParticipantSource;
}

export interface AgentName2Id {
  agent_id?: string;
  agent_name?: string;
}

export interface AuditEntityData {
  entity_id: string;
  entity_type: EntityType;
  name: string;
  icon_url: string;
  audit_not_pass: boolean;
  not_pass_reason: Array<string>;
}

export interface BatchCreateSocietyRoleRequest {
  meta_id: string;
  role_list: Array<CreateRole>;
  /** 可见性配置 */
  role_visibility_config?: RoleVisibilityConfig;
  Base?: base.Base;
}

export interface BatchCreateSocietyRoleResponse {
  role_list: Array<Role>;
  role_visibility_config: RoleVisibilityConfig;
  /** Host workinfo的变更 */
  host_work_info?: WorkInfo;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface BatchDeleteSocietyRoleRequest {
  meta_id: string;
  role_id_list: Array<string>;
  /** 可见性配置 */
  role_visibility_config?: RoleVisibilityConfig;
  /** Host workinfo的变更 */
  host_work_info?: WorkInfo;
  Base?: base.Base;
}

export interface BatchDeleteSocietyRoleResponse {
  role_visibility_config: RoleVisibilityConfig;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface BatchUpdateSocietyRoleRequest {
  meta_id: string;
  role_list: Array<UpdateRole>;
  /** 可见性配置 */
  role_visibility_config?: RoleVisibilityConfig;
  /** Host workinfo的变更 */
  host_work_info?: WorkInfo;
  Base?: base.Base;
}

export interface BatchUpdateSocietyRoleResponse {
  role_list: Array<Role>;
  role_visibility_config: RoleVisibilityConfig;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface ChatMessage {
  role?: string;
  type?: string;
  content?: string;
  content_type?: string;
  /** 屏蔽下游的message_id，传的是task_id */
  message_id?: string;
  reply_id?: string;
  section_id?: string;
  extra_info?: ExtraInfo;
  /** 正常、打断状态 拉消息列表时使用，chat运行时没有这个字段 */
  status?: string;
  /** 打断位置 */
  broken_pos?: number;
  sender_id?: string;
}

export interface ContextCompressionConfig {
  compression_type: CompressionType;
  /** 压缩点位,达到多少条开始压缩 */
  compression_point: number;
}

export interface CreateProcessRequest {
  process: ProcessItem;
  agent_list?: Array<AgentItem>;
  device_id: string;
  Base?: base.Base;
}

export interface CreateProcessResponse {
  process_id: string;
  process: Process;
  agent_list: Array<Agent>;
  current_agent_id: string;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface CreateRole {
  name: string;
  role_type: RoleType;
  /** 3: required Visibility visibility, // 废弃
废弃,使用下面的participant_id */
  preset_bot_id?: string;
  nickname?: string;
  description?: string;
  /** 区分是哪个渠道的bot，store还是teamspace */
  participant_source?: ParticipantSource;
  participant_id?: string;
}

export interface CreateSocietyMetaRequest {
  name: string;
  description: string;
  space_id: string;
  icon_uri: string;
  Base?: base.Base;
}

export interface CreateSocietyMetaResponse {
  meta_id: string;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface DeleteSocietyMessageRequest {
  process_id: string;
  /** 删除第几轮消息 */
  round: number;
  Base?: base.Base;
}

export interface DeleteSocietyMessageResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteSocietyMetaRequest {
  meta_id: string;
  Base?: base.Base;
}

export interface DeleteSocietyMetaResponse {
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface DuplicateSocietyMetaRequest {
  meta_id: string;
  meta_version?: string;
  target_space_id?: string;
  Base?: base.Base;
}

export interface DuplicateSocietyMetaResponse {
  copy_meta_id: string;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface Event {
  ProductID?: number;
  AppID?: number;
  DeviceID?: Int64;
  EventType?: EventTypeCode;
  Extended?: Record<string, string>;
  UserID?: Int64;
  Header?: Record<string, Array<string>>;
  ConnUUID?: string;
}

export interface ExtraInfo {
  local_message_id?: string;
  input_tokens?: string;
  output_tokens?: string;
  token?: string;
  /** "success" or "fail" */
  plugin_status?: string;
  time_cost?: string;
  workflow_tokens?: string;
  bot_state?: string;
  plugin_request?: string;
  tool_name?: string;
  plugin?: string;
  mock_hit_info?: string;
  log_id?: string;
  stream_id?: string;
  message_title?: string;
  stream_plugin_running?: string;
  society_state?: string;
  message_id?: string;
  conversation_id?: string;
}

export interface GenerateMetaStoreCategoryRequest {
  name: string;
  description: string;
  prompt: string;
  Base?: base.Base;
}

export interface GenerateMetaStoreCategoryResponse {
  category_id: string;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetMetaRoleListRequest {
  meta_id: string;
  meta_version?: string;
  Base?: base.Base;
}

export interface GetMetaRoleListResponse {
  role_list: Array<Role>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetMetaVariablesRequest {
  meta_id: string;
  meta_version?: string;
  Base?: base.Base;
}

export interface GetMetaVariablesResponse {
  variables: Array<bot_common.Variable>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetProcessDetailRequest {
  process_id: string;
  Base?: base.Base;
}

export interface GetProcessDetailResponse {
  process: Process;
  agent_list: Array<Agent>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetProcessMemoryRequest {
  process_id: string;
  agent_id: string;
  Base?: base.Base;
}

export interface GetProcessMemoryResponse {
  memorys?: Array<MemoryKVItem>;
  BaseResp: base.BaseResp;
}

export interface GetPublishEntityListRequest {
  order_id: string;
  Base?: base.Base;
}

export interface GetPublishEntityListResponse {
  entity_list: Array<PublishEntityData>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetPublishMetaResultRequest {
  order_id: string;
  Base?: base.Base;
}

export interface GetPublishMetaResultResponse {
  result_list: Array<PublishEntityResult>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetSocietyMessageListRequest {
  process_id: string;
  agent_id: string;
  /** 左开区间，可以用于切换视角后收到第一条推送消息时，传最后一条消息的task_id，查询结果直接加到消息列表后面，不用整体刷新了 */
  before_task_id?: string;
  /** 右开区间，倒序往前加载，最前一条消息的task_id，为空则从最新的消息开始 */
  after_task_id?: string;
  /** 默认20 */
  count?: number;
  Base?: base.Base;
}

export interface GetSocietyMessageListResponse {
  message_list: Array<RoundChatMessage>;
  hasmore: boolean;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetSocietyMetaDetailRequest {
  meta_id: string;
  /** 是play页面进来的，还是调试页面进来的。废弃 */
  process_mode: ProcessMode;
  meta_version?: string;
  need_process_info?: boolean;
  query_source?: QuerySource;
  Base?: base.Base;
}

export interface GetSocietyMetaDetailResponse {
  meta: Meta;
  role_list: Array<Role>;
  /** 3: optional ProcessDetail latest_process, // 废弃 */
  latest_debug_process?: ProcessDetail;
  latest_product_process?: ProcessDetail;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface GetSocietyMetaListRequest {
  space_id: string;
  creator_id?: string;
  query?: string;
  page: number;
  size: number;
  publish_filter?: PublishFilter;
  query_source?: QuerySource;
  Base?: base.Base;
}

export interface GetSocietyMetaListResponse {
  meta_list: Array<Meta>;
  total: number;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface HostConfig {
  model_id: string;
  max_round: number;
}

export interface MemoryKVItem {
  keyword?: string;
  value?: string;
  create_time?: string;
  update_time?: string;
  is_system?: boolean;
  prompt_disabled?: boolean;
  description?: string;
}

export interface Meta {
  id: string;
  name: string;
  description: string;
  space_id: string;
  user_id: string;
  icon_uri: string;
  icon_url: string;
  create_time: string;
  update_time: string;
  society_prompt: string;
  /** 按照角色名计数，host, 狼人*2， 村民*3 */
  role_count_list: Array<RoleCount>;
  username: string;
  nickname: string;
  user_icon_url: string;
  /** 是否允许删除 */
  deletable: boolean;
  /** host的模型配置,最大轮数配置 */
  host_config?: HostConfig;
  /** 上下文压缩配置 */
  context_compression_config?: ContextCompressionConfig;
  /** 背景图url */
  background_img_url: string;
  /** 可见性配置 */
  role_visibility_config: RoleVisibilityConfig;
  has_published: boolean;
  store_url: string;
  host_available_model_list?: Array<ModelInfo>;
}

export interface ModelDescGroup {
  group_name: string;
  desc: Array<string>;
}

export interface ModelInfo {
  model_id: string;
  name: string;
  icon_url: string;
  token_limit: Int64;
  model_desc: Array<ModelDescGroup>;
}

export interface OperateProcessRequest {
  process_id: string;
  operate_process_type: OperateProcessType;
  Base?: base.Base;
}

export interface OperateProcessResponse {
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface Process {
  id: string;
  process_mode: ProcessMode;
  meta_id: string;
  user_id: string;
  create_time: string;
  update_time: string;
  status: ProcessStatus;
  wait_user_reply?: UserInputTask;
  process_profile?: ProcessProFile;
}

export interface ProcessDetail {
  process: Process;
  agent_list: Array<Agent>;
}

export interface ProcessItem {
  process_mode: ProcessMode;
  meta_id: string;
  meta_version?: string;
}

export interface ProcessProFile {
  code?: number;
  message?: string;
  extra_info?: ExtraInfo;
}

export interface PublishConnector {
  connector_id: string;
  entity_settings_list: Array<PublishEntitySettings>;
}

export interface PublishEntityChangeLog {
  entity_id: string;
  entity_type: EntityType;
  change_log: string;
}

export interface PublishEntityData {
  entity_id: string;
  entity_type: EntityType;
  name: string;
  description: string;
  prompt: string;
  icon_url: string;
  product_publish_mode: ProductPublishMode;
  category?: string;
  allow_product_open_source: boolean;
}

export interface PublishEntityResult {
  entity_id: string;
  entity_type: EntityType;
  name: string;
  icon_url: string;
  publish_status: PublishStatus;
  reason: Array<string>;
  space_id: string;
}

export interface PublishEntitySettings {
  entity_id: string;
  entity_type: EntityType;
  product_publish_mode?: ProductPublishMode;
  store_category?: string;
}

export interface PublishingMetaRequest {
  order_id: string;
  change_log_list: Array<PublishEntityChangeLog>;
  connector_list: Array<PublishConnector>;
  Base?: base.Base;
}

export interface PublishingMetaResponse {
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface PublishMetaPreCreateRequest {
  meta_id: string;
  Base?: base.Base;
}

export interface PublishMetaPreCreateResponse {
  order_id: string;
  audit_data_list: Array<AuditEntityData>;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface Role {
  id: string;
  name: string;
  role_type: RoleType;
  /** 4: required Visibility visibility, // 废弃
5: required i64 preset_bot_id (api.js_conv='true' agw.js_conv="str"), // 废弃,使用下方的participant_id */
  nickname: string;
  description: string;
  meta_id: string;
  space_id: string;
  user_id: string;
  status: RoleStatus;
  create_time: Int64;
  update_time: Int64;
  icon_uri: string;
  icon_url: string;
  persona: string;
  bot_name: string;
  bot_status: bot_common.BotStatus;
  /** 区分是哪个渠道的bot，store还是teamspace */
  participant_source: ParticipantSource;
  participant_id: string;
  participant_creator_id?: string;
  /** 是否是级联发布 */
  is_cascade_publish?: boolean;
  work_info?: WorkInfo;
  biz_role_id?: string;
}

export interface RoleCount {
  name: string;
  count: number;
}

export interface RoleVisibilityConfig {
  role_cfg_list: Array<RoleVisibilityItem>;
}

export interface RoleVisibilityItem {
  name: string;
  visibility_type: bot_common.SocietyVisibility;
  visibility_roles?: Array<string>;
}

export interface RoundChatMessage {
  message: ChatMessage;
  is_finish: boolean;
  round: number;
  index_in_round: number;
  agent_id: string;
  seq_id: number;
}

export interface UpdateRole {
  id: string;
  name?: string;
  role_type?: RoleType;
  visibility?: bot_common.SocietyVisibility;
  /** 5: optional i64 preset_bot_id (api.js_conv='true' agw.js_conv="str"), //废弃,使用下面的participant_id */
  nickname?: string;
  description?: string;
  /** 区分是哪个渠道的bot，store还是teamspace */
  participant_source?: ParticipantSource;
  participant_id?: string;
}

export interface UpdateSocietyHostRequest {
  meta_id: string;
  name?: string;
  persona?: string;
  icon_uri?: string;
  /** 可见性配置 */
  role_visibility_config?: RoleVisibilityConfig;
  /** host的模型配置,最大轮数配置 */
  host_config?: HostConfig;
  work_info?: WorkInfo;
  Base?: base.Base;
}

export interface UpdateSocietyHostResponse {
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface UpdateSocietyMeta {
  id: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  society_prompt?: string;
  /** 上下文压缩配置 */
  context_compression_config?: ContextCompressionConfig;
  /** 背景图url */
  background_img_uri?: string;
  /** 可见性配置 */
  role_visibility_config?: RoleVisibilityConfig;
}

export interface UpdateSocietyMetaRequest {
  meta: UpdateSocietyMeta;
  Base?: base.Base;
}

export interface UpdateSocietyMetaResponse {
  meta_id: string;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface UpdateUserAgentRequest {
  process_id: string;
  /** 修改角色，对应切换角色 */
  agent_id?: string;
  /** 修改frontier，对应回到进程 */
  device_id?: string;
  Base?: base.Base;
}

export interface UpdateUserAgentResponse {
  user_agent: UserAgent;
  message_list: Array<RoundChatMessage>;
  hasmore: boolean;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface UserAgent {
  process_id?: string;
  user_id?: string;
  agent_id?: string;
  type?: UserAgentType;
}

export interface UserAgentReplyRequest {
  process_id: string;
  /** 用户回复内容 */
  content: string;
  /** 前置消息society_state里的task_id */
  task_id: string;
  Base?: base.Base;
}

export interface UserAgentReplyResponse {
  /** true代表当前不应该有用户回复，前端主动刷新？重置一下？ */
  already_replied: boolean;
  code: Int64;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface UserInputTask {
  task_id?: string;
  receivers?: Array<AgentName2Id>;
  round?: Int64;
  index_in_round?: Int64;
  reply_agent_id?: string;
}

/** 自己定义的WorkflowDetail，跟数据库里存的结构对应 */
export interface WorkflowDetail {
  workflow_id: string;
  plugin_id: string;
  name: string;
  desc: string;
  parameters: Array<bot_common.PluginParameter>;
  plugin_icon: string;
  flow_mode: bot_common.WorkflowMode;
  node_param_data?: Record<string, Array<WorkflowNodeParamData>>;
}

export interface WorkflowNodeParamData {
  node_type?: string;
  param_name?: string;
  param_value?: string;
}

export interface WorkInfo {
  workflows?: Array<WorkflowDetail>;
  variables?: Array<bot_common.Variable>;
}
/* eslint-enable */
