/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as eval_set from './eval_set';
import * as base from './base';
import * as datasetv2 from './datasetv2';
import * as common from './common';

export type Int64 = string | number;

export interface BatchCreateEvaluationSetItemsReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  items?: Array<eval_set.EvaluationSetItem>;
  /** items 中存在无效数据时，默认不会写入任何数据；设置 skipInvalidItems=true 会跳过无效数据，写入有效数据                                                    // items 中存在无效数据时，默认不会写入任何数据；设置 skipInvalidItems=true 会跳过无效数据，写入有效数据 */
  skip_invalid_items?: boolean;
  /** 批量写入 items 如果超出数据集容量限制，默认不会写入任何数据；设置 partialAdd=true 会写入不超出容量限制的前 N 条 */
  allow_partial_add?: boolean;
  base?: base.Base;
}

export interface BatchCreateEvaluationSetItemsResp {
  /** key: item 在 items 中的索引 */
  added_items?: Record<number, Int64>;
  errors?: Array<datasetv2.ItemErrorGroup>;
  baseResp?: base.BaseResp;
}

export interface BatchDeleteEvaluationSetItemsReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  item_ids?: Array<Int64>;
  base?: base.Base;
}

export interface BatchDeleteEvaluationSetItemsResp {
  baseResp?: base.BaseResp;
}

export interface BatchGetEvaluationSetItemsReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  version_id?: Int64;
  item_ids?: Array<Int64>;
  base?: base.Base;
}

export interface BatchGetEvaluationSetItemsResp {
  items?: Array<eval_set.EvaluationSetItem>;
  baseResp?: base.BaseResp;
}

export interface BatchGetEvaluationSetVersionsReq {
  space_id: Int64;
  version_ids: Array<Int64>;
  deleted_at?: boolean;
  base?: base.Base;
}

export interface BatchGetEvaluationSetVersionsResp {
  versioned_evaluation_set?: Array<VersionedEvaluationSet>;
  baseResp?: base.BaseResp;
}

export interface CreateEvaluationSetReq {
  space_id: Int64;
  name?: string;
  description?: string;
  evaluation_set_schema?: eval_set.EvaluationSetSchema;
  base?: base.Base;
}

export interface CreateEvaluationSetResp {
  evaluation_set_id?: Int64;
  baseResp?: base.BaseResp;
}

export interface CreateEvaluationSetVersionReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  /** 展示的版本号，SemVer2 三段式，需要大于上一版本 */
  version?: string;
  desc?: string;
  base?: base.Base;
}

export interface CreateEvaluationSetVersionResp {
  id?: Int64;
  baseResp?: base.BaseResp;
}

export interface DeleteEvaluationSetReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  base?: base.Base;
}

export interface DeleteEvaluationSetResp {
  baseResp?: base.BaseResp;
}

export interface GetEvaluationSetReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  deleted_at?: boolean;
  base?: base.Base;
}

export interface GetEvaluationSetResp {
  evaluation_set?: eval_set.EvaluationSet;
  baseResp?: base.BaseResp;
}

export interface GetEvaluationSetVersionReq {
  space_id: Int64;
  version_id: Int64;
  evaluation_set_id?: Int64;
  deleted_at?: boolean;
  base?: base.Base;
}

export interface GetEvaluationSetVersionResp {
  version?: eval_set.EvaluationSetVersion;
  evaluation_set?: eval_set.EvaluationSet;
  baseResp?: base.BaseResp;
}

export interface ListEvaluationSetItemsReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  version_id?: Int64;
  page?: number;
  /** 分页大小 (0, 200]，默认为 20 */
  page_size?: number;
  cursor?: string;
  orderBy?: common.OrderBy;
  base?: base.Base;
}

export interface ListEvaluationSetItemsResp {
  items?: Array<eval_set.EvaluationSetItem>;
  total?: Int64;
  nextCursor?: string;
  baseResp?: base.BaseResp;
}

export interface ListEvaluationSetsReq {
  space_id: Int64;
  /** 支持模糊搜索 */
  name?: string;
  creators?: Array<string>;
  evaluation_set_ids?: Array<Int64>;
  page?: number;
  /** 分页大小 (0, 200]，默认为 20 */
  page_size?: number;
  cursor?: string;
  /** 排列顺序，默认按照 createdAt 顺序排列，目前仅支持按照 createdAt 和 UpdatedAt 排序 */
  orderBy?: common.OrderBy;
  base?: base.Base;
}

export interface ListEvaluationSetsResp {
  evaluation_sets?: Array<eval_set.EvaluationSet>;
  total?: Int64;
  nextCursor?: string;
  baseResp?: base.BaseResp;
}

export interface ListEvaluationSetVersionsReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  /** 根据版本号模糊匹配 */
  version_like?: string;
  page?: number;
  /** 分页大小 (0, 200]，默认为 20 */
  page_size?: number;
  cursor?: string;
  base?: base.Base;
}

export interface ListEvaluationSetVersionsResp {
  versions?: Array<eval_set.EvaluationSetVersion>;
  total?: Int64;
  nextCursor?: string;
  baseResp?: base.BaseResp;
}

export interface UpdateEvaluationSetItemReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  item_id: Int64;
  /** 每轮对话 */
  turns?: Array<eval_set.Turn>;
  base?: base.Base;
}

export interface UpdateEvaluationSetItemResp {
  baseResp?: base.BaseResp;
}

export interface UpdateEvaluationSetReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  name?: string;
  description?: string;
  base?: base.Base;
}

export interface UpdateEvaluationSetResp {
  baseResp?: base.BaseResp;
}

export interface UpdateEvaluationSetSchemaReq {
  space_id: Int64;
  evaluation_set_id: Int64;
  /** fieldSchema.key 为空时：插入新的一列
fieldSchema.key 不为空时：更新对应的列
硬删除（不支持恢复数据）的情况下，不需要写入入参的 field list；
软删（支持恢复数据）的情况下，入参的 field list 中仍需保留该字段，并且需要把该字段的 deleted 置为 true */
  fields?: Array<eval_set.FieldSchema>;
  base?: base.Base;
}

export interface UpdateEvaluationSetSchemaResp {
  baseResp?: base.BaseResp;
}

export interface VersionedEvaluationSet {
  version?: eval_set.EvaluationSetVersion;
  evaluation_set?: eval_set.EvaluationSet;
}
/* eslint-enable */
