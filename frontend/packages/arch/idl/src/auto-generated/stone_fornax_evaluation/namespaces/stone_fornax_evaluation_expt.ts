/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as datasetv2 from './datasetv2';
import * as evaluator from './evaluator';
import * as eval_set from './eval_set';
import * as eval_target from './eval_target';
import * as common from './common';
import * as stone_fornax_evaluation_eval_target from './stone_fornax_evaluation_eval_target';

export type Int64 = string | number;

/** 聚合器类型 */
export enum AggregatorType {
  Average = 1,
  Sum = 2,
  Max = 3,
  Min = 4,
  /** 得分的分布情况 */
  Distribution = 5,
}

export enum DataType {
  /** 默认，有小数的浮点数值类型 */
  Double = 0,
  /** 得分分布 */
  ScoreDistribution = 1,
}

export enum ExptAggregateCalculateStatus {
  Unknown = 0,
  Idle = 1,
  Calculating = 2,
}

export enum ExptRetryMode {
  Unknown = 0,
  RetryAll = 1,
  RetryFailure = 2,
  RetryTargetItems = 3,
}

export enum ExptStatus {
  Unknown = 0,
  /** 待执行 */
  Pending = 2,
  /** 执行中 */
  Processing = 3,
  /** 执行成功 */
  Success = 11,
  /** 执行失败 */
  Failed = 12,
  /** 用户终止 */
  Terminated = 13,
  /** 系统内部异常终止 */
  SystemTerminated = 14,
}

export enum FieldType {
  Unknown = 0,
  /** 评估器得分, FieldKey为evaluatorVersionID,value为score */
  EvaluatorScore = 1,
  CreatorBy = 2,
  ExptStatus = 3,
  TurnRunState = 4,
  TargetID = 5,
  EvalSetID = 6,
  EvaluatorID = 7,
  TargetType = 8,
  SourceTarget = 9,
  EvaluatorVersionID = 20,
  TargetVersionID = 21,
  EvalSetVersionID = 22,
}

export enum FilterLogicOp {
  Unknown = 0,
  And = 1,
  Or = 2,
}

export enum FilterOperatorType {
  Unknown = 0,
  /** 等于 */
  Equal = 1,
  /** 不等于 */
  NotEqual = 2,
  /** 大于 */
  Greater = 3,
  /** 大于等于 */
  GreaterOrEqual = 4,
  /** 小于 */
  Less = 5,
  /** 小于等于 */
  LessOrEqual = 6,
  /** 包含 */
  In = 7,
  /** 不包含 */
  NotIn = 8,
}

export enum ItemRunState {
  Unknown = -1,
  /** 排队中 */
  Queueing = 0,
  /** 执行中 */
  Processing = 1,
  /** 成功 */
  Success = 2,
  /** 失败 */
  Fail = 3,
  /** 终止执行 */
  Terminal = 5,
}

export enum TurnRunState {
  /** 未开始执行 */
  Queueing = 0,
  /** 执行成功 */
  Success = 1,
  /** 执行失败 */
  Fail = 2,
  Processing = 3,
  Terminal = 4,
}

export interface AggregateData {
  data_type: DataType;
  value?: number;
  score_distribution?: ScoreDistribution;
}

/** 一种聚合器类型的聚合结果 */
export interface AggregatorResult {
  aggregator_type: AggregatorType;
  data?: AggregateData;
}

export interface CheckExperimentNameRequest {
  space_id: Int64;
  name?: string;
  Base?: base.Base;
}

export interface CheckExperimentNameResponse {
  pass?: boolean;
  message?: string;
  BaseResp?: base.BaseResp;
}

export interface CloneExperimentRequest {
  expt_id?: Int64;
  space_id?: Int64;
  Base?: base.Base;
}

export interface CloneExperimentResponse {
  experiment?: Experiment;
  BaseResp?: base.BaseResp;
}

export interface ColumnEvalSetField {
  /** 唯一键 */
  key?: string;
  /** 展示名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 类型，如 文本，图片，etc. */
  content_type?: string;
  /** 默认渲染格式，如 code, json, etc.mai */
  default_display_format?: datasetv2.FieldDisplayFormat;
}

export interface ColumnEvaluator {
  evaluator_version_id: Int64;
  evaluator_id: Int64;
  evaluator_type: evaluator.EvaluatorType;
  /** 评估器名称 */
  name?: string;
  /** 评估器版本 */
  version?: string;
  /** 评估器描述 */
  description?: string;
}

export interface DeleteExperimentRequest {
  space_id: Int64;
  expt_id: Int64;
  Base?: base.Base;
}

export interface DeleteExperimentResponse {
  BaseResp?: base.BaseResp;
}

/** 评估器版本粒度聚合结果 */
export interface EvaluatorAggregateResult {
  /** 图表包含实验和评估器信息。支持跳转查看实验关联评测集、评估器详情 */
  evaluator_version_id: Int64;
  aggregator_results?: Array<AggregatorResult>;
  name?: string;
  /** 评估器版本 */
  version?: string;
}

export interface EvaluatorFieldMapping {
  evaluator_version_id: Int64;
  from_eval_set?: Array<FieldMapping>;
  from_target?: Array<FieldMapping>;
}

export interface Experiment {
  id?: Int64;
  name?: string;
  desc?: string;
  creator_by?: string;
  status?: ExptStatus;
  status_message?: string;
  start_time?: Int64;
  end_time?: Int64;
  eval_set_version_id?: Int64;
  target_version_id?: Int64;
  evaluator_version_ids?: Array<Int64>;
  eval_set?: eval_set.EvaluationSet;
  eval_target?: eval_target.EvalTarget;
  evaluators?: Array<evaluator.Evaluator>;
  eval_set_id?: Int64;
  target_id?: Int64;
  base_info?: common.BaseInfo;
  expt_stats?: ExptStatistics;
  target_field_mapping?: TargetFieldMapping;
  evaluator_field_mapping?: Array<EvaluatorFieldMapping>;
}

export interface ExperimentFilter {
  filters?: Filters;
}

export interface ExperimentResult {
  experiment_id: Int64;
  payload?: ExperimentTurnPayload;
}

/** 实际行级payload */
export interface ExperimentTurnPayload {
  turn_id?: Int64;
  /** 评测数据集数据 */
  eval_set?: TurnEvalSet;
  /** 评测对象结果 */
  target_output?: TurnTargetOutput;
  /** 评测规则执行结果 */
  evaluator_output?: TurnEvaluatorOutput;
  /** 评测系统相关数据日志、error */
  system_info?: TurnSystemInfo;
}

/** 实验粒度聚合结果 */
export interface ExptAggregateResult {
  experiment_id: Int64;
  /** 评估器维度聚合结果,key 为evaluator_version_id */
  evaluator_results?: Record<Int64, EvaluatorAggregateResult>;
  /** 聚合计算状态 */
  status?: ExptAggregateCalculateStatus;
}

export interface ExptFilterOption {
  fuzzy_name?: string;
  filters?: Filters;
}

export interface ExptStatistics {
  evaluator_aggregate_results?: Array<EvaluatorAggregateResult>;
  token_usage?: TokenUsage;
  credit_cost?: number;
  pending_turn_cnt?: number;
  success_turn_cnt?: number;
  fail_turn_cnt?: number;
  terminated_turn_cnt?: number;
  processing_turn_cnt?: number;
}

export interface FieldMapping {
  field_name?: string;
  const_value?: string;
  from_field_name?: string;
}

/** 字段过滤器 */
export interface FilterCondition {
  /** 过滤字段，比如评估器ID */
  field?: FilterField;
  /** 操作符，比如等于、包含、大于、小于等 */
  operator?: FilterOperatorType;
  /** 操作值;支持多种类型的操作值；
当 Operator为In, NotIn时，Value按照逗号分隔 */
  value?: string;
  source_target?: SourceTarget;
}

export interface FilterField {
  field_type: FieldType;
  filed_key?: string;
}

export interface Filters {
  /** 过滤条件 */
  filter_conditions?: Array<FilterCondition>;
  /** 过滤条件间的逻辑操作，默认为And */
  logic_op?: FilterLogicOp;
}

export interface ItemResult {
  item_id: Int64;
  /** row粒度实验结果详情 */
  turn_results?: Array<TurnResult>;
  system_info?: ItemSystemInfo;
  /** item在eval_set中的index */
  item_index?: Int64;
}

export interface ItemSystemInfo {
  item_run_state?: ItemRunState;
  /** Row执行时关联的logID */
  log_id?: string;
  error?: RunError;
}

export interface KillExperimentRequest {
  expt_id?: Int64;
  space_id?: Int64;
  Base?: base.Base;
}

export interface KillExperimentResponse {
  BaseResp?: base.BaseResp;
}

export interface MDeleteExperimentRequest {
  space_id: Int64;
  expt_ids: Array<Int64>;
  Base?: base.Base;
}

export interface MDeleteExperimentResponse {
  BaseResp?: base.BaseResp;
}

export interface MGetExperimentAggrResultRequest {
  space_id: Int64;
  experiment_ids: Array<Int64>;
  Base?: base.Base;
}

export interface MGetExperimentAggrResultResponse {
  expt_aggregate_result?: Array<ExptAggregateResult>;
  BaseResp?: base.BaseResp;
}

export interface MGetExperimentRequest {
  space_id: Int64;
  expt_ids: Array<Int64>;
  Base?: base.Base;
}

export interface MGetExperimentResponse {
  experiments?: Array<Experiment>;
  BaseResp?: base.BaseResp;
}

export interface MGetExperimentResultRequest {
  space_id: Int64;
  experiment_ids: Array<Int64>;
  /** 实验对比的基准实验ID */
  baseline_experiment_id?: Int64;
  /** key: experiment_id */
  filters?: Record<Int64, ExperimentFilter>;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface MGetExperimentResultResponse {
  /** 数据集表头信息 */
  column_eval_set_fields: Array<ColumnEvalSetField>;
  /** 评估器表头信息 */
  column_evaluators?: Array<ColumnEvaluator>;
  /** item粒度实验结果详情 */
  item_results?: Array<ItemResult>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface PullExperimentsRequest {
  space_id: Int64;
  page?: number;
  page_size?: number;
  filter_option?: ExptFilterOption;
  order_bys?: Array<common.OrderBy>;
  Base?: base.Base;
}

export interface PullExperimentsResponse {
  experiments?: Array<Experiment>;
  total?: number;
  BaseResp?: base.BaseResp;
}

export interface RetryExperimentRequest {
  retry_mode?: ExptRetryMode;
  space_id?: Int64;
  expt_id?: Int64;
  item_ids?: Array<Int64>;
  Base?: base.Base;
}

export interface RetryExperimentResponse {
  run_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface RunError {
  code: Int64;
  /** code对应的message */
  message?: string;
  /** 错误详情 */
  detail?: string;
}

export interface ScoreDistribution {
  score_distribution_items?: Array<ScoreDistributionItem>;
}

export interface ScoreDistributionItem {
  /** TOP5以外的聚合展示为“其他” */
  score: string;
  count: Int64;
  percentage: number;
}

export interface SourceTarget {
  eval_target_type?: eval_target.EvalTargetType;
  source_target_ids?: Array<string>;
}

export interface SubmitExperimentRequest {
  space_id: Int64;
  eval_set_version_id?: Int64;
  TargetVersionID?: Int64;
  evaluator_version_ids?: Array<Int64>;
  name?: string;
  desc?: string;
  eval_set_id?: Int64;
  TargetID?: Int64;
  target_field_mapping?: TargetFieldMapping;
  evaluator_field_mapping?: Array<EvaluatorFieldMapping>;
  item_concur_num?: number;
  evaluators_concur_num?: number;
  create_eval_target_param?: stone_fornax_evaluation_eval_target.CreateEvalTargetParam;
  Base?: base.Base;
}

export interface SubmitExperimentResponse {
  experiment?: Experiment;
  run_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface TargetFieldMapping {
  from_eval_set?: Array<FieldMapping>;
}

export interface TokenUsage {
  input_tokens?: Int64;
  output_tokens?: Int64;
}

export interface TurnEvalSet {
  turn?: eval_set.Turn;
}

export interface TurnEvaluatorOutput {
  /** key: evaluator_version_id */
  evaluator_records?: Record<Int64, evaluator.EvaluatorRecord>;
}

/** 行级结果 可能包含多个实验 */
export interface TurnResult {
  /** 可能为空 */
  turn_id?: Int64;
  /** 参与对比的实验序列，对于单报告序列长度为1 */
  experiment_results?: Array<ExperimentResult>;
  /** turn在item中的轮次 */
  turn_index?: Int64;
}

export interface TurnSystemInfo {
  turn_run_state?: TurnRunState;
  /** Row执行时关联的logID */
  log_id?: string;
  error?: RunError;
}

export interface TurnTargetOutput {
  eval_target_record?: eval_target.EvalTargetRecord;
}

export interface UpdateExperimentRequest {
  space_id: Int64;
  expt_id: Int64;
  name?: string;
  desc?: string;
  Base?: base.Base;
}

export interface UpdateExperimentResponse {
  experiment?: Experiment;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
