/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as datasetv2 from './datasetv2';

export type Int64 = string | number;

/** 模型系列，特别指不同模型接入商 */
export enum ModelClass {
  Undefined = 0,
  /** gpt */
  GPT = 1,
  /** 字节 */
  SEED = 2,
  /** google */
  Gemini = 3,
  /** 亚马逊 */
  Claude = 4,
  /** 文心一言 */
  Ernie = 5,
  /** 百川 */
  Baichuan = 6,
  /** 阿里 */
  Qwen = 7,
  /** 智谱 */
  GML = 8,
  /** 深度求索 */
  DeepSeek = 9,
}

export enum Provider {
  /** GPT OpenAPI平台 */
  GPTOpenAPI = 1,
  /** 火山方舟 */
  Maas = 2,
  /** 暂时特指seed从bot_engine接入 */
  BotEngine = 3,
  /** merlin平台 */
  Merlin = 4,
  /** merlin-seed平台 */
  MerlinSeed = 5,
}

export enum Role {
  System = 1,
  User = 2,
  Assistant = 3,
  Tool = 4,
}

export enum TenantType {
  /** 字节 */
  ByteDance = 0,
  /** 懂车帝 */
  Dcar = 1,
}

export interface ArgsSchema {
  key?: string;
  support_content_types?: Array<string>;
  /** 序列化后的jsonSchema字符串，例如："{\"type\": \"object\", \"properties\": {\"name\": {\"type\": \"string\"}, \"age\": {\"type\": \"integer\"}, \"isStudent\": {\"type\": \"boolean\"}}, \"required\": [\"name\", \"age\", \"isStudent\"]}" */
  json_schema?: string;
}

export interface Audio {
  /** mp3/wav, etc */
  format?: string;
  url?: string;
}

export interface BaseInfo {
  created_by?: UserInfo;
  updated_by?: UserInfo;
  created_at?: Int64;
  updated_at?: Int64;
  deleted_at?: Int64;
}

export interface Content {
  content_type?: string;
  format?: datasetv2.FieldDisplayFormat;
  text?: string;
  /** 图片内容 */
  image?: Image;
  /** 图文混排时，图文内容 */
  multi_part?: Array<Content>;
  audio?: Audio;
}

export interface Image {
  name?: string;
  url?: string;
  uri?: string;
  thumb_url?: string;
}

export interface Message {
  role?: Role;
  content?: Content;
  ext?: Record<string, string>;
}

/** 模型配置数据 */
export interface ModelConfig {
  /** 模型ID */
  id?: Int64;
  /** 模型名称 */
  name?: string;
  /** 模型系列 */
  model_class?: ModelClass;
  temperature?: number;
  max_tokens?: number;
  top_k?: number;
  top_p?: number;
  json_mode?: boolean;
  /** deprecated */
  function_call_mode?: boolean;
  presence_penalty?: number;
  frequency_penalty?: number;
  /** 模型提供方 */
  provider?: Provider;
  /** 模型提供方的模型唯一标识（应对saas无法传modelID的场景） */
  provider_model_id?: string;
}

export interface OrderBy {
  /** 排序字段 */
  field?: string;
  /** 是否升序，默认倒序 */
  is_asc?: boolean;
}

export interface UserInfo {
  /** 姓名 */
  name?: string;
  /** 英文名称 */
  en_name?: string;
  /** 用户头像url */
  avatar_url?: string;
  /** 72 * 72 头像 */
  avatar_thumb?: string;
  /** 用户应用内唯一标识 */
  open_id?: string;
  /** 用户应用开发商内唯一标识 */
  union_id?: string;
  /** 企业标识 */
  tenant_key?: string;
  /** 用户在租户内的唯一标识 */
  user_id?: string;
  /** 用户邮箱 */
  email?: string;
  /** 租户 */
  tenant?: TenantType;
}
/* eslint-enable */
