/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export enum CozeBotInfoType {
  /** 草稿 bot */
  DraftBot = 1,
  /** 商店 bot */
  ProductBot = 2,
}

export enum EvalTargetRunStatus {
  Unknown = 0,
  Success = 1,
  Fail = 2,
}

export enum EvalTargetType {
  /** CozeBot */
  CozeBot = 1,
  /** Prompt */
  FornaxPrompt = 2,
}

export enum ModelPlatform {
  Unknown = 0,
  GPTOpenAPI = 1,
  MAAS = 2,
}

export enum SubmitStatus {
  Undefined = 0,
  /** 未提交 */
  UnSubmit = 1,
  /** 已提交 */
  Submitted = 2,
}

/** Coze2.0Bot */
export interface CozeBot {
  bot_id?: Int64;
  bot_version?: string;
  bot_info_type?: CozeBotInfoType;
  model_info?: ModelInfo;
  /** DTO使用，不存数据库 */
  bot_name?: string;
  /** DTO使用，不存数据库 */
  avatar_url?: string;
  /** DTO使用，不存数据库 */
  description?: string;
  /** 如果是发布版本则这个字段不为空 */
  publish_version?: string;
  base_info?: common.BaseInfo;
}

export interface EvalPrompt {
  prompt_id?: string;
  version?: string;
  /** DTO使用，不存数据库 */
  name?: string;
  /** DTO使用，不存数据库 */
  prompt_key?: string;
  /** DTO使用，不存数据库 */
  submit_status?: SubmitStatus;
  /** DTO使用，不存数据库 */
  description?: string;
}

/** TODO TargetVersion版本信息在这里要体检 */
export interface EvalTarget {
  /** 基本信息
一个对象的唯一标识 */
  id?: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** 源对象ID，例如prompt ID */
  source_target_id?: string;
  /** 评测对象类型 */
  eval_target_type?: EvalTargetType;
  /** 版本信息
目标版本 */
  eval_target_version?: EvalTargetVersion;
  /** 系统信息 */
  base_info?: common.BaseInfo;
}

export interface EvalTargetContent {
  /** 输入schema */
  input_schemas?: Array<common.ArgsSchema>;
  /** 输出schema */
  output_schemas?: Array<common.ArgsSchema>;
  /** 101-200 EvalTarget类型
EvalTargetType=0 时，传参此字段。 评测对象为 CozeBot 时, 需要设置 CozeBot 信息 */
  coze_bot?: CozeBot;
  /** EvalTargetType=1 时，传参此字段。 评测对象为 EvalPrompt 时, 需要设置 Prompt 信息 */
  prompt?: EvalPrompt;
}

export interface EvalTargetInputData {
  /** 历史会话记录 */
  history_messages?: Array<common.Message>;
  /** 变量 */
  input_fields?: Record<string, common.Content>;
  ext?: Record<string, string>;
}

export interface EvalTargetOutputData {
  /** 变量 */
  output_fields?: Record<string, common.Content>;
  /** 运行消耗 */
  eval_target_usage?: EvalTargetUsage;
  /** 运行报错 */
  eval_target_run_error?: EvalTargetRunError;
  /** 运行耗时 */
  time_consuming_ms?: Int64;
}

export interface EvalTargetRecord {
  /** 评估记录ID */
  id?: Int64;
  /** 空间ID */
  space_id?: Int64;
  target_id?: Int64;
  target_version_id?: Int64;
  /** 实验执行ID */
  experiment_run_id?: Int64;
  /** 评测集数据项ID */
  item_id?: Int64;
  /** 评测集数据项轮次ID */
  turn_id?: Int64;
  /** 链路ID */
  trace_id?: string;
  /** 链路ID */
  log_id?: string;
  /** 输入数据 */
  eval_target_input_data?: EvalTargetInputData;
  /** 输出数据 */
  eval_target_output_data?: EvalTargetOutputData;
  status?: EvalTargetRunStatus;
  base_info?: common.BaseInfo;
}

export interface EvalTargetRunError {
  code?: number;
  message?: string;
}

export interface EvalTargetUsage {
  input_tokens?: Int64;
  output_tokens?: Int64;
}

export interface EvalTargetVersion {
  /** 基本信息
版本唯一标识 */
  id?: Int64;
  /** 空间ID */
  space_id?: Int64;
  /** 对象唯一标识 */
  target_id?: Int64;
  /** 源对象版本，例如prompt是0.0.1，bot是版本号12233等 */
  source_target_version?: string;
  /** 目标对象内容 */
  eval_target_content?: EvalTargetContent;
  /** 系统信息 */
  base_info?: common.BaseInfo;
}

export interface ModelInfo {
  model_id?: Int64;
  model_name?: string;
  /** DTO使用，不存数据库 */
  show_name?: string;
  /** DTO使用，不存数据库 */
  max_tokens?: Int64;
  /** 模型家族信息 */
  model_family?: Int64;
  /** 模型平台 */
  platform?: ModelPlatform;
}
/* eslint-enable */
