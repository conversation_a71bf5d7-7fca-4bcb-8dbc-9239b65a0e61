/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ProductDraftStatus {
  Pending = 1,
  Approved = 2,
  Rejected = 3,
}

export enum ProductEntityType {
  Bot = 1,
  Plugin = 2,
  CozeToken = 50,
  Common = 99,
}

export enum ProductStatus {
  Listed = 1,
  Unlisted = 2,
}

export enum ProductUnlistType {
  ByAdmin = 1,
  ByUser = 2,
}

export enum SortType {
  Heat = 1,
  Newest = 2,
}

export enum VerifyStatus {
  /** 未认证 */
  Pending = 1,
  /** 认证成功 */
  Succeed = 2,
  /** 认证失败 */
  Failed = 3,
  /** 认证中 */
  InProgress = 4,
}
/* eslint-enable */
