/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface DelProfileMemoryRequest {
  bot_id?: string;
  keywords?: Array<string>;
}

export interface DelProfileMemoryResponse {
  code?: Int64;
  msg?: string;
}

export interface GetProfileMemoryData {
  memories?: Array<KVItem>;
}

export interface GetProfileMemoryRequest {
  bot_id?: string;
  task_id?: string;
  space_id?: string;
}

export interface GetProfileMemoryResponse {
  code?: Int64;
  msg?: string;
  data?: GetProfileMemoryData;
}

export interface KVItem {
  keyword?: string;
  value?: string;
  create_time?: string;
  update_time?: string;
}

export interface UpsertProfileMemoryRequest {
  bot_id?: Int64;
  profile?: Array<KVItem>;
}

export interface UpsertProfileMemoryResponse {
  code?: Int64;
  msg?: string;
}
/* eslint-enable */
