/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum DocumentSourceType {
  /** 本地上传 */
  Document = 0,
  /** url */
  Web = 1,
  /** 自定义 */
  Custom = 2,
  /** 第三方 */
  ThirdParty = 3,
}

export enum DocumentStatus {
  /** 上传中 */
  Processing = 0,
  /** 生效 */
  Enable = 1,
  /** 失效 */
  Disable = 2,
  /** 删除 */
  Deleted = 3,
  /** 失败 */
  Failed = 9,
}

export enum DocumentUpdateType {
  NoUpdate = 0,
  Cover = 1,
  Append = 2,
}

/** 文件类型 */
export enum FormatType {
  /** 文本 */
  Text = 0,
  /** 表格 */
  Table = 1,
}

export interface CreateDocumentRequest {
  /** 知识库 id */
  dataset_id?: string;
  /** 空间 id */
  space_id?: string;
  /** 文件信息 */
  document?: DocumentInfo;
}

export interface CreateDocumentResponse {
  code?: number;
  msg?: string;
  /** document id */
  id?: string;
}

/** 表格的列信息 */
export interface DocTableColumn {
  id?: string;
  /** 列名 */
  column_name?: string;
  /** 是否为语义匹配列 */
  is_semantic?: boolean;
  /** 列原本在 excel 的序号 */
  sequence?: string;
}

export interface DocTableSheet {
  /** sheet 的编号 */
  id?: Int64;
  /** sheet 名 */
  sheet_name?: string;
  /** 总行数 */
  total_row?: Int64;
}

/** 文件信息 */
export interface DocumentInfo {
  name?: string;
  document_id?: string;
  /** 文件资源 */
  tos_uri?: string;
  /** 使用的bot数量 */
  bot_used_count?: number;
  /** 创建时间 */
  create_time?: number;
  /** 更新时间 */
  update_time?: number;
  /** 创建人 */
  creator_id?: string;
  /** 包含分段数量 */
  slice_count?: number;
  /** 文件名后缀 */
  type?: string;
  /** 文件大小 字节数 */
  size?: number;
  /** 字符数 */
  char_count?: number;
  /** 状态 */
  status?: DocumentStatus;
  /** 命中次数 */
  hit_count?: number;
  /** 来源 */
  source_type?: DocumentSourceType;
  /** 更新类型 */
  update_type?: DocumentUpdateType;
  /** 更新间隔 */
  update_interval?: number;
  /** 切片规则 */
  rule?: string;
  /** 文件类型 */
  format_type?: FormatType;
  /** 表格类型的表结构 */
  table_meta?: Array<DocTableColumn>;
  /** url 地址 */
  web_url?: string;
  /** 状态的详细信息；如果切片失败，返回失败信息 */
  status_descript?: string;
  /** 三方同步过来的文档是否已断开连接 */
  is_disconnect?: boolean;
  /** 三方文件 id */
  data_source_id?: Int64;
}

export interface GetDocumentTableInfoRequest {
  /** 如果为第一次 url 上传的表格，传递该值 */
  submit_web_id?: string;
  /** 如果为第一次本地文件上传的表格，传递该值 */
  tos_uri?: string;
  /** 如果为已有 document 的表格，传递该值 */
  document_id?: string;
  /** 三方数据源的文件 id */
  source_file_id?: string;
  /** 文件类型 */
  source_type?: DocumentSourceType;
}

export interface GetDocumentTableInfoResponse {
  code?: number;
  msg?: string;
  sheet_list?: Array<DocTableSheet>;
  /** key: sheet_id -> list<DocTableColumn> */
  table_meta?: Record<string, Array<DocTableColumn>>;
  /** key: sheet_id -> list_preview_data, list idx: line_idx, list elem: key(sequence) -> value(cell_data) */
  preview_data?: Record<string, Array<Record<string, string>>>;
}

export interface ListDocumentData {
  documents_info?: Array<DocumentInfo>;
  total?: number;
}

export interface ListDocumentRequest {
  dataset_id?: string;
  page?: number;
  size?: number;
  document_id?: string;
}

export interface ListDocumentResponse {
  code?: number;
  msg?: string;
  data?: ListDocumentData;
}

export interface UpdateDocumentRequest {
  document_id?: string;
  status?: DocumentStatus;
  document_name?: string;
  /** 表格类型元数据信息 */
  table_meta?: Array<DocTableColumn>;
}

export interface UpdateDocumentResponse {
  code?: number;
  msg?: string;
}

export interface UpdateWebRuleRequest {
  document_id?: string;
  update_type?: DocumentUpdateType;
  update_interval?: number;
}

export interface UpdateWebRuleResponse {
  code?: number;
  msg?: string;
}
/* eslint-enable */
