/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as flow_devops_nclc_custom_component from './namespaces/flow_devops_nclc_custom_component';
import * as flow_devops_nclc_eino_plugin from './namespaces/flow_devops_nclc_eino_plugin';
import * as flow_devops_nclc_entity from './namespaces/flow_devops_nclc_entity';
import * as flow_devops_nclc_schema_frontend from './namespaces/flow_devops_nclc_schema_frontend';
import * as flow_devops_nclc_schema_registry from './namespaces/flow_devops_nclc_schema_registry';
import * as flow_devops_nclc_workflow from './namespaces/flow_devops_nclc_workflow';

export {
  base,
  flow_devops_nclc_custom_component,
  flow_devops_nclc_eino_plugin,
  flow_devops_nclc_entity,
  flow_devops_nclc_schema_frontend,
  flow_devops_nclc_schema_registry,
  flow_devops_nclc_workflow,
};
export * from './namespaces/base';
export * from './namespaces/flow_devops_nclc_custom_component';
export * from './namespaces/flow_devops_nclc_eino_plugin';
export * from './namespaces/flow_devops_nclc_entity';
export * from './namespaces/flow_devops_nclc_schema_frontend';
export * from './namespaces/flow_devops_nclc_schema_registry';
export * from './namespaces/flow_devops_nclc_workflow';

export type Int64 = string | number;

export default class FornaxNclcService<T> {
  private request: any = () => {
    throw new Error('FornaxNclcService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/nclc/v1/test_run/:space_id/:flow_id
   *
   * TestRun 测试运行工作流.
   */
  TestRun(
    req: flow_devops_nclc_workflow.TestRunRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.TestRunResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/nclc/v1/test_run/${_req['space_id']}/${_req['flow_id']}`,
    );
    const method = 'POST';
    const data = {
      data: _req['data'],
      inputs: _req['inputs'],
      base: _req['base'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/nclc/v1/create_flow
   *
   * CreateFlow 创建工作流.
   */
  CreateFlow(
    req: flow_devops_nclc_workflow.CreateFlowRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.CreateFlowResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/create_flow');
    const method = 'POST';
    const data = { flow: _req['flow'], base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/nclc/v1/export/:space_id/:flow_id
   *
   * Export 导出工作流.
   */
  Export(
    req: flow_devops_nclc_workflow.ExportRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.ExportResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/nclc/v1/export/${_req['space_id']}/${_req['flow_id']}`,
    );
    const method = 'POST';
    const data = { data: _req['data'], base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/nclc/v1/query_flow/:space_id/:flow_id
   *
   * QueryFlow 查询工作流.
   */
  QueryFlow(
    req: flow_devops_nclc_workflow.QueryFlowRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.QueryFlowResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/nclc/v1/query_flow/${_req['space_id']}/${_req['flow_id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/nclc/v1/delete_flow/:space_id/:flow_id
   *
   * DeleteFlow 删除工作流.
   */
  DeleteFlow(
    req: flow_devops_nclc_workflow.DeleteFlowRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.DeleteFlowResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/nclc/v1/delete_flow/${_req['space_id']}/${_req['flow_id']}`,
    );
    const method = 'POST';
    const data = { base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/nclc/v1/update_flow_meta/:space_id/:flow_id
   *
   * UpdateFlowMeta 更新工作流元信息.
   */
  UpdateFlowMeta(
    req: flow_devops_nclc_workflow.UpdateFlowMetaRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.UpdateFlowMetaResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/nclc/v1/update_flow_meta/${_req['space_id']}/${_req['flow_id']}`,
    );
    const method = 'PUT';
    const data = { meta: _req['meta'], base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/nclc/v1/list_flows/:space_id
   *
   * ListFlows 按 spaceID 查询工作流.
   */
  ListFlows(
    req: flow_devops_nclc_workflow.ListFlowsRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.ListFlowsResponse> {
    const _req = req;
    const url = this.genBaseURL(`/api/nclc/v1/list_flows/${_req['space_id']}`);
    const method = 'GET';
    const params = { base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/nclc/v1/list_components/:space_id
   *
   * ListComponents 按 space 拉取组件元信息.
   */
  ListComponents(
    req: flow_devops_nclc_schema_frontend.ListComponentsRequest,
    options?: T,
  ): Promise<flow_devops_nclc_schema_frontend.ListComponentsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/nclc/v1/list_components/${_req['space_id']}`,
    );
    const method = 'GET';
    const params = { base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * PUT /api/nclc/v1/update_flow/:space_id/:flow_id
   *
   * UpdateFlow 更新工作流.
   */
  UpdateFlow(
    req: flow_devops_nclc_workflow.UpdateFlowRequest,
    options?: T,
  ): Promise<flow_devops_nclc_workflow.UpdateFlowResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/nclc/v1/update_flow/${_req['space_id']}/${_req['flow_id']}`,
    );
    const method = 'PUT';
    const data = { data: _req['data'], base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/nclc/v1/component/update
   *
   * 更新组件
   */
  UpdateCustomComponent(
    req: flow_devops_nclc_custom_component.UpdateCustomComponentRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.UpdateCustomComponentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/update');
    const method = 'PUT';
    const data = { component: _req['component'], base: _req['base'] };
    const params = {
      space_id: _req['space_id'],
      component_id: _req['component_id'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/nclc/v1/component/list
   *
   * 获取组件列表
   */
  ListCustomComponent(
    req: flow_devops_nclc_custom_component.ListCustomComponentRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.ListCustomComponentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      key_word: _req['key_word'],
      creator_i_d: _req['creator_i_d'],
      base: _req['base'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/nclc/v1/component/get
   *
   * 获取组件详情
   */
  GetCustomComponent(
    req: flow_devops_nclc_custom_component.GetCustomComponentRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.GetCustomComponentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/get');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      component_id: _req['component_id'],
      base: _req['base'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * DELETE /api/nclc/v1/component/delete
   *
   * 删除组件
   */
  DeleteCustomComponent(
    req: flow_devops_nclc_custom_component.DeleteCustomComponentRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.DeleteCustomComponentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/delete');
    const method = 'DELETE';
    const params = {
      space_id: _req['space_id'],
      component_id: _req['component_id'],
      base: _req['base'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'X-Jwt-Token': _req['X-Jwt-Token'],
    };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/nclc/v1/component/release
   *
   * 发布组件
   */
  ReleaseCustomComponent(
    req: flow_devops_nclc_custom_component.ReleaseCustomComponentRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.ReleaseCustomComponentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/release');
    const method = 'POST';
    const data = {
      component_release: _req['component_release'],
      base: _req['base'],
    };
    const params = {
      component_id: _req['component_id'],
      space_id: _req['space_id'],
    };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'X-Jwt-Token': _req['X-Jwt-Token'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/nclc/v1/component/type/list
   *
   * 获取组件类型列表
   */
  ListSupportComponentType(
    req: flow_devops_nclc_custom_component.ListSupportComponentTypeRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.ListSupportComponentTypeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/type/list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** POST /api/nclc/v1/component/ide_launch */
  IDELaunch(
    req: flow_devops_nclc_custom_component.IDELaunchRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.IDELaunchResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/ide_launch');
    const method = 'POST';
    const data = { base: _req['base'] };
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'X-Jwt-Token': _req['X-Jwt-Token'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * POST /api/nclc/v1/component/create
   *
   * 自定义组件
   *
   * 创建组件
   */
  CreateCustomComponent(
    req: flow_devops_nclc_custom_component.CreateCustomComponentRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.CreateCustomComponentResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/create');
    const method = 'POST';
    const data = { component: _req['component'], base: _req['base'] };
    const params = { space_id: _req['space_id'] };
    const headers = {
      'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'],
      'X-Jwt-Token': _req['X-Jwt-Token'],
    };
    return this.request({ url, method, data, params, headers }, options);
  }

  /**
   * GET /api/nclc/v1/component/release/list
   *
   * 获取组件版本发布列表
   */
  ListCustomComponentReleaseRecord(
    req: flow_devops_nclc_custom_component.ListCustomComponentReleaseRecordRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.ListCustomComponentReleaseRecordResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/release/list');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      component_id: _req['component_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      base: _req['base'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/nclc/v1/component/release/get
   *
   * 获取组件版本发布详情, 用于轮询发布状态
   */
  GetCustomComponentReleaseRecord(
    req: flow_devops_nclc_custom_component.GetCustomComponentReleaseRecordRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.GetCustomComponentReleaseRecordResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/release/get');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      component_id: _req['component_id'],
      release_id: _req['release_id'],
      base: _req['base'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/nclc/v2/component/template_info/list
   *
   * 获取组件模板信息列表
   */
  ListComponentTemplateInfo(
    req: flow_devops_nclc_custom_component.ListComponentTemplateInfoRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.ListComponentTemplateInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v2/component/template_info/list');
    const method = 'GET';
    const params = { space_id: _req['space_id'], base: _req['base'] };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/nclc/v1/component/component_key_valid
   *
   * 用于创建前校验ComponentKey冲突
   */
  IsComponentKeyValid(
    req: flow_devops_nclc_custom_component.IsComponentKeyValidRequest,
    options?: T,
  ): Promise<flow_devops_nclc_custom_component.IsComponentKeyValidResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/nclc/v1/component/component_key_valid');
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      component_key: _req['component_key'],
      base: _req['base'],
    };
    const headers = { 'FlowDevops-Agw-UserId': _req['FlowDevops-Agw-UserId'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/nclc/v1/plugin/eino_tool/source */
  GetEinoToolSource(
    req?: flow_devops_nclc_eino_plugin.EinoToolSourceRequest,
    options?: T,
  ): Promise<flow_devops_nclc_eino_plugin.EinoToolSourceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/nclc/v1/plugin/eino_tool/source');
    const method = 'GET';
    const params = {
      arch: _req['arch'],
      system: _req['system'],
      plugin_version: _req['plugin_version'],
      platform: _req['platform'],
      base: _req['base'],
    };
    const headers = { Host: _req['Host'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /api/nclc/v1/plugin/tools/version */
  GetEinoToolsVersion(
    req?: flow_devops_nclc_eino_plugin.EinoToolsVersionRequest,
    options?: T,
  ): Promise<flow_devops_nclc_eino_plugin.EinoToolsVersionResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/nclc/v1/plugin/tools/version');
    const method = 'GET';
    const params = {
      platform: _req['platform'],
      plugin_version: _req['plugin_version'],
      base: _req['base'],
    };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
