/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export interface ComponentMeta {
  /** component 跨空间的唯一标识。自定义组件可能是组件作者自己确定的。可读。 */
  type: string;
  /** 组件所属的分组，如 ChatModel, Prompt, Retriever 等。可读。 */
  group: string;
  /** 一个 Group 对应一个 interface name，决定是否可以进入特定的 slot。可读。 */
  interface_name: string;
  /** Group 决定了这个组件能否进入编排，还是只能作为 slot 配置。true 则可以进入编排，可以作为 passthrough 节点的上游 */
  is_composable: boolean;
  definition: Definition;
  /** 除了上面的 interface name 之外，这个组件还可以用作哪些 interface，从而可以进入对应的 Slot */
  assignable_to?: Array<string>;
}

export interface Definition {
  info: Info;
  /** 前端框架用的，服务端不感知内容 */
  meta?: string;
  slots?: Array<Slot>;
  /** 如果一个组件，只能作为配置进入 slot，此字段为空 */
  input?: TypeMeta;
  /** 如果一个组件，只能作为配置进入 slot，此字段为空 */
  output?: TypeMeta;
  /** 里面的字段都展开为基本类型，直接在页面上写。如果 Config 中某个字段是嵌套的 Object，那个 Object 也不应该是 Component，否则应该进入 slot，而不是 config */
  config?: TypeSchema;
}

export interface Doc {
  name: string;
  link: string;
}

export interface Info {
  name: string;
  icon?: string;
  description?: string;
  docs?: Array<Doc>;
}

export interface ListComponentsRequest {
  'FlowDevops-Agw-UserId'?: string;
  space_id: string;
  base?: base.Base;
}

export interface ListComponentsResponse {
  /** key 是 group */
  official_components?: Record<string, Array<ComponentMeta>>;
  /** key 是 group */
  custom_components?: Record<string, Array<ComponentMeta>>;
  base_resp?: base.BaseResp;
}

export interface Option {
  label: string;
  /** JSON 序列化的结果 */
  value: string;
}

export interface OptionEnum {
  ode?: string;
  options?: Array<Option>;
}

export interface Slot {
  /** 可读 */
  slot_name: string;
  /** 这个 slot 接受的类型，一般是个接口定义，可读 */
  interface_name: string;
  required?: boolean;
  /** 数组类型的 slot */
  multiple?: boolean;
}

export interface TypeMeta {
  /** input 或 output 的类型枚举，数字 */
  type_id: string;
  /** 对一个结构体的 schema 描述，可以是 input、output、config，或者里面的某个字段 */
  schema: TypeSchema;
  /** 可读 */
  type_name?: string;
}

export interface TypeSchema {
  /** 可读 */
  type: string;
  /** 当类型为数组时，有值，代表数组中元素的 schema */
  items?: TypeSchema;
  /** 当类型为 object 时，有值，key 为字段名称，value 为字段的 schema */
  properties?: Record<string, TypeSchema>;
  /** 当类型为 object 时，有值，数组元素为字段的 schema */
  property_list?: Array<TypeSchema>;
  name?: string;
  title?: string;
  description?: string;
  /** JSON 序列化的结果 */
  default_value?: string;
  hidden?: boolean;
  read_only?: boolean;
  required?: boolean;
  option_enum?: OptionEnum;
  /** 前端需要的定制信息，服务端不感知 */
  style_meta?: string;
}
/* eslint-enable */
