/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** the struct to define vector search index */
export interface SearchIndex {
  /** name of db, the underlying implement db */
  db_name: string;
  /** name of index, not the underlying implement index, might be empty when upsert */
  index?: string;
}

/** just to define the source of traffic */
export interface TrafficSource {
  app_id: string;
  bot_id: string;
  user_id: string;
}
/* eslint-enable */
