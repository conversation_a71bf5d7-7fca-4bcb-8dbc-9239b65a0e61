/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

/** the task status */
export enum AsyncTaskStatus {
  NotFinished = 0,
  Interrupted = 1,
  NoResults = 2,
  WithResults = 3,
}

/** reference to copilot_common.ConversationType in "
Make it consist with copilot_common.ConversationType to help coping */
export enum ConversationType {
  Unknown = 0,
  Single = 1,
  Group = 2,
}

export enum TimeCapsuleEventType {
  /** 开始总结事件 */
  StartSummary = 2,
}

export enum TimeCapsuleItemType {
  /** "原始对话" */
  Chat = 1,
  /** "总结后的话题" */
  Topic = 2,
  /** "精华记忆" */
  Essential = 3,
}

/** the async task result element, each represents a memory-item operation */
export interface AsyncResultElement {
  /** since the userid(connector_uid), appid(connector_id), botid are Implicitly included in the context, we do not include those
< the item type in memory */
  item_type: string;
  /** < the biz_id(id in biz) in memory */
  id: string;
  /** < the iid in memory, will be blank "" in case of deleted results. */
  iid: string;
}

/** the async task result */
export interface AsyncTaskInfo {
  task_name: string;
  task_type: string;
  task_status: AsyncTaskStatus;
  upsert_elements: Array<AsyncResultElement>;
  delete_elements: Array<AsyncResultElement>;
  enqueue_elements?: Array<AsyncResultElement>;
}

/** referecne to flow.bot.engine.BizInfo in */
export interface BizInfo {
  message_id?: Int64;
  conversation_id?: Int64;
  section_id?: Int64;
  conversation_type?: ConversationType;
}

/** reference to copilot_common.CopilotContent in "
Make it consist with copilot_common.CopilotContent to help coping */
export interface ChatContext {
  /** 1:  map<string, string>        scene_context // tts, search, wiki, wiki_link
short memory */
  message_context?: Array<Message>;
}

export interface CitationRankingRequest {
  bot_id?: string;
  connector_uid?: string;
  connector_id?: string;
  llm_answer: string;
  search_results: Array<SearchResultItem>;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface CitationRankingResponse {
  search_results: Array<SearchResultItem>;
  BaseResp?: base.BaseResp;
}

/** reference to copilot_common.FileInfo in "
Use cast directly to simplify the struct construction */
export interface FileInfo {
  name?: string;
  url?: string;
  uri?: string;
  md5?: string;
}

/** reference to copilot_common.ImageInfo in "
Use cast directly to simplify the struct construction */
export interface ImageInfo {
  name?: string;
  /** 本期使用这里，先不用uri */
  url?: string;
  uri?: string;
  md5?: string;
}

export interface ListItemsResultItem {
  iid?: string;
  text: string;
  event_ms: Int64;
  ext?: Record<string, string>;
  tags?: Array<string>;
  biz_id?: string;
  item_type?: TimeCapsuleItemType;
  update_ms?: Int64;
}

export interface LocationInfo {
  Longitude?: number;
  Latitude?: number;
  city?: string;
  country?: string;
  province?: string;
  district?: string;
  town?: string;
  country_code?: string;
}

/** reference to flow.bot.engine.Message in "
Make it consist with copilot.Message to help coping */
export interface Message {
  /** < 取值：system/user/assistant/tool/placeholder */
  role: string;
  content?: string;
  /** < 位置信息， 地理位置信息，端上授权才会传递 */
  location?: LocationInfo;
  /** < 上传的文件 */
  files?: Array<FileInfo>;
  /** < 上传的图片 */
  images?: Array<ImageInfo>;
  /** < 业务信息 */
  biz_info?: BizInfo;
  ext?: Record<string, string>;
}

export interface SearchItem {
  /** 搜索内容，可以是用户输入的原始query,也可以是需要进行查询的关键词 */
  query: string;
  /** 发起搜索时的位置信息 */
  location_info?: LocationInfo;
  /** 发起搜索时的时区信息 */
  time_zone?: string;
  /** 发起搜索时的语言信息 */
  language?: string;
}

export interface SearchResultItem {
  text: string;
  event_ms?: Int64;
  ext?: Record<string, string>;
  tags?: Array<string>;
  item_type?: string;
  id?: string;
}

export interface TimeCapsuleAddItemRequest {
  bot_id: string;
  connector_uid: string;
  connector_id: string;
  item_type: TimeCapsuleItemType;
  biz_id: string;
  text: string;
  event_ms: Int64;
  update_ms?: Int64;
  ext?: Record<string, string>;
  tags?: Array<string>;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface TimeCapsuleAddItemResponse {
  iid: string;
  BaseResp?: base.BaseResp;
}

export interface TimeCapsuleClearAllItemsRequest {
  bot_id: string;
  connector_uid?: string;
  connector_id: string;
  event_ms: Int64;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface TimeCapsuleClearAllItemsResponse {
  BaseResp?: base.BaseResp;
}

export interface TimeCapsuleClearItemsRequest {
  bot_id: string;
  connector_uid?: string;
  connector_id: string;
  event_ms: Int64;
  ext?: Record<string, string>;
  item_type?: TimeCapsuleItemType;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface TimeCapsuleClearItemsResponse {
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface TimeCapsuleDeleteItemsRequest {
  bot_id: string;
  connector_uid?: string;
  connector_id: string;
  item_type: TimeCapsuleItemType;
  iids?: Array<string>;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface TimeCapsuleDeleteItemsResponse {
  BaseResp?: base.BaseResp;
}

export interface TimeCapsuleListItemsRequest {
  bot_id: string;
  time_capsule_item_type: TimeCapsuleItemType;
  connector_uid?: string;
  connector_id?: string;
  start_event_ms?: Int64;
  end_event_ms?: Int64;
  offset?: number;
  limit?: number;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface TimeCapsuleListItemsResponse {
  list_items_result_list?: Array<ListItemsResultItem>;
  total_num?: number;
  BaseResp?: base.BaseResp;
}

export interface TimeCapsuleSearchItemsRequest {
  bot_id: string;
  connector_uid: string;
  connector_id: string;
  search_item: SearchItem;
  /** TimeCapsule召回的最大长度限制，由上游copilot计算之后传递进来 */
  max_length?: Int64;
  /** multi-agent模式下，当前引用的bot_id */
  ref_bot_id?: string;
  ext?: Record<string, string>;
  /** to specify the search strategy, empty to use default */
  search_strategy?: string;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface TimeCapsuleSearchItemsResponse {
  search_result_list?: Array<SearchResultItem>;
  wraped_text?: string;
  BaseResp?: base.BaseResp;
}

export interface TimeCapsuleSearchRequest {
  bot_id: string;
  connector_uid?: string;
  connector_id?: string;
  search_item?: SearchItem;
  /** TimeCapsule召回的最大长度限制，由上游copilot计算之后传递进来 */
  max_length?: Int64;
  /** 默认是false，当为true的时候，可以专门针对一些bot，不返回TimeCapsule召回结果。最开始主要是为了一方应用而设计 */
  skip_block?: boolean;
  /** refered to bot_platform/flow_bot_retriever/retriever.thrift */
  message_id?: Int64;
  conversation_id?: Int64;
  section_id?: Int64;
  chat_context?: ChatContext;
  /** multi-agent模式下，当前引用的bot_id */
  ref_bot_id?: string;
  /** 支持通用透传协议，可直接与最上游业务方和其他Hook接入方对接 */
  ext?: Record<string, string>;
  /** the strategy specified by chat_engine, the name is negotiated in advance */
  strategy_bundle?: string;
  x_api_forward?: string;
  Base?: base.Base;
}

export interface TimeCapsuleSearchResponse {
  search_result_list?: Array<SearchResultItem>;
  wraped_text?: string;
  version?: Int64;
  BaseResp?: base.BaseResp;
}

export interface TimeCapsuleUpdateItemRequest {
  iid: string;
  bot_id: string;
  connector_uid?: string;
  connector_id: string;
  item_type: TimeCapsuleItemType;
  biz_id: string;
  text: string;
  event_ms: Int64;
  update_ms?: Int64;
  ext?: Record<string, string>;
  tags?: Array<string>;
  'X-Api-Forward'?: string;
  Base?: base.Base;
}

export interface TimeCapsuleUpdateItemResponse {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
