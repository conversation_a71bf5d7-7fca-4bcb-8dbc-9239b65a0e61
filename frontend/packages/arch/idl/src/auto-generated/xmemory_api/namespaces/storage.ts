/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** the source of generatio, enumed to be expanded later */
export enum GenerateSource {
  /** original type */
  Original = 1,
  /** reflected by other items */
  Reflected = 2,
}

export enum Role {
  Human = 1,
  Assistant = 2,
}

/** the index data, that we store into the index storage */
export interface IndexItemMeta {
  /** as in ItemIndex */
  iid: string;
  /** as in ItemIndex */
  app_id: string;
  /** as in ItemIndex */
  bot_id: string;
  /** as in ItemIndex */
  item_type: string;
  /** as in ItemIndex */
  user_id: string;
  /** in ms, UnixMilli, to help merge and sort the index result */
  event_ms: Int64;
  /** the generated sorce to help merge and sort the index result */
  gen_src: GenerateSource;
}

/** the item interface */
export interface Item {
  /** the id for the memory-item, assigned by x-memory */
  iid?: string;
  /** the id in original bussiness, userd for updating */
  biz_id?: string;
  /** the app, stands for Cici, Doubao, etc */
  app_id: string;
  /** the id of the bot, for original */
  bot_id: string;
  /** the type of the item, use item_type to avoid type-key word */
  item_type: string;
  /** id of user, connectorUID in Coze's Context */
  user_id: string;
  /** time-stamp of the meomory-item, first occurance, in ms, UnixMilli */
  event_ms?: Int64;
  /** time-stamp of last update, in ms, UnixMilli */
  update_ms?: Int64;
  /** tags */
  tags?: Array<string>;
  /** the text representaion of the item, for Use-Convenience */
  text?: string;
  /** len(text), just easy the reading */
  text_len?: number;
  /** the extended information, for Expandability */
  ext?: Record<string, string>;
  /** the reflection source */
  reflect_src?: ReflectRelation;
  /** the generated source */
  gen_src?: GenerateSource;
  /** the reflection destinations */
  reflect_dst?: Array<ReflectRelation>;
}

/** the index that is can identify an item */
export interface ItemIndex {
  /** the iid */
  iid: string;
  /** the app, stands for Cici, Doubao, etc */
  app_id: string;
  /** the id of the bot, for original */
  bot_id: string;
  /** the type of the item, use item_type to avoid type-key word */
  item_type: string;
  /** id of user, connectorUID in Coze's Context */
  user_id: string;
}

/** the conditions to get an element */
export interface ItemMeta {
  /** the id for the memory-item, assigned by x-memory */
  iid?: string;
  /** the id in original bussiness, userd for updating */
  biz_id?: string;
  /** the app, stands for Cici, Doubao, etc */
  app_id: string;
  /** the id of the bot, for original */
  bot_id: string;
  /** the type of the item, use item_type to avoid type-key word */
  item_type: string;
  /** id of user, connectorUID in Coze's Context */
  user_id: string;
}

/** the struct to describe the reflect relations */
export interface ReflectRelation {
  /** the text reflection of the task */
  task: string;
  /** the type of reflection peer */
  peer_type: string;
  /** the id of the reflection peer */
  peer_iids: Array<string>;
}

/** the index search result element, with the index element and the score */
export interface ScoredSearchItem {
  /** the index part */
  index_item_meta: IndexItemMeta;
  /** the score part */
  score: number;
}
/* eslint-enable */
