/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_prompt_common from './flow_devops_prompt_common';

export type Int64 = string | number;

export interface CancelOptimizeExecutionRequest {
  task_id: Int64;
  space_id?: Int64;
  execution_id: Int64;
  base?: base.Base;
}

export interface CancelOptimizeExecutionResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface CreateOptimizeTaskRequest {
  /** 空间ID */
  space_id?: Int64;
  /** task名称 */
  display_name: string;
  /** task描述 */
  description?: string;
  /** 优化对象 */
  target?: flow_devops_prompt_common.OptimizeTarget;
  /** 列表可见 */
  visible?: boolean;
  base?: base.Base;
}

export interface CreateOptimizeTaskResponse {
  /** 创建成功返回PromptOpTask */
  task?: flow_devops_prompt_common.OptimizeTask;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface DeleteOptimizeExecutionRequest {
  task_id: Int64;
  space_id?: Int64;
  execution_id: Int64;
  base?: base.Base;
}

export interface DeleteOptimizeExecutionResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface DeleteOptimizeTaskRequest {
  task_id: Int64;
  space_id?: Int64;
  base?: base.Base;
}

export interface DeleteOptimizeTaskResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ExecOptimizeTaskRequest {
  task_id: Int64;
  space_id?: Int64;
  base?: base.Base;
}

export interface ExecOptimizeTaskResponse {
  execution?: flow_devops_prompt_common.OptimizeExecution;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetOptimizeExecutionDashboardRequest {
  execution_id: Int64;
  task_id: Int64;
  space_id?: Int64;
  base?: base.Base;
}

export interface GetOptimizeExecutionDashboardResponse {
  /** 包含1～3个优化结果 */
  result?: Array<flow_devops_prompt_common.OptimizeResult>;
  execution?: flow_devops_prompt_common.OptimizeExecution;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetOptimizeExecutionRequest {
  execution_id: Int64;
  task_id: Int64;
  space_id?: Int64;
  base?: base.Base;
}

export interface GetOptimizeExecutionResponse {
  execution?: flow_devops_prompt_common.OptimizeExecution;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface GetOptimizeTaskRequest {
  task_id: Int64;
  space_id?: Int64;
  base?: base.Base;
}

export interface GetOptimizeTaskResponse {
  /** 返回PromptOpTask */
  task?: flow_devops_prompt_common.OptimizeTask;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListOptimizeExecutionRequest {
  task_id: Int64;
  space_id?: Int64;
  page: number;
  page_size: number;
  base?: base.Base;
}

export interface ListOptimizeExecutionResponse {
  executions?: Array<flow_devops_prompt_common.OptimizeExecution>;
  total?: number;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ListOptimizeTaskRequest {
  space_id?: Int64;
  page: number;
  page_size: number;
  /** 名称 */
  name?: string;
  /** target对象ID */
  target_id?: string;
  /** target对象版本 */
  target_version?: string;
  /** target对象类型 */
  target_type?: flow_devops_prompt_common.OptimizeTargetType;
  /** 创建者ID */
  creator_id?: string;
  base?: base.Base;
}

export interface ListOptimizeTaskResponse {
  tasks?: Array<flow_devops_prompt_common.OptimizeTask>;
  total?: number;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface UpdateOptimizeTaskRequest {
  task_id: Int64;
  space_id?: Int64;
  name?: string;
  desc?: string;
  /** 优化对象, prompt仅支持修改version */
  target?: flow_devops_prompt_common.OptimizeTarget;
  base?: base.Base;
}

export interface UpdateOptimizeTaskResponse {
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}
/* eslint-enable */
