/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_prompt_common from './flow_devops_prompt_common';
import * as mcp from './mcp';

export type Int64 = string | number;

export enum AccountMode {
  /** 使用共享模型账号：限流也使用公共限流，不能保障稳定性 */
  SharedAccount = 1,
  /** 使用自己业务申请的独立模型账号：限流走独立账号的配置 */
  CustomAccount = 2,
}

export enum Model {
  GPT35Turbo16k = 1,
}

export enum ReplyType {
  /** 最终结果 */
  ReplyTypeFinalAnswer = 0,
}

export enum Role {
  RoleSystem = 0,
  RoleUser = 1,
  RoleAssistant = 2,
}

export enum StreamState {
  /** 非流式 */
  StreamStateNone = 0,
  /** 流式传输开始（首包） */
  StreamStateBegin = 1,
  /** 流式传输中 */
  StreamStateStreaming = 2,
  /** 流失传输结束（尾包） */
  StreamStateEnd = 3,
}

export enum UsageScenario {
  Default = 1,
  Evaluation = 2,
  /** Prompt as a Service调用 */
  PromptAsAService = 3,
  /** AI打标 */
  AIAnnotate = 4,
  /** 质量分 */
  AIScore = 5,
  /** 数据标签 */
  AITag = 6,
}

export interface CustomAccount {
  /** 模型名称 */
  model_name: string;
  /** 模型账号 */
  api_key: string;
  /** skyLark模型的账号需要额外透传ak/sk信息 */
  sky_lark_account_extra?: SkyLarkAccountExtra;
  /** bot_engine connector_id（仅seed模型需要传） */
  connector_id?: Int64;
}

export interface EvalPromptVersionRequest {
  /** Prompt key */
  prompt_key: string;
  /** 流量标识 */
  traffic: string;
  /** 用于fornax鉴权 */
  Authorization?: string;
  base?: base.Base;
}

export interface EvalPromptVersionResponse {
  /** 是否命中灰度 */
  hit?: boolean;
  /** 返回版本 */
  version?: string;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface ExecuteRequest {
  /** PromptKey与PromptID传递一个即可，推荐使用自己定义的PromptKey，可读性更高 */
  prompt_key?: string;
  prompt_id?: Int64;
  /** 该字段计划下线，请不要传值，不传值则使用最新发布版本 */
  version?: string;
  message?: flow_devops_prompt_common.Message;
  /** 上下文信息 */
  contexts?: Array<flow_devops_prompt_common.Message>;
  /** 变量 */
  variables?: Array<flow_devops_prompt_common.Variable>;
  /** 账号模式(默认使用共享账号，但共享账号不保障稳定性，推荐申请并使用自己的独立模型账号) */
  account_mode?: AccountMode;
  /** 独立模型账号列表，账号模式选择独立账号时，需要传递自己的独立账号信息 */
  custom_accounts?: Array<CustomAccount>;
  /** 是否单步调试 */
  single_step_debug?: boolean;
  /** 串联调试记录(function call单步调试) */
  debug_trace_key?: string;
  /** 使用场景 */
  usage_scenario?: UsageScenario;
  /** 流量标识 */
  traffic?: string;
  /** 请求额外参数 */
  request_extra?: RequestExtra;
  /** 自定义模型参数(传递该参数会覆盖模型参数) */
  custom_model_config?: flow_devops_prompt_common.ModelConfig;
  /** 发布标签 */
  release_label?: string;
  /** MCP 动态配置 */
  mcp_execute_config?: mcp.MCPExecuteConfig;
  /** 用于fornax鉴权 */
  token?: string;
  /** 用于识别老用户身份，待用户迁移后下线 */
  'Agw-Auth'?: string;
  /** 用于fornax鉴权 */
  Authorization?: string;
  base?: base.Base;
}

export interface ExecuteResponse {
  item?: flow_devops_prompt_common.ReplyItem;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface MPullPromptQuery {
  /** Prompt key */
  prompt_key: string;
  /** 版本 */
  version?: string;
  /** 发布label */
  release_label?: string;
}

export interface MPullPromptRequest {
  /** Prompt查询 */
  prompt_queries: Array<MPullPromptQuery>;
  /** 加密选项 */
  encrypt_option?: flow_devops_prompt_common.PromptEncryptOption;
  /** 用于fornax鉴权 */
  token?: string;
  /** 用于识别老用户身份，待用户迁移后下线 */
  'Agw-Auth'?: string;
  /** 用于fornax鉴权 */
  Authorization?: string;
  base?: base.Base;
}

export interface MPullPromptResponse {
  /** 返回Prompt列表 */
  prompts?: Array<flow_devops_prompt_common.Prompt>;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}

export interface RequestExtra {
  /** GPTOpenAPI平台extra参数 */
  gpt_openapi_extra?: string;
}

export interface SkyLarkAccountExtra {
  ak: string;
  sk: string;
}

export interface StreamingExecuteRequest {
  /** PromptKey与PromptID传递一个即可，推荐使用自己定义的PromptKey，可读性更高 */
  prompt_key?: string;
  prompt_id?: Int64;
  /** 该字段计划下线，请不要传值，不传值则使用最新发布版本 */
  version?: string;
  message?: flow_devops_prompt_common.Message;
  /** 上下文信息 */
  contexts?: Array<flow_devops_prompt_common.Message>;
  /** 变量 */
  variables?: Array<flow_devops_prompt_common.Variable>;
  /** 账号模式(默认使用共享账号，但共享账号不保障稳定性，推荐申请并使用自己的独立模型账号) */
  account_mode?: AccountMode;
  /** 独立模型账号列表，账号模式选择独立账号时，需要传递自己的独立账号信息 */
  custom_accounts?: Array<CustomAccount>;
  /** 是否单步调试 */
  single_step_debug?: boolean;
  /** 串联调试记录(function call单步调试) */
  debug_trace_key?: string;
  /** 使用场景 */
  usage_scenario?: UsageScenario;
  /** 流量标识 */
  traffic?: string;
  /** 请求额外参数 */
  request_extra?: RequestExtra;
  /** 自定义模型参数(传递该参数会覆盖模型参数) */
  custom_model_config?: flow_devops_prompt_common.ModelConfig;
  /** 发布标签 */
  release_label?: string;
  /** MCP 动态配置 */
  mcp_execute_config?: mcp.MCPExecuteConfig;
  /** 用于fornax鉴权 */
  token?: string;
  /** 用于识别老用户身份，待用户迁移后下线 */
  'Agw-Auth'?: string;
  /** 用于fornax鉴权 */
  Authorization?: string;
  base?: base.Base;
}

export interface StreamingExecuteResponse {
  item?: flow_devops_prompt_common.ReplyItem;
  code?: number;
  msg?: string;
  base_resp?: base.BaseResp;
}
/* eslint-enable */
