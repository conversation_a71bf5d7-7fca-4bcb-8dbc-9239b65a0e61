/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum NodeType {
  Undefined = 0,
  CustomNodeType = 1,
  Prompt = 2,
  Model = 3,
}

export enum OfficialSlotType {
  Undefined = 0,
  Prompt = 1,
}

export enum ValueKind {
  Undefined = 0,
  Bool = 1,
  Integer = 2,
  Double = 3,
  String = 4,
  Map = 10,
  Array = 11,
  Object = 12,
}

export interface FieldDescriptor {
  name: string;
  description?: string;
  typeDescriptor: TypeDescriptor;
  constraint?: ValueConstraint;
}

export interface Graph {
  id?: string;
  uid?: string;
  name?: string;
  /** 空间 ID */
  spaceID?: string;
  /** 应用 ID */
  appID?: string;
  /** 描述 */
  description?: string;
  /** 创建人 */
  createdBy?: string;
  /** 创建时间 */
  createdAt?: Int64;
  /** 更新人 */
  updatedBy?: string;
  /** 更新时间 */
  updatedAt?: Int64;
}

export interface NodeTemplate {
  name: string;
  type: NodeType;
  slotSchemas?: Array<FieldDescriptor>;
}

export interface SlotSet {
  id: string;
  graphID: string;
  /** slotSet 版本号 */
  version: string;
  /** 版本描述 */
  versionMessage?: string;
  /** TODO: 暂不包含 slot 相关字段 */
  createdBy?: string;
  createdAt?: string;
}

export interface TypeDescriptor {
  name?: string;
  kind?: ValueKind;
  fields?: Array<FieldDescriptor>;
  key?: TypeDescriptor;
  value?: TypeDescriptor;
}

export interface ValueConstraint {
  /** [1,10]: 多类型适用
works for string, array, map types */
  maxLength?: Int64;
  /** [11,20] integer 适用 */
  intGT?: Int64;
  intGTE?: Int64;
  intLT?: Int64;
  intLTE?: Int64;
  /** [21,30] double 适用 */
  doubleGT?: number;
  doubleGTE?: number;
  doubleLT?: number;
  doubleLTE?: number;
}
/* eslint-enable */
