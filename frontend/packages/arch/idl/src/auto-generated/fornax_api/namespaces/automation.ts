/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_task from './flow_devops_evaluation_task';

export type Int64 = string | number;

export enum ApplyStatus {
  Pending = 1,
  Approved = 2,
  Rejected = 3,
  Canceled = 4,
}

export enum BuiltinSpanFilterType {
  Undefined = 0,
  /** 业务含义(区别于 Trace Root)上的根节点 */
  BizRootSpan = 1,
  /** 模型 span */
  LLMSpan = 2,
  /** 非内置过滤 */
  CustomSpan = 3,
}

/** Schedule */
export enum ExecutePeriodic {
  Undefined = 0,
  Everyday = 1,
  Workdays = 2,
  Weekends = 3,
  Monday = 4,
  Tuesday = 5,
  Wednesday = 6,
  Thursday = 7,
  Friday = 8,
  Saturday = 9,
  Sunday = 10,
}

/** 比较算子 */
export enum FilterCmpOp {
  Undefined = 0,
  /** greater than */
  GT = 1,
  /** greater than or equal */
  GTE = 2,
  /** less than */
  LT = 3,
  /** less than or equal */
  LTE = 4,
  In = 5,
  NotIn = 6,
  /** equal */
  Eq = 7,
  /** not equal */
  NEq = 8,
  Like = 9,
  NotLike = 10,
  /** 有该 tag */
  Exists = 11,
  /** 没有该 tag */
  NotExists = 12,
}

/** 逻辑算子 */
export enum FilterLogicOp {
  Undefined = 0,
  Nop = 1,
  And = 2,
  Or = 3,
  Not = 4,
}

export enum ObjectType {
  Undefined = 0,
  CozeBot = 1,
  FornaxPSM = 2,
  Prompt = 3,
  Doubao = 4,
  FornaxAgent = 5,
}

export enum SecurityLevel {
  L4 = 4,
}

export enum Span2ColumnSourceType {
  PromptVariable = 1,
  SpanMeta = 2,
  SpanTag = 3,
}

export enum TaskStatusType {
  Undefined = 0,
  Unstarted = 1,
  Running = 2,
  Succeeded = 3,
  Failed = 4,
  Pending = 5,
  Stopped = 6,
}

/** Task */
export enum TaskType {
  Undefined = 0,
  /** Span 导入数据集 */
  SpanToDataset = 1,
  /** 在线评测 Span */
  SpanEval = 2,
  /** 批量执行自动化用例 */
  BatchExecAutoUseCase = 3,
  /** Deprecated: 统一使用 SpanToDataset
数据回流用于模型精调 */
  SpanToDatasetForModelSFT = 4,
}

export enum ValueKind {
  Undefined = 0,
  Bool = 1,
  Integer = 2,
  Double = 3,
  String = 4,
}

export interface ApplyTicket {
  id?: string;
  url?: string;
  /** 工单状态 */
  status?: ApplyStatus;
  /** 审批节点 */
  node?: string;
  /** 生效时间 */
  validateAt?: string;
  /** 失效时间 */
  invalidateAt?: string;
}

export interface BackfillStat {
  /** 已从观测检索到 span 数量 */
  retrievedSpanCount?: string;
  /** 已写入到数据集中 span 数量 */
  backfilledSpanCount?: string;
  /** 回填状态 */
  backfillStatus?: TaskStatusType;
}

export interface BatchExecAutoUseCaseStat {
  common?: StatusDetailCommon;
  /** 各用例执行情况 */
  evalCaseRun?: Array<EvalCaseRun>;
}

export interface ColumnInfo {
  /** 数据集列名 */
  name: string;
  /** 注释 */
  desc?: string;
}

/** ProcessorConfig */
export interface DatasetConfig {
  /** 为0时表示新建数据集 */
  datasetID: string;
  datasetName?: string;
  /** 默认将 span 的 input&output tag 写入 dataset 同名列中, 通过 extraColumns 定义其他列的回流 */
  extraColumns?: Array<Span2ColumnConfig>;
  /** omitDefaultColumns=true 时，不写入默认的 input、output 列 */
  omitDefaultColumns?: boolean;
  datasetDesc?: string;
}

/** Deprecated */
export interface DatasetForModelSFTConfig {
  datasetID?: string;
  datasetName?: string;
  customColumns?: Array<ColumnInfo>;
}

export interface EffectiveTime {
  /** unix timestamp */
  startAt?: string;
  /** unix timestamp */
  endAt?: string;
  /** 生效日，以开始生效时间为准 */
  effectiveDays?: ExecutePeriodic;
  /** 生效时间-开始， HH:mm:SS */
  effectivePeriodStart?: string;
  /** 生效时间-结束， HH:mm:SS */
  effectivePeriodEnd?: string;
  /** 生效时间-所在时区，±HH:MM。 */
  effectivePeriodTimeZone?: string;
}

export interface EvalCase {
  id: string;
  name?: string;
}

export interface EvalCaseRun {
  /** 用例 ID */
  caseID?: string;
  /** 评测任务 ID */
  taskID?: string;
  caseName?: string;
  status?: flow_devops_evaluation_task.TaskStatus;
}

export interface EvalCasesConfig {
  evalCases: Array<EvalCase>;
}

export interface EvalRuleConfig {
  evalRuleID: string;
  evalRuleName?: string;
}

/** Rule */
export interface Rule {
  /** 任务对象类型 */
  objectType?: ObjectType;
  /** 任务对象唯一键，对应 cozeBot 和 fornax psm 的 appUID */
  objectUID?: string;
  objectName?: string;
  /** 通过 filter 指定任务对象的其他信息。如 fornax psm 的 env+cluster */
  objectFilter?: SpanFilter;
  /** 采样配置 */
  sampler?: Sampler;
  /** 筛选条件 */
  spanFilter?: SpanFilter;
  /** Deprecated, typo, 换用 triggerTime(13) 字段 */
  tiggerTime?: TriggerTime;
  /** 生效时间窗口 */
  effectiveTime?: EffectiveTime;
  /** 任务对象在 fornax 平台上的 id */
  objectID?: Int64;
  /** 内置过滤器, 包含服务端定义的复杂过滤逻辑. 用于页面的"数据类型"字段过滤 */
  builtinFilter?: BuiltinSpanFilterType;
  /** 回流历史数据 */
  effectiveTimeFromPast?: EffectiveTime;
  /** 定时触发时效配置 */
  triggerTime?: TriggerTime;
  /** Processor Config
数据集 */
  dataset?: DatasetConfig;
  /** 在线评测规则 */
  evalRule?: EvalRuleConfig;
  /** 批量评测用例 */
  evalCases?: EvalCasesConfig;
  /** Deprecated 数据回流用于模型精调 */
  datasetForModelSFT?: DatasetForModelSFTConfig;
}

/** Sampler */
export interface Sampler {
  /** 采样率 */
  sampleRate?: number;
  /** 采样上限 */
  sampleSize?: string;
}

export interface Span2ColumnConfig {
  sourceType: Span2ColumnSourceType;
  /** 根据 sourceType 不同, 其值为 span 属性, prompt 变量的 key 等 */
  sourceField: string;
  /** 指定 sourceField 的 JSON 提取路径 */
  sourceFieldJSONPath?: string;
  /** dataset column 名 */
  datasetColumn: string;
}

export interface SpanEvalStat {
  common?: StatusDetailCommon;
  /** 评测规则 ID */
  ruleID?: string;
  /** 评测任务 ID */
  taskID?: string;
  caseID?: Int64;
  /** 符合 filter 的 span 条数 */
  matchedSpan?: string;
  /** 采样器命中的 span 条数 */
  sampledSpan?: string;
  firstWrittenAt?: string;
  lastWrittenAt?: string;
  /** 等待评测 */
  queuing?: string;
  /** 评测中 */
  evaluating?: string;
  /** 评测完成 */
  evaluated?: string;
  /** 评测失败 */
  evaluateFailed?: string;
}

/** Span 过滤器 */
export interface SpanFilter {
  op: FilterLogicOp;
  tagFilters?: Array<TagFilter>;
  spanFilters?: Array<SpanFilter>;
}

export interface SpanToDatasetStat {
  common?: StatusDetailCommon;
  /** 数据集 ID */
  datasetID?: string;
  /** 符合 filter 的 span 条数 */
  matchedSpan?: string;
  /** 采样器命中的 span 条数 */
  sampledSpan?: string;
  /** 采样上限 */
  sampleLimit?: string;
  /** 首条写入时间 */
  firstWrittenAt?: string;
  /** 最近写入时间 */
  lastWrittenAt?: string;
  /** 写入数据集的 span 条数 */
  writtenSpan?: string;
  /** 数据集已写满 */
  hitsDatasetLimit?: boolean;
}

/** Task Status Detail */
export interface StatusDetailCommon {
  /** task 终止执行的原因 */
  pauseReason?: string;
}

/** 字段过滤器 */
export interface TagFilter {
  tag: string;
  op: FilterCmpOp;
  valueKind: ValueKind;
  /** JSON 编码的值，类型与 valueKind 一致。 当 op 为 In, NotIn 等集合相关算子值时，类型为 valueKind 对应的数组。 */
  value?: string;
}

/** 字段过滤器选项 */
export interface TagFilterOption {
  tag?: string;
  description?: string;
  isRequired?: boolean;
  type?: ValueKind;
  /** 允许的比较运算 */
  operators?: Array<FilterCmpOp>;
  /** 选项列表 */
  options?: Array<string>;
  /** 是否允许自定义选项 */
  allowCustomOption?: boolean;
  /** 默认单位 */
  defaultUnit?: string;
}

/** Task */
export interface Task {
  /** 任务 id */
  id?: string;
  /** 名称 */
  name?: string;
  /** 所在空间 */
  spaceID?: string;
  /** 类型 */
  type?: TaskType;
  /** 状态 */
  status?: TaskStatusType;
  /** 描述 */
  description?: string;
  rule?: Rule;
  /** 是否可删除 */
  deletable?: boolean;
  /** 是否可编辑 */
  editable?: boolean;
  /** 密级 */
  securityLevel?: SecurityLevel;
  /** 审批工单信息 */
  ticket?: ApplyTicket;
  /** Status detail
数据集导入状态详情 */
  spanToDatatasetStat?: SpanToDatasetStat;
  /** 在线评测状态详情 */
  spanEvalStat?: SpanEvalStat;
  /** 批量评测状态详情 */
  batchExecAutoUseCaseStat?: BatchExecAutoUseCaseStat;
  /** 回填历史数据详情 */
  backfillStat?: BackfillStat;
  /** 创建者 */
  createdBy?: string;
  /** 更新者 */
  updatedBy?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
}

export interface TriggerTime {
  execAt?: string;
  /** 按天重复 */
  repeatDays?: ExecutePeriodic;
}
/* eslint-enable */
