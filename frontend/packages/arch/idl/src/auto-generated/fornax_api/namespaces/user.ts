/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as auth from './auth';

export type Int64 = string | number;

export interface UserInfo {
  ssoUserName?: string;
  userID?: string;
  email?: string;
  tenant?: auth.TenantType;
}

/** UserInfoDetail 用户详细信息，包含姓名、头像等 */
export interface UserInfoDetail {
  /** 姓名 */
  name?: string;
  /** 英文名称 */
  en_name?: string;
  /** 用户头像url */
  avatar_url?: string;
  /** 72 * 72 头像 */
  avatar_thumb?: string;
  /** 用户应用内唯一标识 */
  open_id?: string;
  /** 用户应用开发商内唯一标识 */
  union_id?: string;
  /** 企业标识 */
  tenant_key?: string;
  /** 用户在租户内的唯一标识（目前实际返给前端时都转成了fornax UserID） */
  user_id?: string;
  /** 用户邮箱 */
  email?: string;
  /** 租户 */
  tenant?: auth.TenantType;
  /** 飞书UserID */
  ext_user_id?: string;
  /** sso_user_name，来自DB，如果是懂车帝租户，会带有__dcar后缀 */
  sso_user_name?: string;
}
/* eslint-enable */
