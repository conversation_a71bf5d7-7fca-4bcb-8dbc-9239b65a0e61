/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as auth from './auth';

export type Int64 = string | number;

export enum GrayReleaseStrategy {
  /** 不开启灰度 */
  None = 0,
  /** 实例灰度 */
  InstanceGrayRelease = 1,
}

/** 空间角色类型 */
export enum SpaceRoleType {
  Undefined = 0,
  /** 负责人 */
  Owner = 1,
  /** 开发者 */
  Developer = 2,
  /** 测试人员 */
  Tester = 3,
}

/** 空间类型 */
export enum SpaceType {
  Undefined = 0,
  Personal = 1,
  Team = 2,
  /** 官方空间 */
  Official = 3,
}

export interface ByteTreeNode {
  id?: string;
  name?: string;
  i18nName?: string;
  path?: string;
  i18nPath?: string;
  levelID?: Int64;
  isLeaf?: boolean;
  type?: string;
}

export interface CozeBotFeatureConfig {
  enabled: boolean;
  botIDAllowList?: Array<Int64>;
}

/** 空间配置 */
export interface FeatureConfig {
  /** 开启特性的空间ID */
  EnabledSpaceIDList?: Array<Int64>;
  /** 是否全量开启 */
  EnableAll?: boolean;
}

export interface ReleaseApprovalConfig {
  /** 是否开启审核 */
  enable?: boolean;
  /** 灰度策略 */
  gray_release_strategy?: GrayReleaseStrategy;
}

/** 空间 */
export interface Space {
  /** 空间ID */
  id?: Int64;
  /** 空间名称 */
  name?: string;
  /** 空间描述 */
  description?: string;
  /** 空间类型 */
  space_type?: SpaceType;
  /** 空间创建人 */
  creator?: string;
  /** 创建时间 */
  create_tsms?: Int64;
  /** 更新时间 */
  update_tsms?: Int64;
  /** 发布审核配置 */
  release_approval_config?: ReleaseApprovalConfig;
  /** 空间来源 */
  space_origin?: string;
  /** 服务树节点ID */
  tree_node_id?: string;
  /** 具体配置内容 */
  trace_config?: TraceConfig;
  /** 空间下的服务账号列表 */
  accounts?: Array<auth.ServiceAccount>;
}

/** 空间成员 */
export interface SpaceMember {
  /** 空间ID */
  space_id?: Int64;
  /** 成员 */
  member?: auth.AuthPrincipal;
  /** 空间角色类型 */
  space_role_type?: SpaceRoleType;
}

/** 观测配置 */
export interface TraceConfig {
  /** 是否加密trace 默认false */
  trace_encrypt?: boolean;
  /** ttl天数 默认7，目前合法值有7/30/90 */
  ttl?: number;
}
/* eslint-enable */
