/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum Action {
  Undefined = 0,
  Authorize = 1,
  Revoke = 2,
}

/** 主体类型 */
export enum AuthPrincipalType {
  Undefined = 0,
  /** 用户 */
  User = 1,
  /** 部门 */
  Department = 2,
  /** Coze标识 */
  CozeIdentifier = 3,
}

export enum Connector {
  undefined = 0,
  feishu = 1,
  coze = 2,
  gitlab = 3,
}

/** 资源类型 */
export enum ResourceType {
  Undefined = 0,
  Space = 1,
  Prompt = 2,
  Application = 3,
  Evaluation = 4,
  Trace = 5,
  Agent = 6,
}

export enum TenantType {
  /** 字节 */
  ByteDance = 0,
  /** 懂车帝 */
  Dcar = 1,
}

/** 操作信息 */
export interface AuthAction {
  /** 唯一标识 */
  unique_key?: string;
  /** 操作展示名称 */
  name?: string;
  /** 实体类型，世纪不绑定实体，仅记录操作对象 */
  entity_type?: string;
}

/** Coze标识 */
export interface AuthCozeIdentifier {
  /** 身份票据 */
  identity_ticket?: string;
}

/** 鉴权部门 */
export interface AuthDepartment {
  /** 部门ID */
  department_id?: string;
}

/** 鉴权资源，客体 */
export interface AuthEntity {
  /** 实体唯一ID */
  id?: string;
  /** 实体类型 */
  entity_type?: string;
  /** 空间ID */
  space_id?: string;
  /** 实体owner用户ID */
  owner_user_id?: string;
}

export interface AuthorizationRecord {
  connector?: Connector;
  action?: Action;
  scopes?: Array<string>;
  created_at?: Int64;
  is_expired?: boolean;
}

/** 鉴权主体 */
export interface AuthPrincipal {
  /** 主体类型 */
  auth_principal_type?: AuthPrincipalType;
  /** 鉴权用户 */
  auth_user?: AuthUser;
  /** 鉴权部门 */
  auth_department?: AuthDepartment;
  /** Coze标识 */
  auth_coze_identifier?: AuthCozeIdentifier;
}

/** 角色信息 */
export interface AuthRole {
  /** 唯一标识 */
  unique_key?: string;
  /** 角色展示名称 */
  name?: string;
  /** 关联的Action列表 */
  actions?: Array<AuthAction>;
  /** 实体类型 */
  entity_type?: string;
}

/** 鉴权用户 */
export interface AuthUser {
  /** 邮箱前缀，与FornaxUserID传一个即可 */
  sso_username?: string;
  /** Fornax用户ID */
  fornax_user_id?: string;
  /** 租户类型 */
  tenant?: TenantType;
}

export interface OAuthConfig {
  connector: Connector;
  clientID?: string;
  redirectURI: string;
  authorizeURI?: string;
}

export interface ServiceAccount {
  id: Int64;
  defaultSpaceID: Int64;
  accessKey: string;
  secretKey?: string;
  secretKeyCipher?: string;
}

/** 主体+客体+权限点，鉴权结果 */
export interface SubjectActionObjectAuthRes {
  /** 主体+客体+权限点 鉴权对 */
  subject_action_objects?: SubjectActionObjects;
  /** 是否允许 */
  is_allowed?: boolean;
}

/** 主体+客体+权限点，鉴权组合信息 */
export interface SubjectActionObjects {
  /** 主体，鉴权时通常为用户 */
  subject?: AuthPrincipal;
  /** 权限唯一标识 */
  action?: string;
  /** 客体列表，默认按照或的逻辑处理 */
  objects?: Array<AuthEntity>;
}

/** 主体+客体+角色 */
export interface SubjectRoleObject {
  /** 主体，授权时可以时用户或部门 */
  subject?: AuthPrincipal;
  /** 角色信息 */
  role?: AuthRole;
  /** 客体 */
  object?: AuthEntity;
}

export interface UploadToken {
  access_key_id: string;
  secret_access_key: string;
  session_token: string;
  expired_time: string;
  current_time: string;
}
/* eslint-enable */
