/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ResourceType {
  Undefined = 0,
  Space = 1,
  Prompt = 2,
  Application = 3,
  Evaluation = 4,
  Trace = 5,
  Agent = 6,
}

/** 密级标签 */
export enum SecurityLevel {
  Undefined = 0,
  L1 = 1,
  L2 = 2,
  L3 = 3,
  L4 = 4,
}

export interface Resource {
  resourceType?: ResourceType;
  resourceID?: string;
  spaceID?: Int64;
  securityLevel?: SecurityLevel;
  ownerIDs?: Array<string>;
}

export interface ResourceIdentifier {
  resourceType?: ResourceType;
  resourceID?: string;
}
/* eslint-enable */
