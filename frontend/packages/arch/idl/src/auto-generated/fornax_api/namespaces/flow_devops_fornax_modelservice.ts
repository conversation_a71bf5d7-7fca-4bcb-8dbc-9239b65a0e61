/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as model from './model';
import * as flow_devops_evaluation_dataset from './flow_devops_evaluation_dataset';

export type Int64 = string | number;

export interface AccountFilter {
  usageScenario?: model.UsageScenario;
  region?: model.Region;
}

export interface CheckUserHasDeployPermRequest {
  /** 目前只支持merlin */
  provider: model.Provider;
  byteTreeNodeID?: string;
  /** provider=merlin时必传 */
  'x-jwt-token'?: string;
}

export interface CheckUserHasDeployPermResponse {
  hasPerm?: boolean;
  /** 用户无权限时展示详情 */
  detail?: string;
  /** 无权限时展示申请工单链接 */
  applyTicketURL?: string;
}

export interface CloneTrainingDatasetReq {
  datasetID?: string;
  name?: string;
  desc?: string;
  /** 空间ID */
  space_id?: string;
}

export interface CloneTrainingDatasetResp {
  datasetID?: string;
}

export interface CreateProviderAccountRequest {
  spaceID: string;
  name: string;
  provider?: model.Provider;
  /** [20, 30) provider 账号 */
  maasAccount?: model.MaasAuth;
  /** 用于 JwtToken 鉴权 */
  Authorization?: string;
}

export interface CreateProviderAccountResponse {
  accountID?: Int64;
}

export interface CreateSftTaskRequest {
  spaceID?: string;
  accountID?: string;
  task?: model.SftTask;
  /** 创建merlin任务时必传 */
  jwtToken?: string;
}

export interface CreateSftTaskResponse {
  taskID?: string;
  invalidDatasets?: Array<model.InvalidDataSet>;
}

export interface CreateTrainingDataImportTaskReq {
  datasetID?: string;
  fileType?: model.TrainingDataFileType;
  mode?: model.DataImportMode;
  dataSource?: model.DataSource;
  /** 空间ID */
  space_id?: string;
}

export interface CreateTrainingDataImportTaskResp {
  taskID?: string;
}

export interface CreateTrainingDatasetReq {
  name?: string;
  type?: model.TrainingDatasetType;
  desc?: string;
  /** 空间ID */
  spaceID?: string;
}

export interface CreateTrainingDatasetResp {
  datasetID?: string;
}

export interface DeleteModelAndAccountRequest {
  modelID?: Int64;
}

export interface DeleteModelAndAccountResponse {}

export interface DeleteProviderAccountRequest {
  accountID: string;
}

export interface DeleteProviderAccountResponse {}

export interface DeleteSftTaskRequest {
  spaceID?: string;
  accountID?: string;
  taskID?: Int64;
  /** 用于 JwtToken 鉴权 */
  Authorization?: string;
}

export interface DeleteSftTaskResponse {}

export interface DeleteTrainingDataRowsReq {
  dataset_id?: string;
  rowIDs?: Array<string>;
  /** 空间ID */
  space_id?: string;
}

export interface DeleteTrainingDataRowsResp {
  rowIDs?: Array<string>;
}

export interface DeleteTrainingDatasetReq {
  dataset_id?: string;
  /** 空间ID */
  space_id?: string;
}

export interface DeleteTrainingDatasetResp {
  datasetID?: string;
}

export interface ExportSftTaskOutputToProviderRequest {
  /** 必填，本期只支持merlin */
  provider?: model.Provider;
  /** 空间ID，必填用于权限校验 */
  spaceID?: string;
  /** 任务ID，必填 */
  taskID?: string;
  /** 任务产出的名称 */
  taskOutputName?: string;
  /** provider=merlin时必传，必须传未过期的用户的token */
  jwtToken?: string;
}

export interface ExportSftTaskOutputToProviderResponse {
  /** 任务产出在导出后的属性，此接口已改为异步，这个属性已弃用 */
  outputAfterExport?: model.SftTaskOutput;
}

export interface GetAccountRelatedResourcesRequest {
  accountID: string;
}

export interface GetAccountRelatedResourcesResponse {
  resources?: model.ProviderAccountRelatedResources;
}

export interface GetAccountsRequest {
  accountIDs?: Array<Int64>;
  spaceID?: Int64;
}

export interface GetAccountsResponse {
  accounts?: Array<model.Account>;
}

export interface GetAvailableQuotaByByteTreeNodeRequest {
  /** 服务树节点ID */
  byte_tree_node_id: string;
  /** 单实例资源配置 */
  resourceOption: model.InstanceResourceOption;
  'x-jwt-token'?: string;
}

export interface GetAvailableQuotaByByteTreeNodeResponse {
  quotas?: Partial<Record<model.IDC, Array<model.DeployQuota>>>;
}

export interface GetByteTreeParentNodeByPSMRequest {
  psm: string;
  'x-jwt-token': string;
}

export interface GetByteTreeParentNodeByPSMResponse {
  node?: model.ByteTreeNode;
  createdInMerlin?: boolean;
}

export interface GetInstanceSpecOptionsRequest {}

export interface GetInstanceSpecOptionsResponse {
  instanceResourceOpts?: Array<model.InstanceResourceOption>;
}

export interface GetMaaSRegionConfigsRequest {}

export interface GetMaaSRegionConfigsResponse {
  configs?: Array<model.MaaSRegionConfig>;
}

export interface GetMemoryEstimationRequest {
  spaceID?: string;
  task: model.SftTask;
}

export interface GetMemoryEstimationResponse {
  isMemoryInsufficient: boolean;
  requiredMemoryPerGPU?: string;
  trueMemoryPerGPU?: string;
}

export interface GetModelRequest {
  spaceID?: Int64;
  modelID?: Int64;
}

export interface GetModelResponse {
  model?: model.Model;
}

export interface GetProviderAccountMaskedDataRequest {
  accountID: string;
  provider: model.Provider;
  accountType: model.ProviderAccountType;
}

export interface GetProviderAccountMaskedDataResponse {
  maskedData?: model.ProviderAccount;
}

export interface GetSftTaskCustomModelRequest {
  spaceID?: string;
  accountID?: string;
  customModelID?: string;
  /** 只支持火山方舟 */
  providerType?: model.Provider;
}

export interface GetSftTaskCustomModelResponse {
  customModel?: model.SftTaskCustomModel;
}

export interface GetSftTaskFoundationModelConfigRequest {
  spaceID?: string;
  accountID?: string;
  /** Provider为merlin时传空 */
  modelVersionConfigID?: string;
  providerType?: model.Provider;
  /** Provider为merlin时传vendor/model_name */
  foundationModelName?: string;
  /** 训练类型 */
  trainingMethod?: model.SftTaskTrainingMethod;
}

export interface GetSftTaskFoundationModelConfigResponse {
  hyperParams?: Array<model.SftTaskHyperParam>;
  presetDatasets?: Array<model.SftTaskPresetDataset>;
  hyperParamsCategories?: Array<model.SftTaskHyperParamCategory>;
}

export interface GetSFTTaskOutputAbilityRequest {
  spaceID: string;
  taskID: string;
}

export interface GetSFTTaskOutputAbilityResponse {
  ability?: model.Ability;
}

export interface GetSftTaskProgressRequest {
  /** 必填，暂时只支持merlin */
  provider?: model.Provider;
  /** 空间ID，必填用于权限校验 */
  spaceID?: string;
  /** 任务ID，必填 */
  taskID?: string;
}

export interface GetSftTaskProgressResponse {
  progress?: string;
}

export interface GetSftTaskRequest {
  spaceID?: string;
  accountID?: string;
  taskID?: string;
  /** 用于JwtToken鉴权，这种情况下spaceID和accountID在鉴权后获得 */
  Authorization?: string;
}

export interface GetSftTaskResponse {
  task?: model.SftTask;
}

export interface GetTrainingDataImportTaskReq {
  task_id?: string;
  space_id?: string;
}

export interface GetTrainingDataImportTaskResp {
  task?: model.TrainingDataImportTask;
}

export interface GetTrainingFileUploadMaterialReq {
  fileName?: string;
  opType?: model.TrainingFileOpType;
  /** 空间ID */
  space_id?: string;
}

export interface GetTrainingFileUploadMaterialResp {
  url?: string;
  signature?: string;
}

export interface GetUserProviderAuthStatusRequest {
  spaceID?: string;
  provider?: model.Provider;
}

export interface GetUserProviderAuthStatusResponse {
  /** 是否已授权，当剩余时长小于一周时，会被置为未授权，此时需要用户再次授权 */
  isAuthorized?: boolean;
  /** 授权剩余时长, 单位 s */
  expiresIn?: Int64;
}

export interface InsertTrainingDataRowsReq {
  dataset_id?: string;
  row_groups?: Array<flow_devops_evaluation_dataset.RowGroup>;
  /** 空间ID */
  space_id?: string;
}

export interface InsertTrainingDataRowsResp {
  rowIDs?: Array<string>;
}

export interface ListModelAndAccountRequest {
  spaceID?: Int64;
  isPublicModel?: boolean;
  /** 根据模型状态进行过滤, 默认返回所有状态的模型 */
  modelStatuses?: Array<model.ModelStatus>;
}

export interface ListModelAndAccountResponse {
  modelAccounts?: Array<ModelAccount>;
}

export interface ListModelRequest {
  spaceID?: Int64;
  /** 根据模型状态进行过滤, 默认返回所有状态的模型 */
  modelStatuses?: Array<model.ModelStatus>;
}

export interface ListModelResponse {
  models?: Array<model.Model>;
}

export interface ListProviderAccountsBySpaceRequest {
  spaceID: string;
}

export interface ListProviderAccountsBySpaceResponse {
  /** 账号列表 notice: 此处不返回 secretKey */
  accounts?: Array<model.ProviderAccount>;
  hasOperationPerm?: boolean;
}

export interface ListSftTaskCustomModelsRequest {
  spaceID?: string;
  accountID?: string;
  /** 支持火山方舟/merlin */
  providerType?: model.Provider;
  nameKeyword?: string;
  /** providerType=merlin时必传 */
  userJwtToken?: string;
  pageNum?: number;
  pageSize?: number;
}

export interface ListSftTaskCustomModelsResponse {
  models?: Array<model.SftTaskCustomModel>;
  hasMore?: boolean;
}

export interface ListSftTaskFoundationModelsRequest {
  spaceID?: string;
  accountID?: string;
  providerType?: model.Provider;
  nameKeyword?: string;
  pageNum?: number;
  pageSize?: number;
}

export interface ListSftTaskFoundationModelsResponse {
  models?: Array<model.SftTaskFoundationModel>;
  hasMore?: boolean;
}

export interface ListSftTaskFoundationModelVersionsRequest {
  spaceID?: string;
  accountID?: string;
  modelName?: string;
  trainingType?: model.SftTaskTrainingType;
  trainingMethod?: model.SftTaskTrainingMethod;
  /** 只支持火山方舟 */
  providerType?: model.Provider;
  pageNum?: number;
  pageSize?: number;
}

export interface ListSftTaskFoundationModelVersionsResponse {
  models?: Array<model.SftTaskFoundationModel>;
  hasMore?: boolean;
}

export interface ListSftTaskResourceRequest {
  /** 必填，本期只支持merlin */
  provider?: model.Provider;
  /** 空间ID，必填用于权限校验 */
  spaceID?: string;
  /** 用于 JwtToken 鉴权 */
  Authorization?: string;
}

export interface ListSftTaskResourceResponse {
  merlinRscClusters?: Array<model.MerlinResourceCluster>;
}

export interface ListSftTasksRequest {
  spaceID?: string;
  accountID?: string;
  creator?: string;
  taskNameKeyWord?: string;
  taskID?: string;
  /** 暂时只支持Completed */
  status?: model.SftTaskStatusPhase;
  /** 非必填，支持maas和merlin */
  provider?: model.Provider;
  pageNum?: number;
  pageSize?: number;
  /** 用于JwtToken鉴权 */
  Authorization?: string;
}

export interface ListSftTasksResponse {
  tasks?: Array<model.SftTask>;
  hasMore?: boolean;
}

export interface ListTrainingDataImportTasksReq {
  datasetID?: string;
  taskID?: string;
  withErrLog?: boolean;
  space_id?: string;
  pageSize?: string;
  nextToken?: string;
}

export interface ListTrainingDataImportTasksResp {
  tasks?: Array<model.TrainingDataImportTask>;
  hasMore?: boolean;
  nextToken?: string;
}

export interface ModelAccount {
  model?: model.Model;
  accounts?: Array<model.Account>;
}

export interface ModelContextRange {
  /** 上限，不传代表不设限 */
  UpperBound?: number;
  /** 下限，不传代表不设限 */
  LowerBound?: number;
}

export interface ModelFilter {
  /** 模型tag过滤项，value中list内部各个元素在过滤时是or关系，各个key之间在过滤时是and关系 */
  modelFilterTags?: Partial<Record<model.ModelFilterKey, Array<string>>>;
  /** 模型状态 */
  modelStatuses?: Array<model.ModelStatus>;
  /** 模型支持的上下文长度的范围 */
  modelContextRange?: ModelContextRange;
  /** 模型厂商 */
  modelVendors?: Array<string>;
  /** 名称关键字 */
  name?: string;
  /** coze空间id */
  cozeSpaceID?: Int64;
}

export interface OApiGetSftTaskRequest {
  spaceID?: string;
  taskID?: string;
  Authorization?: string;
}

export interface OApiGetSftTaskResponse {
  task?: model.SftTask;
}

export interface OApiReportSftTaskEventRequest {
  spaceID?: string;
  taskID?: string;
  Authorization?: string;
  eventType?: model.SftTaskRunEventType;
  msg?: string;
  artifact?: model.SftTaskOutput;
  urlInfo?: model.SftTaskProviderURLInfo;
  progress?: model.SftTaskProgress;
  code?: model.SftTaskErrCode;
}

export interface OApiReportSftTaskEventResponse {}

export interface OApiUpdateModelStatusRequest {
  spaceID?: Int64;
  modelID?: Int64;
  Authorization?: string;
  /** SSO用户名 */
  ssoUsername?: string;
  status?: model.ModelStatus;
}

export interface OApiUpdateModelStatusResponse {}

export interface OApiUpsertModelAndAccountRequest {
  modelAccount?: ModelAccount;
  Authorization?: string;
  /** SSO用户名 */
  ssoUsername?: string;
}

export interface OApiUpsertModelAndAccountResponse {
  /** 修改后的model和acccount信息 */
  modelAccount?: ModelAccount;
}

export interface SaaSGetModelFilterParamsRequest {}

export interface SaaSGetModelFilterParamsResponse {
  modelFilterTags?: Partial<Record<model.ModelFilterKey, Array<string>>>;
  modelContextRange?: ModelContextRange;
  modelVendors?: Array<string>;
}

export interface SaaSListModelRequest {
  cursorID?: string;
  limit?: number;
  /** 筛选项 */
  Filter?: ModelFilter;
  /** 因为coze暂时给不了rpc接口，所以后端需要拿到cookie去请求coze的前端接口 */
  cookie?: string;
}

export interface SaaSListModelResponse {
  models?: Array<model.Model>;
  cursorID?: string;
  hasMore?: boolean;
}

export interface SearchByteTreeNodesRequest {
  name?: string;
  /** 英文搜索 */
  i18nSearch?: string;
  /** 仅搜索叶子节点 */
  isLeaf?: boolean;
  /** 父节点ID */
  parentID?: Int64;
  /** 搜索父节点下满足条件的子孙节点 */
  inherit?: boolean;
  /** 根据节点包含的 resource provider 过滤，仅叶节点有 provider 信息 */
  resourceProvider?: Array<model.ByteTreeNodeResourceProvider>;
  'x-jwt-token'?: string;
}

export interface SearchByteTreeNodesResponse {
  nodes?: Array<model.ByteTreeNode>;
}

export interface SyncModelStatusRequest {
  spaceID: Int64;
  modelID: Int64;
}

export interface SyncModelStatusResponse {}

export interface SyncTrainingDatasetToVolcanoTOSReq {
  dataset_id?: string;
  accountID?: string;
  /** 空间ID */
  space_id?: string;
}

export interface SyncTrainingDatasetToVolcanoTOSResp {
  datasetID?: string;
  file?: model.TOSFile;
  isCached?: boolean;
}

export interface TerminateSftTaskRequest {
  spaceID?: string;
  accountID?: string;
  taskID?: string;
  /** 用于 JwtToken 鉴权 */
  Authorization?: string;
}

export interface TerminateSftTaskResponse {}

export interface UpdateModelStatusRequest {
  spaceID?: Int64;
  modelID?: Int64;
  status?: model.ModelStatus;
}

export interface UpdateModelStatusResponse {}

export interface UpdateProviderAccountRequest {
  spaceID: string;
  accountID: string;
  provider?: model.Provider;
  name?: string;
  /** [20, 30) provider 账号 */
  maasAccount?: model.MaasAuth;
  /** 用于 JwtToken 鉴权 */
  Authorization?: string;
}

export interface UpdateProviderAccountResponse {}

export interface UpdateTrainingDataRowsReq {
  dataset_id?: string;
  row_group?: flow_devops_evaluation_dataset.RowGroup;
  /** 空间ID */
  space_id?: string;
}

export interface UpdateTrainingDataRowsResp {
  rowID?: string;
}

export interface UpsertModelAndAccountRequest {
  modelAccount?: ModelAccount;
  /** 从精调任务新创建 merlin 模型时必传，用于调用 Bernard 服务 */
  'x-jwt-token'?: string;
}

export interface UpsertModelAndAccountResponse {
  /** 修改后的model和acccount信息 */
  modelAccount?: ModelAccount;
}

export interface ValidateProviderAccountRequest {
  spaceID: string;
  isUpdate: boolean;
  provider?: model.Provider;
  maasAccount?: model.MaasAuth;
  /** 用于 JwtToken 鉴权 */
  Authorization?: string;
}

export interface ValidateProviderAccountResponse {
  isValidate?: boolean;
  maasValidate?: model.MaaSAccountValidate;
}
/* eslint-enable */
