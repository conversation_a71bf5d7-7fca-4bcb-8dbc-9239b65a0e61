/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

/** 打标目标对象类型 */
export enum TagBindTargetObjectType {
  /** 用例 */
  Case = 1,
  /** 任务，用例的执行记录 */
  Task = 2,
  /** 数据集 */
  Dataset = 3,
  /** 数据集/对话组 */
  DatasetRowGroup = 4,
  /** 规则组 */
  RuleGroup = 5,
  /** 规则 */
  Rule = 6,
  /** 评测报告/对话组 */
  ReportRowGroup = 7,
  /** Prompt */
  Prompt = 8,
  /** 人工标注项 */
  ManualAnnotationItem = 9,
}

/** 标签层级 */
export enum TagLevel {
  Primary = 1,
  Secondary = 2,
}

export enum TagStatus {
  Valid = 1,
  InValid = 2,
}

export interface CreateTagRequest {
  space_id: Int64;
  UserID?: string;
  /** tag支持的打标对象类型,指定parent_id创建二级标签时不传 */
  target_object_types?: Array<TagBindTargetObjectType>;
  tag: Tag;
  Base?: base.Base;
}

export interface CreateTagResponse {
  tag?: Tag;
  BaseResp?: base.BaseResp;
}

export interface CreateTagTargetBindRequest {
  tag_id: Int64;
  UserID?: string;
  /** 标签所处空间 */
  space_id: Int64;
  target_object_id: Int64;
  target_object_type: TagBindTargetObjectType;
  /** 标签的自定义查询索引 */
  custom_search_key: string;
  Base?: base.Base;
}

export interface CreateTagTargetBindResponse {
  BaseResp?: base.BaseResp;
}

export interface GetTagByNameRequest {
  tag_name?: string;
  UserID?: string;
  space_id: Int64;
  Base?: base.Base;
}

export interface GetTagByNameResponse {
  tag?: Tag;
  BaseResp?: base.BaseResp;
}

export interface ListTagRequest {
  space_id: Int64;
  UserID?: string;
  page?: Int64;
  page_size?: Int64;
  /** -- search fields -- */
  fuzzy_name?: string;
  creator_id?: Int64;
  target_object_types?: Array<TagBindTargetObjectType>;
  /** 标签的自定义查询索引,对于不同的目标对象类型TargetObjectType,自定义查询索引约定为特定的业务id */
  custom_search_key?: string;
  /** 忽略已禁用的标签 */
  ignore_invalid_tag?: boolean;
  Base?: base.Base;
}

export interface ListTagResponse {
  tags?: Array<Tag>;
  total?: Int64;
  Session?: Session;
  BaseResp?: base.BaseResp;
}

export interface MCreateTagTargetBindRequest {
  tag_ids: Array<Int64>;
  UserID?: string;
  /** 标签所处空间 */
  space_id: Int64;
  target_object_id: Int64;
  target_object_type: TagBindTargetObjectType;
  /** 标签的自定义查询索引 */
  custom_search_key: string;
  Base?: base.Base;
}

export interface MCreateTagTargetBindResponse {
  BaseResp?: base.BaseResp;
}

export interface MGetTagListByTargetIDsRequest {
  space_id: Int64;
  UserID?: string;
  target_ids: Array<Int64>;
  target_object_type: TagBindTargetObjectType;
  Base?: base.Base;
}

export interface MGetTagListByTargetIDsResponse {
  tags?: Record<Int64, Array<Tag>>;
  BaseResp?: base.BaseResp;
}

export interface MGetTagsRequest {
  tag_ids?: Array<Int64>;
  UserID?: string;
  space_id: Int64;
  Base?: base.Base;
}

export interface MGetTagsResponse {
  tags?: Array<Tag>;
  BaseResp?: base.BaseResp;
}

export interface MGetTagTargetBindRequest {
  tag_ids: Array<Int64>;
  UserID?: string;
  /** 标签所处空间 */
  space_id: Int64;
  target_object_type: TagBindTargetObjectType;
  /** 标签的自定义查询索引 */
  custom_search_key?: string;
  Base?: base.Base;
}

export interface MGetTagTargetBindResponse {
  target_ids: Record<Int64, Array<Int64>>;
  BaseResp?: base.BaseResp;
}

export interface RemoveTagTargetBindRequest {
  space_id: Int64;
  UserID?: string;
  tag_id: Int64;
  target_object_type: TagBindTargetObjectType;
  target_object_id: Int64;
  Base?: base.Base;
}

export interface RemoveTagTargetBindResponse {
  BaseResp?: base.BaseResp;
}

export interface Session {
  UserID?: string;
}

export interface Tag {
  tag_id?: Int64;
  /** 标签关联对象类型信息 */
  bind_info?: Array<TagBindInfo>;
  /** 标签名称 */
  name: string;
  /** 标签层级 */
  level: TagLevel;
  /** 标签层级 */
  children?: Array<Tag>;
  /** 父标签ID,一级标签的父标签就是本身 */
  parent_tag_id?: Int64;
  /** 标签生效状态 */
  status?: TagStatus;
  /** 创建者 */
  creator_id?: Int64;
  /** 创建时间 */
  create_time?: Int64;
  /** 更新时间 */
  update_time?: Int64;
}

export interface TagBindInfo {
  /** 关联对象类型 */
  target_object_type: TagBindTargetObjectType;
  /** 关联对象数量 */
  count?: Int64;
}

export interface UpdateTagRequest {
  space_id: Int64;
  UserID?: string;
  /** 覆盖当前的关联对象类型列表,可能触发删除关联对象类型 */
  target_object_types?: Array<TagBindTargetObjectType>;
  tag: Tag;
  Base?: base.Base;
}

export interface UpdateTagResponse {
  tag?: Tag;
  BaseResp?: base.BaseResp;
}

export interface UpdateTargetBindTagsRequest {
  space_id: Int64;
  UserID?: string;
  tag_ids: Array<Int64>;
  target_object_type: TagBindTargetObjectType;
  target_object_id: Int64;
  custom_search_key: string;
  Base?: base.Base;
}

export interface UpdateTargetBindTagsResponse {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
