/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as model from './model';

export type Int64 = string | number;

export enum AIDPMsgOpType {
  Unknown = 0,
  InitItem = 1,
  PassSubmit = 2,
  BackSubmit = 3,
  DiscardSubmit = 4,
  AbandonSubmit = 5,
  DirectSubmit = 6,
  UpdateAnswer = 7,
  SendLabel = 8,
  ExitItem = 9,
}

export enum AnnotatePlatform {
  Undefined = 0,
  AIDP = 1,
}

export enum AuditResultEnum {
  /** 未检查 */
  Init = 1,
  /** 标记合格 */
  Pass = 2,
  /** 标记不合格 */
  Reject = 3,
}

export enum AuditStatusEnum {
  /** 不涉及审核操作 */
  None = 0,
  /** 通过 */
  Pass = 1,
  /** 回收 */
  Recycle = 2,
  /** 打回 */
  Back = 3,
}

export enum DatasetFileType {
  Undefined = 0,
  /** JSONL      = 1 */
  CSV = 2,
  PARQUET = 3,
}

export enum DatasetType {
  Undefined = 0,
  /** 数据处理数据集 */
  Default = 1,
  /** 评测集 */
  Eval = 2,
  /** 结果集 */
  EvalResult = 3,
}

export enum DataSourceType {
  Undefined = 0,
  /** TOS        = 1 */
  HDFS = 2,
}

export enum FieldType {
  /** DefinedText 类型 */
  Undefined = 0,
  String = 1,
  Number = 2,
  Bool = 4,
  Array = 5,
  Object = 6,
  Null = 7,
  /** Dataset 原生类型 */
  MarkdownBox = 11,
  Image = 12,
  File = 13,
  JSONString = 14,
  TextFile = 15,
  MultiContent = 16,
}

export enum InterpreterType {
  Undefined = 0,
  Aqua = 1,
  BizIDE = 2,
}

export enum MsgOpType {
  /** 未知类型提交，暂时不会出现，只做该字段兜底取值使用 */
  Unknown = 0,
  /** 题目送标，等同于消息里的 IsInit = true */
  InitItem = 1,
  /** 提交后被通过，题目向后置节点流转，等同于 AuditNodeList[$x].AuditStatus = 1 */
  PassSubmit = 2,
  /** 提交后被打回，题目向前置节点流转，等同于 AuditNodeList[$x].AuditStatus = 3 */
  BackSubmit = 3,
  /** 废弃提交，等同于消息里的 IsDiscard = true */
  DiscardSubmit = 4,
  /** 点击无效后提交 */
  AbandonSubmit = 5,
  /** 直接提交，等同于消息里的 IsDirectSubmit = true */
  DirectSubmit = 6,
  /** 更新答案，等同于消息里的 IsUpdate = true */
  UpdateAnswer = 7,
}

export enum RunLogSourceType {
  Undefined = 0,
  /** 文本 */
  Text = 1,
  /** 从TOS读取 */
  TOS = 2,
  /** 从StreamLog读取 */
  StreamLog = 3,
}

export enum ScriptType {
  Undefined = 0,
  Python = 1,
  JavaScript = 2,
}

export enum TaskStatus {
  Undefined = 0,
  /** 正在初始化 */
  Initializing = 1,
  /** 正在运行 */
  Running = 2,
  /** 成功完成 */
  Done = 3,
  /** 失败 */
  Failed = 4,
  /** 手动终止 */
  Terminated = 5,
  /** 成功完成，但有错误 */
  DoneWithError = 6,
}

export enum TaskType {
  Undefined = 0,
  DataProcessing = 1,
  FineTuning = 2,
}

/** 模版类型
 Notice: 此处类型不完整 */
export enum TemplateType {
  /** Neeko类型模板 */
  Neeko = 1000,
}

export interface AIDPConfig {
  /** AIDP 用户 ID */
  userID?: string;
  /** AIDP 任务 ID */
  taskID?: string;
  /** AIDP 任务详情链接 */
  taskURL?: string;
}

export interface AnnotateStatusDetail {
  /** 总数据量 */
  inputSize?: string;
  /** 已送标数据量 */
  sent?: string;
  /** 已取标数据量 */
  received?: string;
}

export interface AnnotateTask {
  id?: string;
  name?: string;
  spaceID?: string;
  /** 标注数据的平台 */
  platform?: AnnotatePlatform;
  /** AIDP 配置 */
  aidpConfig?: AIDPConfig;
  /** 任务状态 */
  status?: TaskStatus;
  /** 待标注字段映射关系 */
  sendMapConfig?: Array<SendFieldMapping>;
  /** 输入数据集信息 */
  inputDatasetInfo?: DatasetInfo;
  /** 输出数据集信息 */
  outputDatasetInfo?: DatasetInfo;
  /** 取标字段 */
  receiveFields?: Array<string>;
  /** 任务终止/失败的原因 */
  terminateReason?: string;
  /** 任务状态详情 */
  statusDetail?: AnnotateStatusDetail;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间，对于完成/失败/终止的任务，为结束时间 */
  updatedAt?: string;
  createdBy?: string;
}

export interface DataProcessingTask {
  id?: string;
  name?: string;
  taskType?: TaskType;
  inDataset?: DatasetInfo;
  /** Deprecated，使用outDatasets */
  outDataset?: DatasetInfo;
  userScript?: UserScript;
  taskStatus?: TaskStatus;
  trainingDatasetType?: model.TrainingDatasetType;
  /** 输出数据集列表 */
  outDatasets?: Array<DatasetInfo>;
  /** 数据集类型 */
  datasetType?: DatasetType;
  /** Fornax空间ID */
  spaceID?: string;
  /** 创建人ID */
  createdBy?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 更新人ID */
  updatedBy?: string;
  /** 更新时间 */
  updatedAt?: string;
}

export interface DatasetColumnMapping {
  fromColumn: string;
  toColumn: string;
}

export interface DatasetImportTask {
  id?: string;
  datasetID?: string;
  status?: TaskStatus;
  fileType?: DatasetFileType;
  dataSource?: DataSource;
  overwrite?: boolean;
  /** 文件大小，单位：byte */
  totalSize?: number;
  /** 已处理数据大小，单位：byte */
  processedSize?: number;
  /** 已处理的行数 */
  processedLineCount?: number;
  /** 已导入的行数 */
  outputLineCount?: number;
  errLog?: string;
  msg?: string;
  /** 批量导入详情 */
  batchImportDetail?: Array<DataSource>;
  /** Fornax空间ID */
  spaceID?: string;
  /** 创建人ID */
  createdBy?: string;
  /** 创建时间，秒 */
  createdAt?: string;
  /** 更新人ID */
  updatedBy?: string;
  /** 更新时间，秒 */
  updatedAt?: string;
}

export interface DatasetInfo {
  id?: string;
  name?: string;
  description?: string;
}

export interface DataSource {
  type?: DataSourceType;
  /** 2: TOSFile         tosFile */
  hdfsPath?: string;
}

export interface ExportDatasetStatusDetail {
  /** 源数据集中的样本总数 */
  totalSamples: Int64;
  /** 成功写入到目标数据集的样本数 */
  addedSamples?: Int64;
  /** 错误信息 */
  errors?: Array<string>;
}

export interface ExportDatasetTask {
  id?: string;
  spaceID: string;
  fromDatasetID: string;
  columnMappings?: Array<DatasetColumnMapping>;
  toDatasetID?: string;
  /** 为 true 时覆盖更新 */
  overwrite?: boolean;
  status?: TaskStatus;
  statusDetail?: ExportDatasetStatusDetail;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface FieldInfo {
  name?: string;
  type?: FieldType;
}

export interface ScriptRunResultItem {
  isSuccess?: boolean;
  input?: string;
  output?: string;
  error?: string;
  latencyInMs?: string;
  /** 期望输出到的数据集名称。如果为空则表明未找到目标数据集 */
  datasetName?: string;
}

export interface SendFieldMapping {
  /** 输入数据集中的字段名 */
  inputField?: string;
  /** 映射的送标数据字段名 */
  sendField?: string;
}

export interface TaskRun {
  id?: string;
  taskID?: string;
  realInDatasetID?: string;
  totalQty?: string;
  processedQty?: string;
  outputQty?: string;
  scriptHash?: string;
  message?: string;
  status?: TaskStatus;
  createdBy?: string;
  createdAt?: string;
  endedAt?: string;
}

export interface UserScript {
  content?: string;
  scriptType?: ScriptType;
  interpreterType?: InterpreterType;
  templateVersion?: string;
}

export interface UserScriptTemplateConfig {
  version?: string;
  template?: string;
  scriptType?: ScriptType;
  interpreterType?: InterpreterType;
}
/* eslint-enable */
