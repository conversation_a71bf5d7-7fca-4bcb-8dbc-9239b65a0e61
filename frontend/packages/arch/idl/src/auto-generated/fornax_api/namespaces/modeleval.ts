/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_task from './flow_devops_evaluation_task';
import * as flow_devops_evaluation_evaluator from './flow_devops_evaluation_evaluator';
import * as model from './model';
import * as flow_devops_evaluation_dataset from './flow_devops_evaluation_dataset';

export type Int64 = string | number;

export enum DatasetPreHandlerType {
  None = 0,
  PromptTemplate = 1,
}

export enum InputPreHandlerType {
  None = 0,
  URL = 1,
}

export enum MerlinSeedModelType {
  Unknown = 0,
  Hdfs = 1,
}

export enum OfflineEvalTaskModelSource {
  Unknown = 0,
  MerlinSeed = 1,
  FornaxSftTask = 2,
  /** 基座模型 */
  FoundationModel = 3,
}

export enum OfflineEvalTaskStatus {
  Preparing = 0,
  Launching = 1,
  Inferring = 2,
  Evaluating = 3,
  Success = 4,
  Failed = 5,
  Terminating = 6,
  Terminated = 7,
  PartialFailed = 8,
}

export interface InferResExportStatus {
  /** 结果文件的 hdfs 地址 */
  hdfsPath?: string;
  /** 当前文件的导出进度 */
  cursor?: string;
  /** 是否导出完成 */
  isExported?: boolean;
}

export interface OfflineEvalProduction {
  datasetID?: string;
  resultSetID?: string;
  caseTaskID?: string;
  /** 每次读取时需要rpc获得EvalCaseTask(Get和List都需要)，写时不应该写到数据库里 */
  evalCaseTask?: flow_devops_evaluation_task.Task;
  /** 最后写入的一条数据的 ID */
  lastOutputCursor?: Int64;
}

export interface OfflineEvalTask {
  id?: string;
  name?: string;
  desc?: string;
  model?: OfflineEvalTaskModel;
  datasets?: Array<OfflineEvalTaskDataset>;
  /** 每次读取时需要rpc获得RuleGroup.Rules(只Get需要，List不需要) */
  evalRuleGroup?: flow_devops_evaluation_evaluator.RuleGroup;
  resource?: model.SftTaskResource;
  ckptConfig?: OfflineEvalTaskCkptConfig;
  status?: OfflineEvalTaskStatus;
  provider?: model.Provider;
  providerTaskID?: string;
  caseID?: string;
  ckptResult?: OfflineEvalTaskCkptResult;
  errCode?: string;
  errMessage?: string;
  displayErrMsg?: string;
  /** 规则组是否已删除 */
  isRuleGroupDeleted?: boolean;
  /** Fornax空间ID */
  spaceID?: string;
  /** 创建人ID */
  createdBy?: string;
  /** 创建时间，秒 */
  createdAt?: string;
  /** 更新人ID */
  updatedBy?: string;
  /** 更新时间，秒 */
  updatedAt?: string;
}

export interface OfflineEvalTaskCkptConfig {
  /** key是name */
  items?: Record<string, OfflineEvalTaskCkptConfigItem>;
}

export interface OfflineEvalTaskCkptConfigItem {
  name?: string;
  def?: string;
  nextCkptName?: string;
  maxRetryTime?: string;
  retryIntervalMilliSecond?: string;
  /** 重试时间间隔的变化方式，支持固定间隔和随时间渐进式变化 */
  retryIntervalChangeType?: string;
  /** 每重试x次，重试时间间隔会发生变化 */
  retryIntervalChangeTimes?: string;
  /** 每次重试时间间隔变化的步长，单位为ms，可以为负数 */
  retryIntervalChangeStep?: string;
  customConfigs?: Record<string, string>;
  /** 触发下一个checkpoint的时间间隔，单位为ms */
  triggerNextCkptIntervalMilliSecond?: string;
}

export interface OfflineEvalTaskCkptResult {
  /** 评测集上传到hdfs的地址 */
  datasetHdfsAddress?: string;
  /** 推理结果保存的hdfs地址，可能为文件夹(开源模型必须时文件而非文件夹) */
  inferResultHdfsAddress?: string;
  /** 推理结果导出进度 */
  resultExportStatuses?: Array<InferResExportStatus>;
  /** merlin推理任务状态 */
  merlinDataProcessingInstanceStatusGroup?: string;
  /** merlin推理任务状态详情 */
  merlinDataProcessingInstanceStatus?: string;
  /** 传入离线推理任务的数据列名 */
  inferTaskColumnName?: string;
  /** 离线评测产物 */
  evalProductions?: Array<OfflineEvalProduction>;
  /** 结果集是否导出完成 */
  resultSetExported?: boolean;
  /** merlin seed离线推理任务实际上就是在merlin任务用例外包了一层，在这里记录这个merlin任务用例id */
  merlinJobID?: string;
  /** merlin seed离线推理任务链接 */
  merlinSeedTaskUrl?: string;
  /** merlin 任务实例是否终止 */
  merlinJobTerminated?: boolean;
  /** 保存除了 plainText 以外的类型的数据的原始信息 */
  originDataColumnName?: string;
  /** 批量推理结果的列名，老数据可能为空，为空时该值取output（parquet中的列名。如果是jsonl，格式是方舟固定的） */
  outputColumnName?: string;
  /** 原始批量推理结果的列名（parquet中的列名） */
  originOutputColumnName?: string;
  /** 批量推理任务的id */
  batchInferTaskID?: Int64;
  /** 批量推理任务的状态 */
  batchInferTaskStatus?: string;
  /** 批量推理输入数据的其中一列列名，这列记录了数据中每一行的唯一id */
  itemIDColumnName?: string;
  /** 评测集上传到tos的桶名 */
  datasetTosBucketName?: string;
  /** 评测集上传到tos后的文件路径 */
  datasetTosObjectKey?: string;
  /** 推理结果保存到tos的桶名 */
  inferResultTosBucketName?: string;
  /** 推理结果保存到tos后的文件夹路径 */
  inferResultTosObjectKey?: string;
  /** 火山项目名，用于记录用户托管的字节云方舟账号的项目名称 */
  volcEngineProjectName?: string;
  /** 评测集中的图片上传到hdfs的地址（文件夹） */
  datasetImageHdfsAddress?: string;
}

export interface OfflineEvalTaskDataset {
  evalDataset?: flow_devops_evaluation_dataset.DatasetInfo;
  /** 数据集预处理 */
  datasetPreHandler?: OfflineEvalTaskDatasetPreHandler;
  /** 模型输入预处理 */
  inputPreHandler?: OfflineEvalTaskInputPreHandler;
  /** 是否已经上传到 hdfs */
  uploaded?: boolean;
  /** 上传进度游标 */
  uploadCursor?: Int64;
  /** 是否已删除 */
  isDeleted?: boolean;
}

export interface OfflineEvalTaskDatasetPreHandler {
  type?: DatasetPreHandlerType;
  promptKey?: string;
  promptVersion?: string;
  inputColumn?: string;
  promptID?: string;
  /** prompt是否已删除 */
  isPromptDeleted?: boolean;
}

export interface OfflineEvalTaskInputPreHandler {
  type?: InputPreHandlerType;
  url?: string;
}

export interface OfflineEvalTaskModel {
  source?: OfflineEvalTaskModelSource;
  /** 被评测的模型（当source是OpenSource/Ark基础模型时，模型信息写在这里） */
  foundationModel?: model.SftTaskFoundationModel;
  /** 被评测的模型的标识（此时模型是sft产物，identification是方舟custom_id / model_version，且会记录foundation model） */
  identification?: string;
  sftTaskID?: string;
  sftTaskProvider?: model.Provider;
  merlinModelName?: string;
  merlinModelVersion?: string;
  trainingType?: model.SftTaskTrainingType;
  trainingMethod?: model.SftTaskTrainingMethod;
  sftTask?: model.SftTask;
  merlinSeedModelType?: MerlinSeedModelType;
  /** 被评测的模型文件的hdfs地址（当source是seed/sft/OpenSource时，模型hdfs地址写在这里） */
  modelAddress?: string;
  tokenizerAddress?: string;
  networkConfigContext?: string;
  quantConfigContext?: string;
  temperature?: number;
  topP?: number;
  topK?: string;
  maxOutputToken?: string;
  maxContextToken?: string;
  batchSize?: string;
}
/* eslint-enable */
