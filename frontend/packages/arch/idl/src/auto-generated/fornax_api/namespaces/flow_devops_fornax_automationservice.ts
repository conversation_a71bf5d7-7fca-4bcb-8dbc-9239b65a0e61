/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as automation from './automation';
import * as flow_devops_fornaxob_fieldfilter from './flow_devops_fornaxob_fieldfilter';
import * as flow_devops_fornaxob_common from './flow_devops_fornaxob_common';

export type Int64 = string | number;

export interface ApplyStatusCallBackReq {
  workflowID: string;
  spaceID: string;
  status: string;
  'x-jwt-token'?: string;
}

export interface ApplyStatusCallBackResp {
  code?: Int64;
  message?: string;
}

export interface CreateTaskReq {
  space_id: string;
  task: automation.Task;
}

export interface CreateTaskResp {
  taskID?: string;
}

export interface DeleteTaskReq {
  task_id: string;
}

export interface DeleteTaskResp {}

export interface GetFilterOptionsReq {
  objectType: automation.ObjectType;
  /** 该参数未提供或 false 且 builtInFilter 为空时, 返回该对象任意一个 builtinFilter 对应的选项
该参数为 true 且 builtInFilter 为空时, 返回支持的 builtinFilter 列表 */
  apiV2?: boolean;
  builtInFilter?: automation.BuiltinSpanFilterType;
}

export interface GetFilterOptionsResp {
  options?: Array<automation.TagFilterOption>;
  objFilterOptions?: Array<automation.TagFilterOption>;
  builtInOptions?: Array<automation.BuiltinSpanFilterType>;
}

export interface GetTaskDetailReq {
  task_id: string;
  space_id: string;
}

export interface GetTaskDetailResp {
  task?: automation.Task;
}

export interface GetTaskStatusDetailReq {
  task_id: string;
}

export interface GetTaskStatusDetailResp {
  type?: automation.TaskType;
  spanToDatasetStatus?: automation.SpanToDatasetStat;
  SpanEvalRunStatus?: automation.SpanEvalStat;
  batchExecuteCaseStatus?: automation.BatchExecAutoUseCaseStat;
}

export interface ListBPMApproversReq {
  /** 权限组 key */
  group_key: string;
}

export interface ListBPMApproversResp {
  approvers?: Array<string>;
  code?: Int64;
  message?: string;
}

export interface ListTasksBySpaceReq {
  space_id: string;
  taskID?: string;
  taskName?: string;
  taskType?: Array<automation.TaskType>;
  taskStatuses?: Array<automation.TaskStatusType>;
  createdBy?: string;
  /** 起始为空，滚动传入 resp 里的 nextCursor */
  cursor?: string;
  /** 默认为 20 */
  pageSize?: string;
  /** 默认为 -1，不为0时表示按更新时间倒序返回，否则按创建时间倒序。滚动传入 resp 里的 nextUpdatedAt */
  updatedAtLTE?: string;
}

export interface ListTasksBySpaceResp {
  tasks?: Array<automation.Task>;
  nextCursor?: string;
  hasMore?: boolean;
  nextUpdatedAt?: string;
}

export interface Rule2TraceQueryReq {
  space_id: string;
  /** 自动化任务的筛选条件，里面的字段不一定都会用到 */
  rule?: automation.Rule;
}

export interface Rule2TraceQueryResp {
  filters?: Record<string, flow_devops_fornaxob_fieldfilter.FieldFilter>;
  platformType?: flow_devops_fornaxob_common.PlatformType;
}

export interface StopTaskReq {
  task_id: string;
}

export interface StopTaskResp {}

export interface TaskBatchExecCasesStatus {
  taskID: string;
  allCasesDeleted?: boolean;
  allCasesFinished?: boolean;
}

export interface TaskSpanToDatasetStatus {
  taskID: string;
  datasetDeleted?: boolean;
  datasetLimitHit?: boolean;
}

export interface UpdateTaskReq {
  task_id: string;
  task?: automation.Task;
}

export interface UpdateTaskResp {}
/* eslint-enable */
