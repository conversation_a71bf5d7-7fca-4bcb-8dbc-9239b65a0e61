/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_marketplace_interaction_common from './flow_marketplace_interaction_common';
import * as marketplace_common from './marketplace_common';

export type Int64 = string | number;

export enum UserRole {
  SuperManager = 1,
  Default = 2,
  Anonymous = 3,
}

export interface BanUserRequest {
  user_id?: string;
}

export interface BanUserResponse {
  code: number;
  message: string;
}

export interface BotInfo {
  bot_id?: string;
  bot_name?: string;
  bot_avatar_url?: string;
  bot_description?: string;
}

export interface CancelPinPostRequest {
  post_id?: string;
}

export interface CancelPinPostResponse {
  code: number;
  message: string;
}

export interface CommentInfo {
  bot_id?: string;
  content?: string;
  comment_id?: string;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  /** 只有一级评论有 */
  reply_count?: number;
  /** user_id 和 bot_id 二选一 */
  user_id?: string;
  created_at?: string;
  bot_reply_status?: flow_marketplace_interaction_common.CommentBotReplyStatus;
  status?: flow_marketplace_interaction_common.CommentStatus;
  author_type?: flow_marketplace_interaction_common.AuthorType;
}

export interface DeleteCommentRequest {
  comment_id?: string;
}

export interface DeleteCommentResponse {
  code: number;
  message: string;
}

export interface DeletePostRequest {
  post_id?: string;
}

export interface DeletePostResponse {
  code: number;
  message: string;
}

export interface DeleteUserBehaviorRecordRequest {
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  item_id_list?: Array<string>;
  behavior_type?: flow_marketplace_interaction_common.UserBehaviorType;
}

export interface DeleteUserBehaviorRecordResponse {
  code: number;
  message: string;
}

export interface DoFollowRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  is_cancel?: boolean;
  Cookie?: string;
}

export interface DoFollowResponse {
  code: number;
  message: string;
}

export interface DoReactionRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  reaction_type?: flow_marketplace_interaction_common.ReactionType;
  emoji_type?: flow_marketplace_interaction_common.EmojiType;
  is_cancel?: boolean;
  Cookie?: string;
}

export interface DoReactionResponse {
  code: number;
  message: string;
}

export interface FolloweeItem {
  user_info?: UserInfo;
  yestoday_update_bot_count?: number;
  follow_type?: marketplace_common.FollowType;
}

export interface FollowerItem {
  user_info?: UserInfo;
  follow_type?: marketplace_common.FollowType;
  yestoday_update_bot_count?: number;
}

export interface GetCommentListData {
  comment_list?: Array<PackCommentInfo>;
  has_more?: boolean;
  cursor?: string;
}

export interface GetCommentListRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  cursor?: string;
  limit?: number;
  Cookie?: string;
}

export interface GetCommentListResponse {
  code: number;
  message: string;
  data?: GetCommentListData;
}

export interface GetFolloweeListData {
  followee_item_list?: Array<FolloweeItem>;
  has_more?: boolean;
  cursor?: string;
}

export interface GetFolloweeListRequest {
  cursor?: string;
  limit?: number;
  keyword?: string;
}

export interface GetFolloweeListResponse {
  code: number;
  message: string;
  data?: GetFolloweeListData;
}

export interface GetFollowerListData {
  follower_item_list?: Array<FollowerItem>;
  has_more?: boolean;
  cursor?: string;
}

export interface GetFollowerListRequest {
  cursor?: string;
  limit?: number;
}

export interface GetFollowerListResponse {
  code: number;
  message: string;
  data?: GetFollowerListData;
}

export interface GetImgURLData {
  url?: string;
}

export interface GetImgURLRequest {
  Key?: string;
}

export interface GetImgURLResponse {
  code: number;
  message: string;
  data?: GetImgURLData;
}

export interface GetLikeStatisticsListData {
  like_statistics_list?: Array<LikeStatisticsItem>;
  has_more?: boolean;
  cursor?: string;
}

export interface GetLikeStatisticsListRequest {
  cursor?: string;
  limit?: number;
  keyword?: string;
}

export interface GetLikeStatisticsListResponse {
  code: number;
  message: string;
  data?: GetLikeStatisticsListData;
}

export interface GetMetaData {
  user_role?: UserRole;
  /** 全局封禁 */
  is_global_ban?: boolean;
  status?: flow_marketplace_interaction_common.DiscussionStatus;
  user_status?: flow_marketplace_interaction_common.UserStatus;
}

export interface GetMetaRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
}

export interface GetMetaResponse {
  code: number;
  message: string;
  data?: GetMetaData;
}

export interface GetPostDetailData {
  post_info?: PostInfo;
  /** 帖子互动数据统计 */
  post_emoji_count?: Partial<
    Record<flow_marketplace_interaction_common.EmojiType, number>
  >;
  /** 用户互动信息 */
  user_emoji_list?: Array<flow_marketplace_interaction_common.EmojiType>;
  author_user_info?: UserInfo;
  mention_list?: Array<flow_marketplace_interaction_common.Mention>;
}

export interface GetPostDetailRequest {
  post_id?: string;
  is_need_latest_data?: boolean;
  Cookie?: string;
}

export interface GetPostDetailResponse {
  code: number;
  message: string;
  data?: GetPostDetailData;
}

export interface GetPostLabelListData {
  post_label_list?: Array<flow_marketplace_interaction_common.PostLabel>;
}

export interface GetPostLabelListRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
}

export interface GetPostLabelListResponse {
  code: number;
  message: string;
  data?: GetPostLabelListData;
}

export interface GetPostListData {
  post_list?: Array<PackPostInfo>;
  has_more?: boolean;
  cursor?: string;
}

export interface GetPostListRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  cursor?: string;
  limit?: number;
  preview_top_post_id?: string;
  is_need_latest_data?: boolean;
  Cookie?: string;
}

export interface GetPostListResponse {
  code: number;
  message: string;
  data?: GetPostListData;
}

export interface GetProductLikeData {
  like_count?: number;
  user_reaction?: flow_marketplace_interaction_common.ReactionType;
}

export interface GetProductLikeRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
}

export interface GetProductLikeResponse {
  code: number;
  message: string;
  data?: GetProductLikeData;
}

export interface GetReplyListData {
  reply_info_list?: Array<PackReplyInfo>;
  has_more?: boolean;
  cursor?: string;
}

export interface GetReplyListRequest {
  comment_id?: string;
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  cursor?: string;
  limit?: number;
  Cookie?: string;
}

export interface GetReplyListResponse {
  code: number;
  message: string;
  data?: GetReplyListData;
}

export interface GetUploadTokenRequest {}

export interface GetUploadTokenResponse {
  code: number;
  message: string;
  data?: flow_marketplace_interaction_common.UploadTokenData;
}

export interface GetUserInteractionData {
  /** 关注数 */
  followee_count?: number;
  /** 粉丝数 */
  follower_count?: number;
  /** 收获点赞数 */
  gain_like_count?: number;
  /** 关注类型 */
  follow_type?: marketplace_common.FollowType;
}

export interface GetUserInteractionDataRequest {
  user_id?: string;
  /** 是否需要获赞数 */
  need_gain_like_count?: boolean;
}

export interface GetUserInteractionDataResponse {
  code: number;
  message: string;
  data?: GetUserInteractionData;
}

export interface GetVisitorUserListData {
  visitor_user_list?: Array<VisitorUser>;
  has_more?: boolean;
  cursor?: string;
}

export interface GetVisitorUserListRequest {
  cursor?: string;
  limit?: number;
  begin_at?: string;
  end_at?: string;
}

export interface GetVisitorUserListResponse {
  code: number;
  message: string;
  data?: GetVisitorUserListData;
}

export interface LikeStatisticsItem {
  bot_id?: string;
  avatar?: string;
  desc?: string;
  like_count?: number;
  bot_name?: string;
  product_id?: string;
}

export interface PackBotReplyInfo {
  bot_comment_id?: string;
  is_block?: boolean;
  block_reason?: string;
  comment_info?: CommentInfo;
  reply_info?: ReplyInfo;
  bot_info?: BotInfo;
}

export interface PackCommentInfo {
  comment_id?: string;
  user_info?: UserInfo;
  bot_info?: BotInfo;
  is_author?: boolean;
  /** 若为一级评论，则返回两条该评论下的回复 */
  pack_reply_info_list?: Array<PackReplyInfo>;
  comment_info?: CommentInfo;
  /** 当前用户 emoji */
  user_emoji_list?: Array<flow_marketplace_interaction_common.EmojiType>;
  emoji_count?: Partial<
    Record<flow_marketplace_interaction_common.EmojiType, number>
  >;
  mention_list?: Array<flow_marketplace_interaction_common.Mention>;
  has_more_reply?: boolean;
  reply_count?: number;
}

export interface PackPostInfo {
  post_info?: PostInfo;
  author_user_info?: UserInfo;
  mention_list?: Array<flow_marketplace_interaction_common.Mention>;
}

export interface PackReplyInfo {
  reply_id?: string;
  user_info?: UserInfo;
  bot_info?: BotInfo;
  mention_list?: Array<flow_marketplace_interaction_common.Mention>;
  /** 当前用户 reaction */
  user_emoji_list?: Array<flow_marketplace_interaction_common.EmojiType>;
  emoji_count?: Partial<
    Record<flow_marketplace_interaction_common.EmojiType, number>
  >;
  reply_info?: ReplyInfo;
  is_author?: boolean;
}

export interface PinPostRequest {
  post_id?: string;
  Cookie?: string;
}

export interface PinPostResponse {
  code: number;
  message: string;
}

export interface PostInfo {
  id?: string;
  comment_count?: number;
  title?: string;
  label?: flow_marketplace_interaction_common.PostLabel;
  user_id?: string;
  /** uri -> 资源的映射 */
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  content?: string;
  created_at?: string;
  /** 分享对话内容 */
  conversation?: flow_marketplace_interaction_common.Conversation;
  /** 置顶类型 */
  pin_type?: flow_marketplace_interaction_common.PinType;
  status?: flow_marketplace_interaction_common.PostStatus;
}

export interface PublishCommentData {
  comment_id?: string;
  comment_info?: CommentInfo;
  /** 如果提及机器人，那么会返回这个字段 */
  pack_bot_reply_info?: PackBotReplyInfo;
}

export interface PublishCommentRequest {
  /** 评论所属的帖子 id */
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  mention_list?: Array<flow_marketplace_interaction_common.Mention>;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  comment_content?: string;
  comment_content_text?: string;
  Cookie?: string;
}

export interface PublishCommentResponse {
  code: number;
  message: string;
  data?: PublishCommentData;
}

export interface PublishPostData {
  post_id?: string;
  /** 如果提及机器人，那么会返回这个字段 */
  pack_bot_reply_info?: PackBotReplyInfo;
}

export interface PublishPostRequest {
  item_id: string;
  item_type: flow_marketplace_interaction_common.InteractionItemType;
  title: string;
  content: string;
  label?: string;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  /** 提及 */
  mention_list?: Array<flow_marketplace_interaction_common.Mention>;
  /** 分享对话内容 */
  conversation?: flow_marketplace_interaction_common.Conversation;
  content_text?: string;
  Cookie?: string;
}

export interface PublishPostResponse {
  code: number;
  message: string;
  data?: PublishPostData;
}

export interface ReplyCommentData {
  reply_comment_id?: string;
  /** 如果提及机器人，那么会返回这个字段 */
  pack_bot_reply_info?: PackBotReplyInfo;
  reply_info?: ReplyInfo;
}

export interface ReplyCommentRequest {
  /** 评论所属的帖子 id */
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  mention_list?: Array<flow_marketplace_interaction_common.Mention>;
  reply_content?: string;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  reply_to_comment_id?: string;
  reply_content_text?: string;
  Cookie?: string;
}

export interface ReplyCommentResponse {
  code: number;
  message: string;
  data?: ReplyCommentData;
}

export interface ReplyInfo {
  reply_id?: string;
  /** user_id 和 bot_id 二选一 */
  user_id?: string;
  bot_id?: string;
  created_at?: string;
  resource?: Record<string, flow_marketplace_interaction_common.Resource>;
  content?: string;
  bot_reply_status?: flow_marketplace_interaction_common.CommentBotReplyStatus;
  status?: flow_marketplace_interaction_common.CommentStatus;
}

export interface ReportUserBehaviorRequest {
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
  behavior_type?: flow_marketplace_interaction_common.UserBehaviorType;
}

export interface ReportUserBehaviorResponse {
  code: number;
  message: string;
}

export interface SetSelfViewRequest {
  /** 评论ID */
  item_id?: string;
  item_type?: flow_marketplace_interaction_common.InteractionItemType;
}

export interface SetSelfViewResponse {
  code: number;
  message: string;
}

export interface StreamReplyRequest {
  /** 评论ID */
  comment_id?: string;
  /** chunk 序列开始号，<=0表示从首包开始 */
  seq_start?: Int64;
  /** 非必传，chunk 序列结束号，<=0表示无穷大（直到消息结束） */
  seq_end?: Int64;
}

export interface StreamReplyResponse {
  comment_id?: string;
}

export interface UserInfo {
  user_id?: string;
  avatar_url?: string;
  user_name?: string;
  nick_name?: string;
  user_status?: flow_marketplace_interaction_common.UserStatus;
  signature?: string;
  user_label?: flow_marketplace_interaction_common.UserLabel;
}

export interface VisitorUser {
  user_info?: UserInfo;
  visitor_at?: string;
  follow_type?: marketplace_common.FollowType;
  yestoday_update_bot_count?: number;
}
/* eslint-enable */
