/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as flow_devops_dp_manage from './namespaces/flow_devops_dp_manage';
import * as model_arena from './namespaces/model_arena';
import * as multi_version from './namespaces/multi_version';
import * as rule from './namespaces/rule';

export { base, flow_devops_dp_manage, model_arena, multi_version, rule };
export * from './namespaces/base';
export * from './namespaces/flow_devops_dp_manage';
export * from './namespaces/model_arena';
export * from './namespaces/multi_version';
export * from './namespaces/rule';

export type Int64 = string | number;

export default class DpManageService<T> {
  private request: any = () => {
    throw new Error('DpManageService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /api/devops/dp/v1/ping */
  Ping(
    req: flow_devops_dp_manage.PingReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.PingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/dp/v1/ping');
    const method = 'GET';
    const params = { PingMessage: _req['PingMessage'] };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/devops/dp/bot_diff */
  BotDiff(
    req: flow_devops_dp_manage.BotDiffReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.BotDiffResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/dp/bot_diff');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      left: _req['left'],
      right: _req['right'],
      filter: _req['filter'],
      template_key: _req['template_key'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/dp/bot_merge */
  BotMerge(
    req: flow_devops_dp_manage.BotMergeRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.BotMergeResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/dp/bot_merge');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      diff_res: _req['diff_res'],
      origin_bot_dl: _req['origin_bot_dl'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/dp/record_changelog */
  RecordChangelog(
    req: flow_devops_dp_manage.RecordChangelogRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.RecordChangelogResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/dp/record_changelog');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      llm_result: _req['llm_result'],
      user_result: _req['user_result'],
      publish_id: _req['publish_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/dp/bot_diff_3way */
  BotDiff3Way(
    req: flow_devops_dp_manage.BotDiff3WayReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.BotDiff3WayResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/dp/bot_diff_3way');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      current_version: _req['current_version'],
      target_version: _req['target_version'],
      template_key: _req['template_key'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/dp/get_topo_info
   *
   * topology
   */
  GetTopoInfo(
    req: flow_devops_dp_manage.GetTopoInfoReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.GetTopoInfoResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/dp/get_topo_info');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      resource_id: _req['resource_id'],
      version: _req['version'],
      env: _req['env'],
      resource_type: _req['resource_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/multi_version/rules
   *
   * 获取当前规则列表:预发布版本只下发生效中列表，灰度版本只下发最新一条灰度版本(灰度中/已下线)
   */
  GetMultiVersionActiveList(
    req: flow_devops_dp_manage.GetMultiVersionActiveListRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.GetMultiVersionActiveListResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/multi_version/rules`,
    );
    const method = 'GET';
    const data = {
      page: _req['page'],
      size: _req['size'],
      version_type: _req['version_type'],
    };
    const params = {
      query_type: _req['query_type'],
      version: _req['version'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * GET /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/multi_version/latest_param
   *
   * 获取bot上次多版本发布配置
   */
  GetMultiVersionLatestParams(
    req: flow_devops_dp_manage.GetMultiVersionLatestParamsRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.GetMultiVersionLatestParamsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/multi_version/latest_param`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/multi_version/publish
   *
   * multi version 多版本发布
   */
  PublishMultiVersion(
    req: flow_devops_dp_manage.PublishMultiVersionRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.PublishMultiVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/multi_version/publish`,
    );
    const method = 'POST';
    const data = {
      connector_ids: _req['connector_ids'],
      version_type: _req['version_type'],
      publish_id: _req['publish_id'],
      x_tt_env: _req['x_tt_env'],
      version_identifier: _req['version_identifier'],
      gray_id_list: _req['gray_id_list'],
      commit_version: _req['commit_version'],
      bot_version: _req['bot_version'],
      gray_config: _req['gray_config'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/multi_version/connectors
   *
   * 获取允许多版本发布的渠道ID
   */
  GetMultiVersionAllowedConnectors(
    req: flow_devops_dp_manage.GetMultiVersionAllowedConnectorsRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.GetMultiVersionAllowedConnectorsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/multi_version/connectors`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/multi_version/rules/:rule_id/inactivate
   *
   * 下线渠道策略
   */
  InactivateMultiVersion(
    req: flow_devops_dp_manage.InactivateMultiVersionRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.InactivateMultiVersionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/multi_version/rules/${_req['rule_id']}/inactivate`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/dp/coze/ide/fg
   *
   * 插件fg开关
   */
  CozeIDEPluginFg(
    req?: flow_devops_dp_manage.CozeIDEPluginFgRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.CozeIDEPluginFgResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/dp/coze/ide/fg');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/multi_version/rules/:rule_id/update_gray
   *
   * 更新灰度配置
   */
  UpdateGrayIDList(
    req: flow_devops_dp_manage.UpdateGrayIDListRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.UpdateGrayIDListResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/multi_version/rules/${_req['rule_id']}/update_gray`,
    );
    const method = 'POST';
    const data = {
      gray_id_list: _req['gray_id_list'],
      gray_config: _req['gray_config'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/arena/rand_bot_id */
  RandBotID(
    req?: flow_devops_dp_manage.RandBotIDReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.RandBotIDResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/rand_bot_id');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/arena/detail/create_pk */
  CreateArenaPK(
    req?: flow_devops_dp_manage.CreateArenaPKReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.CreateArenaPKResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/detail/create_pk');
    const method = 'POST';
    const data = {
      product_id: _req['product_id'],
      bot_id: _req['bot_id'],
      bot_version: _req['bot_version'],
      Base: _req['Base'],
    };
    const headers = { Host: _req['Host'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /models/arena/detail/vote */
  ArenaVote(
    req?: flow_devops_dp_manage.ArenaVoteReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaVoteResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/detail/vote');
    const method = 'POST';
    const data = {
      pk_id: _req['pk_id'],
      result: _req['result'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /models/arena/list_arena_bot
   *
   * 模型竞技场
   */
  ListArenaBot(
    req?: flow_devops_dp_manage.ListArenaBotReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ListArenaBotResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/list_arena_bot');
    const method = 'POST';
    const data = {
      bot_type_id: _req['bot_type_id'],
      page_size: _req['page_size'],
      page_number: _req['page_number'],
      rand3: _req['rand3'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/leaderboard/get */
  ArenaLeaderBoardGet(
    req?: flow_devops_dp_manage.ArenaLeaderBoardGetReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaLeaderBoardGetResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/leaderboard/get');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/arena/get_conf */
  ArenaGetConf(
    req?: flow_devops_dp_manage.ArenaGetConfReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaGetConfResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/get_conf');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/arena/detail/break_message */
  ArenaChatBreakMsg(
    req?: flow_devops_dp_manage.ArenaChatBreakMsgReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaChatBreakMsgResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/detail/break_message');
    const method = 'POST';
    const data = {
      conversation_id: _req['conversation_id'],
      query_message_id: _req['query_message_id'],
      answer_message_id: _req['answer_message_id'],
      broken_pos: _req['broken_pos'],
      local_message_id: _req['local_message_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /models/arena/detail/chat
   *
   * chat流式接口
   */
  ArenaChatStream(
    req: flow_devops_dp_manage.ArenaChatStreamReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaChatStreamResp> {
    const _req = req;
    const url = this.genBaseURL('/models/arena/detail/chat');
    const method = 'POST';
    const data = {
      bot_id: _req['bot_id'],
      conversation_id: _req['conversation_id'],
      bot_version: _req['bot_version'],
      user: _req['user'],
      query: _req['query'],
      extra: _req['extra'],
      content_type: _req['content_type'],
      regen_message_id: _req['regen_message_id'],
      local_message_id: _req['local_message_id'],
      insert_history_message_list: _req['insert_history_message_list'],
      device_id: _req['device_id'],
      space_id: _req['space_id'],
      toolList: _req['toolList'],
    };
    const headers = { Origin: _req['Origin'], Host: _req['Host'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/rollback */
  RollbackBot(
    req: flow_devops_dp_manage.RollbackBotRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.RollbackBotResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/rollback`,
    );
    const method = 'POST';
    const data = {
      connector_ids: _req['connector_ids'],
      commit_version: _req['commit_version'],
      bot_version: _req['bot_version'],
      publish_id: _req['publish_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /models/arena/question/list_bank
   *
   * 模型竞技场：题库接口
   */
  ArenaListQuestionBank(
    req?: flow_devops_dp_manage.ArenaListQuestionBankReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaListQuestionBankResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/question/list_bank');
    const method = 'POST';
    const data = {
      bank_type_id: _req['bank_type_id'],
      page_size: _req['page_size'],
      page_num: _req['page_num'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/arena/question/rand_question */
  ArenaRandQuestion(
    req?: flow_devops_dp_manage.ArenaRandQuestionReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaRandQuestionResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/question/rand_question');
    const method = 'POST';
    const data = {
      bank_id: _req['bank_id'],
      count: _req['count'],
      used_seq_id_list: _req['used_seq_id_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/check_review */
  CheckBotInReviewWhiteList(
    req: flow_devops_dp_manage.CheckBotInReviewWhiteListRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.CheckBotInReviewWhiteListResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/check_review`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/list_publish_versions
   *
   * coze dev
   */
  ListCurrentPublishVersions(
    req: flow_devops_dp_manage.ListCurrentPublishVersionsRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.ListCurrentPublishVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/list_publish_versions`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/list_history_versions */
  ListHistoryVersions(
    req: flow_devops_dp_manage.ListHistoryVersionsRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.ListHistoryVersionsResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/list_history_versions`,
    );
    const method = 'POST';
    const data = {
      version_types: _req['version_types'],
      connector_ids: _req['connector_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/dp/coze/spaces/:space_id/bots/:bot_id/list_operation_histories */
  ListOperationHistory(
    req: flow_devops_dp_manage.ListOperationHistoryRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.ListOperationHistoryResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/dp/coze/spaces/${_req['space_id']}/bots/${_req['bot_id']}/list_operation_histories`,
    );
    const method = 'POST';
    const data = {
      version_types: _req['version_types'],
      connector_ids: _req['connector_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /models/leaderboard/get_v2
   *
   * 模型竞技场：排行榜接口
   */
  ArenaLeaderboardGetV2(
    req?: flow_devops_dp_manage.ArenaLeaderboardGetV2Req,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaLeaderboardGetV2Resp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/leaderboard/get_v2');
    const method = 'POST';
    const data = { id: _req['id'], Base: _req['Base'] };
    const headers = { Host: _req['Host'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /api/devops/dp/coze/hook/config_check */
  CheckHookConfig(
    req: flow_devops_dp_manage.CheckHookConfigRequest,
    options?: T,
  ): Promise<flow_devops_dp_manage.CheckHookConfigResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/dp/coze/hook/config_check');
    const method = 'POST';
    const data = { psm: _req['psm'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/arena/get_model_list */
  ArenaGetModelList(
    req?: flow_devops_dp_manage.ArenaGetModelListReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaGetModelListResp> {
    const _req = req || {};
    const url = this.genBaseURL('/models/arena/get_model_list');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const headers = { Host: _req['Host'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /models/arena/feedback_tag
   *
   * 模型竞技场：pk反馈接口
   */
  ArenaSubmitPkFeedbackTag(
    req: flow_devops_dp_manage.ArenaSubmitPkFeedbackTagReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaSubmitPkFeedbackTagResp> {
    const _req = req;
    const url = this.genBaseURL('/models/arena/feedback_tag');
    const method = 'POST';
    const data = {
      pk_id: _req['pk_id'],
      left_model_tags: _req['left_model_tags'],
      right_model_tags: _req['right_model_tags'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /models/arena/feedback_text */
  ArenaSubmitPkFeedbackText(
    req: flow_devops_dp_manage.ArenaSubmitPkFeedbackTextReq,
    options?: T,
  ): Promise<flow_devops_dp_manage.ArenaSubmitPkFeedbackTextResp> {
    const _req = req;
    const url = this.genBaseURL('/models/arena/feedback_text');
    const method = 'POST';
    const data = {
      pk_id: _req['pk_id'],
      feedback_text: _req['feedback_text'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
