/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** BotVersionOperation 操作类型
 NEXT ID: 5 */
export enum BotVersionOperation {
  Undefined = 0,
  /** 上线 */
  Publish = 1,
  /** 修改内容 */
  Modify = 2,
  /** 下线 */
  Offline = 3,
  /** 回滚 */
  Rollback = 4,
}

export enum GrayFieldType {
  Undefined = 0,
  UID = 1,
  DID = 2,
  CustomKey = 3,
}

/** HistoryVersionStatus 描述版本状态
 NEXT ID: 3 */
export enum HistoryVersionStatus {
  Undefined = 0,
  Valid = 1,
  InValid = 2,
}

/** HistoryVersionType 版本记录类型
 NEXT ID: 6 */
export enum HistoryVersionType {
  Undefined = 0,
  /** 提交版本 */
  Commit = 1,
  /** 线上发布 */
  Online = 2,
  /** 预发布PPE */
  PrePublish = 3,
  /** 灰度发布 */
  Gray = 4,
  /** 回滚版本 */
  Rollback = 5,
}

/** PublishVersionType 当前生效版本发布类型
 NEXT ID: 4 */
export enum PublishVersionType {
  Undefined = 0,
  /** 线上正式版 */
  Online = 1,
  /** 预发布PPE */
  PrePublish = 2,
  /** 灰度发布 */
  Gray = 3,
}

export enum RuleStatus {
  UnDefined = 0,
  Online = 1,
  Offline = 2,
}

export enum VersionType {
  UnDefined = 0,
  XTtEnv = 1,
  VersionIdentifier = 2,
  Gray = 3,
}

/** 当前生效的策略 */
export interface ActiveRule {
  /** 策略id */
  rule_id?: string;
  /** 渠道信息 */
  connector_info?: ConnectorInfo;
  /** 创建人名字 */
  creator_name?: string;
  /** 创建时间 */
  create_time?: string;
  /** 版本，commit version */
  version?: string;
  /** pre类型/灰度类型 */
  version_type?: VersionType;
  /** 泳道名，VersionType=XTtEnv时用 */
  x_tt_env?: string;
  /** 版本标识，VersionType=VersionIdentifier时用 */
  version_identifier?: string;
  /** bot version，用来换取commit version */
  bot_version?: string;
  /** [Deprecated]灰度id列表，VersionType=Gray时用(灰度id列表) */
  gray_id_list?: Array<string>;
  /** 规则状态 */
  status?: RuleStatus;
  /** 下线时间 */
  offline_time?: string;
  /** 操作人名字 */
  operator_name?: string;
  /** 灰度配置，VersionType=Gray时使用(支持黑白名单，灰度比例) */
  gray_config?: GrayConfig;
}

export interface AllowedConnector {
  id?: string;
  has_published?: boolean;
}

/** AllowList
 NEXT ID: 3 */
export interface AllowList {
  /** 比较字段 */
  field?: GrayField;
  /** 比较字段值 */
  values?: Array<string>;
}

/** BlockList
 NEXT ID: 3 */
export interface BlockList {
  /** 比较字段 */
  field?: GrayField;
  /** 比较字段值 */
  values?: Array<string>;
}

/** BotHistoryVersion 版本历史
 NEXT ID: 14 */
export interface BotHistoryVersion {
  /** 记录id */
  history_id?: string;
  /** 记录类型 */
  history_version_type?: HistoryVersionType;
  /** 创建人信息 */
  creator?: Creator;
  /** 创建时间 */
  create_time?: string;
  /** 版本说明 */
  remark?: string;
  /** 渠道信息, 提交版本没有此项 */
  connector_infos?: Array<ConnectorInfo>;
  /** bot发布版本号 */
  bot_version?: string;
  /** 版本对应的提交版本号 */
  commit_version?: string;
  /** 当前版本状态 */
  history_version_status?: HistoryVersionStatus;
  /** 预发布PPE的env名称 */
  x_tt_env?: string;
  /** [Deprecated]灰度id列表 */
  gray_id_list?: Array<string>;
  /** 下线时间 */
  offline_time?: string;
  /** 规则ID */
  rule_id?: string;
  /** 发布时候的id, commit操作没有 */
  publish_id?: string;
  /** 灰度配置，VersionType=Gray时使用(支持黑白名单，灰度比例) */
  gray_config?: GrayConfig;
}

/** BotOperationHistory 历史操作
 NEXT ID: 14 */
export interface BotOperationHistory {
  /** 记录id */
  history_id?: string;
  /** 操作类型 */
  bot_version_operation?: BotVersionOperation;
  /** 记录类型 */
  history_version_type?: HistoryVersionType;
  /** 操作人信息 */
  creator?: Creator;
  /** 操作时间 */
  create_time?: string;
  /** 版本说明 */
  remark?: string;
  /** 渠道信息, 提交版本没有此项 */
  connector_infos?: Array<ConnectorInfo>;
  /** bot发布版本号 */
  bot_version?: string;
  /** 版本对应的提交版本号 */
  commit_version?: string;
  /** 当前生效状态 */
  history_version_status?: HistoryVersionStatus;
  /** 预发布PPE的env名称 */
  x_tt_env?: string;
  /** [Deprecated]灰度id列表 */
  gray_id_list?: Array<string>;
  /** 规则ID */
  rule_id?: string;
  /** 发布时候的id, commit操作没有 */
  publish_id?: string;
  /** 灰度配置，VersionType=Gray时使用(支持黑白名单，灰度比例) */
  gray_config?: GrayConfig;
}

/** BotPublishVersion 当前生效发布版本信息
 NEXT ID: 11 */
export interface BotPublishVersion {
  bot_id?: string;
  /** 渠道信息 */
  connector_info?: ConnectorInfo;
  /** 创建人信息 */
  creator?: Creator;
  /** 创建时间 */
  create_time?: string;
  /** 版本说明 */
  remark?: string;
  /** 发布版本类型 */
  publish_version_type?: PublishVersionType;
  /** bot发布版本号 */
  bot_version?: string;
  /** 版本对应的提交版本号 */
  commit_version?: string;
  /** PPE和灰度发布关联的规则ID */
  rule_id?: string;
  /** PPE预发布，表示泳道名字 */
  x_tt_env?: string;
  /** [Deprecated]灰度id列表，VersionType=Gray时用(灰度id列表) */
  gray_id_list?: Array<string>;
  /** 发布时候的id */
  publish_id?: string;
  /** 是否支持回滚 */
  support_rollback?: boolean;
  /** 灰度配置，VersionType=Gray时使用(支持黑白名单，灰度比例) */
  gray_config?: GrayConfig;
}

export interface ConnectorInfo {
  id?: string;
  /** 渠道名 */
  name?: string;
  /** 渠道图标 */
  icon?: string;
}

/** Creator 用户信息
 NEXT ID: 4 */
export interface Creator {
  id?: string;
  name?: string;
  avatar_url?: string;
}

/** GrayBucket 百分比灰度配置
 NEXT ID: 4 */
export interface GrayBucket {
  /** 灰度计算字段 */
  field?: GrayField;
  /** 灰度比例, 取[0, size]的大小 */
  in_gray?: number;
  /** bucket 大小, 默认10000 */
  size?: number;
}

/** GrayConfig 灰度配置
 目前顺序为 BlockList -> AllowList -> Bucket的校验顺序
 NEXT ID: 4 */
export interface GrayConfig {
  /** 在blockList中的表示不命中 */
  block_list?: BlockList;
  /** 在allowList中的表示命中，未命中的走bucket */
  allow_list?: AllowList;
  /** 百分比灰度配置 */
  bucket?: GrayBucket;
}

export interface GrayField {
  /** 灰度字段类型 */
  type: GrayFieldType;
  /** 自定义字段json path */
  custom_field_path?: string;
}
/* eslint-enable */
