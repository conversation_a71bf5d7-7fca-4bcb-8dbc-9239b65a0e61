/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ArenaModelStatus {
  Normal = 0,
  UnReleasedInCoze = 1,
}

export enum ArenaScene {
  InhouseBotPK = 1,
  InhouseModelPK = 2,
  ReleaseBotPK = 3,
  ReleaseModelPK = 4,
}

export enum LeaderboardIntention {
  All = 0,
  Knowledge = 1,
  Creation = 2,
  Language = 3,
  RoleplayAndConversation = 4,
  Code = 5,
  ToolUsage = 6,
  MathAndInference = 7,
}

export enum PkFeedbackTagType {
  Win = 1,
  Lose = 2,
}

export enum PKRecordStatus {
  Pending = 0,
  StartConversation = 1,
  ReceiveReply = 2,
  FinishVote = 3,
}

export enum PKScene {
  Bot = 0,
  Model = 1,
  All = 2,
}

export enum VoteResult {
  LeftGood = 1,
  RightGood = 2,
  BothGood = 3,
  BothBad = 4,
}

export interface AnenaUser {
  /** 昵称，可重复 */
  name?: string;
  avatar_url?: string;
  /** 用户名，全局唯一 */
  user_name?: string;
}

export interface ArenaBot {
  product_id?: string;
  name?: string;
  description?: string;
  owner?: AnenaUser;
  bot_type?: ArenaBotType;
  icon?: string;
  is_conf_public?: boolean;
  bot_id?: string;
  bot_version?: string;
}

export interface ArenaBotType {
  id?: string;
  name?: string;
}

/** 某一期排行榜完整信息 */
export interface ArenaLeaderboard {
  /** 基本信息 */
  basic?: ArenaLeaderboardBasic;
  /** 本期排行榜使用的数据的截止日期时间戳（ms） */
  time_right_bound?: string;
  /** 获得的投票总数 */
  vote_count?: string;
  /** 模型总数 */
  model_count?: string;
  /** 详细信息
模型信息 */
  models?: Array<ArenaModel>;
  /** 排行榜详情 */
  leaderboard_details?: Partial<
    Record<PKScene, Partial<Record<LeaderboardIntention, LeaderboardDetail>>>
  >;
  /** pk的总次数 */
  pk_count?: number;
  /** 本期排行榜使用的数据的开始日期时间戳（ms） */
  time_left_bound?: string;
}

export interface ArenaLeaderboardBasic {
  /** 排行榜id */
  id?: string;
  /** 名字 */
  name?: string;
}

export interface ArenaModel {
  id?: string;
  name?: string;
  company?: string;
  last_know_time?: string;
  temperature?: string;
  frequency_penalty?: string;
  presence_penalty?: string;
  top_p?: string;
  top_k?: string;
  output_format?: string;
  max_token?: string;
  diversity?: string;
  icon?: string;
  /** 详细描述 */
  desc?: string;
  /** 简要描述 */
  brief_desc?: string;
  /** 发布时间 */
  publish_timestamp?: string;
  /** 更新时间 */
  update_timestamp?: string;
  /** 模型厂商信息 */
  company_info?: ArenaModelCompany;
  /** 版本 */
  version?: string;
  /** 状态 */
  status?: ArenaModelStatus;
}

export interface ArenaModelCompany {
  name?: string;
  icon_uri?: string;
  icon_url?: string;
  url?: string;
}

export interface ArenaPKRecord {
  pk_id?: string;
  user_id?: string;
  product_id?: string;
  bot_id?: string;
  bot_version?: string;
  left_model_id?: string;
  left_model?: ArenaModel;
  left_conversation_id?: string;
  right_model_id?: string;
  right_model?: ArenaModel;
  right_conversation_id?: string;
  status?: PKRecordStatus;
  result?: VoteResult;
  result_time?: string;
}

export interface ArenaQuestion {
  bank_id?: string;
  id?: string;
  /** 每次用户选择一道题目后，需要将那道题目的序号ID加入到request的UsedList中 */
  seq_id?: number;
  content?: string;
  refer_answer?: string;
}

export interface ArenaQuestionBank {
  bank_id?: string;
  name?: string;
  description?: string;
  bank_type?: ArenaQuestionBankType;
  has_refer_answer?: boolean;
  icon?: string;
}

export interface ArenaQuestionBankType {
  id?: string;
  name?: string;
}

export interface ArenaRank {
  ranking?: string;
  model_info?: ArenaModel;
  elo_score?: string;
  vote_count?: string;
  confidence?: string;
}

export interface LeaderboardDetail {
  /** 场景 */
  scene?: PKScene;
  /** 子榜分类，主要根据用户query做区分 */
  intention?: LeaderboardIntention;
  /** 某个模型在某期磅单的某个子榜中的排名、分数、置信度等 */
  rankings?: Array<LeaderboardRanking>;
  /** 在排行榜首页展示前x名 */
  display_rank_cnt?: number;
}

export interface LeaderboardRanking {
  /** 模型id */
  model_id?: string;
  /** 模型版本 */
  model_version?: string;
  /** 排名 */
  ranking?: number;
  /** 获得的投票数 */
  vote_count?: number;
  /** 置信度上界 */
  confidence_upper?: string;
  /** 置信度下界 */
  confidence_lower?: string;
  /** 模型elo分 */
  elo_score?: string;
  /** 雷达分 */
  radar_score?: string;
  /** 此模型在当前scene和intention下，参与pk的总次数 */
  pk_count?: number;
}

export interface PkFeedbackTag {
  type?: PkFeedbackTagType;
  name?: string;
}
/* eslint-enable */
