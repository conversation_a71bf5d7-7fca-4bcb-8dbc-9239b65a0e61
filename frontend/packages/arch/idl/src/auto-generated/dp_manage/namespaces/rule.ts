/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** ActionType 执行类型
 NEXT ID: 2 */
export enum ActionType {
  Undefined = 0,
  ConstReturn = 1,
}

/** BizType - 业务线
 NEXT ID: 3 */
export enum BizType {
  Default = 0,
  CozeBot = 1,
  FornaxSlot = 2,
  FornaxPrompt = 3,
}

/** ComboTyppe 组合类型 */
export enum ComboType {
  Undefined = 0,
  /** conditions中都为true, 返回true, 否则false */
  And = 1,
  /** conditions中任一true, 返回true, 否则false */
  Or = 2,
}

/** ConditionType 条件类型
 NEXT ID: 7 */
export enum ConditionType {
  Undefined = 0,
  XTtEnv = 1,
  VersionIdentifier = 2,
  TargetIDs = 3,
  Gray = 4,
  OperatorExpr = 5,
  Combo = 6,
}

/** ConstValueType 常量返回值类型
 NEXT ID: 6 */
export enum ConstValueType {
  Undefined = 0,
  Bool = 1,
  String = 2,
  Int = 3,
  Double = 4,
  StringList = 5,
}

/** ExprType 列出当前支持的表达式类型
 NEXT ID: 3 */
export enum ExprType {
  Undefined = 0,
  ConstExpr = 1,
  VarExpr = 2,
}

/** GrayField - 灰度字段
 NEXT ID: 3 */
export enum GrayFieldType {
  Undefined = 0,
  /** 对应commonArgs中的uid */
  Uid = 1,
  /** 对应commonArgs中的deviceID */
  Did = 2,
  /** 用户自定义字段 */
  CustomKey = 3,
}

/** ChoiceType - 判断条件类型
 NEXT ID: 6 */
export enum OperatorType {
  Undefined = 0,
  /** 等于, lhs == rhs */
  Eq = 1,
  /** in list, lhs in rhs, rhs需要是一个List */
  In = 2,
  /** not in list, lhs not in rhs, rhs需要是一个List */
  NotIn = 3,
  /** 不等于, lhs != rhs */
  NotEq = 4,
  /** 大于, lhs > rhs */
  Gt = 5,
  /** 小于, lhs < rhs */
  Lt = 6,
  /** 大于等于, lhs >= rhs */
  Gte = 7,
  /** 小于等于, lhs <= rhs */
  Lte = 8,
  /** 为空, lhs == nil */
  IsNull = 9,
  /** 不为空, lhs != nil */
  IsNotNull = 10,
  /** 总是为true */
  AlwaysTrue = 11,
}

export enum RuleStatus {
  Undefined = 0,
  Online = 1,
  Offline = 2,
}

/** SchemaType - 数据结构字段类型
 NEXT ID: 6 */
export enum SchemaType {
  Undefined = 0,
  Bool = 1,
  Int = 2,
  Double = 3,
  String = 4,
  Object = 5,
}

/** TargetIDSource 需要判定的目标值数据来源 */
export enum TargetIDSource {
  Undefined = 0,
  /** 约定为traffic参数中的commonArgs.uid */
  Uid = 1,
}

export enum TargetOpType {
  Undefined = 0,
  In = 1,
  NotIn = 2,
}

/** TrafficResultType 返回的结果类型
 NEXT ID: 2 */
export enum TrafficResultType {
  Undefined = 0,
  ConstValue = 1,
}

/** Action - 执行动作定义
 NEXT ID: 3 */
export interface Action {
  actionType?: ActionType;
  constReturnAction?: ConstReturnAction;
}

/** AllowList
 NEXT ID: 3 */
export interface AllowList {
  /** 比较字段 */
  field?: GrayField;
  /** 比较字段值 */
  values?: Array<string>;
}

/** BizSchemaMeta - 描述某个业务线的数据信息
 NEXT ID: 3 */
export interface BizSchemaMeta {
  bizType?: BizType;
  schema?: Record<string, Schema>;
}

/** BlockList
 NEXT ID: 3 */
export interface BlockList {
  /** 比较字段 */
  field?: GrayField;
  /** 比较字段值 */
  values?: Array<string>;
}

/** ComboCondition 组合条件判断 */
export interface ComboCondition {
  /** 组合类型 */
  comboType: ComboType;
  /** 组合条件 */
  conditions?: Array<Condition>;
}

/** CommonArgs 通用参数, 一般请求可能会有的通用参数, 如果有需要传入
 NEXT ID: 5, 12 */
export interface CommonArgs {
  uid?: Int64;
  appID?: number;
  deviceID?: Int64;
  platform?: string;
  /** 指定key的参数 */
  params?: Record<string, string>;
  /** 额外的json参数 */
  extra?: string;
}

/** Condition 判断条件
 NEXT ID: 7 */
export interface Condition {
  /** 判断类型 */
  type?: ConditionType;
  /** conditionType 写入对应字段
metainfo中的x-tt-env 等于 xxx */
  xTtEnv?: string;
  /** 显示传参version      等于 xxx */
  versionIdentifier?: string;
  /** 指定id列表进行in or not in的判断 */
  targetList?: TargetList;
  /** 百分比灰度判断 */
  grayConfig?: GrayConfig;
  /** 计算表达式书写 */
  operatorExpr?: OperatorExpr;
  /** 组合条件 */
  comboCondition?: ComboCondition;
}

/** ConstExpr 常量表达式，会根据常量的类型返回对应的值
 NEXT ID: 3 */
export interface ConstExpr {
  valueType: ConstValueType;
  value?: ConstValue;
}

/** ConstReturnAction 常量返回类型
 NEXT ID: 4 */
export interface ConstReturnAction {
  key?: string;
  valueType?: ConstValueType;
  value?: ConstValue;
}

/** ConstValue 常量的实际值
 NEXT ID: 6 */
export interface ConstValue {
  boolValue?: boolean;
  stringValue?: string;
  intValue?: Int64;
  doubleValue?: number;
  stringListValue?: Array<string>;
}

/** DimensionMeta - 描述某个业务线的数据维度信息
 NEXT ID: 3 */
export interface DimensionMeta {
  bizType?: BizType;
  schema?: Schema;
}

/** Expr 描述了在条件判断过程中执行的表达式
 根据exprType对应不同的表达式内容
 NEXT ID: 4 */
export interface Expr {
  /** 表达式类型 */
  exprType: ExprType;
  /** exprType为ConstExpr时使用 */
  constExpr?: ConstExpr;
  /** exprType为VarExpr时使用 */
  varExpr?: string;
}

/** GrayBucket 百分比灰度配置
 NEXT ID: 4 */
export interface GrayBucket {
  /** 灰度计算字段 */
  field?: GrayField;
  /** 灰度比例, 取[0, size]的大小 */
  inGray?: number;
  /** bucket 大小, 默认10000 */
  size?: number;
}

/** GrayConfig 灰度配置
 目前顺序为 BlockList -> AllowList -> Bucket的校验顺序
 NEXT ID: 4 */
export interface GrayConfig {
  /** 在blockList中的表示不命中 */
  blockList?: BlockList;
  /** 在allowList中的表示命中，未命中的走bucket */
  allowList?: AllowList;
  /** 百分比灰度配置 */
  bucket?: GrayBucket;
}

export interface GrayField {
  /** 灰度字段类型 */
  type: GrayFieldType;
  /** 自定义字段json path */
  customFieldPath?: string;
}

/** OperatorExpr 运算表达式
 例如: 对于运算操作 x > y, x是左参数lhs, y是右参数rhs
 NEXT ID: 4 */
export interface OperatorExpr {
  /** 条件类型 */
  operator: OperatorType;
  /** 运算操作的左侧表达式 */
  lhs?: Expr;
  /** 运算操作的右侧表达式 */
  rhs?: Expr;
}

/** Rule - 业务配置规则
 NEXT ID: 6 */
export interface Rule {
  ruleID?: Int64;
  /** 业务线 */
  bizType?: BizType;
  /** 规则配置的维度，会是根据schema的一个json object */
  dimension?: string;
  /** 判断条件 */
  condition?: Condition;
  /** condition pass后的执行动作 */
  action?: Action;
  /** 业务自定义标签，用于区分同一个bizType和dimension下不同场景的规则 */
  tag?: string;
  /** 规则状态 */
  status?: RuleStatus;
  /** 操作人ID */
  operator?: string;
  /** 更新时间 */
  updateTime?: Int64;
  /** 创建时间 */
  createTime?: Int64;
  /** 优先级, 默认0, 同一dimension下, 数字越大优先级越高 */
  priority?: number;
}

/** Schema - 描述维度的数据结构
 NEXT ID: 7 */
export interface Schema {
  type?: SchemaType;
  /** 业务方注册时可自定义名称 */
  name?: string;
  children?: Array<Schema>;
  /** 对于dimension类型的 filePath要精确到叶子结点，其他类型的可以是Object级别的路径 */
  fieldPath?: string;
  isDimension?: boolean;
  /** 是否必须字段，参与校验 isDimension=true时 isRequired=true, isDimension=false时，isRequired按需 */
  isRequired?: boolean;
}

/** SystemArgs 流量参数
 NEXT ID: 2 */
export interface SystemArgs {
  /** 版本标识 */
  versionIdentifier?: string;
}

/** TargetList 根据op判断targetIDSource是否包含在targetIDs中
 NEXT ID: 4 */
export interface TargetList {
  op?: TargetOpType;
  targetIDs?: Array<string>;
  targetIDSource?: TargetIDSource;
}

/** Traffic 流量信息
 NEXT ID: 5 */
export interface Traffic {
  bizType: BizType;
  /** 业务通用参数，是一个json object */
  dimension?: string;
  /** 通用流量参数 */
  commonArgs?: CommonArgs;
  /** 系统流量参数 */
  systemArgs?: SystemArgs;
}

/** TrafficResult 流量判断结果
 NEXT ID: 4 */
export interface TrafficResult {
  hit?: boolean;
  resultType?: TrafficResultType;
  constValue?: TrafficResultConstValue;
}

/** TrafficResultConstValue 返回常量值
 NEXT ID: 3 */
export interface TrafficResultConstValue {
  valueType?: ConstValueType;
  value?: ConstValue;
}

/** TrafficV2 流量信息
 NEXT ID: 5 */
export interface TrafficV2 {
  bizType: BizType;
  /** 通用流量参数 */
  commonArgs?: CommonArgs;
  /** 系统流量参数 */
  systemArgs?: SystemArgs;
  /** 业务流量参数 json object */
  bizArgs?: string;
}
/* eslint-enable */
