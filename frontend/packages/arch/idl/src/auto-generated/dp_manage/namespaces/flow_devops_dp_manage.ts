/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as model_arena from './model_arena';
import * as multi_version from './multi_version';

export type Int64 = string | number;

/** 分支 */
export enum Branch {
  Undefined = 0,
  /** 草稿 */
  PersonalDraft = 1,
  /** space草稿 */
  Base = 2,
  /** 发布版本 */
  Publish = 3,
}

export enum DiffActionType {
  Unknown = 0,
  Add = 1,
  Delete = 2,
  Modify = 3,
  Remove = 4,
  /** 不变，空更新 */
  Remain = 5,
}

/** - env boe-online环境的隔离(暂无)
 - publish 草稿态-发布态的隔离
 - version 版本之间的隔离，个人草稿之间，不同渠道之间都属于这个粒度 */
export enum DiffIsolation {
  Env = 10,
  Publish = 20,
  Version = 30,
}

export enum DiffMode {
  Default = 0,
  OnlyDetermineDiff = 1,
}

export enum DiffScene {
  BotDraft = 0,
}

export enum DiffStyle {
  Default = 0,
  /** 文本详情 */
  TextDetail = 1,
}

export enum Env {
  Online = 1,
  Submit = 2,
  Draft = 3,
}

export enum ResourceType {
  Bot = 1,
  Workflow = 2,
}

export enum TopoType {
  AgentFlow = 1,
  Agent = 2,
  Workflow = 3,
}

export interface ArenaChatBreakMsgReq {
  conversation_id?: string;
  query_message_id?: string;
  answer_message_id?: string;
  broken_pos?: number;
  local_message_id?: string;
  Base?: base.Base;
}

export interface ArenaChatBreakMsgResp {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaChatStreamReq {
  bot_id?: string;
  conversation_id: string;
  bot_version?: string;
  user?: string;
  query: string;
  extra?: Record<string, string>;
  /** 文件 file 图片 image 等 */
  content_type?: string;
  /** 重试消息id */
  regen_message_id?: string;
  /** 前端本地的message_id 在extra_info 里面透传返回 */
  local_message_id?: string;
  insert_history_message_list?: Array<string>;
  device_id?: string;
  space_id?: string;
  toolList?: Array<Tool>;
  Origin?: string;
  Host?: string;
}

export interface ArenaChatStreamResp {}

export interface ArenaGetConfReq {
  Base?: base.Base;
}

export interface ArenaGetConfResp {
  data?: ArenaGetConfRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaGetConfRespData {
  /** 空bot的product id */
  blank_bot_product_id?: string;
  bot_types?: Array<model_arena.ArenaBotType>;
  question_bank_types?: Array<model_arena.ArenaQuestionBankType>;
  leaderboards?: Array<model_arena.ArenaLeaderboardBasic>;
  /** 是否有权限查看排行榜 */
  access_to_leaderboard?: boolean;
}

export interface ArenaGetModelListReq {
  Host?: string;
  Base?: base.Base;
}

export interface ArenaGetModelListResp {
  data?: ArenaGetModelListRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaGetModelListRespData {
  /** 模型信息 */
  models?: Array<model_arena.ArenaModel>;
}

export interface ArenaLeaderBoardCreateNewVersionRespData {
  version?: string;
}

export interface ArenaLeaderBoardGetReq {
  Base?: base.Base;
}

export interface ArenaLeaderBoardGetResp {
  data?: ArenaLeaderBoardGetRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaLeaderBoardGetRespData {
  /** 榜单公开时间 */
  reveal_time?: string;
  /** 按照1-n排名的顺序返回 */
  ranks?: Array<model_arena.ArenaRank>;
  /** 本期榜单的投票统计截止时间 */
  data_ddl_time?: string;
  /** 投票数 */
  vote_count?: string;
}

export interface ArenaLeaderboardGetV2Req {
  /** 排行榜id */
  id?: string;
  Host?: string;
  Base?: base.Base;
}

export interface ArenaLeaderboardGetV2Resp {
  data?: ArenaLeaderboardGetV2RespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaLeaderboardGetV2RespData {
  arena_leaderboard?: model_arena.ArenaLeaderboard;
}

export interface ArenaListQuestionBankReq {
  bank_type_id?: string;
  page_size?: number;
  page_num?: number;
  Base?: base.Base;
}

export interface ArenaListQuestionBankResp {
  data?: ArenaListQuestionBankRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaListQuestionBankRespData {
  banks?: Array<model_arena.ArenaQuestionBank>;
  has_more?: boolean;
}

export interface ArenaRandQuestionReq {
  bank_id?: string;
  count?: number;
  used_seq_id_list?: Array<number>;
  Base?: base.Base;
}

export interface ArenaRandQuestionResp {
  data?: ArenaRandQuestionRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaRandQuestionRespData {
  questions?: Array<model_arena.ArenaQuestion>;
  /** 数组内的seqid=req中的seqid+本次随机到的seqid */
  used_seq_id_list?: Array<number>;
}

export interface ArenaRegisterModelRespData {
  Version?: string;
}

export interface ArenaSubmitPkFeedbackTagReq {
  pk_id: string;
  left_model_tags?: Array<string>;
  right_model_tags?: Array<string>;
  Base?: base.Base;
}

export interface ArenaSubmitPkFeedbackTagResp {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaSubmitPkFeedbackTextReq {
  pk_id: string;
  feedback_text?: string;
  Base?: base.Base;
}

export interface ArenaSubmitPkFeedbackTextResp {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaUpdateQuestionBankRespData {
  AddCount?: number;
  UpdateCount?: number;
  DeleteCount?: number;
}

export interface ArenaUpdateQuestionRespData {
  AddCount?: number;
  UpdateCount?: number;
  DeleteCount?: number;
}

export interface ArenaVoteReq {
  pk_id?: string;
  result?: model_arena.VoteResult;
  Base?: base.Base;
}

export interface ArenaVoteResp {
  data?: ArenaVoteRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ArenaVoteRespData {
  left_model?: model_arena.ArenaModel;
  right_model?: model_arena.ArenaModel;
  left_total_votes?: string;
  right_total_votes?: string;
}

export interface BotDiff3WayReq {
  space_id: string;
  bot_id: string;
  /** 当前用户草稿 */
  current_version: BotVersion;
  /** 目标space草稿 */
  target_version: BotVersion;
  /** 文本行级diff时传diff_template_v2 */
  template_key?: string;
  Base?: base.Base;
}

export interface BotDiff3WayResp {
  data?: BotDiff3WayRespDate;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface BotDiff3WayRespDate {
  diff_res?: Array<DiffDisplay3WayNode>;
  /** bot的详情，merge时用 */
  origin_bot_dl: string;
  /** 目标分支的版本号 */
  target_version: string;
  /** 最新的公共草稿的版本 */
  latest_commit_version?: string;
  BaseResp?: base.BaseResp;
}

export interface BotDiffReq {
  space_id: string;
  bot_id: string;
  left: BotVersion;
  right: BotVersion;
  filter?: DiffFilter;
  /** 草稿diff时传diff_template_v2，发布diff时传diff_template_when_publish_v2 */
  template_key?: string;
  Base?: base.Base;
}

export interface BotDiffResp {
  data?: BotDiffRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface BotDiffRespData {
  diff_display_node?: Array<DiffDisplayNode>;
  origin_bot_dl: string;
  /** 公共分支的版本号 */
  target_version?: string;
}

export interface BotMergeRequest {
  space_id: string;
  bot_id: string;
  diff_res?: Array<DiffDisplayNode>;
  origin_bot_dl: string;
  Base?: base.Base;
}

export interface BotMergeResponse {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface BotVersion {
  branch?: Branch;
  version_id?: string;
  connector_id?: string;
}

export interface CheckBotInReviewWhiteListRequest {
  space_id: Int64;
  bot_id: Int64;
  Base?: base.Base;
}

export interface CheckBotInReviewWhiteListResponse {
  in_white_list?: boolean;
  BaseResp?: base.BaseResp;
}

export interface CheckHookConfigRequest {
  psm: string;
  Base?: base.Base;
}

export interface CheckHookConfigResponse {
  psm_check_result?: boolean;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CozeIDEPluginFgRequest {
  Base?: base.Base;
}

export interface CozeIDEPluginFgResponse {
  enable_multi_version?: boolean;
  enable_gray?: boolean;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CreateArenaPKReq {
  /** 当随机bot or 选择bot时，下方所有参数都必填；当直接评测模型时，都不用填 */
  product_id?: string;
  bot_id?: string;
  bot_version?: string;
  Host?: string;
  Base?: base.Base;
}

export interface CreateArenaPKResp {
  data?: CreateArenaPKRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface CreateArenaPKRespData {
  pk_id?: string;
  bot_id?: string;
  bot_version?: string;
  left_conversation_id?: string;
  right_conversation_id?: string;
  left_feedback_tags?: Array<model_arena.PkFeedbackTag>;
  right_feedback_tags?: Array<model_arena.PkFeedbackTag>;
}

export interface DiffDisplay3WayNode {
  /** 节点展示名称 */
  display_name: string;
  /** 当前分支相对公共祖先，该节点发生的变化 */
  diff_res_current?: DiffDisplayAction;
  /** 目标合入分支相对公共祖先，该节点发生的变化 */
  diff_res_target?: DiffDisplayAction;
  /** 子节点列表 */
  sub_nodes?: Array<DiffDisplay3WayNode>;
  /** 该节点是否冲突 */
  is_conflict?: boolean;
}

export interface DiffDisplayAction {
  action: DiffActionType;
  /** 用于前端展示的旧值 */
  display_left?: string;
  /** 用于前端展示的新值，如果旧值和新值都为空字符串，则不展示 */
  display_right?: string;
  /** 透传即可，后端UpdateBotDraft时需用到 */
  origin_left?: string;
  /** 透传即可，后端UpdateBotDraft时需用到 */
  origin_right?: string;
  /** 记录用户的merge结果 */
  selected_value?: string;
  /** 透传即可，后端UpdateBotDraft时需用到 */
  json_path?: string;
  /** 如果有此值，最终展示以该字段为准 */
  overwrite_display?: string;
  /** diff风格，为1时说明当前节点支持查看文本详情的diff */
  diff_style?: DiffStyle;
  /** 手动merge时merge结果记录在这个字段中，此时不需要赋值selectedValue */
  manual_merge_value?: string;
}

export interface DiffDisplayNode {
  /** 节点展示名称 */
  display_name: string;
  /** 该节点发生的变化 */
  diff_res?: DiffDisplayAction;
  /** 子节点列表 */
  sub_nodes?: Array<DiffDisplayNode>;
}

export interface DiffFilter {
  Isolation?: DiffIsolation;
}

export interface Edge {
  edge_id?: string;
  edge_name?: string;
  source_node_id?: string;
  target_node_id?: string;
}

export interface GetDiffRespData {
  diff_display_node?: Array<DiffDisplayNode>;
}

export interface GetMultiVersionActiveListRequest {
  space_id: Int64;
  bot_id: Int64;
  /** 默认从0开始 */
  page?: number;
  /** 默认20条 */
  size?: number;
  /** 发布类型 */
  version_type?: multi_version.VersionType;
  /** 查询发布类型 */
  query_type?: multi_version.VersionType;
  /** 指定生效的版本, commitVersion */
  version?: Int64;
  Base?: base.Base;
}

export interface GetMultiVersionActiveListResponse {
  active_rules?: Array<multi_version.ActiveRule>;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetMultiVersionAllowedConnectorsRequest {
  space_id: Int64;
  bot_id: Int64;
  Base?: base.Base;
}

export interface GetMultiVersionAllowedConnectorsResponse {
  allowed_connectors?: Array<multi_version.AllowedConnector>;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetMultiVersionLatestParamsRequest {
  space_id: Int64;
  bot_id: Int64;
  Base?: base.Base;
}

export interface GetMultiVersionLatestParamsResponse {
  params_map?: Partial<Record<multi_version.VersionType, string>>;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetPKRecordRespData {
  PKRecord?: model_arena.ArenaPKRecord;
}

export interface GetTopoInfoReq {
  space_id: string;
  resource_id: string;
  version: string;
  env: Env;
  resource_type: ResourceType;
  Base?: base.Base;
}

export interface GetTopoInfoResp {
  data?: TopoInfo;
  BaseResp?: base.BaseResp;
}

export interface InactivateMultiVersionRequest {
  space_id: Int64;
  bot_id: Int64;
  rule_id: Int64;
  Base?: base.Base;
}

export interface InactivateMultiVersionResponse {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListArenaBotReq {
  /** 1选填，不传就代表获得全部类型，1-3一起使用，分页获得全部or某种类型的bot列表 */
  bot_type_id?: string;
  page_size?: number;
  page_number?: number;
  /** 4单独使用，获得所有分类，且每个分类随机返回3个bot */
  rand3?: boolean;
  Base?: base.Base;
}

export interface ListArenaBotResp {
  data?: ListArenaBotRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListArenaBotRespData {
  bots?: Array<model_arena.ArenaBot>;
  has_more?: boolean;
}

export interface ListCurrentPublishVersionsRequest {
  space_id: Int64;
  bot_id: Int64;
  Base?: base.Base;
}

export interface ListCurrentPublishVersionsResponse {
  versions?: Array<multi_version.BotPublishVersion>;
  BaseResp?: base.BaseResp;
}

export interface ListHistoryVersionsRequest {
  space_id: Int64;
  bot_id: Int64;
  /** 不传则返回所有类型的 */
  version_types?: Array<multi_version.HistoryVersionType>;
  /** 不传则返回所有渠道 */
  connector_ids?: Array<string>;
  Base?: base.Base;
}

export interface ListHistoryVersionsResponse {
  versions?: Array<multi_version.BotHistoryVersion>;
  BaseResp?: base.BaseResp;
}

export interface ListOperationHistoryRequest {
  space_id: Int64;
  bot_id: Int64;
  /** 不传则返回所有类型的 */
  version_types?: Array<multi_version.HistoryVersionType>;
  /** 不传则返回所有渠道 */
  connector_ids?: Array<string>;
  Base?: base.Base;
}

export interface ListOperationHistoryResponse {
  histories?: Array<multi_version.BotOperationHistory>;
  BaseResp?: base.BaseResp;
}

export interface MultiVersionUpdateKey {
  bot_id?: Int64;
  connector_id?: string;
}

export interface Node {
  node_id?: string;
  resource_name?: string;
  resource_kind?: Int64;
  resource_id?: string;
}

export interface parametersStruct {
  value?: string;
  /** "uri" */
  resource_type?: string;
}

export interface PingReq {
  PingMessage: string;
}

export interface PingResp {
  PongMessage: string;
}

export interface PublishMultiVersionRequest {
  space_id: Int64;
  bot_id: Int64;
  connector_ids: Array<string>;
  version_type: multi_version.VersionType;
  /** 发布幂等 */
  publish_id: string;
  x_tt_env?: string;
  version_identifier?: string;
  /** 标识用户身份的ID列表 */
  gray_id_list?: Array<string>;
  /** pre发布到灰度发布时需要指定commitversion */
  commit_version?: string;
  /** ppe发布到灰度发布时需要 */
  bot_version?: string;
  /** 灰度配置 */
  gray_config?: multi_version.GrayConfig;
  Base?: base.Base;
}

export interface PublishMultiVersionResponse {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface RandBotIDReq {
  Base?: base.Base;
}

export interface RandBotIDResp {
  data?: RandBotIDRespData;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface RandBotIDRespData {
  product_id?: string;
}

export interface RecordChangelogRequest {
  bot_id: string;
  llm_result: string;
  user_result: string;
  publish_id: string;
  space_id?: string;
  Base?: base.Base;
}

export interface RecordChangelogResponse {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface RollbackBotRequest {
  space_id: Int64;
  bot_id: Int64;
  connector_ids: Array<string>;
  /** 回滚到的commitVersion */
  commit_version?: string;
  /** 回滚到的botVersion(用于校验) */
  bot_version?: string;
  /** 幂等 */
  publish_id?: string;
  Base?: base.Base;
}

export interface RollbackBotResponse {
  BaseResp?: base.BaseResp;
}

export interface Tool {
  plugin_id?: string;
  parameters?: Record<string, parametersStruct>;
  api_name?: string;
}

export interface TopoInfo {
  topo_type?: TopoType;
  nodes?: Array<Node>;
  edges?: Array<Edge>;
}

export interface UpdateGrayIDListRequest {
  space_id: Int64;
  bot_id: Int64;
  rule_id?: Int64;
  gray_id_list?: Array<string>;
  gray_config?: multi_version.GrayConfig;
  Base?: base.Base;
}

export interface UpdateGrayIDListResponse {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
