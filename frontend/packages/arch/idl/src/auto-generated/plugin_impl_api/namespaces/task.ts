/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface CreateDelayTaskRequest {
  /** 延迟多少分钟 */
  delay_min: Int64;
  task_content: string;
  user_question: string;
}

export interface CreateDelayTaskResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  action_for_playground?: string;
}

export interface CreatePeriodicTaskRequest {
  cron_expression: string;
  terminate_condition?: string;
  task_content: string;
  user_question: string;
  time_zone?: string;
}

export interface CreatePeriodicTaskResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  action_for_playground?: string;
}

export interface CreateTimedTaskRequest {
  time_to_execute: string;
  task_content: string;
  user_question: string;
  time_zone?: string;
  /** 绝对时间1，相对时间2 */
  absolute?: Int64;
}

export interface CreateTimedTaskResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  action_for_playground?: string;
}

export interface GetPresetTaskSwitchRequest {}

export interface GetPresetTaskSwitchResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  switch_status?: number;
}

export interface GetTaskListRequest {}

export interface GetTaskListResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
  task_list_item?: Array<TaskInfo>;
}

export interface RemoveAllTaskRequest {}

export interface RemoveAllTaskResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
}

export interface RemoveTaskRequest {
  task_id: string;
}

export interface RemoveTaskResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
}

export interface TaskInfo {
  task_id?: string;
  user_question?: string;
  create_time?: string;
  next_time?: string;
  action_for_playground?: string;
  status?: number;
  /** 0非预设 1预设(测试) 2预设(发布) */
  preset_type?: number;
  /** 终止条件 */
  terminate_condition?: string;
  /** 任务内容 */
  task_content?: string;
  /** 周期任务crontab表达式 */
  cron_expression?: string;
  time_zone?: string;
}

export interface UpdatePresetTaskSwitchRequest {
  /** 0默认 1打开 2关闭 */
  switch_status: number;
}

export interface UpdatePresetTaskSwitchResponse {
  response_type?: string;
  template_id?: string;
  response_for_model?: string;
  code?: number;
  msg?: string;
  error_info?: string;
}
/* eslint-enable */
