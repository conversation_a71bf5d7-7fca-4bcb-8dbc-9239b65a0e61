/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface AppendMemoryRequest {
  keyword: string;
  value: string;
}

export interface AppendMemoryResponse {
  status?: string;
  reason?: string;
}

export interface GetKVMemoryRequest {
  /** 查询关键字列表 */
  keywords?: Array<string>;
}

export interface GetKVMemoryResponse {
  status?: string;
  reason?: string;
  data?: Array<KVItem>;
}

export interface KVItem {
  keyword: string;
  value: string;
}

export interface KVListItem {
  keyword: string;
  value?: Array<string>;
}

export interface ListMemoryRequest {
  keywords?: Array<string>;
}

export interface ListMemoryResponse {
  status?: string;
  reason?: string;
  data?: Array<KVListItem>;
}

export interface SetKVMemoryRequest {
  data?: Array<KVItem>;
}

export interface SetKVMemoryResponse {
  status?: string;
  reason?: string;
}
/* eslint-enable */
