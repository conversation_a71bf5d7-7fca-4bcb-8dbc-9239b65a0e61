/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bill from './namespaces/bill';
import * as chunking from './namespaces/chunking';
import * as dataset from './namespaces/dataset';
import * as encyclopedia from './namespaces/encyclopedia';
import * as file_code from './namespaces/file_code';
import * as kvmemory from './namespaces/kvmemory';
import * as life from './namespaces/life';
import * as musiclm from './namespaces/musiclm';
import * as news from './namespaces/news';
import * as table from './namespaces/table';
import * as task from './namespaces/task';
import * as toutiao from './namespaces/toutiao';
import * as twitter from './namespaces/twitter';
import * as waimai from './namespaces/waimai';
import * as waimai_ExchangeTokenResponse from './namespaces/waimai_ExchangeTokenResponse';
import * as waimai_FoodListResponse from './namespaces/waimai_FoodListResponse';
import * as waimai_ShopcartOperationResponse from './namespaces/waimai_ShopcartOperationResponse';

export {
  bill,
  chunking,
  dataset,
  encyclopedia,
  file_code,
  kvmemory,
  life,
  musiclm,
  news,
  table,
  task,
  toutiao,
  twitter,
  waimai,
  waimai_ExchangeTokenResponse,
  waimai_FoodListResponse,
  waimai_ShopcartOperationResponse,
};
export * from './namespaces/bill';
export * from './namespaces/chunking';
export * from './namespaces/dataset';
export * from './namespaces/encyclopedia';
export * from './namespaces/file_code';
export * from './namespaces/kvmemory';
export * from './namespaces/life';
export * from './namespaces/musiclm';
export * from './namespaces/news';
export * from './namespaces/table';
export * from './namespaces/task';
export * from './namespaces/toutiao';
export * from './namespaces/twitter';
export * from './namespaces/waimai';
export * from './namespaces/waimai_ExchangeTokenResponse';
export * from './namespaces/waimai_FoodListResponse';
export * from './namespaces/waimai_ShopcartOperationResponse';

export type Int64 = string | number;

export enum ChunkingStrategy {
  /** 基于plain text切分 */
  PLAIN_TEXT = 0,
  /** 基于[paragraph/段落]的切分 */
  PARAGRAPH = 1,
  /** 基于文档逻辑结构（文档树/章节）切分 */
  DOC_TREE = 2,
  /** 基于文档的语义标签(如摘要、引言等)切分 */
  SEMANTIC = 3,
}

export default class PluginImplApiService<T> {
  private request: any = () => {
    throw new Error('PluginImplApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /waimai/oauth/authorize */
  Authorize(
    req: waimai.AuthorizeRequest,
    options?: T,
  ): Promise<waimai.AuthorizeResponse> {
    const _req = req;
    const url = this.genBaseURL('/waimai/oauth/authorize');
    const method = 'GET';
    const params = {
      response_type: _req['response_type'],
      client_id: _req['client_id'],
      redirect_uri: _req['redirect_uri'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /waimai/oauth/exchange_token */
  ExchangeToken(
    req: waimai.ExchangeTokenRequest,
    options?: T,
  ): Promise<waimai.ExchangeTokenResponse> {
    const _req = req;
    const url = this.genBaseURL('/waimai/oauth/exchange_token');
    const method = 'POST';
    const data = { code: _req['code'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /waimai/restaurant_list */
  RestaurantList(
    req?: waimai.RestaurantListRequest,
    options?: T,
  ): Promise<waimai.RestaurantListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/restaurant_list');
    const method = 'GET';
    const params = {
      query: _req['query'],
      longitude: _req['longitude'],
      latitude: _req['latitude'],
      sortKey: _req['sortKey'],
      food: _req['food'],
      restaurant_types: _req['restaurant_types'],
    };
    return this.request({ url, method, params }, options);
  }

  /** GET /waimai/food_list */
  FoodList(
    req: waimai.FoodListRequest,
    options?: T,
  ): Promise<waimai.FoodListResponse> {
    const _req = req;
    const url = this.genBaseURL('/waimai/food_list');
    const method = 'GET';
    const params = {
      restaurant_id: _req['restaurant_id'],
      longitude: _req['longitude'],
      latitude: _req['latitude'],
      query_spu_ids: _req['query_spu_ids'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /waimai/submit_order */
  SubmitOrder(
    req?: waimai.SubmitOrderRequest,
    options?: T,
  ): Promise<waimai.SubmitOrderResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/submit_order');
    const method = 'POST';
    const data = {
      restaurant_id: _req['restaurant_id'],
      user_caution: _req['user_caution'],
      address_id: _req['address_id'],
      token: _req['token'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/order_again */
  OrderAgain(
    req?: waimai.OrderAgainRequest,
    options?: T,
  ): Promise<waimai.OrderAgainResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/order_again');
    const method = 'POST';
    const data = { order_id: _req['order_id'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /waimai/order_list */
  OrderList(
    req?: waimai.OrderListRequest,
    options?: T,
  ): Promise<waimai.OrderListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/order_list');
    const method = 'GET';
    const params = { cursor: _req['cursor'] };
    return this.request({ url, method, params }, options);
  }

  /** GET /waimai/address_list */
  AddressList(
    req?: waimai.AddressListRequest,
    options?: T,
  ): Promise<waimai.AddressListResponse> {
    const url = this.genBaseURL('/waimai/address_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /waimai/save_address */
  SaveAddress(
    req: waimai.SaveAddressRequest,
    options?: T,
  ): Promise<waimai.SaveAddressResponse> {
    const _req = req;
    const url = this.genBaseURL('/waimai/save_address');
    const method = 'POST';
    const data = {
      address_id: _req['address_id'],
      name: _req['name'],
      phone: _req['phone'],
      address: _req['address'],
      longitude: _req['longitude'],
      latitude: _req['latitude'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/shopcart_operation */
  ShopcartOperation(
    req?: waimai.ShopcartOperationRequest,
    options?: T,
  ): Promise<waimai.ShopcartOperationResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/shopcart_operation');
    const method = 'POST';
    const data = {
      restaurant_id: _req['restaurant_id'],
      shopcart_operation: _req['shopcart_operation'],
      food_list: _req['food_list'],
      user_caution: _req['user_caution'],
      address_id: _req['address_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/shopping_cart */
  ShoppingCart(
    req?: waimai.ShoppingCartRequest,
    options?: T,
  ): Promise<waimai.ShoppingCartResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/shopping_cart');
    const method = 'POST';
    const data = {
      restaurant_id: _req['restaurant_id'],
      shopcart_operation: _req['shopcart_operation'],
      user_caution: _req['user_caution'],
      address_id: _req['address_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/shopping_cart/add_food */
  ShoppingCartAddFood(
    req?: waimai.ShoppingCartFoodRequest,
    options?: T,
  ): Promise<waimai.ShoppingCartResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/shopping_cart/add_food');
    const method = 'POST';
    const data = {
      restaurant_id: _req['restaurant_id'],
      food_list: _req['food_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/shopping_cart/update_food */
  ShoppingCartUpdateFood(
    req?: waimai.ShoppingCartFoodRequest,
    options?: T,
  ): Promise<waimai.ShoppingCartResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/shopping_cart/update_food');
    const method = 'POST';
    const data = {
      restaurant_id: _req['restaurant_id'],
      food_list: _req['food_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/shopping_cart/reduce_food */
  ShoppingCartReduceFood(
    req?: waimai.ShoppingCartFoodRequest,
    options?: T,
  ): Promise<waimai.ShoppingCartResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/shopping_cart/reduce_food');
    const method = 'POST';
    const data = {
      restaurant_id: _req['restaurant_id'],
      food_list: _req['food_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/shopping_cart/delete_food */
  ShoppingCartDeleteFood(
    req?: waimai.ShoppingCartFoodRequest,
    options?: T,
  ): Promise<waimai.ShoppingCartResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/waimai/shopping_cart/delete_food');
    const method = 'POST';
    const data = {
      restaurant_id: _req['restaurant_id'],
      food_list: _req['food_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /waimai/script */
  Script(
    req: waimai.ScriptRequest,
    options?: T,
  ): Promise<waimai.ScriptResponse> {
    const _req = req;
    const url = this.genBaseURL('/waimai/script');
    const method = 'POST';
    const data = { job_name: _req['job_name'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /kvmemory/get_keyword_memory
   *
   * KV Memory -------
   */
  GetKVMemory(
    req?: kvmemory.GetKVMemoryRequest,
    options?: T,
  ): Promise<kvmemory.GetKVMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/kvmemory/get_keyword_memory');
    const method = 'POST';
    const data = { keywords: _req['keywords'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /kvmemory/set_keyword_memory */
  SetKVMemory(
    req?: kvmemory.SetKVMemoryRequest,
    options?: T,
  ): Promise<kvmemory.SetKVMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/kvmemory/set_keyword_memory');
    const method = 'POST';
    const data = { data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /kvmemory/list_keyword_memory */
  ListMemory(
    req?: kvmemory.ListMemoryRequest,
    options?: T,
  ): Promise<kvmemory.ListMemoryResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/kvmemory/list_keyword_memory');
    const method = 'POST';
    const data = { keywords: _req['keywords'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /kvmemory/append_keyword_memory */
  AppendMemory(
    req: kvmemory.AppendMemoryRequest,
    options?: T,
  ): Promise<kvmemory.AppendMemoryResponse> {
    const _req = req;
    const url = this.genBaseURL('/kvmemory/append_keyword_memory');
    const method = 'POST';
    const data = { keyword: _req['keyword'], value: _req['value'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /task/create_timed_task
   *
   * ------------------------------task-----------------------
   */
  CreateTimedTask(
    req: task.CreateTimedTaskRequest,
    options?: T,
  ): Promise<task.CreateTimedTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/task/create_timed_task');
    const method = 'POST';
    const data = {
      time_to_execute: _req['time_to_execute'],
      task_content: _req['task_content'],
      user_question: _req['user_question'],
      time_zone: _req['time_zone'],
      absolute: _req['absolute'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /task/create_periodic_task */
  CreatePeriodicTask(
    req: task.CreatePeriodicTaskRequest,
    options?: T,
  ): Promise<task.CreatePeriodicTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/task/create_periodic_task');
    const method = 'POST';
    const data = {
      cron_expression: _req['cron_expression'],
      terminate_condition: _req['terminate_condition'],
      task_content: _req['task_content'],
      user_question: _req['user_question'],
      time_zone: _req['time_zone'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /task/task_list */
  GetTaskList(
    req?: task.GetTaskListRequest,
    options?: T,
  ): Promise<task.GetTaskListResponse> {
    const url = this.genBaseURL('/task/task_list');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /task/remove_task */
  RemoveTask(
    req: task.RemoveTaskRequest,
    options?: T,
  ): Promise<task.RemoveTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/task/remove_task');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /task/remove_all_task */
  RemoveAllTask(
    req?: task.RemoveAllTaskRequest,
    options?: T,
  ): Promise<task.RemoveAllTaskResponse> {
    const url = this.genBaseURL('/task/remove_all_task');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * POST /life/get_city_rank_list_type
   *
   * ------------------------------life-----------------------
   */
  GetCityRankListType(
    req?: life.GetCityRankListTypeRequest,
    options?: T,
  ): Promise<life.GetCityRankListTypeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/life/get_city_rank_list_type');
    const method = 'POST';
    const data = {
      city_code: _req['city_code'],
      city: _req['city'],
      longitude: _req['longitude'],
      latitude: _req['latitude'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /life/get_city_rank_list */
  GetCityRankList(
    req: life.GetCityRankListRequest,
    options?: T,
  ): Promise<life.GetCityRankListResponse> {
    const _req = req;
    const url = this.genBaseURL('/life/get_city_rank_list');
    const method = 'POST';
    const data = {
      city: _req['city'],
      poi_type: _req['poi_type'],
      sub_poi_type: _req['sub_poi_type'],
      district: _req['district'],
      rank_type: _req['rank_type'],
      longitude: _req['longitude'],
      latitude: _req['latitude'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /life/get_poi_detail */
  GetPoiDetail(
    req: life.GetPoiDetailRequest,
    options?: T,
  ): Promise<life.GetPoiDetailResponse> {
    const _req = req;
    const url = this.genBaseURL('/life/get_poi_detail');
    const method = 'POST';
    const data = {
      poi_id: _req['poi_id'],
      longitude: _req['longitude'],
      latitude: _req['latitude'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /life/get_search_list */
  GetSearchList(
    req: life.GetSearchListRequest,
    options?: T,
  ): Promise<life.GetSearchListResponse> {
    const _req = req;
    const url = this.genBaseURL('/life/get_search_list');
    const method = 'POST';
    const data = {
      query: _req['query'],
      city: _req['city'],
      longitude: _req['longitude'],
      latitude: _req['latitude'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /news/search_everything
   *
   * News API -------
   */
  SearchEverything(
    req: news.SearchEverythingRequest,
    options?: T,
  ): Promise<news.SearchEverythingResponse> {
    const _req = req;
    const url = this.genBaseURL('/news/search_everything');
    const method = 'POST';
    const data = { q: _req['q'], language: _req['language'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /music/gen_music
   *
   * MusicLM -------
   */
  GenMusic(
    req: musiclm.GenMusicRequest,
    options?: T,
  ): Promise<musiclm.GenMusicResponse> {
    const _req = req;
    const url = this.genBaseURL('/music/gen_music');
    const method = 'POST';
    const data = { prompt: _req['prompt'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /news/search_ttnews */
  SearchTTNews(
    req: news.SearchTTNewsRequest,
    options?: T,
  ): Promise<news.SearchTTNewsResponse> {
    const _req = req;
    const url = this.genBaseURL('/news/search_ttnews');
    const method = 'POST';
    const data = { q: _req['q'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /table/execute_sql
   *
   * TableExecuteSql API -------
   */
  TableExecuteSql(
    req: table.ExecuteSqlRequest,
    options?: T,
  ): Promise<table.ExecuteSqlResponse> {
    const _req = req;
    const url = this.genBaseURL('/table/execute_sql');
    const method = 'POST';
    const data = { raw_sql: _req['raw_sql'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /dataset/recall
   *
   * Dataset -------
   */
  RecallDataset(
    req: dataset.RecallDatasetRequest,
    options?: T,
  ): Promise<dataset.RecallDatasetResponse> {
    const _req = req;
    const url = this.genBaseURL('/dataset/recall');
    const method = 'POST';
    const data = { question: _req['question'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /task/update_preset_task_switch */
  UpdatePresetTaskSwitch(
    req: task.UpdatePresetTaskSwitchRequest,
    options?: T,
  ): Promise<task.UpdatePresetTaskSwitchResponse> {
    const _req = req;
    const url = this.genBaseURL('/task/update_preset_task_switch');
    const method = 'POST';
    const data = { switch_status: _req['switch_status'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /task/get_preset_task_switch */
  GetPresetTaskSwitch(
    req?: task.GetPresetTaskSwitchRequest,
    options?: T,
  ): Promise<task.GetPresetTaskSwitchResponse> {
    const url = this.genBaseURL('/task/get_preset_task_switch');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** POST /bill/add_bill */
  AddBill(
    req: bill.AddBillRequest,
    options?: T,
  ): Promise<bill.AddBillResponse> {
    const _req = req;
    const url = this.genBaseURL('/bill/add_bill');
    const method = 'POST';
    const data = {
      date: _req['date'],
      category: _req['category'],
      amount: _req['amount'],
      description: _req['description'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /bill/modify_bill */
  ModifyBill(
    req: bill.ModifyBillRequest,
    options?: T,
  ): Promise<bill.ModifyBillResponse> {
    const _req = req;
    const url = this.genBaseURL('/bill/modify_bill');
    const method = 'POST';
    const data = { sql: _req['sql'], force: _req['force'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /bill/delete_bill */
  DeleteBill(
    req: bill.DeleteBillRequest,
    options?: T,
  ): Promise<bill.DeleteBillResponse> {
    const _req = req;
    const url = this.genBaseURL('/bill/delete_bill');
    const method = 'POST';
    const data = { sql: _req['sql'], force: _req['force'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /bill/analyse_bill */
  AnalyseBill(
    req: bill.AnalyseBillRequest,
    options?: T,
  ): Promise<bill.AnalyseBillResponse> {
    const _req = req;
    const url = this.genBaseURL('/bill/analyse_bill');
    const method = 'POST';
    const data = { sql: _req['sql'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /twitter/create
   *
   * Twitter -------
   */
  CreateTwitter(
    req: twitter.CreateTwitterRequest,
    options?: T,
  ): Promise<twitter.CreateTwitterResponse> {
    const _req = req;
    const url = this.genBaseURL('/twitter/create');
    const method = 'POST';
    const data = { content: _req['content'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /encyclopedia/search_encyclopedia
   *
   * 百科 -------
   */
  SearchEncyclopedia(
    req: encyclopedia.SearchEncyclopediaRequest,
    options?: T,
  ): Promise<encyclopedia.SearchEncyclopediaResponse> {
    const _req = req;
    const url = this.genBaseURL('/encyclopedia/search_encyclopedia');
    const method = 'POST';
    const data = { keyword: _req['keyword'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /life/get_poi_rate_feed */
  GetPoiRateFeed(
    req: life.GetPoiRateFeedRequest,
    options?: T,
  ): Promise<life.GetPoiRateFeedResponse> {
    const _req = req;
    const url = this.genBaseURL('/life/get_poi_rate_feed');
    const method = 'POST';
    const data = { poi_id: _req['poi_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /file_code/csv_data_analysis
   *
   * 文件代码分析
   */
  CsvDataAnalysis(
    req?: file_code.CsvDataAnalysisRequest,
    options?: T,
  ): Promise<file_code.CsvDataAnalysisResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/file_code/csv_data_analysis');
    const method = 'POST';
    const data = {
      file_url: _req['file_url'],
      prompt: _req['prompt'],
      model_name: _req['model_name'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /chunking/file_chunking
   *
   * ------------------------------chunking-----------------------
   */
  FileChunking(
    req?: chunking.FileChunkingRequest,
    options?: T,
  ): Promise<chunking.FileChunkingResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/chunking/file_chunking');
    const method = 'POST';
    const data = { file_url: _req['file_url'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /task/create_delay_task */
  CreateDelayTask(
    req: task.CreateDelayTaskRequest,
    options?: T,
  ): Promise<task.CreateDelayTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/task/create_delay_task');
    const method = 'POST';
    const data = {
      delay_min: _req['delay_min'],
      task_content: _req['task_content'],
      user_question: _req['user_question'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
