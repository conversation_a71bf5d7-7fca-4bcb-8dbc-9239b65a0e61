/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as common from './namespaces/common';
import * as file from './namespaces/file';
import * as open from './namespaces/open';
import * as task from './namespaces/task';
import * as template from './namespaces/template';

export { base, common, file, open, task, template };
export * from './namespaces/base';
export * from './namespaces/common';
export * from './namespaces/file';
export * from './namespaces/open';
export * from './namespaces/task';
export * from './namespaces/template';

export type Int64 = string | number;

export default class NoticeService<T> {
  private request: any = () => {
    throw new Error('NoticeService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** POST /api/notice/task/delete */
  DeleteTask(
    req: task.DeleteTaskRequest,
    options?: T,
  ): Promise<task.DeleteTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/notice/task/delete');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/notice/upload
   *
   * 文件上传
   */
  UploadFile(
    req?: file.UploadFileRequest,
    options?: T,
  ): Promise<file.UploadFileResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/notice/upload');
    const method = 'POST';
    const data = { file_type: _req['file_type'], data: _req['data'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/template/update */
  UpdateTemplate(
    req: template.UpdateTemplateRequest,
    options?: T,
  ): Promise<template.UpdateTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/notice/template/update');
    const method = 'POST';
    const data = {
      id: _req['id'],
      name: _req['name'],
      content: _req['content'],
      template_type: _req['template_type'],
      template_status: _req['template_status'],
      template_scene: _req['template_scene'],
      bot_id: _req['bot_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/template/list */
  GetTemplateList(
    req?: template.GetTemplateListRequest,
    options?: T,
  ): Promise<template.GetTemplateListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/notice/template/list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      size: _req['size'],
      template_type: _req['template_type'],
      template_status: _req['template_status'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/notice/task/create
   *
   * 任务
   */
  CreateDraftTask(
    req?: task.CreateDraftTaskRequest,
    options?: T,
  ): Promise<task.CreateDraftTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/notice/task/create');
    const method = 'POST';
    const data = { info: _req['info'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/task/update */
  UpdateDraftTask(
    req: task.UpdateDraftTaskRequest,
    options?: T,
  ): Promise<task.UpdateDraftTaskResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/notice/task/update');
    const method = 'POST';
    const data = { task_id: _req['task_id'], info: _req['info'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/task/list */
  GetTaskList(
    req?: task.GetTaskListRequest,
    options?: T,
  ): Promise<task.GetTaskListResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/notice/task/list');
    const method = 'POST';
    const data = {
      content: _req['content'],
      page: _req['page'],
      size: _req['size'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/task/export_fail */
  ExportFailList(
    req: task.ExportFailListRequest,
    options?: T,
  ): Promise<task.ExportFailListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/notice/task/export_fail');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/notice/template/create
   *
   * 模板
   */
  CreateTemplate(
    req: template.CreateTemplateRequest,
    options?: T,
  ): Promise<template.CreateTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/notice/template/create');
    const method = 'POST';
    const data = {
      name: _req['name'],
      content: _req['content'],
      template_type: _req['template_type'],
      template_status: _req['template_status'],
      template_scene: _req['template_scene'],
      bot_id: _req['bot_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/template/delete */
  DeleteTemplate(
    req: template.DeleteTemplateRequest,
    options?: T,
  ): Promise<template.DeleteTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/notice/template/delete');
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/task/submit */
  SubmitTask(
    req?: task.SubmitTaskRequest,
    options?: T,
  ): Promise<task.SubmitTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/notice/task/submit');
    const method = 'POST';
    const data = {
      task_id: _req['task_id'],
      info: _req['info'],
      test_uid_list: _req['test_uid_list'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/task/notice_bots */
  GetNoticeBots(
    req?: task.GetNoticeBotsRequest,
    options?: T,
  ): Promise<task.GetNoticeBotsResponse> {
    const url = this.genBaseURL('/api/notice/task/notice_bots');
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/notice/template/record */
  CreateRecord(
    req: template.CreateRecordRequest,
    options?: T,
  ): Promise<template.CreateRecordResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/notice/template/record');
    const method = 'POST';
    const data = {
      id: _req['id'],
      name: _req['name'],
      content: _req['content'],
      template_scene: _req['template_scene'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/notice/task/stop */
  StopTask(
    req?: task.StopTaskRequest,
    options?: T,
  ): Promise<task.StopTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/notice/task/stop');
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
