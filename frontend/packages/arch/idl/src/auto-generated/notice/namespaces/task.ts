/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export enum MessageType {
  Trigger = 1,
}

export enum NoticeCarrierType {
  /** 历史存量任务 */
  CozeHome = 1,
  /** 通知中心 */
  NoticeCenter = 2,
  /** 关注feed */
  FollowFeed = 3,
}

export enum NoticeIdentityType {
  Bot = 1,
}

export enum NoticeStatus {
  /** 草稿态 */
  Draft = 1,
  /** 待执行 */
  Ready = 2,
  /** 执行中 */
  Executing = 3,
  /** 完成 */
  Done = 4,
  /** 被删除 */
  Deleted = 5,
}

export enum NoticeTimeType {
  /** 立刻 */
  Immediate = 1,
  /** 定时 */
  Timing = 2,
}

export enum NoticeType {
  Single = 1,
  Orientation = 2,
  All = 3,
}

export interface CozeMessage {
  /** 会话id */
  ConversationId: string;
  /** 消息类型 */
  MsgType: MessageType;
  /** 幂等 */
  UniqueKey: string;
  /** 消息体，由playground处理 */
  Payload: string;
  /** botId */
  BotId: Int64;
  /** 额外信息 */
  Extra?: Record<string, string>;
}

export interface CreateDraftTaskData {
  /** task id */
  task_id?: string;
}

export interface CreateDraftTaskRequest {
  info?: TaskInfo;
}

export interface CreateDraftTaskResponse {
  code?: number;
  msg?: string;
  data?: CreateDraftTaskData;
}

export interface DeleteTaskRequest {
  /** task id */
  task_id: string;
}

export interface DeleteTaskResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
}

export interface ExportFailListRequest {
  /** task id */
  task_id: string;
}

export interface ExportFailListResponse {
  file_url?: string;
}

export interface GetNoticeBotsRequest {}

export interface GetNoticeBotsResponse {
  data?: Array<NoticeBot>;
}

export interface GetTaskListData {
  data?: Array<Task>;
  total?: number;
}

export interface GetTaskListRequest {
  /** 内容搜索 */
  content?: string;
  page?: number;
  size?: number;
}

export interface GetTaskListResponse {
  code?: number;
  msg?: string;
  data?: GetTaskListData;
}

export interface NoticeBot {
  bot_id?: string;
  bot_icon_url?: string;
  bot_name?: string;
}

export interface ReportReadData {
  task_id?: Int64;
  user_id?: string;
  read_time?: Int64;
}

export interface StopTaskRequest {
  /** task id */
  task_id?: string;
}

export interface StopTaskResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
}

export interface SubmitTaskRequest {
  /** task id */
  task_id?: string;
  info?: TaskInfo;
  /** 测试账号ID，空列表则正式开始任务 */
  test_uid_list?: Array<string>;
}

export interface SubmitTaskResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
}

export interface Task {
  info?: TaskInfo;
  data?: TaskData;
}

export interface TaskData {
  /** 任务数据
task id */
  task_id?: string;
  /** 成功通知总数 */
  success_count?: number;
  /** 失败通知总数 */
  failed_count?: number;
  /** 通知总数 */
  total_count?: number;
  /** 操作人 */
  last_modify_user?: string;
  /** 通知状态 */
  notice_status?: NoticeStatus;
  /** 创建时间 */
  create_time?: string;
  bot_icon_url?: string;
  bot_name?: string;
  /** 完成时间 */
  finish_time?: string;
}

export interface TaskInfo {
  /** 任务信息 */
  notice_identity_type?: NoticeIdentityType;
  notice_identity?: string;
  notice_carrier_type?: NoticeCarrierType;
  /** 模板 id */
  template_id?: string;
  /** 单个用户ID */
  single_uid?: string;
  /** 用户ID文件 */
  uid_file?: Array<UIDFile>;
  /** 通知内容 */
  notice_content?: string;
  /** 通知类型 */
  notice_type?: NoticeType;
  notice_time_type?: NoticeTimeType;
  /** 定时通知时间 */
  notice_time?: string;
  /** 时区 */
  time_zone?: string;
  user_name?: string;
}

export interface UIDFile {
  uri?: string;
  url?: string;
  file_name?: string;
  size?: string;
}

export interface UpdateDraftTaskRequest {
  /** 任务id */
  task_id: string;
  info?: TaskInfo;
}

export interface UpdateDraftTaskResponse {
  code?: number;
  msg?: string;
  data?: common.EmptyData;
}
/* eslint-enable */
