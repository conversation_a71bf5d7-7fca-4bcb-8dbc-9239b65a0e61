/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum Channel {
  Undefined = 0,
  /** 飞书 */
  Lark = 1,
  /** OpenAPI */
  OpenAPI = 2,
}

export enum LarkReleaseStatus {
  Undefined = 0,
  /** 审核中 */
  InReview = 10,
  /** 审核通过 */
  Approved = 20,
  /** 审核拒绝 */
  ReviewReject = 30,
  /** 发布失败 */
  Error = 40,
}

export enum OpenAPIReleaseStatus {
  Undefined = 0,
  /** 发布成功 */
  Success = 20,
  /** 发布失败 */
  Error = 30,
}

export enum OperationType {
  Undefined = 0,
  /** 创建发布单 */
  Create = 1,
  /** 更新发布单 */
  Update = 2,
  /** 发布渠道 */
  ReleaseChannel = 3,
}

export enum Status {
  Undefined = 0,
  /** 发布中 */
  Publishing = 10,
  /** 发布成功 */
  Success = 20,
  /** 发布失败 */
  Error = 30,
}

export enum TemplateImportStatus {
  Undefined = 0,
  /** 进行中 */
  InProgress = 10,
  /** 成功 */
  Success = 20,
  /** 失败 */
  Error = 30,
  /** 超时 */
  Timeout = 40,
}

export enum TemplateLanguage {
  Unknown = 0,
  Golang = 1,
  Typescript = 2,
  Python = 3,
}

export enum TemplateType {
  Unknown = 0,
  ChatBot = 1,
  OpenAPI = 2,
}

export interface Agent {
  /** agent id */
  id: Int64;
  /** agent name */
  name: string;
  /** agent所属fornax空间id */
  spaceID: Int64;
  /** agent description */
  desc?: string;
  /** agent头像 */
  avatar?: Image;
  /** agent隐式的项目名 */
  projectName: string;
  /** agent模版仓库 */
  repo?: TemplateRepo;
  /** agent ide空间地址 */
  ideSpaceURL?: string;
  /** agent关联的仓库 */
  associatedRepo?: AssociatedRepo;
  /** agent是否发布过 */
  ifReleased?: boolean;
  /** agent faas function信息 */
  faasInfo?: FaaSInfo;
  /** agent飞书应用配置 */
  larkInfo?: LarkInfo;
  /** 创建人 */
  createdBy?: string;
  /** 更新人 */
  updatedBy?: string;
  /** 创建时间 */
  createdAt?: Int64;
  /** 更新时间 */
  updatedAt?: Int64;
}

export interface AgentOpenAPIAccount {
  id?: Int64;
  agentID?: Int64;
  token?: string;
  spaceAccountID?: Int64;
  owner?: string;
  /** 创建时间 */
  createTime?: Int64;
  /** 更新时间 */
  updateTime?: Int64;
}

export interface AgentReleaseInfo {
  id?: Int64;
  agentID?: Int64;
  /** 版本 */
  version?: string;
  /** 版本描述 */
  versionDesc?: string;
  status?: Status;
  avatar?: Image;
  name?: string;
  desc?: string;
  /** 发布人 */
  publisher?: string;
  /** 创建时间 */
  createTime?: Int64;
  /** 更新时间 */
  updateTime?: Int64;
  channels?: Array<Channel>;
  /** Faas相关发布信息 */
  faasInfo?: FaaSInfo;
  /** lark相关发布信息 */
  larkInfo?: LarkInfo;
  /** openAPI发布信息 */
  openAPIInfo?: OpenAPIInfo;
}

export interface AssociatedRepo {
  /** codebase id */
  ID?: Int64;
  /** 仓库名 */
  repoName?: string;
  /** 仓库ssh地址 */
  repoURL?: string;
  /** 分支 */
  branch?: string;
  /** 模板导入状态 */
  templateImportStatus?: TemplateImportStatus;
  /** gitlab project id */
  externalID?: Int64;
}

export interface Avatar {
  region?: string;
  url?: string;
  uri?: string;
}

export interface FaaSCluster {
  /** faas集群 */
  cluster: string;
  /** faas集群所在区域 */
  region: string;
}

export interface FaaSInfo {
  id?: string;
  psm?: string;
  clusters?: Array<FaaSCluster>;
  owner?: string;
}

export interface Image {
  uri: string;
  url: string;
}

export interface LarkInfo {
  appID?: string;
  appSecret?: string;
  versionID?: string;
  larkReleaseStatus?: LarkReleaseStatus;
}

export interface OpenAPIInfo {
  openAPIReleaseStatus?: OpenAPIReleaseStatus;
}

export interface TemplateMetaInfo {
  name?: string;
  description?: string;
  type?: string;
  projectName?: string;
  path?: string;
  tags?: Array<string>;
  avatar?: Array<Avatar>;
  templateType?: string;
  repoURL?: string;
  repoName?: string;
}

export interface TemplateRepo {
  /** 模版仓库名 */
  repoName: string;
  /** 模版仓库地址 */
  repoURL: string;
  /** 模版仓库相对路径 */
  relativePath: string;
  /** 模版语言 */
  language: TemplateLanguage;
  /** 模版类型 */
  templateType?: TemplateType;
}
/* eslint-enable */
