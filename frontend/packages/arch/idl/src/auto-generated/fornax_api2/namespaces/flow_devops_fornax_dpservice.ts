/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as dp from './dp';
import * as model from './model';
import * as flow_devops_evaluation_dataset from './flow_devops_evaluation_dataset';

export type Int64 = string | number;

export interface CheckUserHasAIDPAccountReq {}

export interface CheckUserHasAIDPAccountResp {
  hasAIDPAccount?: boolean;
}

export interface CreateAnnotateTaskReq {
  spaceID: string;
  name: string;
  platform: dp.AnnotatePlatform;
  /** 输入数据集 ID */
  inputDatasetID: string;
  /** 输出数据集名称 */
  outputDatasetName: string;
  aidpConfig?: dp.AIDPConfig;
  /** 字段映射配置 */
  fieldMapConfig?: Array<dp.SendFieldMapping>;
}

export interface CreateAnnotateTaskResp {
  taskID?: string;
}

export interface CreateDataProcessingTaskReq {
  /** 任务名称 */
  name?: string;
  /** 任务类型 */
  taskType?: dp.TaskType;
  /** 输入数据集 */
  inDatasetID?: string;
  /** Deprecated. 输出数据集名称，用outDatasetNames代替 */
  outDatasetName?: string;
  /** 输出数据集Schema */
  outDatasetSchema?: Array<dp.FieldInfo>;
  /** 用户脚本 */
  userScript?: dp.UserScript;
  /** 试运行签名 */
  dryRunID?: string;
  trainingDatasetType?: model.TrainingDatasetType;
  /** 输出数据集列表 */
  outDatasets?: Array<dp.DatasetInfo>;
  /** 输出到数据集的类型。数据集、评测集、结果集 */
  datasetType?: dp.DatasetType;
  /** 空间ID */
  spaceID?: string;
}

export interface CreateDataProcessingTaskResp {
  /** 任务ID */
  taskID?: string;
}

export interface CreateDatasetImportTaskReq {
  datasetID: string;
  fileType?: dp.DatasetFileType;
  dataSource?: dp.DataSource;
  overwrite?: boolean;
  /** 空间ID */
  space_id?: string;
}

export interface CreateDatasetImportTaskResp {
  taskID?: string;
}

export interface DeleteDataProcessingTaskReq {
  task_id?: string;
  /** 空间ID */
  spaceID?: string;
}

export interface DeleteDataProcessingTaskResp {
  taskID?: string;
}

export interface DryRunDataProcessingTaskReq {
  /** 输入数据集ID */
  datasetID?: string;
  /** 用户脚本 */
  userScript?: dp.UserScript;
  /** 样本条数，默认前10条 */
  sampleCount?: number;
  trainingDatasetType?: model.TrainingDatasetType;
  /** 空间ID */
  spaceID?: string;
}

export interface DryRunDataProcessingTaskResp {
  /** 运行结果 */
  results?: Array<dp.ScriptRunResultItem>;
  /** 签名，在保存任务时需要透传到服务端 */
  runID?: string;
  /** 运行结果的Schema */
  schema?: Array<dp.FieldInfo>;
  /** 编译错误 */
  compileError?: string;
  /** 标准输出 */
  stdout?: string;
  /** 标准错误 */
  stderr?: string;
}

export interface ExportDatasetReq {
  spaceID: string;
  datasetID: string;
  columnMappings?: Array<dp.DatasetColumnMapping>;
  /** 导出到新的评测集/结果集/标注集时提供 */
  toDatasetName?: string;
  toDatasetDesc?: string;
  toDatasetPublishOption?: flow_devops_evaluation_dataset.PublishOption;
  /** 创建训练集时必填 */
  toDatasetType?: flow_devops_evaluation_dataset.DatasetType;
  toDatasetTagIDs?: Array<Int64>;
  /** 导出到已有评测集/结果集/标注集时提供 */
  toDatasetID?: string;
  /** 为 true 时覆盖更新 */
  overwrite?: boolean;
}

export interface ExportDatasetResp {
  taskID?: string;
}

export interface GetAnnotateTaskReq {
  taskID: string;
  spaceID: string;
}

export interface GetAnnotateTaskResp {
  taskDetail?: dp.AnnotateTask;
}

export interface GetDatasetImportTaskReq {
  task_id?: string;
  space_id?: string;
}

export interface GetDatasetImportTaskResp {
  task?: dp.DatasetImportTask;
}

export interface GetExportDatasetTaskReq {
  spaceID: string;
  taskID: string;
}

export interface GetExportDatasetTaskResp {
  task: dp.ExportDatasetTask;
}

export interface GetTaskRunLogsReq {
  run_id?: string;
  /** 空间ID */
  space_id?: string;
}

export interface GetTaskRunLogsResp {
  /** 日志源 */
  runLogSourceType?: dp.RunLogSourceType;
  /** 日志内容 */
  content?: string;
}

export interface GetTaskRunsReq {
  task_id?: string;
  /** 空间ID */
  spaceID?: string;
}

export interface GetTaskRunsResp {
  runs?: Array<dp.TaskRun>;
}

export interface GetUserScriptTemplateConfigReq {
  trainingDatasetType?: model.TrainingDatasetType;
}

export interface GetUserScriptTemplateConfigResp {
  configs?: Array<dp.UserScriptTemplateConfig>;
}

export interface ListAnnotateTaskBySpaceReq {
  spaceID: string;
  /** 标注平台任务 ID */
  platformTaskIDs?: Array<string>;
  /** 输入数据集ID */
  inputDatasetIDs?: Array<string>;
  /** 标注平台 */
  platforms?: Array<dp.AnnotatePlatform>;
  /** 任务状态 */
  statuses?: Array<dp.TaskStatus>;
  /** fornax 标注任务 ID */
  taskIDs?: Array<string>;
  /** 任务名称 */
  name?: string;
  /** 创建人 */
  createdBy?: string;
  /** 起始为空，滚动传入 resp 里的 nextCursor */
  cursor?: string;
  /** 默认为 20 */
  pageSize?: string;
}

export interface ListAnnotateTaskBySpaceResp {
  tasks?: Array<dp.AnnotateTask>;
  nextCursor?: string;
  hasMore?: boolean;
}

export interface ListDataProcessingTasksReq {
  name?: string;
  createdBy?: string;
  taskType?: dp.TaskType;
  inDatasetIDs?: Array<string>;
  taskID?: string;
  space_id?: string;
  pageSize?: string;
  nextToken?: string;
}

export interface ListDataProcessingTasksResp {
  tasks?: Array<dp.DataProcessingTask>;
  hasMore?: boolean;
  nextToken?: string;
}

export interface MGetDataProcessingTaskReq {
  taskIDs?: Array<string>;
  /** 空间ID */
  spaceID?: string;
}

export interface MGetDataProcessingTaskResp {
  tasks?: Array<dp.DataProcessingTask>;
}

export interface ProcessCallbackReq {
  runID?: Int64;
  checkpoint?: string;
  extra?: string;
  log?: string;
  'x-fornax-dp-token'?: string;
}

export interface ProcessCallbackResp {}

export interface ReentryTaskRunReq {
  task_run_id?: string;
  forceStart?: boolean;
  /** 空间ID */
  spaceID?: string;
}

export interface ReentryTaskRunResp {
  taskRunID?: string;
}

export interface RefreshAnnotateTaskStatusDetailReq {
  taskID: string;
}

export interface RefreshAnnotateTaskStatusDetailResp {
  taskDetail?: dp.AnnotateTask;
}

export interface RunDataProcessingTaskReq {
  task_id?: string;
  /** 空间ID */
  spaceID?: string;
}

export interface RunDataProcessingTaskResp {
  taskRunID?: string;
}

export interface StopDataProcessingTaskReq {
  task_id?: string;
  /** 空间ID */
  spaceID?: string;
}

export interface StopDataProcessingTaskResp {
  taskRunID?: string;
}

export interface UpdateDataProcessingTaskReq {
  task_id?: string;
  name?: string;
  userScript?: dp.UserScript;
  dryRunID?: string;
  /** 输出数据集Schema */
  outDatasetSchema?: Array<dp.FieldInfo>;
  /** 空间ID */
  spaceID?: string;
}

export interface UpdateDataProcessingTaskResp {
  /** 任务ID */
  taskID?: string;
}
/* eslint-enable */
