/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';

export type Int64 = string | number;

export enum AsyncStatus {
  Running = 1,
  Success = 2,
  Failure = 3,
}

export enum CozeBotInfoType {
  /** 草稿 bot */
  DraftBot = 1,
  /** 商店 bot */
  ProductBot = 2,
}

/** 回调的壳子：
 既作为服务端返回参数
 关联对象时候，又作为前端传参的壳子 */
export interface Object {
  object_type: Int64;
  /** UI上针对评估对象的拉列表页展示的名称,objectMetaName */
  name?: string;
  /** 一方 Agent 中子评估对象的唯一标识和配置等相关信息。建议采用 Json 序列化透传
ObjectMeta 的生产、传递、消费路径：SearchObject(生产方)->评测平台UI->评测平台用户圈选->评测平台服务端->评测对象Playground(消费方)
ObjectMeta 由 评估对象服务方 生成和解析，评测平台仅透传
像内置的接口没有这个字段 */
  object_meta?: string;
  /** 用于筛选哪些object可见 */
  space_id: Int64;
  /** 只用于展示的object信息，例如bot头像 */
  avatar_url?: string;
  /** 回调业务方的env, 前端透传该值，由evaluation解析后执行对应泳道的回调 */
  env?: string;
  /** UI在用例列表展示唯一子对象的id，需回调业务方填 */
  object_meta_id?: string;
  /** UI在用例列表展示，用户在前端选中评测对象后，快照存储用于在用例列表中展示 */
  object_type_name?: string;
  callback_type?: flow_devops_evaluation_entity.CallbackType;
}
/* eslint-enable */
