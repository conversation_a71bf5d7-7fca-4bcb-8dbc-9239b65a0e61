/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agent from './agent';
import * as base from './base';

export type Int64 = string | number;

export interface AgentReleaseRequest {
  info?: agent.AgentReleaseInfo;
  /** 操作类型 */
  operationType: agent.OperationType;
}

export interface AgentReleaseResponse {
  id?: Int64;
}

export interface CheckAgentTokenReq {
  'Fornax-Agent-Token': string;
  agentID: Int64;
  Base?: base.Base;
}

export interface CheckAgentTokenResp {
  ifPass?: boolean;
  BaseResp?: base.BaseResp;
}

export interface CreateAgentReq {
  name: string;
  spaceID: Int64;
  projectName?: string;
  avatar?: agent.Image;
  desc?: string;
  repo?: agent.TemplateRepo;
  /** agent关联的仓库 */
  associatedRepo?: agent.AssociatedRepo;
  faasInfo?: agent.FaaSInfo;
  larkInfo?: agent.LarkInfo;
  'X-Jwt-Token'?: string;
  Base?: base.Base;
}

export interface CreateAgentResp {
  agentID?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DeleteAgentReq {
  agentID: Int64;
  Base?: base.Base;
}

export interface DeleteAgentResp {
  BaseResp?: base.BaseResp;
}

export interface GetAgentByWorkspaceIDReq {
  workspaceID: string;
  Base?: base.Base;
}

export interface GetAgentByWorkspaceIDResp {
  agent?: agent.Agent;
  BaseResp?: base.BaseResp;
}

export interface GetAgentOpenAPITokenReq {
  accountID: Int64;
  agentID: Int64;
  Base?: base.Base;
}

export interface GetAgentOpenAPITokenResp {
  token?: string;
  BaseResp?: base.BaseResp;
}

export interface GetAgentReq {
  agentID: Int64;
  Base?: base.Base;
}

export interface GetAgentResp {
  agent?: agent.Agent;
  BaseResp?: base.BaseResp;
}

export interface GetUserCodebasePermissionRequest {
  repo_id: Int64;
  user_name: string;
  'X-Jwt-Token': string;
  Base?: base.Base;
}

export interface GetUserCodebasePermissionResponse {
  permissions?: string;
  BaseResp?: base.BaseResp;
}

export interface ListAgentOpenAPIAccountsReq {
  agentID: Int64;
  Base?: base.Base;
}

export interface ListAgentOpenAPIAccountsResp {
  accounts?: Array<agent.AgentOpenAPIAccount>;
  BaseResp?: base.BaseResp;
}

export interface ListAgentReleaseRecordRequest {
  agentID: Int64;
  /** 渠道 */
  channels: Array<agent.Channel>;
  /** 起始为空，滚动传入Response里的NextCursor */
  cursor?: Int64;
  pageSize: Int64;
}

export interface ListAgentReleaseRecordResponse {
  infos?: Array<agent.AgentReleaseInfo>;
  nextCursor?: Int64;
  hasMore?: boolean;
  BaseResp?: base.BaseResp;
}

export interface ListAgentsBySpaceReq {
  spaceID: Int64;
  /** 根据名称模糊搜索 */
  name?: string;
}

export interface ListAgentsBySpaceResp {
  agents?: Array<agent.Agent>;
  total?: Int64;
}

export interface ListTemplateMetaInfosRequest {
  Base?: base.Base;
}

export interface ListTemplateMetaInfosResponse {
  templateMetaInfos?: Array<agent.TemplateMetaInfo>;
  BaseResp?: base.BaseResp;
}

export interface OApiListAgentsBySpaceReq {
  spaceID: Int64;
  Authorization: string;
}

export interface OApiListAgentsBySpaceResp {
  agents?: Array<agent.Agent>;
}

export interface Repo {
  id?: Int64;
  externalID?: string;
  name?: string;
  gitHTTPURL?: string;
  gitSSHURL?: string;
}

export interface RepoSearchRequest {
  query?: string;
  page?: Int64;
  perPage?: Int64;
  'X-Jwt-Token': string;
  Base?: base.Base;
}

export interface RepoSearchResponse {
  repos?: Array<Repo>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface UpdateAgentReq {
  agentID: Int64;
  name?: string;
  avatar?: agent.Image;
  desc?: string;
  faasInfo?: agent.FaaSInfo;
  larkInfo?: agent.LarkInfo;
  Base?: base.Base;
}

export interface UpdateAgentResp {
  BaseResp?: base.BaseResp;
}

export interface UpdateLarkReleaseStatusRequest {
  appID: string;
  versionID: string;
  larkReleaseStatus: agent.LarkReleaseStatus;
}

export interface UpdateLarkReleaseStatusResponse {}
/* eslint-enable */
