/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum AnnotationItemDataType {
  Unknown = 0,
  /** 数值打分 */
  Score = 1,
  /** 数值 */
  Value = 2,
  /** 选项 */
  Select = 3,
  /** 纯文本描述 */
  PlainText = 4,
}

export enum ManualAnnotationLabelTaskStatus {
  Created = 10,
  Finished = 50,
}

export enum ManualAnnotationTaskCreateStatus {
  Unknown = 0,
  Creating = 1,
  Failed = 2,
  Success = 3,
}

/** 标注任务回写状态 */
export enum ManualAnnotationTaskWriteBackStatus {
  Unknown = 0,
  /** 任务进行中 */
  Running = 1,
  /** 任务成功 */
  Success = 2,
  /** 任务失败 */
  Failed = 3,
}

export enum ManualStatus {
  /** 不需要人工标注 */
  NoNeed = 0,
  /** 需要人工标注 */
  Need = 1,
}

export enum ObjectType {
  /** 评测任务row，objectID为task_id+row_id */
  EvaluationTaskRow = 1,
}

export enum RowGroupRunState {
  Unknown = -1,
  /** 排队中 */
  Queueing = 0,
  /** 执行中 */
  Processing = 1,
  /** 成功 */
  Success = 2,
  /** 失败 */
  Fail = 3,
  /** 结果待评估 */
  Evaluating = 4,
  /** 终止执行 */
  Terminal = 5,
}

export enum RowRunState {
  /** 未开始执行 */
  Queueing = 0,
  /** 执行成功 */
  Success = 1,
  /** 执行失败 */
  Fail = 2,
}

export enum SourceType {
  /** 评测对比报告，sourceID为评测对比报告ID */
  EvaluationContrastReport = 1,
  /** 评测任务，sourceID为评测任务ID */
  EvaluationTask = 2,
  /** 无来源 */
  NoSource = 100,
}

export enum VisibleArea {
  Unknown = 0,
  /** 在评测模块可见 */
  Evaluation = 1,
  /** 在标注模块可见 */
  ManuaAnnotation = 2,
}
/* eslint-enable */
