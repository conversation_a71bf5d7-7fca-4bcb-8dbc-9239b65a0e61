/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';

export type Int64 = string | number;

export enum ContentType {
  Txt = 1,
  Image = 2,
  Audio = 3,
  Video = 4,
  Link = 6,
  Music = 7,
  Tako = 8,
  File = 9,
  Card = 50,
  BotCard = 51,
  APP = 100,
  OutputSearchResult = 200,
}

/** Evaluator 执行时的处理流程的类型，表达 Evaluator 实现方式的属性信息
 EvaluatorID 是不可枚举的，但 EvaluatorProcessType 是可枚举的
 EvaluatorProcessType 是创建 Evaluator 时必须确定下来的信息。 */
export enum EvaluatorProcessType {
  Prompt = 1,
  PythonCode = 2,
  JSCode = 3,
  BuildinFunc = 4,
  BuildinPrompt = 5,
  /** 目前包含了RPCCallback 和 HTTPCallback */
  CustomCallback = 6,
  /** 人工评测 */
  Manual = 7,
  /** 自定义指标上报 */
  CustomMetric = 8,
  /** 专项测试规则 */
  BuiltinSpecTest = 9,
  /** fornax平台Prompt开发中的prompt */
  FornaxPrompt = 10,
  /** coze2.0 裁判模型评估器 */
  CozePrompt = 11,
}

export enum LLMResponseFormat {
  TEXT = 0,
  MARKDOWN = 1,
  JSON = 2,
}

export enum ReplyType {
  Answer = 1,
  Suggest = 2,
  LLMOutput = 3,
  ToolOutput = 4,
  DataSet = 5,
  QueryKeyword = 6,
  IntermediateOutput = 7,
}

export enum RuleRunState {
  /** 运行状态, 异步下状态流转, 同步下只有 Success / Fail */
  NotInit = 0,
  Init = 1,
  Processing = 2,
  Success = 3,
  Fail = 4,
}

export enum SupportUpdateRuleGroupField {
  Name = 4,
  Desc = 5,
}

export interface BuildinFuncRule {
  contents?: Array<string>;
}

export interface BuildinPromptRule {
  contents?: Array<string>;
  model_name?: string;
  /** 20240815 服务端内部使用 */
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

export interface CozePromptRule {
  model_info: ModelInfo;
  prompt: string;
}

export interface CustomCallback {
  content?: string;
  /** 回调业务方的env */
  env?: string;
}

/** 自定义指标规则 */
export interface CustomMetricsRule {
  data_type: flow_devops_evaluation_entity.EvaluateResultDataType;
  value_type?: flow_devops_evaluation_entity.EvaluateResultValueType;
}

export interface FornaxPromptRule {
  /** prompt开发中prompt的唯一标识 */
  prompt_id: Int64;
  /** prompt版本 */
  version?: string;
  /** 服务端内部使用 */
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

export interface JSRule {
  code: string;
}

export interface LLMSetting {
  model_version: string;
  temperature: number;
  respose_max_length: Int64;
}

/** 多维度人工评测评分规则 */
export interface ManualRule {
  /** 数据类型：数值评分、选项、纯文本 */
  data_type: flow_devops_evaluation_entity.EvaluateResultDataType;
  /** 评分范围 */
  scope?: flow_devops_evaluation_entity.ScoringScope;
  options?: Array<flow_devops_evaluation_entity.EvaluateResultOption>;
}

export interface ModelInfo {
  model_id: Int64;
  temperature: number;
  max_tokens: Int64;
  top_p?: number;
  response_format?: LLMResponseFormat;
  presence_penalty?: number;
  frequency_penalty?: number;
  model_style?: Int64;
  model_name?: string;
}

export interface PromptRule {
  setting: LLMSetting;
  content: string;
  /** 20240815 服务端内部使用 */
  ModelInfo?: flow_devops_evaluation_entity.ModelInfo;
}

export interface PythonRule {
  code: string;
}

export interface Rule {
  /** 规则 id */
  rule_id?: Int64;
  /** 评估器标识 */
  evaluator_type: Int64;
  process_type: EvaluatorProcessType;
  /** 自定义评估器的名称 */
  evaluator_type_name?: string;
  /** 权重 */
  weight?: Int64;
  creator_id?: Int64;
  /** 展示用名称 */
  name?: string;
  /** 评测粒度 */
  granularity?: flow_devops_evaluation_entity.EvaluatorGranularity;
  receive_chat_history?: boolean;
  /** 数据类型 */
  data_type?: flow_devops_evaluation_entity.EvaluateResultDataType;
  /** 不同的 EvaluatorProcessType 对应着不同的结构体定义 */
  prompt_rule?: PromptRule;
  python_rule?: PythonRule;
  js_rule?: JSRule;
  buildin_func_rule?: BuildinFuncRule;
  buildin_prompt_rule?: BuildinPromptRule;
  custom_callback?: CustomCallback;
  manual_rule?: ManualRule;
  custom_metrics?: CustomMetricsRule;
  spec_test_rule?: SpecTestRule;
  fornax_prompt_rule?: FornaxPromptRule;
  coze_prompt_rule?: CozePromptRule;
}

export interface RuleGroup {
  meta: RuleGroupMeta;
  rules: Array<Rule>;
}

export interface RuleGroupMeta {
  /** rule_group_id */
  id: Int64;
  name?: string;
  desc?: string;
  space_id: Int64;
  /** true 时该 rule group 在规则界面可见 */
  is_published?: boolean;
  creator_id?: Int64;
  created_at?: Int64;
  updated_at?: Int64;
  deleted_at?: Int64;
  /** 默认为空 */
  rules_count?: Int64;
}

export interface SpecTestRule {
  content?: string;
}
/* eslint-enable */
