/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as ide from './ide';
import * as model from './model';

export type Int64 = string | number;

export enum IDELaunchSourceType {
  Agent = 0,
  CustomComponent = 1,
}

export interface BindCloudIDESpaceReq {
  workspaceID: string;
  agentID?: Int64;
  Base?: base.Base;
}

export interface BindCloudIDESpaceResp {
  BaseResp?: base.BaseResp;
}

export interface CheckCloudIDESpaceReq {
  workspaceID?: string;
  Base?: base.Base;
}

export interface CheckCloudIDESpaceResp {
  /** cloud ide workspace 是否存在 (90d 回收) */
  exist?: boolean;
  BaseResp?: base.BaseResp;
}

export interface FetchSpacesReq {
  'X-Jwt-Token': string;
  Base?: base.Base;
}

export interface FetchSpacesResp {
  spaces?: Array<ide.SpaceInfo>;
  BaseResp?: base.BaseResp;
}

export interface GetCloudIDESpaceReq {
  agentID?: Int64;
  Base?: base.Base;
}

export interface GetCloudIDESpaceResp {
  workspaceID?: string;
  BaseResp?: base.BaseResp;
}

export interface IDELaunchReq {
  'X-Jwt-Token': string;
  /** 从团队空间创建需要携带agentID，从个人空间创建还没有agentid就不需要传 */
  agentID?: Int64;
  repoName?: string;
  branch?: string;
  agentName?: string;
  sourceType?: IDELaunchSourceType;
}

export interface IDELaunchResp {
  location: string;
  alreadyLaunched?: boolean;
  hasAuth?: boolean;
}

export interface JWTLoginReq {
  'X-Jwt-Token': string;
  session_id?: string;
  Base?: base.Base;
}

export interface JWTLoginResp {
  sessionID: string;
}

export interface OApiListCommonModelInfoReq {
  modelName: string;
  Authorization: string;
}

export interface OApiListCommonModelInfoResp {
  model?: model.Model;
  auth?: model.Authorization;
}

export interface UnbindCloudIDESpaceReq {
  ssoUserName: string;
  agentID?: Int64;
  Base?: base.Base;
}

export interface UnbindCloudIDESpaceResp {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
