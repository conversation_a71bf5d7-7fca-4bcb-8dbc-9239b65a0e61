/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as auth from './auth';
import * as base from './base';

export type Int64 = string | number;

export interface AuthenticateServiceAccountReq {
  /** 认证字符串，见  */
  'Fornax-Auth': string;
  psm: string;
  cluster?: string;
  env?: string;
  isBOE: boolean;
  isTCE: boolean;
  ztiToken?: string;
  payload: string;
  /** 部署阶段 */
  stage?: string;
}

export interface AuthenticateServiceAccountResp {
  jwtToken: string;
}

export interface CheckConnectorAuthorizationReq {
  connector: auth.Connector;
  scope?: string;
  userID?: string;
}

export interface CheckConnectorAuthorizationResp {
  hasAuth?: boolean;
}

export interface GenerateServiceAccountReq {
  spaceID: string;
}

export interface GenerateServiceAccountResp {
  serviceAccount?: auth.ServiceAccount;
}

export interface GetAccessControlListBySpaceReq {
  spaceID: string;
}

export interface GetAccessControlListBySpaceResp {
  /** psm list */
  L2List?: Array<string>;
  L3List?: Array<string>;
  L4List?: Array<string>;
}

export interface GetConnectorOAuthConfigReq {
  connector: auth.Connector;
}

export interface GetConnectorOAuthConfigResp {
  config?: auth.OAuthConfig;
}

export interface GetFrontendAssistantAccessTokenRequest {}

export interface GetFrontendAssistantAccessTokenResp {
  access_token?: string;
  expire_in_seconds?: Int64;
}

export interface GetJWTPublicKeyReq {}

export interface GetJWTPublicKeyResp {
  publicKey: string;
}

export interface GetSecretKeyByAccountReq {
  accountID: string;
}

export interface GetSecretKeyByAccountResp {
  secretKey?: string;
}

export interface GetUploadTokenReq {
  spaceID: string;
  scenario?: string;
  Base?: base.Base;
}

export interface GetUploadTokenResp {
  token?: auth.UploadToken;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface ImportServiceAccountReq {
  spaceID: string;
  accessKey: string;
  secretKey: string;
}

export interface ImportServiceAccountResp {
  id: Int64;
}

export interface ListConnectorAuthorizationsReq {}

export interface ListConnectorAuthorizationsResp {
  records?: Array<auth.AuthorizationRecord>;
}

export interface ListServiceAccountsBySpaceReq {
  spaceID: string;
}

export interface ListServiceAccountsBySpaceResp {
  accounts?: Array<auth.ServiceAccount>;
}

export interface OAuthRedirectReq {
  code: string;
  state: string;
  error_code?: string;
  error_message?: string;
}

export interface OAuthRedirectResp {}

export interface OAuthSetStateReq {
  connector: auth.Connector;
  referer: string;
  scope: string;
  randomStr: string;
}

export interface OAuthSetStateResp {}

export interface RevokeConnectorAuthorizationReq {
  connector: auth.Connector;
}

export interface RevokeConnectorAuthorizationResp {}

export interface SaveAccessControlListReq {
  spaceID: string;
  /** psm list */
  L2List?: Array<string>;
  L3List?: Array<string>;
  L4List?: Array<string>;
}

export interface SaveAccessControlListResp {}
/* eslint-enable */
