/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as graph from './graph';

export type Int64 = string | number;

export enum GraphStatus {
  Undefined = 0,
  /** 无运行时使用 */
  Unused = 1,
  /** 有运行时使用 */
  InUse = 2,
  /** 未配置 slot */
  Unconfigured = 3,
}

export enum IncompatibilityType {
  Undefined = 0,
  SlotDeleted = 1,
  SlotAdded = 2,
  SlotTypeModified = 3,
}

export interface CheckSlotSetCompatibilityReq {
  /** 需要校验的 slotSet */
  slotSetID: string;
  clientID: string;
}

export interface CheckSlotSetCompatibilityResp {
  slots?: Array<SlotSetCompatibility>;
  /** 是否兼容 */
  compatibility?: boolean;
}

export interface ClearSlotSetOfClientReq {
  graphID: string;
  clientID: string;
}

export interface ClearSlotSetOfClientResp {}

export interface ClientGraph {
  graphID: string;
  graphUID: string;
  graphName: string;
  graphDesc?: string;
  slotSetID?: string;
  /** slotSet 版本号 */
  slotSetVersion?: string;
  /** graph状态 */
  graphStatus?: GraphStatus;
  createdBy?: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateGraphReq {
  appID: string;
  uid: string;
  name: string;
  desc?: string;
}

export interface CreateGraphResp {
  graphID?: string;
}

export interface CreateSlotSetReq {
  graphID: string;
  nodes: Array<Node>;
  /** 符合 semver2 的版本号 */
  version: string;
  commitMessage?: string;
}

export interface CreateSlotSetResp {
  slotSetID: string;
}

export interface DeleteGraphReq {
  graphID: string;
}

export interface DeleteGraphResp {}

export interface GetGraphReq {
  graphID: string;
}

export interface GetGraphResp {
  graph?: graph.Graph;
}

export interface GetLatestSlotSetVersionReq {
  graphID: string;
}

export interface GetLatestSlotSetVersionResp {
  /** 版本号最大值 */
  version?: string;
}

export interface GetLatestSlotsOfClientReq {
  graphID: string;
  clientID: string;
}

export interface GetLatestSlotsOfClientResp {
  nodes: Array<Node>;
  slotSetID?: string;
  slotSetVersion?: string;
  /** slot 版本描述 */
  slotSetMessage?: string;
  /** slot 发布记录ID */
  slotReleaseID?: string;
}

export interface GetSlotSetReq {
  slotSetID: string;
  /** 为 false 时不返回 slot 列表 */
  withSlotDetail: boolean;
}

export interface GetSlotSetResp {
  slotSet?: graph.SlotSet;
  /** slot 节点列表 */
  nodes?: Array<Node>;
}

export interface ListGraphsByClientReq {
  appID: string;
  clientID: string;
}

export interface ListGraphsByClientResp {
  clientGraphs?: Array<ClientGraph>;
  total?: string;
}

export interface ListGraphsBySpaceReq {
  spaceID: string;
}

export interface ListGraphsBySpaceResp {
  graphs: Array<graph.Graph>;
}

export interface ListNodeTemplatesReq {}

export interface ListNodeTemplatesResp {
  nodeTemplates?: Array<graph.NodeTemplate>;
  /** 服务端支持的自定义类型 */
  supportedTypes: Array<graph.TypeDescriptor>;
}

export interface ListSlotSetReleasesOfClientReq {
  graphID: string;
  clientID: string;
}

export interface ListSlotSetReleasesOfClientResp {
  /** 版本列表，按更新时间降序排列 */
  releases?: Array<SlotSetRelease>;
}

export interface Node {
  uid: string;
  type: graph.NodeType;
  name: string;
  slots?: Array<Slot>;
}

export interface ReleaseSlotSetReq {
  slotSetID: string;
  clients: Array<string>;
  /** 忽略不兼容 slot，强制发布 */
  forceRelease: boolean;
}

export interface ReleaseSlotSetResp {}

/** 用于前端展示的 Slot 结构 */
export interface Slot {
  uid: string;
  nodeUID?: string;
  nodeName?: string;
  /** 对于官方 SlotType, 有定制的前端交互 */
  officialSlotType?: graph.OfficialSlotType;
  schema?: graph.FieldDescriptor;
  /** JSON 编码的值, 与 SlotSchema 中的 valueType 相对应 */
  value?: string;
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
}

export interface SlotSetCompatibility {
  nodeUID: string;
  slotUID: string;
  compatibility: boolean;
  incompatType?: IncompatibilityType;
  previous?: Slot;
  current?: Slot;
}

export interface SlotSetRelease {
  id: string;
  slotSetID: string;
  /** 展示的 semver2 版本号 */
  slotSetVersion?: string;
  graphID?: string;
  graphUID?: string;
  clientID?: string;
  /** 结构化版本信息; 回滚: is_revert:1; 清空: is_clear:1 */
  releaseLabels?: Record<string, string>;
  /** 版本描述 */
  versionMessage?: string;
  /** 发布人 */
  createdBy?: string;
  /** 发布时间 */
  createdAt?: string;
}

export interface UpdateGraphReq {
  graphID: string;
  name?: string;
  desc?: string;
}

export interface UpdateGraphResp {}
/* eslint-enable */
