/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';
import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';

export type Int64 = string | number;

export enum DatasetStatus {
  Unknown = 0,
  Normal = 1,
  Locked = 2,
}

export enum DatasetType {
  Unknown = 0,
  Chat = 1,
  KV = 2,
  FunctionCall = 3,
  MultiModalUnderstanding = 4,
}

export enum ProfessionalKnowledgeType {
  /** 官方 */
  Official = 1,
  /** 自定义 */
  Customize = 2,
}

/** # service CreateDataset */
export enum PublishOption {
  /** 评测数据集展示时使用(创建与查询,接口默认值） */
  Publish = 1,
  /** 评测任务创建数据集，不对外展示，只用于评测用例与任务使用 */
  NotPublish = 2,
  /** SFT 数据集展示，创建时需要指定该模式，查询时需要指定该模式 */
  SFTPublish = 3,
  /** 数据集展示，创建时需要指定该模式，查询时需要指定该模式 */
  DataProcessPublish = 4,
  /** 评测业务下的结果集场景 */
  EvalResultPublish = 5,
}

export enum ReplyType {
  /** 最终结果 */
  ReplyTypeFinalAnswer = 0,
  /** 工具调用 */
  ReplyTypeToolCall = 1,
}

export enum ResultState {
  Generated = 1,
  Selected = 2,
  Abandoned = 3,
}

/** # dataset info */
export enum RowGroupSource {
  Unknown = 0,
  ManualAdd = 1,
  RuntimeAutoRecord = 2,
}

export enum SecurityLevel {
  Unknown = 0,
  L4 = 4,
}

export enum StreamState {
  /** 非流式 */
  StreamStateNone = 1,
  /** 流式传输开始（首包） */
  StreamStateBegin = 2,
  /** 流式传输中 */
  StreamStateStreaming = 3,
  /** 流失传输结束（尾包） */
  StreamStateEnd = 4,
}

export enum TaskState {
  /** task 状态流转
初始化状态 */
  GenerateTaskInitState = 1,
  /** 运行状态 */
  GenerateTaskRunningState = 2,
  /** 任务完成状态 */
  GenerateTaskGenFinishedState = 3,
  /** 生成的任务全部做了标注 */
  GenerateTaskSelectCompletedState = 4,
  /** 终止状态 */
  GenerateTaskStoppedState = 5,
  /** 失败状态 */
  GenerateTaskFailedState = 6,
}

/** # service UpdateDataset */
export enum UpdateDatasetField {
  Name = 3,
  Desc = 4,
  Tag = 5,
}

/** common
############# DATASET ##############
 dataset
# Column */
export interface ColumnInfo {
  column_id?: Int64;
  /** len <= 256 */
  name: string;
  /** 字段描述 */
  describe?: string;
  /** 是否为评测系统内置定义列 */
  buildin?: boolean;
}

export interface DatasetInfo {
  dataset_id?: Int64;
  name: string;
  space_id: Int64;
  creator_id: Int64;
  desc: string;
  column_schema: Array<ColumnInfo>;
  dataset_status?: DatasetStatus;
  dataset_type?: DatasetType;
  tag?: Array<flow_devops_evaluation_entity.Tag>;
  create_time?: Int64;
  update_time?: Int64;
  /** 密级 */
  security_level?: SecurityLevel;
  /** 过期时间 */
  expire_time?: Int64;
  publish_option?: PublishOption;
  /** 数据集容量, 默认为 5k */
  row_group_capacity?: Int64;
  /** Cell 中文本内容的 bytes 数限制, 默认为 3,2000 */
  max_cell_content_length?: Int64;
  /** 是否允许导出 */
  not_allow_export?: boolean;
}

/** ############# ROW GROUP ############## */
export interface Row {
  row_id?: Int64;
  cells?: Array<string>;
  cells_v2?: Array<flow_devops_evaluation_callback_common.Content>;
}

export interface RowGroup {
  row_group_id?: Int64;
  group_name?: string;
  /** 新增创建时指定tags */
  tags?: Array<string>;
  rows: Array<Row>;
}
/* eslint-enable */
