/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as agent from './namespaces/agent';
import * as aidpcallback from './namespaces/aidpcallback';
import * as app from './namespaces/app';
import * as auth from './namespaces/auth';
import * as automation from './namespaces/automation';
import * as base from './namespaces/base';
import * as dp from './namespaces/dp';
import * as flow_devops_evaluation_callback_common from './namespaces/flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_dataset from './namespaces/flow_devops_evaluation_dataset';
import * as flow_devops_evaluation_entity from './namespaces/flow_devops_evaluation_entity';
import * as flow_devops_evaluation_evaluator from './namespaces/flow_devops_evaluation_evaluator';
import * as flow_devops_evaluation_evaluator_callback from './namespaces/flow_devops_evaluation_evaluator_callback';
import * as flow_devops_evaluation_manual_annotation from './namespaces/flow_devops_evaluation_manual_annotation';
import * as flow_devops_evaluation_object from './namespaces/flow_devops_evaluation_object';
import * as flow_devops_evaluation_object_callback from './namespaces/flow_devops_evaluation_object_callback';
import * as flow_devops_evaluation_task from './namespaces/flow_devops_evaluation_task';
import * as flow_devops_fornax_agentservice from './namespaces/flow_devops_fornax_agentservice';
import * as flow_devops_fornax_appservice from './namespaces/flow_devops_fornax_appservice';
import * as flow_devops_fornax_authservice from './namespaces/flow_devops_fornax_authservice';
import * as flow_devops_fornax_automationservice from './namespaces/flow_devops_fornax_automationservice';
import * as flow_devops_fornax_dpservice from './namespaces/flow_devops_fornax_dpservice';
import * as flow_devops_fornax_graphservice from './namespaces/flow_devops_fornax_graphservice';
import * as flow_devops_fornax_ide from './namespaces/flow_devops_fornax_ide';
import * as flow_devops_fornax_infra from './namespaces/flow_devops_fornax_infra';
import * as flow_devops_fornax_modelevalservice from './namespaces/flow_devops_fornax_modelevalservice';
import * as flow_devops_fornax_modelservice from './namespaces/flow_devops_fornax_modelservice';
import * as flow_devops_fornax_openapi_graphservice from './namespaces/flow_devops_fornax_openapi_graphservice';
import * as flow_devops_fornax_resourceservice from './namespaces/flow_devops_fornax_resourceservice';
import * as flow_devops_fornax_spaceservice from './namespaces/flow_devops_fornax_spaceservice';
import * as flow_devops_fornax_tag from './namespaces/flow_devops_fornax_tag';
import * as flow_devops_fornax_userservice from './namespaces/flow_devops_fornax_userservice';
import * as flow_devops_fornaxob_common from './namespaces/flow_devops_fornaxob_common';
import * as flow_devops_fornaxob_fieldfilter from './namespaces/flow_devops_fornaxob_fieldfilter';
import * as flow_devops_prompt_common from './namespaces/flow_devops_prompt_common';
import * as graph from './namespaces/graph';
import * as ide from './namespaces/ide';
import * as model from './namespaces/model';
import * as modeleval from './namespaces/modeleval';
import * as openapi_graph from './namespaces/openapi_graph';
import * as resource from './namespaces/resource';
import * as space from './namespaces/space';
import * as user from './namespaces/user';

export {
  agent,
  aidpcallback,
  app,
  auth,
  automation,
  base,
  dp,
  flow_devops_evaluation_callback_common,
  flow_devops_evaluation_dataset,
  flow_devops_evaluation_entity,
  flow_devops_evaluation_evaluator,
  flow_devops_evaluation_evaluator_callback,
  flow_devops_evaluation_manual_annotation,
  flow_devops_evaluation_object,
  flow_devops_evaluation_object_callback,
  flow_devops_evaluation_task,
  flow_devops_fornax_agentservice,
  flow_devops_fornax_appservice,
  flow_devops_fornax_authservice,
  flow_devops_fornax_automationservice,
  flow_devops_fornax_dpservice,
  flow_devops_fornax_graphservice,
  flow_devops_fornax_ide,
  flow_devops_fornax_infra,
  flow_devops_fornax_modelevalservice,
  flow_devops_fornax_modelservice,
  flow_devops_fornax_openapi_graphservice,
  flow_devops_fornax_resourceservice,
  flow_devops_fornax_spaceservice,
  flow_devops_fornax_tag,
  flow_devops_fornax_userservice,
  flow_devops_fornaxob_common,
  flow_devops_fornaxob_fieldfilter,
  flow_devops_prompt_common,
  graph,
  ide,
  model,
  modeleval,
  openapi_graph,
  resource,
  space,
  user,
};

export type Int64 = string | number;

export default class FornaxApi2Service<T> {
  private request: any = () => {
    throw new Error('FornaxApi2Service.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/app_manage/v1/apps
   *
   * 创建一个应用
   */
  CreateApp(
    req: flow_devops_fornax_appservice.CreateAppReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.CreateAppResp> {
    const _req = req;
    const url = this.genBaseURL('/api/app_manage/v1/apps');
    const method = 'POST';
    const data = {
      uid: _req['uid'],
      spaceID: _req['spaceID'],
      appType: _req['appType'],
      name: _req['name'],
      description: _req['description'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/app_manage/v1/graphs/:graphID/clients/:clientID/latest_slots
   *
   * 读取一个客户端最新发布的 Slot 列表
   */
  GetLatestSlotsOfClient(
    req: flow_devops_fornax_graphservice.GetLatestSlotsOfClientReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.GetLatestSlotsOfClientResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/graphs/${_req['graphID']}/clients/${_req['clientID']}/latest_slots`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/graphs/:graphID
   *
   * 读取一个 Graph
   */
  GetGraph(
    req: flow_devops_fornax_graphservice.GetGraphReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.GetGraphResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/app_manage/v1/graphs/${_req['graphID']}`);
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/app_manage/v1/slot_sets/:slotSetID/release
   *
   * 发布一个 SlotSet
   */
  ReleaseSlotSet(
    req: flow_devops_fornax_graphservice.ReleaseSlotSetReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.ReleaseSlotSetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/slot_sets/${_req['slotSetID']}/release`,
    );
    const method = 'POST';
    const data = {
      clients: _req['clients'],
      forceRelease: _req['forceRelease'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/app_manage/v1/graphs/:graphID/clients/:clientID/slot_set_releases
   *
   * 读取一个客户端的 Slot 发布历史
   */
  ListSlotSetReleasesOfClient(
    req: flow_devops_fornax_graphservice.ListSlotSetReleasesOfClientReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.ListSlotSetReleasesOfClientResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/graphs/${_req['graphID']}/clients/${_req['clientID']}/slot_set_releases`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/app_manage/v1/apps/:appID/graphs
   *
   * 创建一个 Graph
   */
  CreateGraph(
    req: flow_devops_fornax_graphservice.CreateGraphReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.CreateGraphResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/apps/${_req['appID']}/graphs`,
    );
    const method = 'POST';
    const data = { uid: _req['uid'], name: _req['name'], desc: _req['desc'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/app_manage/v1/apps/:appID/clients/:clientID/graphs
   *
   * 读取应用下的 graph 列表
   */
  ListGraphsByClient(
    req: flow_devops_fornax_graphservice.ListGraphsByClientReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.ListGraphsByClientResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/apps/${_req['appID']}/clients/${_req['clientID']}/graphs`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/slot_sets/:slotSetID/compatibility
   *
   * 校验一个 SlotSet 与客户端最新版本的兼容性
   */
  CheckSlotSetCompatibility(
    req: flow_devops_fornax_graphservice.CheckSlotSetCompatibilityReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.CheckSlotSetCompatibilityResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/slot_sets/${_req['slotSetID']}/compatibility`,
    );
    const method = 'GET';
    const params = { clientID: _req['clientID'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/app_manage/v1/slot_sets/:slotSetID
   *
   * 读取一个 SlotSet
   */
  GetSlotSet(
    req: flow_devops_fornax_graphservice.GetSlotSetReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.GetSlotSetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/slot_sets/${_req['slotSetID']}`,
    );
    const method = 'GET';
    const params = { withSlotDetail: _req['withSlotDetail'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/app_manage/v1/graphs/:graphID
   *
   * 更新一个 Graph
   */
  UpdateGraph(
    req: flow_devops_fornax_graphservice.UpdateGraphReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.UpdateGraphResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/app_manage/v1/graphs/${_req['graphID']}`);
    const method = 'PUT';
    const data = { name: _req['name'], desc: _req['desc'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/app_manage/v1/node_templates
   *
   * 读取官方 Node 的 Type 和 schema
   */
  ListNodeTemplates(
    req?: flow_devops_fornax_graphservice.ListNodeTemplatesReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.ListNodeTemplatesResp> {
    const url = this.genBaseURL('/api/app_manage/v1/node_templates');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/graphs/:graphID/slot_sets/latest_version
   *
   * 读取一个 graph 的 SlotSet 最新版本号
   */
  GetLatestSlotSetVersion(
    req: flow_devops_fornax_graphservice.GetLatestSlotSetVersionReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.GetLatestSlotSetVersionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/graphs/${_req['graphID']}/slot_sets/latest_version`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/spaces/:spaceID/graphs
   *
   * 读取一个空间下的 graph 列表
   */
  ListGraphsBySpace(
    req: flow_devops_fornax_graphservice.ListGraphsBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.ListGraphsBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/spaces/${_req['spaceID']}/graphs`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/apps/:appID/clients
   *
   * 获取 app 下所有的 client
   */
  ListAppClientsByApp(
    req: flow_devops_fornax_appservice.ListAppClientsByAppReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.ListAppClientsByAppResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/apps/${_req['appID']}/clients`,
    );
    const method = 'GET';
    const params = { envType: _req['envType'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/app_manage/v1/apps/:appID/clients
   *
   * 创建一个 client
   */
  CreateAppClient(
    req: flow_devops_fornax_appservice.CreateAppClientReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.CreateAppClientResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/apps/${_req['appID']}/clients`,
    );
    const method = 'POST';
    const data = { env: _req['env'], cluster: _req['cluster'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /open-apis/app_manage/v1/graphs/:graphUID/nodes/:nodeUID/slots */
  GetNodeSlots(
    req: flow_devops_fornax_openapi_graphservice.GetNodeSlotsReq,
    options?: T,
  ): Promise<flow_devops_fornax_openapi_graphservice.GetNodeSlotsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-apis/app_manage/v1/graphs/${_req['graphUID']}/nodes/${_req['nodeUID']}/slots`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      psm: _req['psm'],
      isBOE: _req['isBOE'],
      env: _req['env'],
      cluster: _req['cluster'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, params, headers }, options);
  }

  /** GET /open-apis/app_manage/v1/graphs/:graphUID/slots */
  GetGraphSlots(
    req: flow_devops_fornax_openapi_graphservice.GetGraphSlotsReq,
    options?: T,
  ): Promise<flow_devops_fornax_openapi_graphservice.GetGraphSlotsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-apis/app_manage/v1/graphs/${_req['graphUID']}/slots`,
    );
    const method = 'GET';
    const params = {
      spaceID: _req['spaceID'],
      psm: _req['psm'],
      isBOE: _req['isBOE'],
      env: _req['env'],
      cluster: _req['cluster'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/app_manage/v1/graphs/:graphID/slot_sets
   *
   * 创建一个 SlotSet
   */
  CreateSlotSet(
    req: flow_devops_fornax_graphservice.CreateSlotSetReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.CreateSlotSetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/graphs/${_req['graphID']}/slot_sets`,
    );
    const method = 'POST';
    const data = {
      nodes: _req['nodes'],
      version: _req['version'],
      commitMessage: _req['commitMessage'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/app_manage/v1/graphs/:graphID
   *
   * 删除一个 graph
   */
  DeleteGraph(
    req: flow_devops_fornax_graphservice.DeleteGraphReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.DeleteGraphResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/app_manage/v1/graphs/${_req['graphID']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/app_manage/v1/graphs/:graphID/clients/:clientID/slot_sets
   *
   * 清空一个 Client 的 SlotSet
   */
  ClearSlotSetOfClient(
    req: flow_devops_fornax_graphservice.ClearSlotSetOfClientReq,
    options?: T,
  ): Promise<flow_devops_fornax_graphservice.ClearSlotSetOfClientResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/graphs/${_req['graphID']}/clients/${_req['clientID']}/slot_sets`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/app_manage/v1/apps/:appID
   *
   * 更新一个应用
   */
  UpdateApp(
    req: flow_devops_fornax_appservice.UpdateAppReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.UpdateAppResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/app_manage/v1/apps/${_req['appID']}`);
    const method = 'PUT';
    const data = { name: _req['name'], description: _req['description'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/app_manage/v1/apps/:appID
   *
   * 删除一个应用
   */
  DeleteApp(
    req: flow_devops_fornax_appservice.DeleteAppReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.DeleteAppResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/app_manage/v1/apps/${_req['appID']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/spaces/:spaceID/apps
   *
   * 读取一个空间下的应用列表
   */
  ListAppsBySpace(
    req: flow_devops_fornax_appservice.ListAppsBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.ListAppsBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/spaces/${_req['spaceID']}/apps`,
    );
    const method = 'GET';
    const params = {
      appID: _req['appID'],
      uid: _req['uid'],
      name: _req['name'],
      appType: _req['appType'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/auth/v1/spaces/:spaceID/service_accounts
   *
   * 导入服务账号. 后台接口, 兼容 AGW AKSK.
   */
  ImportServiceAccount(
    req: flow_devops_fornax_authservice.ImportServiceAccountReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.ImportServiceAccountResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/spaces/${_req['spaceID']}/service_accounts`,
    );
    const method = 'PUT';
    const data = { accessKey: _req['accessKey'], secretKey: _req['secretKey'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open-apis/auth/v1/service_accounts/authenticate
   *
   * ↑↑↑↑↑↑
   *
   * ↓↓↓数据面认证接口↓↓↓
   *
   * 认证
   */
  AuthenticateServiceAccount(
    req: flow_devops_fornax_authservice.AuthenticateServiceAccountReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.AuthenticateServiceAccountResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/open-apis/auth/v1/service_accounts/authenticate',
    );
    const method = 'POST';
    const data = {
      psm: _req['psm'],
      cluster: _req['cluster'],
      env: _req['env'],
      isBOE: _req['isBOE'],
      isTCE: _req['isTCE'],
      ztiToken: _req['ztiToken'],
      payload: _req['payload'],
      stage: _req['stage'],
    };
    const headers = { 'Fornax-Auth': _req['Fornax-Auth'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/auth/v1/spaces/:spaceID/service_accounts
   *
   * 生成服务账号
   */
  GenerateServiceAccount(
    req: flow_devops_fornax_authservice.GenerateServiceAccountReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.GenerateServiceAccountResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/spaces/${_req['spaceID']}/service_accounts`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/auth/v1/service_accounts/:accountID/secret_key
   *
   * 获取服务账号密钥
   */
  GetSecretKeyByAccount(
    req: flow_devops_fornax_authservice.GetSecretKeyByAccountReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.GetSecretKeyByAccountResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/service_accounts/${_req['accountID']}/secret_key`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/auth/v1/spaces/:spaceID/service_accounts
   *
   * 获取空间下的服务账号
   */
  ListServiceAccountsBySpace(
    req: flow_devops_fornax_authservice.ListServiceAccountsBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.ListServiceAccountsBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/spaces/${_req['spaceID']}/service_accounts`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/auth/v1/jwt_public_key
   *
   * 获取 JWT 公钥信息
   */
  GetJWTPublicKey(
    req?: flow_devops_fornax_authservice.GetJWTPublicKeyReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.GetJWTPublicKeyResp> {
    const url = this.genBaseURL('/api/auth/v1/jwt_public_key');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/clients/:clientID
   *
   * 获取一个 client
   */
  GetAppClient(
    req: flow_devops_fornax_appservice.GetAppClientReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.GetAppClientResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/clients/${_req['clientID']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/app_manage/v1/apps/:appID/clients/:clientID/evaluation_settings
   *
   * 更新一个 client 的评测配置
   */
  UpsertClientEvaluationSetting(
    req: flow_devops_fornax_appservice.UpsertClientEvaluationSettingReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.UpsertClientEvaluationSettingResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/apps/${_req['appID']}/clients/${_req['clientID']}/evaluation_settings`,
    );
    const method = 'POST';
    const data = { setting: _req['setting'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/app_manage/v1/apps/:appID/clients/:clientID/evaluation_settings
   *
   * 获取一个 client 的评测配置
   */
  GetClientEvaluationSetting(
    req: flow_devops_fornax_appservice.GetClientEvaluationSettingReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.GetClientEvaluationSettingResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/apps/${_req['appID']}/clients/${_req['clientID']}/evaluation_settings`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/model_manage/v1/model_account/delete/:modelID
   *
   * 删除模型，同时删除对应账号
   */
  DeleteModelAndAccount(
    req?: flow_devops_fornax_modelservice.DeleteModelAndAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.DeleteModelAndAccountResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/model_account/delete/${_req['modelID']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/model_manage/v1/model_account/upsert
   *
   * 创建或更新模型及账号
   */
  UpsertModelAndAccount(
    req?: flow_devops_fornax_modelservice.UpsertModelAndAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.UpsertModelAndAccountResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/model_manage/v1/model_account/upsert');
    const method = 'POST';
    const data = { modelAccount: _req['modelAccount'] };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:spaceID/model_account/list
   *
   * 获取模型列表(包含账号信息)
   */
  ListModelAndAccount(
    req?: flow_devops_fornax_modelservice.ListModelAndAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListModelAndAccountResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/model_account/list`,
    );
    const method = 'GET';
    const params = {
      isPublicModel: _req['isPublicModel'],
      modelStatuses: _req['modelStatuses'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:spaceID/model/list
   *
   * 获取模型列表(不包含账号信息)
   */
  ListModel(
    req?: flow_devops_fornax_modelservice.ListModelRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/model/list`,
    );
    const method = 'GET';
    const params = { modelStatuses: _req['modelStatuses'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/app_manage/v1/coze_bot_apps
   *
   * 创建一个 coze bot 类型的 app
   */
  CreateCozeBotApp(
    req: flow_devops_fornax_appservice.CreateCozeBotAppReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.CreateCozeBotAppResp> {
    const _req = req;
    const url = this.genBaseURL('/api/app_manage/v1/coze_bot_apps');
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      botID: _req['botID'],
      desc: _req['desc'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/accounts
   *
   * 获取模型账号信息(有授权认证信息明文)
   */
  GetAccounts(
    req?: flow_devops_fornax_modelservice.GetAccountsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetAccountsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/accounts`,
    );
    const method = 'POST';
    const data = { accountIDs: _req['accountIDs'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ide/v1/ide_space/bind
   *
   * 绑定CloudIDE空间
   */
  BindCloudIDESpace(
    req: flow_devops_fornax_ide.BindCloudIDESpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.BindCloudIDESpaceResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ide/v1/ide_space/bind');
    const method = 'POST';
    const data = {
      workspaceID: _req['workspaceID'],
      agentID: _req['agentID'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/ide/v1/ide_space
   *
   * 获取CloudIDE空间
   */
  GetCloudIDESpace(
    req?: flow_devops_fornax_ide.GetCloudIDESpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.GetCloudIDESpaceResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/ide/v1/ide_space');
    const method = 'GET';
    const params = { agentID: _req['agentID'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/ide/v1/launch
   *
   * 启动 IDE
   */
  IDELaunch(
    req: flow_devops_fornax_ide.IDELaunchReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.IDELaunchResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ide/v1/launch');
    const method = 'GET';
    const params = {
      agentID: _req['agentID'],
      repoName: _req['repoName'],
      branch: _req['branch'],
      agentName: _req['agentName'],
      sourceType: _req['sourceType'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/ide/v1/jwt_login
   *
   * 登录
   */
  JWTLogin(
    req: flow_devops_fornax_ide.JWTLoginReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.JWTLoginResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ide/v1/jwt_login');
    const method = 'GET';
    const params = { session_id: _req['session_id'], Base: _req['Base'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/ide/v1/fornax_spaces
   *
   * 获取fornax空间列表及空间下相关信息
   */
  FetchSpaces(
    req: flow_devops_fornax_ide.FetchSpacesReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.FetchSpacesResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ide/v1/fornax_spaces');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * POST /api/agent_manage/v1/list_agent_release_record
   *
   * 获取发布记录
   */
  ListAgentReleaseRecord(
    req: flow_devops_fornax_agentservice.ListAgentReleaseRecordRequest,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.ListAgentReleaseRecordResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/agent_manage/v1/list_agent_release_record',
    );
    const method = 'POST';
    const data = {
      agentID: _req['agentID'],
      channels: _req['channels'],
      cursor: _req['cursor'],
      pageSize: _req['pageSize'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agent_manage/v1/spaces/:spaceID/agents
   *
   * 获取fornax空间下的agent列表
   */
  ListAgentsBySpace(
    req: flow_devops_fornax_agentservice.ListAgentsBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.ListAgentsBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/spaces/${_req['spaceID']}/agents`,
    );
    const method = 'GET';
    const params = { name: _req['name'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agent_manage/v1/agents
   *
   * 创建agent
   */
  CreateAgent(
    req: flow_devops_fornax_agentservice.CreateAgentReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.CreateAgentResp> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_manage/v1/agents');
    const method = 'POST';
    const data = {
      name: _req['name'],
      spaceID: _req['spaceID'],
      projectName: _req['projectName'],
      avatar: _req['avatar'],
      desc: _req['desc'],
      repo: _req['repo'],
      associatedRepo: _req['associatedRepo'],
      faasInfo: _req['faasInfo'],
      larkInfo: _req['larkInfo'],
      Base: _req['Base'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /open-apis/agent_manage/v1/spaces/:spaceID/agents
   *
   * 给sdk使用，获取fornax空间下的agent列表
   */
  OApiListAgentsBySpace(
    req: flow_devops_fornax_agentservice.OApiListAgentsBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.OApiListAgentsBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-apis/agent_manage/v1/spaces/${_req['spaceID']}/agents`,
    );
    const method = 'GET';
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * PUT /api/agent_manage/v1/agents/:agentID
   *
   * 更新agent
   */
  UpdateAgent(
    req: flow_devops_fornax_agentservice.UpdateAgentReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.UpdateAgentResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/agents/${_req['agentID']}`,
    );
    const method = 'PUT';
    const data = {
      name: _req['name'],
      avatar: _req['avatar'],
      desc: _req['desc'],
      faasInfo: _req['faasInfo'],
      larkInfo: _req['larkInfo'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/agent_manage/v1/agents/:agentID
   *
   * 删除agent
   */
  DeleteAgent(
    req: flow_devops_fornax_agentservice.DeleteAgentReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.DeleteAgentResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/agents/${_req['agentID']}`,
    );
    const method = 'DELETE';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/agent_manage/v1/agent_release
   *
   * 发布
   */
  AgentRelease(
    req: flow_devops_fornax_agentservice.AgentReleaseRequest,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.AgentReleaseResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_manage/v1/agent_release');
    const method = 'POST';
    const data = { info: _req['info'], operationType: _req['operationType'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agent_manage/v1/agents/:agentID
   *
   * 获取agent
   */
  GetAgent(
    req: flow_devops_fornax_agentservice.GetAgentReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.GetAgentResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/agents/${_req['agentID']}`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/ide/v1/ide_space/unbind
   *
   * 解绑CloudIDE空间
   */
  UnbindCloudIDESpace(
    req: flow_devops_fornax_ide.UnbindCloudIDESpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.UnbindCloudIDESpaceResp> {
    const _req = req;
    const url = this.genBaseURL('/api/ide/v1/ide_space/unbind');
    const method = 'POST';
    const data = {
      ssoUserName: _req['ssoUserName'],
      agentID: _req['agentID'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/space_manage/v1/spaces/:spaceID/feature_configs
   *
   * 获取空间下功能配置
   */
  GetFeatureConfigBySpaceID(
    req: flow_devops_fornax_spaceservice.GetFeatureConfigBySpaceIDReq,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.GetFeatureConfigBySpaceIDResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/space_manage/v1/spaces/${_req['spaceID']}/feature_configs`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/ide/v1/common_model/:modelName
   *
   * FornaxIDE获取公共模型接口
   */
  OApiListCommonModelInfo(
    req: flow_devops_fornax_ide.OApiListCommonModelInfoReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.OApiListCommonModelInfoResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/ide/v1/common_model/${_req['modelName']}`,
    );
    const method = 'GET';
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * PUT /api/automation/v1/tasks/:task_id
   *
   * 更新任务
   */
  UpdateTask(
    req: flow_devops_fornax_automationservice.UpdateTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.UpdateTaskResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/automation/v1/tasks/${_req['task_id']}`);
    const method = 'PUT';
    const data = { task: _req['task'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/automation/v1/tasks/:task_id
   *
   * 删除任务
   */
  DeleteTask(
    req: flow_devops_fornax_automationservice.DeleteTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.DeleteTaskResp> {
    const _req = req;
    const url = this.genBaseURL(`/api/automation/v1/tasks/${_req['task_id']}`);
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/automation/v1/filter_options
   *
   * 获取 filter 选项
   */
  GetFilterOptions(
    req: flow_devops_fornax_automationservice.GetFilterOptionsReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.GetFilterOptionsResp> {
    const _req = req;
    const url = this.genBaseURL('/api/automation/v1/filter_options');
    const method = 'GET';
    const params = {
      objectType: _req['objectType'],
      apiV2: _req['apiV2'],
      builtInFilter: _req['builtInFilter'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/automation/v1/spaces/:space_id/tasks
   *
   * 任务列表
   */
  ListTasksBySpace(
    req: flow_devops_fornax_automationservice.ListTasksBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.ListTasksBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/automation/v1/spaces/${_req['space_id']}/tasks`,
    );
    const method = 'GET';
    const params = {
      taskID: _req['taskID'],
      taskName: _req['taskName'],
      taskType: _req['taskType'],
      taskStatuses: _req['taskStatuses'],
      createdBy: _req['createdBy'],
      cursor: _req['cursor'],
      pageSize: _req['pageSize'],
      updatedAtLTE: _req['updatedAtLTE'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/automation/v1/spaces/:space_id/tasks
   *
   * 创建任务
   */
  CreateTask(
    req: flow_devops_fornax_automationservice.CreateTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.CreateTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/automation/v1/spaces/${_req['space_id']}/tasks`,
    );
    const method = 'POST';
    const data = { task: _req['task'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/agent_manage/v1/update_lark_release_status
   *
   * 更新飞书发布状态
   */
  UpdateLarkReleaseStatus(
    req: flow_devops_fornax_agentservice.UpdateLarkReleaseStatusRequest,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.UpdateLarkReleaseStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/agent_manage/v1/update_lark_release_status',
    );
    const method = 'POST';
    const data = {
      appID: _req['appID'],
      versionID: _req['versionID'],
      larkReleaseStatus: _req['larkReleaseStatus'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/agent_manage/v1/agents/:agentID/accounts/:accountID/openapi_token
   *
   * 获取token
   */
  GetAgentOpenAPIToken(
    req: flow_devops_fornax_agentservice.GetAgentOpenAPITokenReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.GetAgentOpenAPITokenResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/agents/${_req['agentID']}/accounts/${_req['accountID']}/openapi_token`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agent_manage/v1/agents/:agentID/openapi_accounts
   *
   * 获取agent的openapi账号
   */
  ListAgentOpenAPIAccounts(
    req: flow_devops_fornax_agentservice.ListAgentOpenAPIAccountsReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.ListAgentOpenAPIAccountsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/agents/${_req['agentID']}/openapi_accounts`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/automation/v1/tasks/:task_id/status_detail
   *
   * 获取 task 状态详情
   */
  GetTaskStatusDetail(
    req: flow_devops_fornax_automationservice.GetTaskStatusDetailReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.GetTaskStatusDetailResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/automation/v1/tasks/${_req['task_id']}/status_detail`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /open-apis/agent_manage/v1/agents/:agentID/authenticate
   *
   * open-api，校验token是否有效
   */
  CheckAgentToken(
    req: flow_devops_fornax_agentservice.CheckAgentTokenReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.CheckAgentTokenResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-apis/agent_manage/v1/agents/${_req['agentID']}/authenticate`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const headers = { 'Fornax-Agent-Token': _req['Fornax-Agent-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/agent_manage/v1/list_template_meta_info
   *
   * agent模板元信息列表
   */
  ListTemplateMetaInfos(
    req?: flow_devops_fornax_agentservice.ListTemplateMetaInfosRequest,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.ListTemplateMetaInfosResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/agent_manage/v1/list_template_meta_info');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/agent_manage/v1/codebase/repo/search
   *
   * 根据关键词查询codebase仓库列表
   */
  RepoSearch(
    req: flow_devops_fornax_agentservice.RepoSearchRequest,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.RepoSearchResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/agent_manage/v1/codebase/repo/search');
    const method = 'GET';
    const params = {
      query: _req['query'],
      page: _req['page'],
      perPage: _req['perPage'],
      Base: _req['Base'],
    };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/agent_manage/v1/codebase/permissions/user/:user_name/repo/:repo_id
   *
   * 获取用户codebase仓库权限
   */
  GetUserCodebasePermission(
    req: flow_devops_fornax_agentservice.GetUserCodebasePermissionRequest,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.GetUserCodebasePermissionResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/codebase/permissions/user/${_req['user_name']}/repo/${_req['repo_id']}`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    const headers = { 'X-Jwt-Token': _req['X-Jwt-Token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * DELETE /api/tag/v1/tag/:tag_id/bind
   *
   * 移除打标
   */
  RemoveTagTargetBind(
    req: flow_devops_fornax_tag.RemoveTagTargetBindRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.RemoveTagTargetBindResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/:tag_id/bind');
    const method = 'DELETE';
    const data = {
      space_id: _req['space_id'],
      UserID: _req['UserID'],
      tag_id: _req['tag_id'],
      target_object_type: _req['target_object_type'],
      target_object_id: _req['target_object_id'],
    };
    const params = { Base: _req['Base'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/tag/v1/tag/:tag_id/bind
   *
   * tag关联关系
   *
   * 关联至打标对象
   */
  CreateTagTargetBind(
    req: flow_devops_fornax_tag.CreateTagTargetBindRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.CreateTagTargetBindResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/:tag_id/bind');
    const method = 'POST';
    const data = {
      tag_id: _req['tag_id'],
      UserID: _req['UserID'],
      space_id: _req['space_id'],
      target_object_id: _req['target_object_id'],
      target_object_type: _req['target_object_type'],
      custom_search_key: _req['custom_search_key'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/auth/v1/connector/authorization_revoke
   *
   * 撤销授权
   */
  RevokeConnectorAuthorization(
    req: flow_devops_fornax_authservice.RevokeConnectorAuthorizationReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.RevokeConnectorAuthorizationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/auth/v1/connector/authorization_revoke');
    const method = 'POST';
    const data = { connector: _req['connector'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/auth/v1/connector/authorization_check
   *
   * 检查是否授权
   */
  CheckConnectorAuthorization(
    req: flow_devops_fornax_authservice.CheckConnectorAuthorizationReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.CheckConnectorAuthorizationResp> {
    const _req = req;
    const url = this.genBaseURL('/api/auth/v1/connector/authorization_check');
    const method = 'POST';
    const data = {
      connector: _req['connector'],
      scope: _req['scope'],
      userID: _req['userID'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/auth/v1/connector/oauth_set_state
   *
   * ↓↓↓授权管理↓↓↓
   *
   * 设置state，防CSRF攻击
   */
  OAuthSetState(
    req: flow_devops_fornax_authservice.OAuthSetStateReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.OAuthSetStateResp> {
    const _req = req;
    const url = this.genBaseURL('/api/auth/v1/connector/oauth_set_state');
    const method = 'POST';
    const data = {
      connector: _req['connector'],
      scope: _req['scope'],
      randomStr: _req['randomStr'],
    };
    const headers = { referer: _req['referer'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/auth/v1/connector/oauth_redirect
   *
   * OAuth redirect
   */
  OAuthRedirect(
    req: flow_devops_fornax_authservice.OAuthRedirectReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.OAuthRedirectResp> {
    const _req = req;
    const url = this.genBaseURL('/api/auth/v1/connector/oauth_redirect');
    const method = 'GET';
    const params = {
      code: _req['code'],
      state: _req['state'],
      error_code: _req['error_code'],
      error_message: _req['error_message'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/auth/v1/connector/authorizations
   *
   * 获取用户所有的授权记录
   */
  ListConnectorAuthorizations(
    req?: flow_devops_fornax_authservice.ListConnectorAuthorizationsReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.ListConnectorAuthorizationsResp> {
    const url = this.genBaseURL('/api/auth/v1/connector/authorizations');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/auth/v1/spaces/:spaceID/access_control_list
   *
   * ↓↓↓访问控制列表相关接口↓↓↓
   *
   * 保存空间下的访问控制列表
   */
  SaveAccessControlList(
    req: flow_devops_fornax_authservice.SaveAccessControlListReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.SaveAccessControlListResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/spaces/${_req['spaceID']}/access_control_list`,
    );
    const method = 'PUT';
    const data = {
      L2List: _req['L2List'],
      L3List: _req['L3List'],
      L4List: _req['L4List'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/auth/v1/spaces/:spaceID/access_control_list
   *
   * 获取空间下的访问控制列表
   */
  GetAccessControlListBySpace(
    req: flow_devops_fornax_authservice.GetAccessControlListBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.GetAccessControlListBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/spaces/${_req['spaceID']}/access_control_list`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/agent_manage/v1/workspaces/:workspaceID/agents
   *
   * 根据cloud ide空间ID获取agent详情
   */
  GetAgentByWorkspaceID(
    req: flow_devops_fornax_agentservice.GetAgentByWorkspaceIDReq,
    options?: T,
  ): Promise<flow_devops_fornax_agentservice.GetAgentByWorkspaceIDResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/agent_manage/v1/workspaces/${_req['workspaceID']}/agents`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/auth/v1/resources/:resourceType/:resourceID
   *
   * 查询资源详情
   */
  GetResource(
    req: flow_devops_fornax_resourceservice.GetResourceReq,
    options?: T,
  ): Promise<flow_devops_fornax_resourceservice.GetResourceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/resources/${_req['resourceType']}/${_req['resourceID']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/app_manage/v1/coze_bot_apps/:botID/permission
   *
   * 校验用户是否有 bot 的权限
   */
  CheckCozeBotPermission(
    req: flow_devops_fornax_appservice.CheckCozeBotPermissionReq,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.CheckCozeBotPermissionResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v1/coze_bot_apps/${_req['botID']}/permission`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/auth/v1/connector/oauth_config
   *
   * 获取渠道的 oauth 配置
   */
  GetConnectorOAuthConfig(
    req: flow_devops_fornax_authservice.GetConnectorOAuthConfigReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.GetConnectorOAuthConfigResp> {
    const _req = req;
    const url = this.genBaseURL('/api/auth/v1/connector/oauth_config');
    const method = 'GET';
    const params = { connector: _req['connector'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/tag/v1/tag/get_tag_by_name
   *
   * 标签名称精准查询
   */
  GetTagByName(
    req: flow_devops_fornax_tag.GetTagByNameRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.GetTagByNameResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/get_tag_by_name');
    const method = 'GET';
    const data = { UserID: _req['UserID'] };
    const params = {
      tag_name: _req['tag_name'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/tag/v1/tag/mget
   *
   * tagID查询
   */
  MGetTags(
    req: flow_devops_fornax_tag.MGetTagsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.MGetTagsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/mget');
    const method = 'POST';
    const data = {
      tag_ids: _req['tag_ids'],
      UserID: _req['UserID'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/tag/v1/tag/:tag_id
   *
   * 修改tag
   */
  UpdateTag(
    req: flow_devops_fornax_tag.UpdateTagRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.UpdateTagResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/:tag_id');
    const method = 'PUT';
    const data = {
      space_id: _req['space_id'],
      UserID: _req['UserID'],
      target_object_types: _req['target_object_types'],
      tag: _req['tag'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/tag/v1/tag/:tag_id/bind/mget
   *
   * 获取tag关联的打标对象
   */
  MGetTagTargetBind(
    req: flow_devops_fornax_tag.MGetTagTargetBindRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.MGetTagTargetBindResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/:tag_id/bind/mget');
    const method = 'POST';
    const data = {
      tag_ids: _req['tag_ids'],
      UserID: _req['UserID'],
      space_id: _req['space_id'],
      target_object_type: _req['target_object_type'],
      custom_search_key: _req['custom_search_key'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/tag/v1/tag
   *
   * 创建tag
   */
  CreateTag(
    req: flow_devops_fornax_tag.CreateTagRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.CreateTagResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      UserID: _req['UserID'],
      target_object_types: _req['target_object_types'],
      tag: _req['tag'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/tag/v1/tag/mget_by_target_ids
   *
   * 获取打标对象tag
   */
  MGetTagListByTargetIDs(
    req: flow_devops_fornax_tag.MGetTagListByTargetIDsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.MGetTagListByTargetIDsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/mget_by_target_ids');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      UserID: _req['UserID'],
      target_ids: _req['target_ids'],
      Base: _req['Base'],
    };
    const params = { target_object_type: _req['target_object_type'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/tag/v1/tag/list
   *
   * 查询一批tag
   */
  ListTag(
    req: flow_devops_fornax_tag.ListTagRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.ListTagResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/list');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      target_object_types: _req['target_object_types'],
      Base: _req['Base'],
    };
    const params = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      fuzzy_name: _req['fuzzy_name'],
      creator_id: _req['creator_id'],
      custom_search_key: _req['custom_search_key'],
      ignore_invalid_tag: _req['ignore_invalid_tag'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/data_processing/v1/tasks/:task_id/run
   *
   * 运行
   */
  RunDataProcessingTask(
    req?: flow_devops_fornax_dpservice.RunDataProcessingTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.RunDataProcessingTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/tasks/${_req['task_id']}/run`,
    );
    const method = 'POST';
    const data = { spaceID: _req['spaceID'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/data_processing/v1/spaces/:spaceID/annotate_tasks
   *
   * 创建标注任务
   */
  CreateAnnotateTask(
    req: flow_devops_fornax_dpservice.CreateAnnotateTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.CreateAnnotateTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['spaceID']}/annotate_tasks`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      platform: _req['platform'],
      inputDatasetID: _req['inputDatasetID'],
      outputDatasetName: _req['outputDatasetName'],
      aidpConfig: _req['aidpConfig'],
      fieldMapConfig: _req['fieldMapConfig'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/data_processing/v1/tasks/:task_id
   *
   * 更新
   */
  UpdateDataProcessingTask(
    req?: flow_devops_fornax_dpservice.UpdateDataProcessingTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.UpdateDataProcessingTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/tasks/${_req['task_id']}`,
    );
    const method = 'PUT';
    const data = {
      name: _req['name'],
      userScript: _req['userScript'],
      dryRunID: _req['dryRunID'],
      outDatasetSchema: _req['outDatasetSchema'],
      spaceID: _req['spaceID'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/data_processing/v1/tasks
   *
   * 创建
   */
  CreateDataProcessingTask(
    req?: flow_devops_fornax_dpservice.CreateDataProcessingTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.CreateDataProcessingTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/data_processing/v1/tasks');
    const method = 'POST';
    const data = {
      name: _req['name'],
      taskType: _req['taskType'],
      inDatasetID: _req['inDatasetID'],
      outDatasetName: _req['outDatasetName'],
      outDatasetSchema: _req['outDatasetSchema'],
      userScript: _req['userScript'],
      dryRunID: _req['dryRunID'],
      trainingDatasetType: _req['trainingDatasetType'],
      outDatasets: _req['outDatasets'],
      datasetType: _req['datasetType'],
      spaceID: _req['spaceID'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/data_processing/v1/tasks/dryrun
   *
   * 试运行
   */
  DryRunDataProcessingTask(
    req?: flow_devops_fornax_dpservice.DryRunDataProcessingTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.DryRunDataProcessingTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/data_processing/v1/tasks/dryrun');
    const method = 'POST';
    const data = {
      datasetID: _req['datasetID'],
      userScript: _req['userScript'],
      sampleCount: _req['sampleCount'],
      trainingDatasetType: _req['trainingDatasetType'],
      spaceID: _req['spaceID'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/data_processing/v1/annotate_tasks/:taskID/refresh_status_detail
   *
   * 刷新标注任务状态详情
   */
  RefreshAnnotateTaskStatusDetail(
    req: flow_devops_fornax_dpservice.RefreshAnnotateTaskStatusDetailReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.RefreshAnnotateTaskStatusDetailResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/data_processing/v1/annotate_tasks/${_req['taskID']}/refresh_status_detail`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/data_processing/v1/tasks/:task_id/runs
   *
   * 获取运行记录
   */
  GetTaskRuns(
    req?: flow_devops_fornax_dpservice.GetTaskRunsReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.GetTaskRunsResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/tasks/${_req['task_id']}/runs`,
    );
    const method = 'GET';
    const params = { spaceID: _req['spaceID'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/automation/v1/spaces/:space_id/tasks/:task_id/detail
   *
   * 获取 task 详情
   */
  GetTaskDetail(
    req: flow_devops_fornax_automationservice.GetTaskDetailReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.GetTaskDetailResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/automation/v1/spaces/${_req['space_id']}/tasks/${_req['task_id']}/detail`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/data_processing/v1/tasks/:task_id/reentry
   *
   * 重入
   */
  ReentryTaskRun(
    req?: flow_devops_fornax_dpservice.ReentryTaskRunReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.ReentryTaskRunResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/tasks/:task_id/reentry`,
    );
    const method = 'POST';
    const data = { forceStart: _req['forceStart'], spaceID: _req['spaceID'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/data_processing/v1/spaces/:spaceID/annotate_tasks/:taskID
   *
   * 获取标注任务详情
   */
  GetAnnotateTask(
    req: flow_devops_fornax_dpservice.GetAnnotateTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.GetAnnotateTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['spaceID']}/annotate_tasks/${_req['taskID']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * PUT /api/app_manage/v2/apps/:appID
   *
   * 更新应用
   */
  UpdateAppV2(
    req: flow_devops_fornax_appservice.UpdateAppV2Req,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.UpdateAppV2Resp> {
    const _req = req;
    const url = this.genBaseURL(`/api/app_manage/v2/apps/${_req['appID']}`);
    const method = 'PUT';
    const data = {
      name: _req['name'],
      description: _req['description'],
      version: _req['version'],
      regions: _req['regions'],
      spaceID: _req['spaceID'],
      appEvaluationInfo: _req['appEvaluationInfo'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/app_manage/v2/apps
   *
   * =========== Fornax应用管理2.0 ===========
   *
   * 创建应用
   */
  CreateAppV2(
    req: flow_devops_fornax_appservice.CreateAppV2Req,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.CreateAppV2Resp> {
    const _req = req;
    const url = this.genBaseURL('/api/app_manage/v2/apps');
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      type: _req['type'],
      entityID: _req['entityID'],
      name: _req['name'],
      description: _req['description'],
      version: _req['version'],
      psm: _req['psm'],
      regions: _req['regions'],
      appEvaluationInfo: _req['appEvaluationInfo'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/data_processing/v1/tasks/:task_id
   *
   * 删除
   */
  DeleteDataProcessingTask(
    req?: flow_devops_fornax_dpservice.DeleteDataProcessingTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.DeleteDataProcessingTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/tasks/${_req['task_id']}`,
    );
    const method = 'DELETE';
    const params = { spaceID: _req['spaceID'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/data_processing/v1/tasks/mget
   *
   * 详情
   */
  MGetDataProcessingTask(
    req?: flow_devops_fornax_dpservice.MGetDataProcessingTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.MGetDataProcessingTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/data_processing/v1/tasks/mget');
    const method = 'POST';
    const data = { taskIDs: _req['taskIDs'], spaceID: _req['spaceID'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/data_processing/v1/tasks/user_script_template_config
   *
   * 获取模板配置
   */
  GetUserScriptTemplateConfig(
    req?: flow_devops_fornax_dpservice.GetUserScriptTemplateConfigReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.GetUserScriptTemplateConfigResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/data_processing/v1/tasks/user_script_template_config',
    );
    const method = 'GET';
    const params = { trainingDatasetType: _req['trainingDatasetType'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * PUT /api/data_processing/v1/tasks/:task_id/stop
   *
   * 停止
   */
  StopDataProcessingTask(
    req?: flow_devops_fornax_dpservice.StopDataProcessingTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.StopDataProcessingTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/tasks/${_req['task_id']}/stop`,
    );
    const method = 'PUT';
    const data = { spaceID: _req['spaceID'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/app_manage/v2/spaces/:spaceID/apps
   *
   * 读取一个空间下的应用列表
   */
  ListAppsBySpaceV2(
    req: flow_devops_fornax_appservice.ListAppsBySpaceV2Req,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.ListAppsBySpaceV2Resp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v2/spaces/${_req['spaceID']}/apps`,
    );
    const method = 'POST';
    const data = {
      keyWord: _req['keyWord'],
      page: _req['page'],
      pageSize: _req['pageSize'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/app_manage/v2/spaces/:spaceID/apps/:appID
   *
   * 获取一个应用
   */
  GetAppV2(
    req: flow_devops_fornax_appservice.GetAppV2Req,
    options?: T,
  ): Promise<flow_devops_fornax_appservice.GetAppV2Resp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/app_manage/v2/spaces/${_req['spaceID']}/apps/${_req['appID']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/data_processing/v1/spaces/:spaceID/annotate_tasks/list
   *
   * 标注任务列表
   */
  ListAnnotateTaskBySpace(
    req: flow_devops_fornax_dpservice.ListAnnotateTaskBySpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.ListAnnotateTaskBySpaceResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['spaceID']}/annotate_tasks/list`,
    );
    const method = 'POST';
    const data = {
      platformTaskIDs: _req['platformTaskIDs'],
      inputDatasetIDs: _req['inputDatasetIDs'],
      platforms: _req['platforms'],
      statuses: _req['statuses'],
      taskIDs: _req['taskIDs'],
      name: _req['name'],
      createdBy: _req['createdBy'],
      cursor: _req['cursor'],
      pageSize: _req['pageSize'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/ide/v1/ide_space/check
   *
   * 检查CloudIDE空间状态
   */
  CheckCloudIDESpace(
    req?: flow_devops_fornax_ide.CheckCloudIDESpaceReq,
    options?: T,
  ): Promise<flow_devops_fornax_ide.CheckCloudIDESpaceResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/ide/v1/ide_space/check');
    const method = 'POST';
    const data = { workspaceID: _req['workspaceID'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/auth/v1/users/kick_user */
  KickUser(
    req: flow_devops_fornax_userservice.KickUserReq,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.KickUserResp> {
    const _req = req;
    const url = this.genBaseURL('/api/auth/v1/users/kick_user');
    const method = 'POST';
    const data = { extUserID: _req['extUserID'], Base: _req['Base'] };
    const headers = { 'Zti-Token': _req['Zti-Token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/data_processing/v1/spaces/:space_id/tasks
   *
   * 列表
   */
  ListDataProcessingTasks(
    req?: flow_devops_fornax_dpservice.ListDataProcessingTasksReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.ListDataProcessingTasksResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['space_id']}/tasks`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      createdBy: _req['createdBy'],
      taskType: _req['taskType'],
      inDatasetIDs: _req['inDatasetIDs'],
      taskID: _req['taskID'],
      pageSize: _req['pageSize'],
      nextToken: _req['nextToken'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/data_processing/v1/annotate_tasks/check_aidp_account
   *
   * 校验用户是否有 AIDP 账号
   */
  CheckUserHasAIDPAccount(
    req?: flow_devops_fornax_dpservice.CheckUserHasAIDPAccountReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.CheckUserHasAIDPAccountResp> {
    const url = this.genBaseURL(
      '/api/data_processing/v1/annotate_tasks/check_aidp_account',
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/models/:modelID/sync_status
   *
   * 同步模型状态
   */
  SyncModelStatus(
    req: flow_devops_fornax_modelservice.SyncModelStatusRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.SyncModelStatusResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/models/${_req['modelID']}/sync_status`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/model_manage/v1/maas_region_configs
   *
   * 获取火山平台区域配置
   */
  GetMaaSRegionConfigs(
    req?: flow_devops_fornax_modelservice.GetMaaSRegionConfigsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetMaaSRegionConfigsResponse> {
    const url = this.genBaseURL('/api/model_manage/v1/maas_region_configs');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/provider_accounts
   *
   * 添加模型提供平台账号
   */
  CreateProviderAccount(
    req: flow_devops_fornax_modelservice.CreateProviderAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.CreateProviderAccountResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/provider_accounts`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      provider: _req['provider'],
      maasAccount: _req['maasAccount'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/provider_accounts/validate
   *
   * 校验模型提供平台账号
   */
  ValidateProviderAccount(
    req: flow_devops_fornax_modelservice.ValidateProviderAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ValidateProviderAccountResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/provider_accounts/validate`,
    );
    const method = 'POST';
    const data = {
      isUpdate: _req['isUpdate'],
      provider: _req['provider'],
      maasAccount: _req['maasAccount'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * PUT /api/model_manage/v1/provider_accounts/:accountID
   *
   * 更新模型提供平台账号
   */
  UpdateProviderAccount(
    req: flow_devops_fornax_modelservice.UpdateProviderAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.UpdateProviderAccountResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/provider_accounts/${_req['accountID']}`,
    );
    const method = 'PUT';
    const data = {
      spaceID: _req['spaceID'],
      provider: _req['provider'],
      name: _req['name'],
      maasAccount: _req['maasAccount'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/model_manage/v1/provider_accounts/:accountID/releated_resources
   *
   * 获取账号关联的资源信息
   */
  GetAccountRelatedResources(
    req: flow_devops_fornax_modelservice.GetAccountRelatedResourcesRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetAccountRelatedResourcesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/provider_accounts/${_req['accountID']}/releated_resources`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:spaceID/provider_accounts
   *
   * 模型提供平台账号列表
   */
  ListProviderAccountsBySpace(
    req: flow_devops_fornax_modelservice.ListProviderAccountsBySpaceRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListProviderAccountsBySpaceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/provider_accounts`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * DELETE /api/model_manage/v1/provider_accounts/:accountID
   *
   * 删除模型提供平台账号
   */
  DeleteProviderAccount(
    req: flow_devops_fornax_modelservice.DeleteProviderAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.DeleteProviderAccountResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/provider_accounts/${_req['accountID']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/get_custom_model
   *
   * 获得定制模型详情
   */
  GetSftTaskCustomModel(
    req?: flow_devops_fornax_modelservice.GetSftTaskCustomModelRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetSftTaskCustomModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/get_custom_model`,
    );
    const method = 'POST';
    const data = {
      accountID: _req['accountID'],
      customModelID: _req['customModelID'],
      providerType: _req['providerType'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/list_foundation_models
   *
   * 获得基础模型列表
   */
  ListSftTaskFoundationModels(
    req?: flow_devops_fornax_modelservice.ListSftTaskFoundationModelsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListSftTaskFoundationModelsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/list_foundation_models`,
    );
    const method = 'POST';
    const data = {
      accountID: _req['accountID'],
      providerType: _req['providerType'],
      nameKeyword: _req['nameKeyword'],
      pageNum: _req['pageNum'],
      pageSize: _req['pageSize'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/get_foundation_model_config
   *
   * 获得基础模型版本配置
   */
  GetSftTaskFoundationModelConfig(
    req?: flow_devops_fornax_modelservice.GetSftTaskFoundationModelConfigRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetSftTaskFoundationModelConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/get_foundation_model_config`,
    );
    const method = 'POST';
    const data = {
      accountID: _req['accountID'],
      modelVersionConfigID: _req['modelVersionConfigID'],
      providerType: _req['providerType'],
      foundationModelName: _req['foundationModelName'],
      trainingMethod: _req['trainingMethod'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/list_custom_models
   *
   * 获得定制模型列表
   */
  ListSftTaskCustomModels(
    req?: flow_devops_fornax_modelservice.ListSftTaskCustomModelsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListSftTaskCustomModelsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/list_custom_models`,
    );
    const method = 'POST';
    const data = {
      accountID: _req['accountID'],
      providerType: _req['providerType'],
      nameKeyword: _req['nameKeyword'],
      userJwtToken: _req['userJwtToken'],
      pageNum: _req['pageNum'],
      pageSize: _req['pageSize'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/list_foundation_model_versions
   *
   * 获得基础模型版本列表
   */
  ListSftTaskFoundationModelVersions(
    req?: flow_devops_fornax_modelservice.ListSftTaskFoundationModelVersionsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListSftTaskFoundationModelVersionsResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/list_foundation_model_versions`,
    );
    const method = 'POST';
    const data = {
      accountID: _req['accountID'],
      modelName: _req['modelName'],
      trainingType: _req['trainingType'],
      trainingMethod: _req['trainingMethod'],
      providerType: _req['providerType'],
      pageNum: _req['pageNum'],
      pageSize: _req['pageSize'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/sft_tasks/create_task
   *
   * 创建精调任务
   */
  CreateSftTask(
    req?: flow_devops_fornax_modelservice.CreateSftTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.CreateSftTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/create_task`,
    );
    const method = 'POST';
    const data = {
      accountID: _req['accountID'],
      task: _req['task'],
      jwtToken: _req['jwtToken'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/sft_tasks/list_tasks
   *
   * 获取精调任务列表
   */
  ListSftTasks(
    req?: flow_devops_fornax_modelservice.ListSftTasksRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListSftTasksResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/list_tasks`,
    );
    const method = 'POST';
    const data = {
      accountID: _req['accountID'],
      creator: _req['creator'],
      taskNameKeyWord: _req['taskNameKeyWord'],
      taskID: _req['taskID'],
      status: _req['status'],
      provider: _req['provider'],
      pageNum: _req['pageNum'],
      pageSize: _req['pageSize'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/training_datasets/:dataset_id/sync_to_volcano_tos
   *
   * 同步数据集到火山TOS
   */
  SyncTrainingDatasetToVolcanoTOS(
    req?: flow_devops_fornax_modelservice.SyncTrainingDatasetToVolcanoTOSReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.SyncTrainingDatasetToVolcanoTOSResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/training_datasets/${_req['dataset_id']}/sync_to_volcano_tos`,
    );
    const method = 'POST';
    const data = { accountID: _req['accountID'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/training_datasets/:dataset_id/import_tasks
   *
   * 创建训练数据导入任务
   */
  CreateTrainingDataImportTask(
    req?: flow_devops_fornax_modelservice.CreateTrainingDataImportTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.CreateTrainingDataImportTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/training_datasets/:dataset_id/import_tasks`,
    );
    const method = 'POST';
    const data = {
      datasetID: _req['datasetID'],
      fileType: _req['fileType'],
      mode: _req['mode'],
      dataSource: _req['dataSource'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/import_tasks/list
   *
   * 查询训练数据导入任务
   */
  ListTrainingDataImportTasks(
    req?: flow_devops_fornax_modelservice.ListTrainingDataImportTasksReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListTrainingDataImportTasksResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/import_tasks/list`,
    );
    const method = 'POST';
    const data = {
      datasetID: _req['datasetID'],
      taskID: _req['taskID'],
      withErrLog: _req['withErrLog'],
      pageSize: _req['pageSize'],
      nextToken: _req['nextToken'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/model_manage/v1/spaces/:space_id/training_datasets/:dataset_id
   *
   * 删除训练数据集
   */
  DeleteTrainingDateset(
    req?: flow_devops_fornax_modelservice.DeleteTrainingDatasetReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.DeleteTrainingDatasetResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/training_datasets/${_req['dataset_id']}`,
    );
    const method = 'DELETE';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/training_datasets/clone
   *
   * 克隆训练数据集
   */
  CloneTrainingDateset(
    req?: flow_devops_fornax_modelservice.CloneTrainingDatasetReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.CloneTrainingDatasetResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/training_datasets/clone`,
    );
    const method = 'POST';
    const data = {
      datasetID: _req['datasetID'],
      name: _req['name'],
      desc: _req['desc'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/training_datasets/:dataset_id/rows
   *
   * 插入数据行
   */
  InsertTrainingDataRows(
    req?: flow_devops_fornax_modelservice.InsertTrainingDataRowsReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.InsertTrainingDataRowsResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/training_datasets/${_req['dataset_id']}/rows`,
    );
    const method = 'POST';
    const data = { row_groups: _req['row_groups'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/training_datasets
   *
   * 创建训练数据集
   */
  CreateTrainingDateset(
    req?: flow_devops_fornax_modelservice.CreateTrainingDatasetReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.CreateTrainingDatasetResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/model_manage/v1/spaces/:space_id/training_datasets',
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      type: _req['type'],
      desc: _req['desc'],
      spaceID: _req['spaceID'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * PUT /api/model_manage/v1/spaces/:space_id/training_datasets/:dataset_id/rows
   *
   * 更新数据行
   */
  UpdateTrainingDataRows(
    req?: flow_devops_fornax_modelservice.UpdateTrainingDataRowsReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.UpdateTrainingDataRowsResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/training_datasets/${_req['dataset_id']}/rows`,
    );
    const method = 'PUT';
    const data = { row_group: _req['row_group'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/model_manage/v1/provider_accounts/:accountID/masked_data
   *
   * 获取账号掩码信息
   */
  GetProviderAccountMaskedData(
    req: flow_devops_fornax_modelservice.GetProviderAccountMaskedDataRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetProviderAccountMaskedDataResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/provider_accounts/${_req['accountID']}/masked_data`,
    );
    const method = 'GET';
    const params = {
      provider: _req['provider'],
      accountType: _req['accountType'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/sft_tasks/get_task/:taskID
   *
   * 获取精调任务信息
   */
  GetSftTask(
    req?: flow_devops_fornax_modelservice.GetSftTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetSftTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/get_task/${_req['taskID']}`,
    );
    const method = 'POST';
    const data = { accountID: _req['accountID'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/sft_tasks/delete_task/:taskID
   *
   * 删除精调任务
   */
  DeleteSftTask(
    req?: flow_devops_fornax_modelservice.DeleteSftTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.DeleteSftTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/delete_task/${_req['taskID']}`,
    );
    const method = 'POST';
    const data = { accountID: _req['accountID'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/sft_tasks/terminate_task/:taskID
   *
   * 终止精调任务
   */
  TerminateSftTask(
    req?: flow_devops_fornax_modelservice.TerminateSftTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.TerminateSftTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/terminate_task/${_req['taskID']}`,
    );
    const method = 'POST';
    const data = { accountID: _req['accountID'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:space_id/upload_material
   *
   * 获取上传文件的url和签名
   */
  GetTrainingFileUploadMaterial(
    req?: flow_devops_fornax_modelservice.GetTrainingFileUploadMaterialReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetTrainingFileUploadMaterialResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/upload_material`,
    );
    const method = 'GET';
    const params = { fileName: _req['fileName'], opType: _req['opType'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/training_datasets/:dataset_id/rows/delete
   *
   * 删除数据行
   */
  DeleteTrainingDataRows(
    req?: flow_devops_fornax_modelservice.DeleteTrainingDataRowsReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.DeleteTrainingDataRowsResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/training_datasets/${_req['dataset_id']}/rows/delete`,
    );
    const method = 'POST';
    const data = { rowIDs: _req['rowIDs'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:space_id/import_tasks/:task_id
   *
   * 获取导入任务
   */
  GetTrainingDataImportTask(
    req?: flow_devops_fornax_modelservice.GetTrainingDataImportTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetTrainingDataImportTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/import_tasks/${_req['task_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/tag/v1/tag/mcreate_bind
   *
   * 批量创建tag关联关系
   */
  MCreateTagTargetBind(
    req: flow_devops_fornax_tag.MCreateTagTargetBindRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.MCreateTagTargetBindResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/mcreate_bind');
    const method = 'POST';
    const data = {
      tag_ids: _req['tag_ids'],
      UserID: _req['UserID'],
      space_id: _req['space_id'],
      target_object_id: _req['target_object_id'],
      target_object_type: _req['target_object_type'],
      custom_search_key: _req['custom_search_key'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/tag/v1/tag/update_bind
   *
   * 批量更新打标对象的标签关联关系
   */
  UpdateTargetBindTags(
    req: flow_devops_fornax_tag.UpdateTargetBindTagsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_tag.UpdateTargetBindTagsResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/tag/v1/tag/update_bind');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      UserID: _req['UserID'],
      tag_ids: _req['tag_ids'],
      target_object_type: _req['target_object_type'],
      target_object_id: _req['target_object_id'],
      custom_search_key: _req['custom_search_key'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** GET /api/data_processing/v1/spaces/:space_id/runs/:run_id/logs */
  GetTaskRunLogs(
    req?: flow_devops_fornax_dpservice.GetTaskRunLogsReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.GetTaskRunLogsResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['space_id']}/runs/${_req['run_id']}/logs`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/list_resource
   *
   * 获得当前用户可选的资源列表
   */
  ListSftTaskResource(
    req?: flow_devops_fornax_modelservice.ListSftTaskResourceRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ListSftTaskResourceResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/model_manage/v1/spaces/:spaceID/list_resource',
    );
    const method = 'POST';
    const data = { provider: _req['provider'], spaceID: _req['spaceID'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-apis/model_manage/v1/spaces/:spaceID/sft_tasks/:taskID/report_event
   *
   * 上报sft事件
   */
  OApiReportSftTaskEvent(
    req?: flow_devops_fornax_modelservice.OApiReportSftTaskEventRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.OApiReportSftTaskEventResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/open-apis/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/${_req['taskID']}/report_event`,
    );
    const method = 'POST';
    const data = {
      eventType: _req['eventType'],
      msg: _req['msg'],
      artifact: _req['artifact'],
      urlInfo: _req['urlInfo'],
      progress: _req['progress'],
      code: _req['code'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /open-apis/model_manage/v1/spaces/:spaceID/sft_tasks/:taskID
   *
   * 上报sft事件
   */
  OApiGetSftTask(
    req?: flow_devops_fornax_modelservice.OApiGetSftTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.OApiGetSftTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/open-apis/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/${_req['taskID']}`,
    );
    const method = 'GET';
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/export_task_output
   *
   * 导出精调任务的产出模型到指定平台
   */
  ExportSftTaskOutputToProvider(
    req?: flow_devops_fornax_modelservice.ExportSftTaskOutputToProviderRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.ExportSftTaskOutputToProviderResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/export_task_output`,
    );
    const method = 'POST';
    const data = {
      provider: _req['provider'],
      taskID: _req['taskID'],
      taskOutputName: _req['taskOutputName'],
      jwtToken: _req['jwtToken'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/sft_tasks/get_task_progress/:taskID
   *
   * 获得精调任务的训练进度
   */
  GetSftTaskProgress(
    req?: flow_devops_fornax_modelservice.GetSftTaskProgressRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetSftTaskProgressResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/get_task_progress/${_req['taskID']}`,
    );
    const method = 'POST';
    const data = { provider: _req['provider'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/automation/v1/bpm/apply_status_callback
   *
   * BPM 回调更新审批状态
   */
  ApplyStatusCallBack(
    req: flow_devops_fornax_automationservice.ApplyStatusCallBackReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.ApplyStatusCallBackResp> {
    const _req = req;
    const url = this.genBaseURL('/api/automation/v1/bpm/apply_status_callback');
    const method = 'POST';
    const data = {
      workflowID: _req['workflowID'],
      spaceID: _req['spaceID'],
      status: _req['status'],
    };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/model_manage/v1/instance_spec_options
   *
   * 获取单个实例资源配置信息
   */
  GetInstanceSpecOptions(
    req?: flow_devops_fornax_modelservice.GetInstanceSpecOptionsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetInstanceSpecOptionsResponse> {
    const url = this.genBaseURL('/api/model_manage/v1/instance_spec_options');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/model_manage/v1/deploy_perm
   *
   * 校验用户是否有部署权限
   */
  CheckUserHasDeployPerm(
    req: flow_devops_fornax_modelservice.CheckUserHasDeployPermRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.CheckUserHasDeployPermResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/model_manage/v1/deploy_perm');
    const method = 'GET';
    const params = {
      provider: _req['provider'],
      byteTreeNodeID: _req['byteTreeNodeID'],
    };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/automation/v1/bpm/approvers
   *
   * 获取 BPM 工单审批人
   */
  ListBPMApprovers(
    req: flow_devops_fornax_automationservice.ListBPMApproversReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.ListBPMApproversResp> {
    const _req = req;
    const url = this.genBaseURL('/api/automation/v1/bpm/approvers');
    const method = 'GET';
    const params = { group_key: _req['group_key'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/model_manage/v1/byte_tree_nodes/search
   *
   * 模糊搜索服务树节点
   */
  SearchByteTreeNodes(
    req?: flow_devops_fornax_modelservice.SearchByteTreeNodesRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.SearchByteTreeNodesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/model_manage/v1/byte_tree_nodes/search');
    const method = 'GET';
    const params = {
      name: _req['name'],
      i18nSearch: _req['i18nSearch'],
      isLeaf: _req['isLeaf'],
      parentID: _req['parentID'],
      inherit: _req['inherit'],
      resourceProvider: _req['resourceProvider'],
    };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, params, headers }, options);
  }

  /**
   * GET /api/model_manage/v1/byte_tree_parent_node/:psm
   *
   * 根据 psm 获取服务树节点信息
   */
  GetByteTreeParentNodeByPSM(
    req: flow_devops_fornax_modelservice.GetByteTreeParentNodeByPSMRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetByteTreeParentNodeByPSMResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/byte_tree_parent_node/${_req['psm']}`,
    );
    const method = 'GET';
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/available_quota/:byte_tree_node_id
   *
   * 根据服务树节点获取可用资源信息
   */
  GetAvailableQuotaByByteTreeNode(
    req: flow_devops_fornax_modelservice.GetAvailableQuotaByByteTreeNodeRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetAvailableQuotaByByteTreeNodeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/available_quota/${_req['byte_tree_node_id']}`,
    );
    const method = 'POST';
    const data = { resourceOption: _req['resourceOption'] };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/:space_id/remove_members
   *
   * 空间角色取消授权
   */
  RemoveSpaceMember(
    req: flow_devops_fornax_spaceservice.RemoveSpaceMemberRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.RemoveSpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/space_manage/v1/spaces/${_req['space_id']}/remove_members`,
    );
    const method = 'POST';
    const data = { space_members: _req['space_members'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/user/v1/users/logout
   *
   * 用户登出
   */
  Logout(
    req?: flow_devops_fornax_userservice.LogoutRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.LogoutResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/v1/users/logout');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/user/v1/users/component/auth
   *
   * 组件鉴权
   */
  AuthComponentSDK(
    req?: flow_devops_fornax_userservice.AuthComponentSDKRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.AuthComponentSDKResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/v1/users/component/auth');
    const method = 'POST';
    const data = {
      noncestr: _req['noncestr'],
      timestamp: _req['timestamp'],
      url: _req['url'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/create
   *
   * 创建空间
   */
  CreateSpace(
    req: flow_devops_fornax_spaceservice.CreateSpaceRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.CreateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/space_manage/v1/spaces/create');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      space_type: _req['space_type'],
      byte_tree_node_id: _req['byte_tree_node_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/:space_id/add_members
   *
   * 空间角色授权
   */
  AddSpaceMember(
    req: flow_devops_fornax_spaceservice.AddSpaceMemberRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.AddSpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/space_manage/v1/spaces/${_req['space_id']}/add_members`,
    );
    const method = 'POST';
    const data = { space_members: _req['space_members'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open-apis/space_manage/v1/spaces/:space_id/add_members
   *
   * 空间角色添加授权
   */
  AddSpaceMemberOApi(
    req?: flow_devops_fornax_spaceservice.AddSpaceMemberOApiReq,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.AddSpaceMemberOApiResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/open-apis/space_manage/v1/spaces/${_req['space_id']}/add_members`,
    );
    const method = 'POST';
    const data = { space_members: _req['space_members'], Base: _req['Base'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/:space_id/update
   *
   * 更新空间
   */
  UpdateSpace(
    req: flow_devops_fornax_spaceservice.UpdateSpaceRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.UpdateSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/space_manage/v1/spaces/${_req['space_id']}/update`,
    );
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      release_approval_config: _req['release_approval_config'],
      byte_tree_node_id: _req['byte_tree_node_id'],
      trace_config: _req['trace_config'],
      Base: _req['Base'],
    };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /open-apis/model_manage/v1/model_account/upsert
   *
   * OpenAPI 创建或更新模型及账号
   */
  OApiUpsertModelAndAccount(
    req?: flow_devops_fornax_modelservice.OApiUpsertModelAndAccountRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.OApiUpsertModelAndAccountResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/open-apis/model_manage/v1/model_account/upsert',
    );
    const method = 'POST';
    const data = {
      modelAccount: _req['modelAccount'],
      ssoUsername: _req['ssoUsername'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * GET /api/user/v1/users/login
   *
   * 用户登录
   */
  Login(
    req?: flow_devops_fornax_userservice.LoginRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.LoginResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/v1/users/login');
    const method = 'GET';
    const params = {
      code: _req['code'],
      state: _req['state'],
      session_id: _req['session_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /open-apis/model_manage/v1/model_account/update_model_status
   *
   * OpenAPI 创建或更新模型及账号
   */
  OApiUpdateModelStatus(
    req?: flow_devops_fornax_modelservice.OApiUpdateModelStatusRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.OApiUpdateModelStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/open-apis/model_manage/v1/model_account/update_model_status',
    );
    const method = 'POST';
    const data = {
      spaceID: _req['spaceID'],
      modelID: _req['modelID'],
      ssoUsername: _req['ssoUsername'],
      status: _req['status'],
    };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/model_account/update_model_status
   *
   * 更新模型状态
   */
  UpdateModelStatus(
    req?: flow_devops_fornax_modelservice.UpdateModelStatusRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.UpdateModelStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/model_account/update_model_status`,
    );
    const method = 'POST';
    const data = { modelID: _req['modelID'], status: _req['status'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:spaceID/model/get
   *
   * 获取模型详情(不包含账号信息)
   */
  GetModel(
    req?: flow_devops_fornax_modelservice.GetModelRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/model/get`,
    );
    const method = 'GET';
    const params = { modelID: _req['modelID'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/:space_id/get_user_roles
   *
   * 获取用户空间权限
   */
  GetUserSpaceRoles(
    req: flow_devops_fornax_spaceservice.GetUserSpaceRolesRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.GetUserSpaceRolesResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/space_manage/v1/spaces/${_req['space_id']}/get_user_roles`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/user/v1/users/user_info
   *
   * 获取用户信息
   */
  GetUserInfo(
    req?: flow_devops_fornax_userservice.GetUserInfoRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.GetUserInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/v1/users/user_info');
    const method = 'POST';
    const data = {
      user_id: _req['user_id'],
      user_name: _req['user_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/list_by_user
   *
   * 空间列表
   */
  ListUserSpace(
    req?: flow_devops_fornax_spaceservice.ListUserSpaceRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.ListUserSpaceResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/space_manage/v1/spaces/list_by_user');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/:space_id/query_members
   *
   * 查询空间成员列表
   */
  QuerySpaceMember(
    req: flow_devops_fornax_spaceservice.QuerySpaceMemberRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.QuerySpaceMemberResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/space_manage/v1/spaces/${_req['space_id']}/query_members`,
    );
    const method = 'POST';
    const data = {
      role_type: _req['role_type'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/user/v1/users/query
   *
   * 模糊搜索用户信息
   */
  QueryUserInfo(
    req: flow_devops_fornax_userservice.QueryUserInfoRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.QueryUserInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/user/v1/users/query');
    const method = 'POST';
    const data = {
      name_like: _req['name_like'],
      page_size: _req['page_size'],
      page_token: _req['page_token'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/space_manage/v1/spaces/:space_id/get
   *
   * 查询空间信息
   */
  GetSpace(
    req: flow_devops_fornax_spaceservice.GetSpaceRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.GetSpaceResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/space_manage/v1/spaces/${_req['space_id']}/get`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/user/v1/users/session
   *
   * 登录态换取用户信息
   */
  GetSessionInfo(
    req?: flow_devops_fornax_userservice.GetSessionInfoRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.GetSessionInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/v1/users/session');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/user/v1/users/batch_get
   *
   * 批量获取用户信息
   */
  MGetUserInfo(
    req?: flow_devops_fornax_userservice.MGetUserInfoRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.MGetUserInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/user/v1/users/batch_get');
    const method = 'POST';
    const data = {
      user_ids: _req['user_ids'],
      user_names: _req['user_names'],
      ext_user_ids: _req['ext_user_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /open-apis/space_manage/v1/spaces/:space_id/remove_members
   *
   * 空间角色删除授权
   */
  RemoveSpaceMemberOApi(
    req: flow_devops_fornax_spaceservice.RemoveSpaceMemberOApiReq,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.RemoveSpaceMemberOApiResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/open-apis/space_manage/v1/spaces/${_req['space_id']}/remove_members`,
    );
    const method = 'POST';
    const data = { space_members: _req['space_members'], Base: _req['Base'] };
    const headers = { Authorization: _req['Authorization'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/auth/v1/spaces/:spaceID/get_upload_token
   *
   * 获取上传 token
   */
  GetUploadToken(
    req: flow_devops_fornax_authservice.GetUploadTokenReq,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.GetUploadTokenResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/auth/v1/spaces/${_req['spaceID']}/get_upload_token`,
    );
    const method = 'POST';
    const data = { scenario: _req['scenario'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/automation/v1/rule_2_trace_query
   *
   * 查询trace的参数映射接口
   */
  Rule2TraceQuery(
    req: flow_devops_fornax_automationservice.Rule2TraceQueryReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.Rule2TraceQueryResp> {
    const _req = req;
    const url = this.genBaseURL('/api/automation/v1/rule_2_trace_query');
    const method = 'POST';
    const data = { space_id: _req['space_id'], rule: _req['rule'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:spaceID/sft_tasks/:taskID/output_ability
   *
   * 获取精调任务产物能力
   */
  GetSFTTaskOutputAbility(
    req: flow_devops_fornax_modelservice.GetSFTTaskOutputAbilityRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetSFTTaskOutputAbilityResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/${_req['taskID']}/output_ability`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /** GET /api/data_processing/v1/spaces/:spaceID/export_dataset_tasks/:taskID */
  GetExportDatasetTask(
    req: flow_devops_fornax_dpservice.GetExportDatasetTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.GetExportDatasetTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['spaceID']}/export_dataset_tasks/${_req['taskID']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/data_processing/v1/spaces/:space_id/import_tasks/:task_id
   *
   * 获取导入任务
   */
  GetDatasetImportTask(
    req?: flow_devops_fornax_dpservice.GetDatasetImportTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.GetDatasetImportTaskResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['space_id']}/import_tasks/${_req['task_id']}`,
    );
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/data_processing/v1/spaces/:space_id/import_tasks
   *
   * 创建导入任务
   */
  CreateDatasetImportTask(
    req: flow_devops_fornax_dpservice.CreateDatasetImportTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.CreateDatasetImportTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['space_id']}/import_tasks`,
    );
    const method = 'POST';
    const data = {
      datasetID: _req['datasetID'],
      fileType: _req['fileType'],
      dataSource: _req['dataSource'],
      overwrite: _req['overwrite'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/data_processing/v1/spaces/:spaceID/datasets/:datasetID/export
   *
   * 数据集导出
   */
  ExportDataset(
    req: flow_devops_fornax_dpservice.ExportDatasetReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.ExportDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/data_processing/v1/spaces/${_req['spaceID']}/datasets/${_req['datasetID']}/export`,
    );
    const method = 'POST';
    const data = {
      columnMappings: _req['columnMappings'],
      toDatasetName: _req['toDatasetName'],
      toDatasetDesc: _req['toDatasetDesc'],
      toDatasetPublishOption: _req['toDatasetPublishOption'],
      toDatasetType: _req['toDatasetType'],
      toDatasetTagIDs: _req['toDatasetTagIDs'],
      toDatasetID: _req['toDatasetID'],
      overwrite: _req['overwrite'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/space_manage/v1/byte_tree/get_byte_tree_node_by_id
   *
   * 获取服务树节点信息
   */
  GetByteTreeNodeByID(
    req: flow_devops_fornax_spaceservice.GetByteTreeNodeByIDRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.GetByteTreeNodeByIDResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/space_manage/v1/byte_tree/get_byte_tree_node_by_id',
    );
    const method = 'POST';
    const data = { node_id: _req['node_id'], Base: _req['Base'] };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/space_manage/v1/byte_tree/has_perm_byte_tree_node
   *
   * 校验用户是否有服务树节点权限
   */
  HasPermByteTreeNode(
    req: flow_devops_fornax_spaceservice.HasPermByteTreeNodeRequest,
    options?: T,
  ): Promise<flow_devops_fornax_spaceservice.HasPermByteTreeNodeResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/space_manage/v1/byte_tree/has_perm_byte_tree_node',
    );
    const method = 'POST';
    const data = {
      byte_tree_node_id: _req['byte_tree_node_id'],
      Base: _req['Base'],
    };
    const headers = { 'x-jwt-token': _req['x-jwt-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/create_offline_eval_task
   *
   * 创建模型离线评测任务
   */
  CreateOfflineEvalTask(
    req?: flow_devops_fornax_modelevalservice.CreateOfflineEvalTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelevalservice.CreateOfflineEvalTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/create_offline_eval_task`,
    );
    const method = 'POST';
    const data = { task: _req['task'], userJwtToken: _req['userJwtToken'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/parse_merlin_seed_model_config
   *
   * 解析merlin seed模型配置
   */
  ParseMerlinSeedModelConfig(
    req?: flow_devops_fornax_modelevalservice.ParseMerlinSeedModelConfigRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelevalservice.ParseMerlinSeedModelConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/parse_merlin_seed_model_config`,
    );
    const method = 'POST';
    const data = {
      checkpointHdfsPath: _req['checkpointHdfsPath'],
      modelSid: _req['modelSid'],
      trainingJobRunID: _req['trainingJobRunID'],
      userJwtToken: _req['userJwtToken'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/list_offline_eval_task
   *
   * 获得离线评测任务列表
   */
  ListOfflineEvalTask(
    req?: flow_devops_fornax_modelevalservice.ListOfflineEvalTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelevalservice.ListOfflineEvalTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/list_offline_eval_task`,
    );
    const method = 'POST';
    const data = {
      nameKeyword: _req['nameKeyword'],
      id: _req['id'],
      creatorID: _req['creatorID'],
      userJwtToken: _req['userJwtToken'],
      pageSize: _req['pageSize'],
      pageNum: _req['pageNum'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/terminate_offline_eval_task
   *
   * 终止离线评测任务
   */
  TerminateOfflineEvalTask(
    req?: flow_devops_fornax_modelevalservice.TerminateOfflineEvalTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelevalservice.TerminateOfflineEvalTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/terminate_offline_eval_task`,
    );
    const method = 'POST';
    const data = { taskID: _req['taskID'], userJwtToken: _req['userJwtToken'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:space_id/offline_eval_task/:task_id
   *
   * 获得离线评测任务列表
   */
  GetOfflineEvalTask(
    req?: flow_devops_fornax_modelevalservice.GetOfflineEvalTaskRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelevalservice.GetOfflineEvalTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['space_id']}/offline_eval_task/${_req['task_id']}`,
    );
    const method = 'POST';
    const data = { userJwtToken: _req['userJwtToken'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/model_manage/v1/spaces/:spaceID/provider_auth
   *
   * 获取用户的授权情况
   */
  GetUserProviderAuthStatus(
    req?: flow_devops_fornax_modelservice.GetUserProviderAuthStatusRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetUserProviderAuthStatusResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/provider_auth`,
    );
    const method = 'GET';
    const params = { provider: _req['provider'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/model_manage/v1/saas_model/list
   *
   * 获取模型列表(商业化)
   */
  SaaSListModel(
    req?: flow_devops_fornax_modelservice.SaaSListModelRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.SaaSListModelResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/model_manage/v1/saas_model/list');
    const method = 'POST';
    const data = {
      cursorID: _req['cursorID'],
      limit: _req['limit'],
      Filter: _req['Filter'],
    };
    const headers = { cookie: _req['cookie'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/saas_model/get_model_filter_params
   *
   * 获取模型列表过滤参数(商业化)
   */
  SaaSGetModelFilterParams(
    req?: flow_devops_fornax_modelservice.SaaSGetModelFilterParamsRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.SaaSGetModelFilterParamsResponse> {
    const url = this.genBaseURL(
      '/api/model_manage/v1/saas_model/get_model_filter_params',
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /** POST /api/data_processing/v1/tasks/callback */
  ProcessCallback(
    req?: flow_devops_fornax_dpservice.ProcessCallbackReq,
    options?: T,
  ): Promise<flow_devops_fornax_dpservice.ProcessCallbackResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/data_processing/v1/tasks/callback');
    const method = 'POST';
    const data = {
      runID: _req['runID'],
      checkpoint: _req['checkpoint'],
      extra: _req['extra'],
      log: _req['log'],
    };
    const headers = { 'x-fornax-dp-token': _req['x-fornax-dp-token'] };
    return this.request({ url, method, data, headers }, options);
  }

  /** POST /v1/loop/files/upload */
  UploadLoopFile(
    req: flow_devops_fornax_infra.UploadLoopFileRequest,
    options?: T,
  ): Promise<flow_devops_fornax_infra.UploadLoopFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/loop/files/upload');
    const method = 'POST';
    const data = { body: _req['body'], Base: _req['Base'] };
    const headers = { 'Content-Type': _req['Content-Type'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/model_manage/v1/spaces/:spaceID/sft_tasks/memory_estimation
   *
   * 对用户提交的sft_task进行显存预估
   */
  GetMemoryEstimation(
    req: flow_devops_fornax_modelservice.GetMemoryEstimationRequest,
    options?: T,
  ): Promise<flow_devops_fornax_modelservice.GetMemoryEstimationResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/model_manage/v1/spaces/${_req['spaceID']}/sft_tasks/memory_estimation`,
    );
    const method = 'POST';
    const data = { task: _req['task'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/auth/v1/assistant/access_token
   *
   * 获取前端助手的access token
   */
  GetFrontendAssistantAccessToken(
    req?: flow_devops_fornax_authservice.GetFrontendAssistantAccessTokenRequest,
    options?: T,
  ): Promise<flow_devops_fornax_authservice.GetFrontendAssistantAccessTokenResp> {
    const url = this.genBaseURL('/api/auth/v1/assistant/access_token');
    const method = 'GET';
    return this.request({ url, method }, options);
  }

  /**
   * POST /api/automation/v1/tasks/:task_id/stop
   *
   * 停止任务
   */
  StopTask(
    req: flow_devops_fornax_automationservice.StopTaskReq,
    options?: T,
  ): Promise<flow_devops_fornax_automationservice.StopTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/automation/v1/tasks/${_req['task_id']}/stop`,
    );
    const method = 'POST';
    return this.request({ url, method }, options);
  }

  /**
   * GET /api/infra/user/v1/users/is_user_in_gray/:strategy
   *
   * 判断用户是否在灰度中(
   */
  IsUserInGray(
    req?: flow_devops_fornax_userservice.IsUserInGrayRequest,
    options?: T,
  ): Promise<flow_devops_fornax_userservice.IsUserInGrayResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      `/api/infra/user/v1/users/is_user_in_gray/${_req['strategy']}`,
    );
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
