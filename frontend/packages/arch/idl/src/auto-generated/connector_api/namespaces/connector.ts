/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum ConnectorPublicType {
  Unknown = 0,
  Private = 1,
  Public = 2,
}

export enum ManageAccountConnectorAction {
  /** 添加渠道 */
  Add = 0,
  /** 移除渠道 */
  Remove = 1,
}

export enum MiniProgramDomainAction {
  /** 添加域名 */
  Add = 0,
  /** 移除域名 */
  Remove = 1,
}

export enum SpaceConnectorType {
  /** 团队内自定义渠道 */
  Team = 1,
  /** 所有的公开渠道 */
  Public = 2,
  /** 官方渠道 */
  Official = 3,
  /** 已安装的公开渠道 */
  InstalledPublic = 4,
}

export interface AccountConnector {
  /** 发布平台 connector_id */
  id?: string;
  /** 发布平台名称 */
  name?: string;
  /** 发布平台图标 */
  icon_url?: string;
  /** 发布平台图标 uri（更新可能会回传） */
  icon_uri?: string;
  /** 发布平台描述 */
  desc?: string;
  /** 0-智能体，1-应用 */
  agent_type?: Array<Int64>;
  /** 渠道类型, 1 自定义渠道, 2.公开渠道, 3.官方渠道 */
  connector_type?: SpaceConnectorType;
  /** 账号下已添加该渠道的空间数量 */
  added_space_count?: Int64;
  /** 账号下空间数量 */
  total_space_count?: Int64;
  /** 按空间查看时，展示渠道是否添加到该空间 */
  is_added_to_space?: boolean;
  /** 是否已添加到账号 */
  is_added_to_account?: boolean;
  /** 不允许安装 */
  cannot_install?: boolean;
  /** 详情页链接 */
  detail_url?: string;
}

export interface BindSpaceConnectorRequest {
  connector_id_list?: Array<string>;
  space_id?: string;
  /** 卸载 */
  uninstall?: boolean;
  /** 空间列表 */
  space_id_list?: Array<string>;
  /** 组织账号ID */
  account_id?: string;
  /** 支持渠道在组织账号下的空间全部开启、全部关闭按钮 */
  operate_all_space?: boolean;
}

export interface BindSpaceConnectorResponse {
  /** key: connector_id, value: 是否成功 */
  manage_result?: Record<string, boolean>;
  code?: number;
  msg?: string;
}

export interface ConnectorMetaInfo {
  /** 发布平台 connector_id */
  id?: string;
  /** 发布平台名称 */
  name?: string;
  /** 发布平台图标 */
  icon_url?: string;
  /** 发布平台图标 uri（更新可能会回传） */
  icon_uri?: string;
  /** 发布平台描述 */
  desc?: string;
  /** 公开类型 */
  public_type?: ConnectorPublicType;
  /** token 在获取列表时返回 */
  callback_url?: string;
  /** 发布空间 */
  space_id_list?: Array<string>;
  /** callback 用来校验签名的 token */
  callback_token?: string;
  /** oauth 配置 */
  oauth_config?: string;
  /** 创建时间 */
  create_time?: Int64;
  /** oauth 应用 id */
  oauth_app_id?: string;
  /** 是否添加到账号 */
  is_added_to_account?: boolean;
}

export interface CopyLinkAreaInfo {
  link_list?: Array<CopyLinkItem>;
  /** 链接区域标题文本 */
  title_text?: string;
  /** 链接区域标题下描述 */
  description?: string;
  /** 步骤号,只是展示指定的步骤号，不影响SchemaArea的展示顺序。 */
  step_order?: Int64;
}

export interface CopyLinkItem {
  /** copy link名称 */
  title?: string;
  link?: string;
}

export interface CreateConnectorRequest {
  connector_title?: string;
  connector_desc?: string;
  connector_icon_uri?: string;
  oauth_app_id?: string;
  callback_url?: string;
  space_id_list?: Array<string>;
  account_id?: string;
}

export interface CreateConnectorResponse {
  code?: number;
  msg?: string;
  callback_token?: string;
}

export interface CreatePreviewVersionRequest {
  agent_id: string;
  /** 0-bot，1-project */
  agent_type: Int64;
  connector_ids?: Array<string>;
  'COZE-WX-TEMPLATE-ID'?: number;
  'COZE-DY-TEMPLATE-ID'?: number;
  Base?: base.Base;
}

export interface CreatePreviewVersionResponse {
  /** key: connector_id, value: link */
  preview_link_map?: Record<Int64, string>;
  BaseResp?: base.BaseResp;
}

export interface DeleteConnectorRequest {
  id?: string;
}

export interface DeleteConnectorResponse {
  code?: number;
  msg?: string;
}

export interface FormSchemaItem {
  /** 提交字段key */
  name?: string;
  /** 展示字段名称 */
  title?: string;
  /** 'Input' | 'InputNumber' ｜'Select' | 'Radio' | 'Checkbox'; 渲染组件 */
  component?: string;
  /** Options[]当为'Select' | 'Radio' | 'Checkbox' 时提供 枚举值 */
  enums?: Array<Options>;
  /** 'string' | 'number' | 'boolean'; 字段类型 (目前没有 array / object场景，暂不考虑 array / object) */
  type?: string;
  /** 校验规则 */
  rules?: Array<FormSchemaRule>;
  /** 提示 */
  placeholder?: string;
}

export interface FormSchemaRule {
  /** string最大长度 */
  max?: number;
  /** string最小长度 */
  min?: number;
  /** string精准长度 */
  len?: number;
  /** 正则 */
  pattern?: string;
  /** 校验错误时的提示信息，走 starling 配置 */
  message?: string;
  /** 是否必填 */
  required?: boolean;
}

export interface GetAccountConnectorListRequest {
  page_token?: string;
  page_size?: number;
  /** 渠道搜索词 */
  search_word?: string;
  space_id?: string;
  connector_type?: SpaceConnectorType;
  /** 组织账号ID */
  account_id?: string;
}

export interface GetAccountConnectorListResponse {
  data?: Array<AccountConnector>;
  has_more?: boolean;
  next_page_token?: string;
  code?: number;
  msg?: string;
}

export interface GetOauthConfigSchemaRequest {}

export interface GetOauthConfigSchemaResponse {
  oauth_schema?: OauthSchema;
  code?: number;
  msg?: string;
}

export interface GetPreviewConnectorRequest {
  Base?: base.Base;
}

export interface GetPreviewConnectorResponse {
  connector_list?: Array<string>;
  BaseResp?: base.BaseResp;
}

export interface GetSpaceConnectorStatusRequest {
  connector_id?: string;
  space_id_list?: Array<string>;
  /** 组织账号ID */
  account_id?: string;
}

export interface GetSpaceConnectorStatusResponse {
  /** key: space_id, value: 绑定状态，1-已绑定 */
  bind_status_map?: Record<string, number>;
  code?: number;
  msg?: string;
}

export interface ListConnectorRequest {
  page_token?: string;
  page_size?: number;
  account_id?: string;
}

export interface ListConnectorResponse {
  data?: Array<ConnectorMetaInfo>;
  has_more?: boolean;
  next_page_token?: string;
  code?: number;
  msg?: string;
}

export interface ListMiniProgramDomainRequest {
  /** 企业ID */
  enterprise_id?: string;
  /** 渠道ID */
  connector_id?: string;
  /** 搜索词 */
  search_word?: string;
}

export interface ListMiniProgramDomainResponse {
  data?: Array<MiniProgramDomain>;
  code?: number;
  msg?: string;
}

export interface ListSpaceConnectorRequest {
  page_token?: string;
  page_size?: number;
  connector_type?: SpaceConnectorType;
  search_word?: string;
  space_id?: string;
}

export interface ListSpaceConnectorResponse {
  data?: Array<SpaceConnector>;
  has_more?: boolean;
  next_page_token?: string;
  code?: number;
  msg?: string;
}

export interface ManageAccountConnectorRequest {
  account_id?: string;
  connector_id_list?: Array<string>;
  /** 添加或移除渠道 */
  action?: ManageAccountConnectorAction;
}

export interface ManageAccountConnectorResponse {
  /** key: connector_id, value: 是否成功 */
  manage_result?: Record<string, boolean>;
  code?: number;
  msg?: string;
}

export interface MiniProgramDomain {
  id?: string;
  domain?: string;
  status?: number;
}

export interface OauthSchema {
  schema_area?: SchemaAreaInfo;
  copy_link_area?: CopyLinkAreaInfo;
  title_text?: string;
  start_text?: string;
}

export interface Options {
  label?: string;
  value?: string;
}

export interface SchemaAreaInfo {
  schema_list?: Array<FormSchemaItem>;
  /** 输入信息区域标题文本 */
  title_text?: string;
  /** 输入信息区域标题下描述 */
  description?: string;
  /** 步骤号,只是展示指定的步骤号，不影响SchemaArea的展示顺序。 */
  step_order?: Int64;
}

export interface SetMiniProgramDomainRequest {
  /** 企业ID */
  enterprise_id?: string;
  domain_list?: Array<string>;
  action?: MiniProgramDomainAction;
  /** 渠道ID */
  connector_id?: string;
}

export interface SetMiniProgramDomainResponse {
  code?: number;
  msg?: string;
}

export interface SpaceConnector {
  /** 发布平台 connector_id */
  id?: string;
  /** 发布平台名称 */
  name?: string;
  /** 发布平台图标 */
  icon_url?: string;
  /** 发布平台图标 uri（更新可能会回传） */
  icon_uri?: string;
  /** 发布平台描述 */
  desc?: string;
  /** 详情页链接 */
  detail_url?: string;
  /** 是否已添加 */
  is_added?: boolean;
  /** 0-智能体，1-应用 */
  agent_type?: Array<Int64>;
  /** 渠道类型, 1 团队内自定义渠道,2.公开渠道, 3.官方渠道 4，已安装的公开渠道 */
  connector_type?: SpaceConnectorType;
  /** 不允许下载 */
  cannot_install?: boolean;
}

export interface UpdateConnectorRequest {
  id?: string;
  connector_title?: string;
  connector_desc?: string;
  connector_icon_uri?: string;
  oauth_app_id?: string;
  callback_url?: string;
  space_id_list?: Array<string>;
  account_id?: string;
}

export interface UpdateConnectorResponse {
  code?: number;
  msg?: string;
  callback_token?: string;
}

export interface UpdateMiniProgramDomainRequest {
  id: string;
  domain: string;
}

export interface UpdateMiniProgramDomainResponse {
  code?: number;
  msg?: string;
}

export interface UpdateOauthConfigRequest {
  /** 渠道 ID */
  connector_id?: string;
  /** oauth 配置 */
  oauth_config?: string;
}

export interface UpdateOauthConfigResponse {
  code?: number;
  msg?: string;
}
/* eslint-enable */
