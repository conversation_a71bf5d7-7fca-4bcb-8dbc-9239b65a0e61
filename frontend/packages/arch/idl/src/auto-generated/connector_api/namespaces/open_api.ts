/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export enum ConnectorAuditStatus {
  /** 未知、无审核 */
  Unknown = 0,
  /** 审核中 */
  Progress = 1,
  /** 审核通过 */
  Audited = 2,
  /** 审核拒绝 */
  Reject = 3,
}

export interface OpenAPIBindConnectorUserConfigRequest {
  connector_id?: string;
  configs?: Array<UserConfig>;
  Base: base.Base;
}

export interface OpenAPIBindConnectorUserConfigResponse {
  code?: number;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface OpenAPIInstallConnectorToWorkspaceRequest {
  workspace_id?: string;
  connector_id?: string;
  Base: base.Base;
}

export interface OpenAPIInstallConnectorToWorkspaceResponse {
  code?: number;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface OpenAPIUpdateConnectorBotRequest {
  bot_id?: string;
  audit_status?: ConnectorAuditStatus;
  reason?: string;
  share_link?: string;
  connector_id?: string;
  Base: base.Base;
}

export interface OpenAPIUpdateConnectorBotResponse {
  code?: number;
  msg?: string;
  BaseResp: base.BaseResp;
}

export interface UserConfig {
  key?: string;
  enums?: Array<UserConfigEnum>;
}

export interface UserConfigEnum {
  value?: string;
  label?: string;
}
/* eslint-enable */
