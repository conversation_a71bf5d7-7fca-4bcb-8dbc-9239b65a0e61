/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as file from './file';

export type Int64 = string | number;

export interface FileListRequest {
  /** 查询文件类型: 1-Images; 2-Document */
  file_type?: number;
  /** 资源所属botId */
  bid: string;
  /** 文件名，模糊搜索 */
  file_name?: string;
  /** 查询起始时间戳, 单位秒 */
  begin_time?: string;
  /** 查询结束时间戳, 单位秒 */
  end_time?: string;
  /** 分页页数，不传默认0 */
  page_num?: number;
  /** 分页大小，不传默认10 */
  page_size?: number;
  /** 渠道 id */
  connector_id: string;
  Base?: base.Base;
}

export interface FileListResponse {
  /** 符合查询条件的总数 */
  total_count?: number;
  /** 查询结果 */
  list?: Array<FileVO>;
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: number;
  msg: string;
  BaseResp: base.BaseResp;
}

export interface FileVO {
  /** 文件资源正式url */
  MainURL?: string;
  /** 【仅图片】缩略图url */
  ThumbnailURL?: string;
  /** 文件名 */
  FileName?: string;
  /** 文件uri（唯一资源标识符） */
  Uri?: string;
  /** 文件id（唯一标识符） */
  FileID?: string;
  /** 文件格式 */
  Format?: string;
  /** 文件类型:  1-Images; 2-Document */
  Type?: number;
  /** 文件创建时间 */
  CreateTime?: string;
  /** 文件更新时间 */
  UpdateTime?: string;
  /** 文件大小 - bytes */
  FileSize?: string;
}

export interface UploadFilesRequest {
  /** 资源所属botId */
  bid: string;
  /** 资源所属connectorId */
  cid: string;
  /** 业务类型 */
  biz_type: string;
  /** 源文件 URL 列表 */
  source_files?: Array<file.SourceFileInfo>;
  Base?: base.Base;
}

export interface UploadFilesResponse {
  /** 成功数 */
  SuccessNum?: number;
  /** 失败数 */
  FailNum?: number;
  /** 文件上传后状态 */
  DestFiles?: Array<file.DestFileInfo>;
  /** 接入 agw 后会将 BaseResp 中的 status_code 和 status_message 注入 */
  code: number;
  msg: string;
  BaseResp: base.BaseResp;
}
/* eslint-enable */
