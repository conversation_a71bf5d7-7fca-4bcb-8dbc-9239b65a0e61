/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as file from './file';

export type Int64 = string | number;

/** --------------------------- * 流式总结 - 插件链路 */
export enum FileSource {
  LinkReader = 1,
  RawText = 2,
}

export enum PluginContentType {
  /** 文本 */
  RawText = 0,
}

export enum PluginContextMode {
  /** 不忽略上下文 */
  IncludeContext = 0,
  /** 忽略上下文 */
  IgnoreContext = 1,
}

export enum PluginOutputMode {
  /** 非流式 */
  NoneStreamMode = 0,
  /** 流式 */
  StreamMode = 1,
}

export enum PluginReturnType {
  /** 输出到模型 */
  ToModel = 0,
  /** 输出到终端 */
  ToUser = 1,
}

export interface FileChunk {
  seq_id?: number;
  content?: string;
}

/** --------------------------- * 文件list - 内部rpc接口 */
export interface FileListRPCMeta {
  Uri?: string;
  Name?: string;
  CreateTime?: Int64;
  Format?: string;
  /** 文件大小 */
  Size?: Int64;
  /** 文件大类, 精确匹配 */
  MdType?: number;
  /** 可访问url, 不保证持久化 */
  Url?: string;
}

export interface PluginSSEParams {
  id: string;
  event: string;
  stream_id: string;
  message_title?: string;
  output_mode?: PluginOutputMode;
  return_type?: PluginReturnType;
  /** 文本内容，默认为0 */
  content_type?: PluginContentType;
  context_mode?: PluginContextMode;
  /** 业务接口返回内容 */
  content?: string;
  is_finish: boolean;
  is_last_msg: boolean;
  is_last_packet_in_msg: boolean;
  ext?: Record<string, string>;
  /** 错误信息 */
  BaseResp?: base.BaseResp;
}

export interface StreamSummaryRequestForPlugin {
  /** 插件链路通参 */
  req_common_params: file.ReqCommonBaseInfo;
  biz_id: string;
  scene_id: string;
  /** 文件url */
  url?: string;
  Base?: base.Base;
}
/* eslint-enable */
