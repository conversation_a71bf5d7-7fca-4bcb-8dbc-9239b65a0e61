/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as file from './namespaces/file';
import * as http_model from './namespaces/http_model';
import * as rpc_model from './namespaces/rpc_model';

export { base, file, http_model, rpc_model };
export * from './namespaces/base';
export * from './namespaces/file';
export * from './namespaces/http_model';
export * from './namespaces/rpc_model';

export type Int64 = string | number;

export default class FileboxService<T> {
  private request: any = () => {
    throw new Error('FileboxService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/filebox/update_filebox_usage_status
   *
   * 用户打开/关闭FileBox开关（同时提供给function call+plugin模式使用）
   */
  UpdateFileBoxUsageStatus(
    req: file.UpdateFileBoxUsageStatusRequest,
    options?: T,
  ): Promise<file.UpdateFileBoxUsageStatusResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/update_filebox_usage_status');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      switch_on: _req['switch_on'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/batch_delete_file
   *
   * 批量删除文件（同时提供给function call+plugin模式使用）
   */
  BatchDeleteFile(
    req: file.BatchDeleteFileRequest,
    options?: T,
  ): Promise<file.BatchDeleteFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/batch_delete_file');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      ids: _req['ids'],
      uris: _req['uris'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/create_album
   *
   * 创建相册（同时提供给function call+plugin模式使用）
   */
  CreateAlbum(
    req: file.CreateAlbumRequest,
    options?: T,
  ): Promise<file.CreateAlbumResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/create_album');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      album_name: _req['album_name'],
      album_desc: _req['album_desc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/add_photos_to_album
   *
   * 上传图片、视频到相册（同时提供给function call+plugin模式使用）
   */
  AddPhotosToAlbum(
    req: file.AddPhotosToAlbumRequest,
    options?: T,
  ): Promise<file.AddPhotosToAlbumResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/add_photos_to_album');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      md_items: _req['md_items'],
      album_name: _req['album_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/update_album
   *
   * 更新相册名称、描述（同时提供给function call+plugin模式使用）
   */
  UpdateAlbum(
    req: file.UpdateAlbumRequest,
    options?: T,
  ): Promise<file.UpdateAlbumResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/update_album');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      album_id: _req['album_id'],
      album_name: _req['album_name'],
      new_album_name: _req['new_album_name'],
      new_album_desc: _req['new_album_desc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/recall_files
   *
   * 接入RAG，语义召回文件信息列表（同时提供给function call+plugin模式使用）
   */
  RecallFileMetaInfos(
    req: file.RecallFileMetaInfosRequest,
    options?: T,
  ): Promise<file.RecallFileMetaInfosResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/recall_files');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      need_rag: _req['need_rag'],
      md_type: _req['md_type'],
      begin_time: _req['begin_time'],
      end_time: _req['end_time'],
      file_uris: _req['file_uris'],
      format: _req['format'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/batch_update_file_meta
   *
   * 批量更新文件元信息
   *
   * plugin 调用
   */
  BatchUpdateFileMeta(
    req: file.BatchUpdateFileMetaRequest,
    options?: T,
  ): Promise<file.BatchUpdateFileMetaResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/batch_update_file_meta');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      update_items: _req['update_items'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/show_album
   *
   * 浏览相册 注意：仅支持语义召回相册，不支持语义召回相册内图片视频
   */
  ShowAlbum(
    req: file.ShowAlbumRequest,
    options?: T,
  ): Promise<file.ShowAlbumResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/show_album');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      album_id: _req['album_id'],
      album_name: _req['album_name'],
      start: _req['start'],
      limit: _req['limit'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/get_file_meta_info
   *
   * 获取文件信息（同时提供给function call+plugin模式使用）
   */
  GetFileMetaInfo(
    req: file.GetFileMetaInfoRequest,
    options?: T,
  ): Promise<file.GetFileMetaInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/get_file_meta_info');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      file_id: _req['file_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/del_album
   *
   * 删除相册（同时提供给function call+plugin模式使用）
   */
  DelAlbum(
    req: file.DelAlbumRequest,
    options?: T,
  ): Promise<file.DelAlbumResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/del_album');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      album_id: _req['album_id'],
      album_name: _req['album_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/summary_file
   *
   * 获取文件总结内容
   */
  SummaryFile(
    req: file.SummaryFileRequest,
    options?: T,
  ): Promise<file.SummaryFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/summary_file');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      file_uri: _req['file_uri'],
      file_name: _req['file_name'],
      format: _req['format'],
      upload_date: _req['upload_date'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/filebox/display_apis
   *
   * Coze 编辑页获取要展示的 Filebox API 信息
   */
  GetDisplayAPIs(
    req?: file.GetDisplayAPIsRequest,
    options?: T,
  ): Promise<file.GetDisplayAPIsResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/filebox/display_apis');
    const method = 'GET';
    const params = { Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/filebox/show_album_list
   *
   * 展示相册列表
   */
  ShowAlbumList(
    req: file.ShowAlbumListRequest,
    options?: T,
  ): Promise<file.ShowAlbumListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/show_album_list');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/filebox/card_detail_info
   *
   * 点击Card获取详情页信息
   */
  GetMDCardDetailPageInfo(
    req: file.GetMDCardDetailPageInfoRequest,
    options?: T,
  ): Promise<file.GetMDCardDetailPageInfoResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/card_detail_info');
    const method = 'GET';
    const params = {
      obj_type: _req['obj_type'],
      dpid: _req['dpid'],
      bid: _req['bid'],
      cid: _req['cid'],
      expires: _req['expires'],
      signature: _req['signature'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/filebox/files/batch_create
   *
   * 批量创建文件
   */
  BatchCreateFiles(
    req: file.BatchCreateFilesRequest,
    options?: T,
  ): Promise<file.BatchCreateFilesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/files/batch_create');
    const method = 'POST';
    const data = {
      req_common_base_info: _req['req_common_base_info'],
      source_urls: _req['source_urls'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/public/filebox/file/update
   *
   * 对外 HTTP 接口：详情页更新图片元信息
   */
  PublicUpdateFile(
    req: file.PublicUpdateFileRequest,
    options?: T,
  ): Promise<file.PublicUpdateFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/public/filebox/file/update');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      detail_page_id: _req['detail_page_id'],
      update_items: _req['update_items'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/public/filebox/file/batch_delete
   *
   * 对外 HTTP 接口：详情页批量删除图片
   */
  PublicBatchDeleteFiles(
    req: file.PublicBatchDeleteFilesRequest,
    options?: T,
  ): Promise<file.PublicBatchDeleteFilesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/public/filebox/file/batch_delete');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      detail_page_id: _req['detail_page_id'],
      uris: _req['uris'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/public/filebox/album/delete
   *
   * 对外 HTTP 接口：详情页删除相册
   */
  PublicDeleteAlbum(
    req: file.PublicDeleteAlbumRequest,
    options?: T,
  ): Promise<file.PublicDeleteAlbumResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/public/filebox/album/delete');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      detail_page_id: _req['detail_page_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/public/filebox/album/batch_remove_photo
   *
   * 对外 HTTP 接口：详情页从相册移除图片
   */
  PublicBatchRemovePhotos(
    req: file.PublicBatchRemovePhotosRequest,
    options?: T,
  ): Promise<file.PublicBatchRemovePhotosResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/public/filebox/album/batch_remove_photo');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      detail_page_id: _req['detail_page_id'],
      ids: _req['ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/public/filebox/page/is_writable
   *
   * 对外 HTTP 接口：判断当前是否可以在详情页渲染写入口
   */
  PageIsWritable(
    req: file.PageIsWritableRequest,
    options?: T,
  ): Promise<file.PageIsWritableResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/public/filebox/page/is_writable');
    const method = 'GET';
    const params = {
      dpid: _req['dpid'],
      UserID: _req['UserID'],
      cid: _req['cid'],
      bid: _req['bid'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/filebox/search_file
   *
   * search relevant document and content chunks
   */
  SearchFile(
    req: file.SearchFileRequest,
    options?: T,
  ): Promise<file.SearchFileResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/search_file');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      file_uri_list: _req['file_uri_list'],
      file_name_list: _req['file_name_list'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/public/filebox/v1/file_list
   *
   * 对外 HTTP 接口：查询文件列表
   */
  FileList(
    req: http_model.FileListRequest,
    options?: T,
  ): Promise<http_model.FileListResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/public/filebox/v1/file_list');
    const method = 'POST';
    const data = {
      file_type: _req['file_type'],
      bid: _req['bid'],
      file_name: _req['file_name'],
      begin_time: _req['begin_time'],
      end_time: _req['end_time'],
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      connector_id: _req['connector_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/public/filebox/v1/upload_files
   *
   * 对外 HTTP 接口：文件上传
   */
  UploadFiles(
    req: http_model.UploadFilesRequest,
    options?: T,
  ): Promise<http_model.UploadFilesResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/public/filebox/v1/upload_files');
    const method = 'POST';
    const data = {
      bid: _req['bid'],
      cid: _req['cid'],
      biz_type: _req['biz_type'],
      source_files: _req['source_files'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/filebox/stream_summary_file
   *
   * 插件 HTTP 接口：流式总结
   */
  StreamSummaryForPlugin(
    req: rpc_model.StreamSummaryRequestForPlugin,
    options?: T,
  ): Promise<rpc_model.PluginSSEParams> {
    const _req = req;
    const url = this.genBaseURL('/api/filebox/stream_summary_file');
    const method = 'POST';
    const data = {
      req_common_params: _req['req_common_params'],
      biz_id: _req['biz_id'],
      scene_id: _req['scene_id'],
      url: _req['url'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
