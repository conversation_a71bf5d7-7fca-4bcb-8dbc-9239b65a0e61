/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface IApp {
  appId?: string;
  typeId?: string;
  name?: string;
  devSandbox?: string;
  projectId?: string;
  spaceId?: string;
  iconUrl?: string;
  globalSettings?: string;
  exts?: string;
  updater?: string;
  creator?: string;
  updatedAt?: string;
  createdAt?: string;
  _id?: string;
}

export interface IBlockInfo {
  exportName: string;
  pkgName: string;
}

export interface IPackageVersionInfo {
  version: string;
  pkgName: string;
}

export interface ISandbox {
  sandboxId?: string;
  appId?: string;
  name?: string;
  pages?: Array<string>;
  routes?: string;
  crdtHistory?: string;
  exts?: string;
  __meta__?: string;
  blocksMap?: Record<string, IBlockInfo>;
  versionsMap?: Record<string, Record<string, IPackageVersionInfo>>;
  branchInfo?: Record<string, string>;
  updater?: string;
  creator?: string;
  updatedAt?: string;
  createdAt?: string;
  _id?: string;
}
/* eslint-enable */
