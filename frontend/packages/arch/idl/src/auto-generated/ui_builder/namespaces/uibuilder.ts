/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as custom_package from './custom_package';
import * as base from './base';
import * as crawl from './crawl';
import * as resource_common from './resource_common';
import * as starry from './starry';

export type Int64 = string | number;

export enum ChannelType {
  /** 网页 */
  Web = 1,
  /** H5, 小程序 */
  Client = 2,
}

export enum CreatedBy {
  /** 任何人 */
  All = 1,
  /** 当前用户 */
  CurrentUser = 2,
}

export enum PreviewMode {
  Web = 1,
  H5 = 2,
}

export enum Scene {
  /** 图片 */
  Picture = 1,
  /** 音频 */
  Audio = 2,
  /** 视频 */
  Video = 3,
  /** 缩略图 */
  Thumbnail = 4,
  /** 文件 */
  File = 5,
}

export enum UserStatus {
  Normal = 1,
  Banned = 2,
  Canceled = 3,
}

export enum UserType {
  External = 0,
  Internal = 1,
}

export interface ChannelData {
  id?: string;
  ui_id?: string;
  channel_id?: number;
  sandbox_id?: string;
  /** ui 与 plugin 关联 */
  ui_relation?: UIRelation;
  /** 描述 */
  description?: string;
  /** dsl 内容 */
  dsl_content?: string;
  creator_id?: Int64;
  /** 创建时间戳 */
  create_time?: string;
  /** 修改时间戳 */
  update_time?: string;
}

export interface CompareComponentData {
  added?: Array<custom_package.ComponentMetaInfo>;
  deleted?: Array<custom_package.ComponentMetaInfo>;
  modified?: Array<custom_package.ComponentMetaInfo>;
  latest_package?: custom_package.Package;
  old_package?: custom_package.Package;
}

export interface CompareCustomComponentRequest {
  space_id: string;
  project_id: string;
  component_name: string;
  old_version_name: string;
  new_version_name?: string;
  Base?: base.Base;
}

export interface CompareCustomComponentResponse {
  data?: CompareComponentData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface ComponentData {
  component_info?: custom_package.Package;
  user_info?: UserBasicInfo;
  is_import?: boolean;
}

export interface ComponentListData {
  list?: Array<ComponentData>;
  total?: string;
}

export interface CreateSnapshotForOpenAPIData {
  /** 快照 URL 地址 */
  url?: string;
}

export interface CreateSnapshotForOpenAPIRequest {
  /** Project ID */
  app_id?: string;
  /** Project Channel */
  channel?: number;
  /** ext_json */
  ext_json?: string;
  /** 截图选择 */
  screenshot_option?: crawl.ScreenshotOption;
  /** 模拟设备信息 */
  device?: crawl.Device;
  /** ConnectorID */
  connector_id?: string;
  /** js 元素选择器 */
  wait_element?: string;
  Base?: base.Base;
}

export interface CreateSnapshotForOpenAPIResponse {
  data?: CreateSnapshotForOpenAPIData;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface CreateSnapshotForWebRequest {
  /** Project ID */
  project_id: string;
  /** Project Version Code */
  version_code: string;
  /** Project Channel */
  channel: number;
  /** ext_json */
  ext_json?: string;
  /** 截图选择 */
  screenshot_option?: crawl.ScreenshotOption;
  /** 模拟设备信息 */
  device?: crawl.Device;
  /** ConnectorID */
  connector_id?: string;
  /** js 选择器 */
  wait_element: string;
  Base?: base.Base;
}

export interface CreateSnapshotForWebResponse {
  data?: SnapshotData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface CreateSnapshotRequest {
  /** Project ID */
  project_id: string;
  /** Project Version Code */
  version_code?: string;
  /** Project Channel */
  channel: number;
  /** ext_json */
  ext_json?: string;
  /** 截图选择 */
  screenshot_option?: crawl.ScreenshotOption;
  /** 模拟设备信息 */
  device?: crawl.Device;
  /** ConnectorID */
  connector_id?: string;
  /** js 选择器 */
  wait_element: string;
  Base?: base.Base;
}

export interface CreateSnapshotResponse {
  data?: SnapshotData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface CreateUIRequest {
  space_id?: string;
  project_id?: string;
  ui_id?: string;
  ui_name?: string;
  channel?: ChannelType;
  'X-Space-Id'?: string;
  'X-Project-Id'?: string;
  Base?: base.Base;
}

export interface CreateUIResponse {
  data?: UIData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteCustomComponentRequest {
  project_id: string;
  component_name: string;
  Base?: base.Base;
}

export interface DeleteCustomComponentResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface DSLData {
  dsl_content?: string;
  ui_relation?: UIRelation;
  ext_pages?: string;
  entry_page_path?: string;
  tab_bar?: string;
}

export interface GenerateCodePackageData {
  /** 出码包 ID */
  generate_id: string;
  /** Project ID */
  project_id: string;
  /** Project Version Code */
  version_code: string;
  /** 任务状态：0待执行;1成功;2出码中;-1失败 */
  status: number;
  /** 文件 URI */
  file_uri?: string;
  /** 文件 URL */
  file_url?: string;
  /** 创建时间戳 */
  create_time?: string;
  /** 上次修改时间戳 */
  update_time?: string;
}

export interface GetCustomComponentListRequest {
  created_by?: CreatedBy;
  search?: string;
  channel_id: ChannelType;
  project_id: string;
  space_id: string;
  'X-Space-Id'?: string;
  page: number;
  size: number;
  Base?: base.Base;
}

export interface GetCustomComponentListResponse {
  data?: ComponentListData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetImportedComponentListRequest {
  channel_id: ChannelType;
  project_id: string;
  space_id: string;
  'X-Space-Id'?: string;
  page?: number;
  size?: number;
  Base?: base.Base;
}

export interface GetImportedComponentListResponse {
  data?: ComponentListData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetPublishSnapshotData {
  /** 快照 */
  snapshot?: string;
}

export interface GetPublishSnapshotRequest {
  /** Project ID */
  project_id: string;
  /** Project Version Code */
  version_code: string;
  /** Project Channel */
  channel: ChannelType;
  Base?: base.Base;
}

export interface GetPublishSnapshotResponse {
  data?: GetPublishSnapshotData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetUIDSLRequest {
  /** Project ID */
  project_id: string;
  /** Project Version Code */
  version_code?: string;
  /** Project Channel */
  channel: ChannelType;
  /** 解析 ext pages */
  with_ext_pages?: boolean;
  /** 是否送审 */
  IsAudit?: boolean;
  /** ConnectorID */
  connector_id?: string;
  Base?: base.Base;
}

export interface GetUIDSLResponse {
  data?: DSLData;
  url_collection?: URLCollection;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetUIInfoByProjectVersionData {
  /** ui id */
  ui_id?: Int64;
  /** ui version */
  ui_version?: Int64;
  /** 跟随发布版本的 channel list */
  channel_list?: Array<number>;
  /** 当前草稿的 channel list */
  draftChannelList?: Array<number>;
}

export interface GetUIPreviewModeData {
  preview_mode_list: Array<PreviewMode>;
}

export interface GetUIRelationData {
  ref_tree_nodes_map?: Partial<
    Record<ChannelType, Array<resource_common.RefTreeNode>>
  >;
}

export interface GetUIRequest {
  space_id?: string;
  project_id?: string;
  ui_id?: string;
  starry_app_id?: string;
  'X-Space-Id'?: string;
  'X-Project-Id'?: string;
  Base?: base.Base;
}

export interface GetUIResponse {
  data?: UIData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface ImportCustomComponentRequest {
  project_id: string;
  component_name: string;
  version_name: string;
  space_id: string;
  channel_id: number;
  component_id?: string;
  Base?: base.Base;
}

export interface ImportCustomComponentResponse {
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface RefreshUIDSLInternalRequest {
  /** dsl */
  dsl_content: string;
  /** 资源过期时间, 秒 */
  expire_time?: number;
  Base?: base.Base;
}

export interface RefreshUIDSLInternalResponse {
  data?: DSLData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface SnapshotData {
  /** 快照 URL 地址 */
  url: string;
}

export interface StarryGetSandboxData {
  Sandbox?: starry.ISandbox;
  App?: starry.IApp;
}

export interface StarryGetSandboxRefreshData {
  Sandbox?: starry.ISandbox;
}

export interface StarryGetSandboxRefreshRequest {
  sandbox_id?: string;
  'X-Space-Id'?: string;
  'X-Project-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetSandboxRefreshResponse {
  data?: StarryGetSandboxRefreshData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface StarryGetSandboxRequest {
  sandbox_id?: string;
  with_app?: boolean;
  is_preview?: boolean;
  'X-Space-Id'?: string;
  'X-Project-Id'?: string;
  Base?: base.Base;
}

export interface StarryGetSandboxResponse {
  data?: StarryGetSandboxData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface StarryUpdateSandboxData {
  actionId?: string;
  sandbox?: string;
}

export interface StarryUpdateSandboxRequest {
  sandbox_id?: string;
  crdt_history?: string;
  action?: string;
  'X-Space-Id'?: string;
  'X-Project-Id'?: string;
  Base?: base.Base;
}

export interface StarryUpdateSandboxResponse {
  data?: StarryUpdateSandboxData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface UIData {
  ui_id?: string;
  space_id?: string;
  project_id?: string;
  starry_app_id?: string;
  /** 5:   string                                 DraftID             (agw.source = 'body',   agw.key = 'draft_id')                           , */
  ui_name?: string;
  publish_status?: number;
  creator_id?: string;
  audit_status?: number;
  /** 审核不通过详情 */
  audit_failure_details?: Array<number>;
  /** 缩略图生成状态 */
  thumb_status?: number;
  /** 缩略图名称 */
  thumb_name?: string;
  /** 创建时间戳 */
  create_time?: string;
  /** 上次修改时间戳 */
  update_time?: string;
  /** UI DSL 内容 */
  dsl_content?: string;
  /** UI 描述 */
  description?: string;
  /** 渠道数据详情 */
  channel_map?: Partial<Record<ChannelType, ChannelData>>;
  /** 渠道列表 */
  channel_list?: Array<ChannelType>;
}

export interface UIRelation {
  plugin_ids?: Array<string>;
  workflow_ids?: Array<string>;
}

export interface UploadFileData {
  /** 文件url */
  upload_url?: string;
  /** 文件uri，提交使用这个 */
  upload_uri?: string;
}

export interface UploadFileRequest {
  /** 文件后缀名 */
  file_type?: string;
  /** 文件名 */
  FileName?: string;
  /** 文件数据 */
  data?: Blob;
  /** 场景 */
  scene?: Scene;
  'X-Space-Id'?: string;
  'X-Project-Id'?: string;
  /** MIME type */
  'Content-Type'?: string;
  base?: base.Base;
}

export interface UploadFileResponse {
  data?: UploadFileData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface URISet {
  uri?: string;
  scene?: Scene;
}

export interface URLCollection {
  urls?: Array<string>;
}

export interface URLMapData {
  /** map[uri]url */
  url_map?: Record<string, string>;
}

export interface UserBasicInfo {
  user_id: string;
  /** 昵称 */
  user_name: string;
  /** 头像 */
  user_avatar: string;
  /** 用户名 */
  user_unique_name?: string;
  /** 用户创建时间 */
  create_time?: Int64;
}
/* eslint-enable */
