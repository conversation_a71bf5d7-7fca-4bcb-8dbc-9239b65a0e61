/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as bot from './namespaces/bot';
import * as flow_devops_coze_evaluation from './namespaces/flow_devops_coze_evaluation';
import * as flow_devops_coze_evaluation_entity from './namespaces/flow_devops_coze_evaluation_entity';
import * as flow_devops_coze_evaluation_evaluator from './namespaces/flow_devops_coze_evaluation_evaluator';
import * as flow_devops_coze_evaluation_v2 from './namespaces/flow_devops_coze_evaluation_v2';
import * as flow_devops_evaluation_callback_common from './namespaces/flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_dataset from './namespaces/flow_devops_evaluation_dataset';
import * as flow_devops_evaluation_entity from './namespaces/flow_devops_evaluation_entity';
import * as flow_devops_evaluation_evaluator from './namespaces/flow_devops_evaluation_evaluator';
import * as flow_devops_evaluation_evaluator_callback from './namespaces/flow_devops_evaluation_evaluator_callback';
import * as flow_devops_evaluation_manual_annotation from './namespaces/flow_devops_evaluation_manual_annotation';
import * as flow_devops_evaluation_object_callback from './namespaces/flow_devops_evaluation_object_callback';
import * as flow_devops_evaluation_task from './namespaces/flow_devops_evaluation_task';
import * as setup from './namespaces/setup';
import * as task from './namespaces/task';
import * as user from './namespaces/user';

export {
  base,
  bot,
  flow_devops_coze_evaluation,
  flow_devops_coze_evaluation_entity,
  flow_devops_coze_evaluation_evaluator,
  flow_devops_coze_evaluation_v2,
  flow_devops_evaluation_callback_common,
  flow_devops_evaluation_dataset,
  flow_devops_evaluation_entity,
  flow_devops_evaluation_evaluator,
  flow_devops_evaluation_evaluator_callback,
  flow_devops_evaluation_manual_annotation,
  flow_devops_evaluation_object_callback,
  flow_devops_evaluation_task,
  setup,
  task,
  user,
};
export * from './namespaces/base';
export * from './namespaces/bot';
export * from './namespaces/flow_devops_coze_evaluation';
export * from './namespaces/flow_devops_coze_evaluation_entity';
export * from './namespaces/flow_devops_coze_evaluation_evaluator';
export * from './namespaces/flow_devops_coze_evaluation_v2';
export * from './namespaces/flow_devops_evaluation_callback_common';
export * from './namespaces/flow_devops_evaluation_dataset';
export * from './namespaces/flow_devops_evaluation_entity';
export * from './namespaces/flow_devops_evaluation_evaluator';
export * from './namespaces/flow_devops_evaluation_evaluator_callback';
export * from './namespaces/flow_devops_evaluation_manual_annotation';
export * from './namespaces/flow_devops_evaluation_object_callback';
export * from './namespaces/flow_devops_evaluation_task';
export * from './namespaces/setup';
export * from './namespaces/task';
export * from './namespaces/user';

export type Int64 = string | number;

export default class EvaluationLiteService<T> {
  private request: any = () => {
    throw new Error('EvaluationLiteService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/devops/coze_evaluation/task/kill
   *
   * 终止评测任务
   */
  KillTask(
    req: flow_devops_coze_evaluation.KillTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.KillTaskResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/task/kill');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/retry
   *
   * 重试评测任务
   */
  RetryTask(
    req: flow_devops_coze_evaluation.RetryTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.RetryTaskResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/task/retry');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/submit
   *
   * 提交评测任务
   */
  SubmitTask(
    req: flow_devops_coze_evaluation.SubmitTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.SubmitTaskResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/task/submit');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      scene_id: _req['scene_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/scene/list
   *
   * 查看评测用例列表
   */
  ListScene(
    req: flow_devops_coze_evaluation.ListSceneReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.ListSceneResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/scene/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/list
   *
   * 筛选评测任务
   */
  ListBatchTask(
    req: flow_devops_coze_evaluation.ListBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.ListBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/task/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      scene_id: _req['scene_id'],
      offset: _req['offset'],
      limit: _req['limit'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/report/overall
   *
   * 查看评测任务报告汇总信息
   */
  GetBatchTaskReportOverall(
    req: flow_devops_coze_evaluation.GetBatchTaskReportOverallReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.GetBatchTaskReportOverallResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/task/report/overall',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      batch_task_id: _req['batch_task_id'],
      scene_id: _req['scene_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/report/detail
   *
   * 查看评测明细
   */
  GetBatchTaskReportDetail(
    req: flow_devops_coze_evaluation.GetBatchTaskReportDetailReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.GetBatchTaskReportDetailResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/task/report/detail',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      batch_task_id: _req['batch_task_id'],
      offset: _req['offset'],
      limit: _req['limit'],
      filters: _req['filters'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/report/score
   *
   * 查看评测打分
   */
  GetBatchTaskScore(
    req: flow_devops_coze_evaluation.GetBatchTaskScoreReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.GetBatchTaskScoreResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/task/report/score',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      batch_task_id: _req['batch_task_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/status/check
   *
   * 轮询查看批量任务状态
   */
  CheckBatchTask(
    req: flow_devops_coze_evaluation.CheckBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.CheckBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/task/status/check',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      batch_task_ids: _req['batch_task_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/delete
   *
   * 删除评测任务
   */
  DeleteTask(
    req: flow_devops_coze_evaluation.DeleteTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.DeleteTaskResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/task/delete');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/version/list
   *
   * 查看评测任务列表（组合使用 查看快照版本、筛选评测任务）
   *
   * 查看快照版本
   */
  ListEvalVersion(
    req: flow_devops_coze_evaluation.ListEvalVersionReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.ListEvalVersionResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/version/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/task/report/filter_meta
   *
   * 查看评测任务报告筛选字段meta
   */
  GetTaskReportFilterMeta(
    req: flow_devops_coze_evaluation.GetTaskReportFilterMetaReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.GetTaskReportFilterMetaResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/task/report/filter_meta',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/setup
   *
   * 初始化用例配置
   */
  Setup(
    req?: flow_devops_coze_evaluation.SetupReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.SetupResp> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/coze_evaluation/setup');
    const method = 'POST';
    const data = { config: _req['config'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/gray/get
   *
   * 获取新版评测灰度状态
   */
  GetCozeEvalGray(
    req: flow_devops_coze_evaluation.GetCozeEvalGrayReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation.GetCozeEvalGrayResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/gray/get');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_id: _req['bot_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/devops/coze_evaluation/v2/dataset/:dataset_id
   *
   * 删除评估集
   */
  DeleteDataset(
    req: flow_devops_coze_evaluation_v2.DeleteDatasetReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.DeleteDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/coze_evaluation/v2/dataset/${_req['dataset_id']}`,
    );
    const method = 'DELETE';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
    };
    const params = { Base: _req['Base'] };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /api/devops/coze_evaluation/template/judge/optimize */
  OptimizeJudgePrompt(
    req: flow_devops_coze_evaluation_evaluator.OptimizeJudgePromptRequest,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_evaluator.OptimizeJudgePromptResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/template/judge/optimize',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      prompt: _req['prompt'],
      batch_task_id: _req['batch_task_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/retry
   *
   * 重试任务
   */
  RetryBatchTask(
    req: flow_devops_coze_evaluation_v2.RetryBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.RetryBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/retry',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/coze_evaluation/v2/batch_task/:task_id/dashboard
   *
   * 下载评测结果
   */
  DashboardTask(
    req: flow_devops_coze_evaluation_v2.DashboardTaskRequest,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.DashboardTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/coze_evaluation/v2/batch_task/${_req['task_id']}/dashboard`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      row_group_run_state: _req['row_group_run_state'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/submit
   *
   * // 任务运行
   *
   * 提交批量任务
   */
  SubmitBatchTask(
    req: flow_devops_coze_evaluation_v2.SubmitBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.SubmitBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/submit',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      is_test_run: _req['is_test_run'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/object/get_operable_bot
   *
   * // 评估对象管理
   *
   * 查询可操作bot
   */
  GetOperableBot(
    req: flow_devops_coze_evaluation_v2.GetOperableBotReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.GetOperableBotResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/object/get_operable_bot',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      bot_ids: _req['bot_ids'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/object/bind_bot
   *
   * 绑定Cozebot
   */
  BindCozeBot(
    req: flow_devops_coze_evaluation_v2.BindCozeBotReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.BindCozeBotResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/object/bind_bot',
    );
    const method = 'POST';
    const data = {
      batch_task_id: _req['batch_task_id'],
      space_id: _req['space_id'],
      eval_objects: _req['eval_objects'],
      is_test_run: _req['is_test_run'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/coze_evaluation/template/judge */
  PullJudgePromptTemplate(
    req: flow_devops_coze_evaluation_evaluator.PullJudgePromptTemplateRequest,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_evaluator.PullJudgePromptTemplateResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/template/judge');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      cursor: _req['cursor'],
      limit: _req['limit'],
      object_type: _req['object_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/dataset/create
   *
   * 创建并导入评估集，并绑定BatchTask
   */
  CreateDataset(
    req: flow_devops_coze_evaluation_v2.CreateDatasetReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.CreateDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/dataset/create',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      batch_task_id: _req['batch_task_id'],
      is_test_run: _req['is_test_run'],
      publish_option: _req['publish_option'],
      tag_list: _req['tag_list'],
      column_schema: _req['column_schema'],
      row_groups: _req['row_groups'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/list
   *
   * // 任务管理
   *
   * 根据space_id查询批量评测任务列表
   */
  ListCozeBatchTask(
    req: flow_devops_coze_evaluation_v2.ListCozeBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.ListCozeBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/list',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      creator_id: _req['creator_id'],
      type: _req['type'],
      status: _req['status'],
      search_name: _req['search_name'],
      offset: _req['offset'],
      limit: _req['limit'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * DELETE /api/devops/coze_evaluation/v2/batch_task/:batch_task_id
   *
   * 删除任务
   */
  DeleteCozeBatchTask(
    req: flow_devops_coze_evaluation_v2.DeleteCozeBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.DeleteCozeBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/coze_evaluation/v2/batch_task/${_req['batch_task_id']}`,
    );
    const method = 'DELETE';
    const data = { space_id: _req['space_id'] };
    const params = { Base: _req['Base'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/dataset/list
   *
   * // 评估集管理
   *
   * 下载评估集模板，或查询评估集列表（包括内置/非内置评估集）
   */
  ListDataset(
    req: flow_devops_coze_evaluation_v2.ListDatasetReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.ListDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/v2/dataset/list');
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      dataset_type: _req['dataset_type'],
      template_type: _req['template_type'],
      offset: _req['offset'],
      limit: _req['limit'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/clone
   *
   * clone批量任务
   */
  CloneCozeBatchTask(
    req: flow_devops_coze_evaluation_v2.CloneCozeBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.CloneCozeBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/clone',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      source_batch_task_id: _req['source_batch_task_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/kill
   *
   * 终止任务
   */
  KillBatchTask(
    req: flow_devops_coze_evaluation_v2.KillBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.KillBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/kill',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      is_test_run: _req['is_test_run'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/create
   *
   * 新建批量任务
   */
  CreateCozeBatchTask(
    req: flow_devops_coze_evaluation_v2.CreateCozeBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.CreateCozeBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/create',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      name: _req['name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/coze_evaluation/v2/dataset/:dataset_id/row_group/list
   *
   * 下载评估集，csv格式前端处理
   */
  ListRowGroups(
    req: flow_devops_coze_evaluation_v2.ListRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.ListRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/coze_evaluation/v2/dataset/${_req['dataset_id']}/row_group/list`,
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      page: _req['page'],
      page_size: _req['page_size'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /api/devops/coze_evaluation/v2/batch_task/:batch_task_id
   *
   * 根据space_id和id查询批量评测任务详情
   */
  GetCozeBatchTask(
    req: flow_devops_coze_evaluation_v2.GetCozeBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.GetCozeBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/coze_evaluation/v2/batch_task/${_req['batch_task_id']}`,
    );
    const method = 'GET';
    const params = { space_id: _req['space_id'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/batch_get_progress
   *
   * 根据space_id和id查询批量评测任务状态
   */
  BatchGetCozeBatchTaskProgress(
    req: flow_devops_coze_evaluation_v2.BatchGetCozeBatchTaskProgressReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.BatchGetCozeBatchTaskProgressResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/batch_get_progress',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_ids: _req['batch_task_ids'],
      is_test_run: _req['is_test_run'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/batch_task/update
   *
   * 更新批量任务
   */
  UpdateCozeBatchTask(
    req: flow_devops_coze_evaluation_v2.UpdateCozeBatchTaskReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.UpdateCozeBatchTaskResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/update',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      name: _req['name'],
      status: _req['status'],
      run_type: _req['run_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/coze_evaluation/v2/dataset/:dataset_id/row_group/insert */
  InsertRowGroups(
    req: flow_devops_coze_evaluation_v2.InsertRowGroupsReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.InsertRowGroupsResp> {
    const _req = req;
    const url = this.genBaseURL(
      `/api/devops/coze_evaluation/v2/dataset/${_req['dataset_id']}/row_group/insert`,
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      row_groups: _req['row_groups'],
      before_row_group_id: _req['before_row_group_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/coze_evaluation/v2/dataset/bind_dataset */
  BindDataset(
    req: flow_devops_coze_evaluation_v2.BindDatasetReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.BindDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/dataset/bind_dataset',
    );
    const method = 'POST';
    const data = {
      dataset_id: _req['dataset_id'],
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      dataset_name: _req['dataset_name'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/coze_evaluation/v2/dataset/generate_test_run
   *
   * 创建试运行评估集，并绑定BatchTask
   */
  GenerateTestRunDataset(
    req: flow_devops_coze_evaluation_v2.GenerateTestRunDatasetReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.GenerateTestRunDatasetResp> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/dataset/generate_test_run',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/coze_evaluation/evaluator/rule/overwrite */
  OverwriteRule(
    req: flow_devops_coze_evaluation_evaluator.OverwriteRuleRequest,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_evaluator.OverwriteRuleResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/evaluator/rule/overwrite',
    );
    const method = 'POST';
    const data = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      rule: _req['rule'],
      cid: _req['cid'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/devops/coze_evaluation/v2/batch_task/credits_check
   *
   * // 成本计算
   *
   * 余额校验&成本预估
   */
  CreditsCheck(
    req: flow_devops_coze_evaluation_v2.CreditsCheckRequest,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.CreditsCheckResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/devops/coze_evaluation/v2/batch_task/credits_check',
    );
    const method = 'GET';
    const params = {
      space_id: _req['space_id'],
      batch_task_id: _req['batch_task_id'],
      credits_check_type: _req['credits_check_type'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /** POST /api/devops/coze_evaluation/v2/dataset/clear */
  ClearTaskDataset(
    req: flow_devops_coze_evaluation_v2.ClearTaskDatasetReq,
    options?: T,
  ): Promise<flow_devops_coze_evaluation_v2.ClearTaskDatasetResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/coze_evaluation/v2/dataset/clear');
    const method = 'POST';
    const data = {
      batch_task_id: _req['batch_task_id'],
      space_id: _req['space_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
