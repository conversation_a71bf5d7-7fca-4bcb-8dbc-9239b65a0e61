/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as user from './user';
import * as bot from './bot';

export type Int64 = string | number;

/** DataType 数据内容类型
 NEXT ID: 2 */
export enum DataType {
  Undefined = 0,
  /** 纯文本类型 */
  PlainText = 1,
  /** markdown */
  Markdown = 2,
}

/** Kind 评测场景分类
 NEXT ID: 2 */
export enum Kind {
  Undefined = 0,
  /** 通用评估用例, 系统默认自带的 */
  Common = 1,
}

export enum QueriesOperation {
  Contains = 0,
  Equal = 1,
}

export enum QueriesValueType {
  String = 0,
  Enum = 1,
  Tree = 2,
}

/** QueryType
 NEXT ID: 3 */
export enum QueryType {
  Undefined = 0,
  /** 单轮对话 */
  Row = 1,
  /** 多轮对话 */
  RowGroup = 2,
}

/** TaskStatus 评测任务状态
 NEXT ID: 6 */
export enum TaskStatus {
  Undefined = 0,
  /** 任务创建 */
  Creating = 1,
  /** 任务执行 */
  Processing = 2,
  /** 任务终止 */
  Termination = 3,
  /** 任务完成 */
  Completed = 4,
  /** 任务失败 */
  Error = 5,
}

/** BatchTask 某个评测场景评测任务结果
 NEXT ID: 10 */
export interface BatchTask {
  /** 评测任务ID */
  batch_task_id?: string;
  /** 空间ID */
  space_id?: string;
  /** Bot ID */
  bot_id?: string;
  /** 任务状态 */
  status?: TaskStatus;
  /** 开始时间 */
  create_time_ms?: string;
  /** 更新时间 */
  update_time_ms?: string;
  /** 创建人 */
  creator?: user.User;
  /** 评测Bot详情 */
  bot_snapshot_version?: bot.BotSnapshotVersion;
  /** 评测场景 */
  scene?: Scene;
  /** 任务完成进度，保留小数点后3位 */
  progress?: number;
  /** 任务错误详情，用于展示 */
  err_msg?: string;
}

/** BatchTaskCheckInfo 轮询任务的返回信息
 NEXT ID: 4 */
export interface BatchTaskCheckInfo {
  /** 评测任务ID */
  batch_task_id?: string;
  /** 评测任务状态 */
  status?: TaskStatus;
}

/** BatchTaskReportDetail 任务评估明细
 NEXT ID: 6 */
export interface BatchTaskReportDetail {
  /** 任务ID */
  batch_task_id?: string;
  /** 空间ID */
  space_id?: string;
  /** Bot ID */
  bot_id?: string;
  /** 输入的列名 */
  columns?: Array<Column>;
  /** 行评测报告 */
  row_reports?: Array<RowReport>;
}

/** BatchTaskReportOverall 评测场景任务的汇总数据
 NEXT ID: 11 */
export interface BatchTaskReportOverall {
  /** 评测任务的ID */
  batch_task_id?: string;
  /** 空间ID */
  space_id?: string;
  /** Bot ID */
  bot_id?: string;
  /** 详细的评测Bot信息 */
  bot_snapshot_version?: bot.BotSnapshotVersion;
  /** 评测场景信息 */
  scene?: Scene;
  /** 模块汇总得分 */
  module_eval_report_overalls?: Array<ModuleEvalReportOverall>;
  /** 总Token数 */
  token?: string;
  /** 总耗时 */
  consuming_ms?: string;
  /** 开始时间 */
  create_time_ms?: string;
  /** 创建人 */
  creator?: user.User;
  /** 全局总分 */
  score?: string;
  /** 任务状态 */
  status?: TaskStatus;
  /** 任务完成进度，保留小数点后3位 */
  progress?: number;
  /** 任务错误详情，用于展示 */
  err_msg?: string;
  /** 同个场景上次评测任务的结果，用于环比 */
  last_eval_task_res?: LastEvalTaskRes;
}

/** BatchTaskScore 任务评估打分
 NEXT ID: 5 */
export interface BatchTaskScore {
  /** 任务ID */
  batch_task_id?: string;
  /** 空间ID */
  space_id?: string;
  /** Bot ID */
  bot_id?: string;
  /** 模块得分信息 */
  module_rule_scores?: Array<ModuleRuleScore>;
}

/** Column 输入数据Content列名
 NEXT ID: 3 */
export interface Column {
  column_id?: string;
  column_name?: string;
}

/** Content 数据集的内容
 NEXT ID: 4 */
export interface Content {
  /** 数据类型 */
  data_type?: DataType;
  /** 纯文本内容 */
  text?: string;
  /** markdown格式 */
  markdown_box?: MarkdownBox;
}

/** FieldMeta 任务评估报告字段meta信息
 NEXT ID: 4 */
export interface FieldMeta {
  /** 支持的筛选动作，如contains, equal */
  operations?: Array<QueriesOperation>;
  /** value类型，如string, enum, tree */
  value_type?: QueriesValueType;
  /** value结果 */
  value_options?: Array<ValueOption>;
}

/** LastEvalTaskRes 同个场景上次评测任务的结果，用于环比
 NEXT ID: 3 */
export interface LastEvalTaskRes {
  /** 全局总分 */
  score?: string;
  /** 模块汇总得分 */
  module_eval_report_overalls?: Array<ModuleEvalReportOverall>;
}

/** MarkdownBox 类型，用于展示markdown内容，字节基于标准 markdown 语法进行了扩展和修改
 @flow-web/md-box: 
 NEXT ID: 2 */
export interface MarkdownBox {
  text?: string;
}

/** Module 评测的模块
 NEXT ID: 4 */
export interface Module {
  /** 模块id */
  module_id?: string;
  /** 模块名字 */
  name?: string;
  /** 模块下的用例分组 */
  sub_divisions?: Array<SubDivision>;
}

/** ModuleEvalReportOverall 模块汇总得分
 NEXT ID: 3 */
export interface ModuleEvalReportOverall {
  /** 模块信息 */
  module?: Module;
  /** 得分 */
  score?: number;
}

/** ModuleRule 模块规则
 NEXT ID: 3 */
export interface ModuleRule {
  module?: Module;
  rules?: Array<RuleScore>;
}

/** ModuleRuleScore 模块规则得分
 NEXT ID: 4 */
export interface ModuleRuleScore {
  /** 模块信息 */
  module?: Module;
  /** 规则得分 */
  rule_scores?: Array<RuleScore>;
  /** 汇总得分 */
  score?: number;
}

export interface QueriesFilter {
  /** 筛选动作 */
  operation: QueriesOperation;
  /** ValueType=String时，在此处传值 */
  string_value?: string;
  /** ValueType=Enum/Tree，在此处传值，或的关系 */
  enums?: Array<string>;
}

/** RowEvalReport 行评估结果数据
 NEXT ID: 5 */
export interface RowEvalReport {
  /** input token */
  input_token?: string;
  /** output token */
  output_token?: string;
  /** 耗时 */
  consuming_ms?: string;
}

/** RowReport 行数据评估报告
 NEXT ID: 10 */
export interface RowReport {
  /** 行ID */
  row_id?: string;
  /** 组ID */
  row_group_id?: string;
  /** 用例输入内容，对应Columns */
  contents?: Array<Content>;
  /** 输出 */
  output?: Content;
  /** 对话类型 */
  query_type?: QueryType;
  /** 模块规则 */
  module_rules?: Array<ModuleRule>;
  /** 开始时间 */
  create_time_ms?: string;
  /** 行评估结果 */
  row_eval_report?: RowEvalReport;
}

/** Rule 评测规则
 NEXT ID: 5 */
export interface Rule {
  /** 规则id */
  rule_id?: string;
  /** 规则展示名称 */
  rule_name?: string;
  /** 评估器id */
  evaluator_type?: string;
  /** 评估器名称 */
  evaluator_type_name?: string;
}

/** RuleScore 规则得分
 NEXT ID: 6 */
export interface RuleScore {
  /** 规则信息 */
  rule?: Rule;
  /** 执行用例数量 */
  executed_case_count?: number;
  /** 总共用例数量 */
  total_case_count?: number;
  /** 耗时 */
  consuming_ms?: string;
  /** 得分 */
  score?: number;
}

/** Scene 评测的场景
 NEXT ID: 5 */
export interface Scene {
  /** 场景id */
  scene_id?: string;
  /** 场景名字 */
  name?: string;
  /** 所属分类 */
  kind?: Kind;
  /** 场景下的模块 */
  modules?: Array<Module>;
}

/** SubDivision 评测的用例分组
 NEXT ID: 3 */
export interface SubDivision {
  /** 分组id */
  sub_division_id?: string;
  /** 分组名字 */
  name?: string;
}

export interface ValueOption {
  /** 前端用来筛选的传参 */
  key?: string;
  /** 前端展示内容 */
  value?: string;
  /** 子级内容 */
  SonValueOption?: Array<ValueOption>;
}
/* eslint-enable */
