/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as task from './task';

export type Int64 = string | number;

/** SetupMode 初始化模式
 NEXT ID: 4 */
export enum SetupMode {
  Undefined = 0,
  /** 插入或者更新 */
  Upsert = 1,
  /** 删除用例 */
  Remove = 2,
  /** 读取用例信息 */
  Read = 3,
}

/** CaseConfig 用例配置
 NEXT ID: 6 */
export interface CaseConfig {
  /** 用例名称 */
  name?: string;
  /** 数据集列名字 */
  columns?: Array<string>;
  /** 数据行 */
  rows?: Array<Row>;
  /** 评估器类型 */
  evaluator_type?: Int64;
  /** 读取到的用例id */
  read_case_id?: string;
}

/** ReadConfig 读取模式下的配置内容
 NEXT ID: 2 */
export interface ReadConfig {
  /** 读取的case名称，如果不传则返回全部的 */
  name?: string;
}

/** RemoveConfig 删除模式下的配置内容
 NEXT ID: 2 */
export interface RemoveConfig {
  /** 删除的case名称 */
  name?: string;
}

/** Row 行中的数据
 NEXT ID: 2 */
export interface Row {
  /** 一行的内容 */
  contents?: Array<task.Content>;
}

/** SetupConfig 初始化配置
 NEXT ID: 5 */
export interface SetupConfig {
  setup_mode: SetupMode;
  upsert_config?: UpsertConfig;
  remove_config?: RemoveConfig;
  read_config?: ReadConfig;
}

/** UpsertConfig 插入或者更新模式的配置内容
 NEXT ID: 2 */
export interface UpsertConfig {
  /** 用例配置信息 */
  case_config?: CaseConfig;
}
/* eslint-enable */
