/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** BotSnapshotVersion 评测的快照Bot */
export interface BotSnapshotVersion {
  /** 空间ID */
  space_id?: string;
  /** BotID */
  bot_id?: string;
  /** 用于展示的语义化快照version */
  snap_version?: string;
  /** 版本创建时间 */
  create_time_ms?: string;
  /** 对应的commmit版本 */
  commit_version?: string;
}
/* eslint-enable */
