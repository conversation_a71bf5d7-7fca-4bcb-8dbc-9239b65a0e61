/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as flow_devops_evaluation_task from './flow_devops_evaluation_task';
import * as flow_devops_evaluation_dataset from './flow_devops_evaluation_dataset';
import * as flow_devops_evaluation_evaluator from './flow_devops_evaluation_evaluator';
import * as flow_devops_evaluation_entity from './flow_devops_evaluation_entity';

export type Int64 = string | number;

export enum BatchTaskStatus {
  /** 创建完待运行 */
  Awaiting = 1,
  /** 进行中 */
  InProgress = 2,
  /** 执行完成，全部成功 */
  Complete = 3,
  /** 执行完成，(部分)任务失败 */
  Failed = 4,
  /** 创建中 */
  Creating = 5,
  /** 暂停 */
  Paused = 6,
  /** 试运行 */
  TestRun = 7,
}

export enum CozeDatasetTemplateType {
  Agent = 1,
  Workflow = 2,
  Model = 3,
  All = 4,
}

export enum CozeDatasetType {
  Template = 1,
  BuiltIn = 2,
}

export enum CozeVersion {
  Normal = 1,
  Professional = 2,
}

export enum CreditsCheckType {
  Retry = 1,
  TestRun = 2,
  Formal = 3,
  CheckFreeCaseOnly = 4,
}

export enum ObjectType {
  Bot = 1,
  Workflow = 2,
  Model = 3,
}

export enum RunType {
  FullEvaluation = 1,
  AgentOnly = 2,
}

export interface BatchGetCozeBatchTaskProgressReq {
  space_id: Int64;
  batch_task_ids?: Array<Int64>;
  /** 默认false */
  is_test_run?: boolean;
  Base?: base.Base;
}

export interface BatchGetCozeBatchTaskProgressResp {
  coze_batch_task_progress?: Array<CozeBatchTaskProgress>;
  BaseResp?: base.BaseResp;
}

export interface BindCozeBotReq {
  batch_task_id: Int64;
  space_id: Int64;
  eval_objects?: Array<flow_devops_evaluation_task.EvalObject>;
  is_test_run?: boolean;
  Base?: base.Base;
}

export interface BindCozeBotResp {
  BaseResp?: base.BaseResp;
}

export interface BindDatasetReq {
  dataset_id: Int64;
  space_id: Int64;
  batch_task_id: Int64;
  dataset_name?: string;
  Base?: base.Base;
}

export interface BindDatasetResp {
  dataset_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface ClearTaskDatasetReq {
  batch_task_id: Int64;
  space_id: Int64;
  Base?: base.Base;
}

export interface ClearTaskDatasetResp {
  BaseResp?: base.BaseResp;
}

export interface CloneCozeBatchTaskReq {
  space_id: Int64;
  source_batch_task_id: Int64;
  Base?: base.Base;
}

export interface CloneCozeBatchTaskResp {
  id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface CostConsumption {
  total_token?: Int64;
  input_token?: Int64;
  output_token?: Int64;
  credits?: number;
  evaluations?: Int64;
}

export interface CostPredication {
  free_case_cost?: Int64;
  case_number?: Int64;
  total_token?: Int64;
  batch_operation_token?: Int64;
  evaluation_rules_token?: Int64;
  credits_needed?: Int64;
  estimated_time?: Int64;
  /** 商业化三期后使用，资源点预估 */
  coze_credits_needed?: Int64;
}

export interface CozeBatchTask {
  coze_batch_task_meta?: CozeBatchTaskMeta;
  coze_batch_task_progress?: CozeBatchTaskProgress;
}

export interface CozeBatchTaskMeta {
  id: Int64;
  space_id?: Int64;
  app_id?: Int64;
  name?: string;
  eval_objects?: Array<flow_devops_evaluation_task.EvalObject>;
  datasets?: Array<flow_devops_evaluation_dataset.DatasetInfo>;
  rule?: flow_devops_evaluation_evaluator.Rule;
  /** 默认 FullEvaluation */
  run_type?: RunType;
  built_in_dataset_ids?: Record<Int64, Int64>;
  creator_id?: Int64;
  created_at?: Int64;
  updated_at?: Int64;
}

export interface CozeBatchTaskProgress {
  id?: Int64;
  cost_consumption?: CostConsumption;
  status?: BatchTaskStatus;
  tasks?: Array<flow_devops_evaluation_task.Task>;
  /** 已完成任务耗时总和 millisecond */
  duration?: Int64;
  /** 预估完成时间 millisecond */
  estimated_time_remaining?: Int64;
  total_row_count?: Int64;
}

export interface CreateCozeBatchTaskReq {
  space_id: Int64;
  name?: string;
  Base?: base.Base;
}

export interface CreateCozeBatchTaskResp {
  id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface CreateDatasetReq {
  space_id: Int64;
  name?: string;
  batch_task_id?: Int64;
  is_test_run?: boolean;
  /** 是否发布到 dataset 列表, 默认不发部 */
  publish_option?: flow_devops_evaluation_dataset.PublishOption;
  /** 是否打上tag, 默认不打上 */
  tag_list?: Array<flow_devops_evaluation_entity.TagInfo>;
  column_schema?: Array<flow_devops_evaluation_dataset.ColumnInfo>;
  row_groups?: Array<flow_devops_evaluation_dataset.RowGroup>;
  Base?: base.Base;
}

export interface CreateDatasetResp {
  dataset_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface CreditsCheckRequest {
  space_id: Int64;
  batch_task_id?: Int64;
  /** 默认Formal */
  credits_check_type?: CreditsCheckType;
  Base?: base.Base;
}

export interface CreditsCheckResponse {
  pass?: boolean;
  coze_version?: CozeVersion;
  cost_predication?: CostPredication;
  resource_left?: ResourceLeft;
  evaluation_model?: string;
  BaseResp?: base.BaseResp;
}

export interface DashboardTaskRequest {
  task_id: Int64;
  space_id: Int64;
  batch_task_id: Int64;
  row_group_run_state?: flow_devops_evaluation_task.RowGroupRunState;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface DashboardTaskResponse {
  /** 每一行是一条数据集的(input, reference_output, variable)
和数据集里的ColumnName一致 */
  column_name: Array<string>;
  dashboard_row_groups: Array<flow_devops_evaluation_task.DashboardRowGroup>;
  column_rule_info?: Array<flow_devops_evaluation_task.ColumnRuleInfo>;
  default_rule_group_id?: Int64;
  task_id?: Int64;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface DeleteCozeBatchTaskReq {
  space_id: Int64;
  batch_task_id: Int64;
  Base?: base.Base;
}

export interface DeleteCozeBatchTaskResp {
  BaseResp?: base.BaseResp;
}

export interface DeleteDatasetReq {
  dataset_id: Int64;
  space_id: Int64;
  batch_task_id?: Int64;
  Base?: base.Base;
}

export interface DeleteDatasetResp {
  BaseResp?: base.BaseResp;
}

export interface GenerateTestRunDatasetReq {
  space_id: Int64;
  batch_task_id: Int64;
  Base?: base.Base;
}

export interface GenerateTestRunDatasetResp {
  dataset_id?: Int64;
  BaseResp?: base.BaseResp;
}

export interface GetCozeBatchTaskReq {
  space_id: Int64;
  batch_task_id: Int64;
  Base?: base.Base;
}

export interface GetCozeBatchTaskResp {
  coze_batch_task?: CozeBatchTask;
  BaseResp?: base.BaseResp;
}

export interface GetOperableBotReq {
  space_id: Int64;
  bot_ids?: Array<Int64>;
  Base?: base.Base;
}

export interface GetOperableBotResp {
  operable_bot_ids?: Array<Int64>;
  BaseResp?: base.BaseResp;
}

export interface InsertRowGroupsReq {
  dataset_id: Int64;
  space_id: Int64;
  row_groups: Array<flow_devops_evaluation_dataset.RowGroup>;
  /** append to tail if null */
  before_row_group_id?: Int64;
  Base?: base.Base;
}

export interface InsertRowGroupsResp {
  row_group_ids?: Array<Int64>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface KillBatchTaskReq {
  space_id: Int64;
  batch_task_id: Int64;
  /** 默认false */
  is_test_run?: boolean;
  Base?: base.Base;
}

export interface KillBatchTaskResp {
  BaseResp?: base.BaseResp;
}

export interface ListCozeBatchTaskReq {
  space_id: Int64;
  creator_id?: Int64;
  type?: Array<ObjectType>;
  status?: Array<BatchTaskStatus>;
  search_name?: string;
  offset?: number;
  limit?: number;
  Base?: base.Base;
}

export interface ListCozeBatchTaskResp {
  coze_batch_tasks?: Array<CozeBatchTask>;
  has_more?: boolean;
  BaseResp?: base.BaseResp;
}

export interface ListDatasetReq {
  space_id: Int64;
  /** 默认Template */
  dataset_type?: CozeDatasetType;
  /** 暂时无效，默认Agent */
  template_type?: CozeDatasetTemplateType;
  offset?: number;
  limit?: number;
  Base?: base.Base;
}

export interface ListDatasetResp {
  datasets?: Array<flow_devops_evaluation_dataset.DatasetInfo>;
  max_dataset_line?: Int64;
  cursor?: string;
  has_more?: boolean;
  BaseResp?: base.BaseResp;
}

export interface ListRowGroupsReq {
  dataset_id: Int64;
  space_id: Int64;
  page?: Int64;
  page_size?: Int64;
  Base?: base.Base;
}

export interface ListRowGroupsResp {
  row_groups?: Array<flow_devops_evaluation_dataset.RowGroup>;
  column_schema?: Array<flow_devops_evaluation_dataset.ColumnInfo>;
  total?: Int64;
  BaseResp?: base.BaseResp;
}

export interface ResourceLeft {
  free_case?: Int64;
  total_free_case?: Int64;
}

export interface RetryBatchTaskReq {
  space_id: Int64;
  batch_task_id: Int64;
  Base?: base.Base;
}

export interface RetryBatchTaskResp {
  BaseResp?: base.BaseResp;
}

export interface SubmitBatchTaskReq {
  space_id: Int64;
  batch_task_id: Int64;
  /** 默认false */
  is_test_run?: boolean;
  Base?: base.Base;
}

export interface SubmitBatchTaskResp {
  task_ids?: Array<Int64>;
  BaseResp?: base.BaseResp;
}

export interface UpdateCozeBatchTaskReq {
  space_id: Int64;
  batch_task_id: Int64;
  name?: string;
  status?: BatchTaskStatus;
  /** 默认 FullEvaluation */
  run_type?: RunType;
  Base?: base.Base;
}

export interface UpdateCozeBatchTaskResp {
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
