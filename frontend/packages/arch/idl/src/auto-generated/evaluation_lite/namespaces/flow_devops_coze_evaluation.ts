/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as task from './task';
import * as bot from './bot';
import * as setup from './setup';

export type Int64 = string | number;

export interface CheckBatchTaskReq {
  space_id: string;
  bot_id: string;
  /** 评测历史使用 */
  batch_task_ids?: Array<string>;
  Base?: base.Base;
}

export interface CheckBatchTaskResp {
  batch_tasks?: Record<string, task.BatchTaskCheckInfo>;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteTaskReq {
  space_id: string;
  batch_task_id: string;
  bot_id?: string;
  Base?: base.Base;
}

export interface DeleteTaskResp {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetBatchTaskReportDetailReq {
  space_id: string;
  bot_id: string;
  batch_task_id?: string;
  offset?: number;
  limit?: number;
  /** key: field名称，对应 GetTaskReportFieldMetasResp.field_metas 的key */
  filters?: Record<string, task.QueriesFilter>;
  Base?: base.Base;
}

export interface GetBatchTaskReportDetailResp {
  detail?: task.BatchTaskReportDetail;
  has_more?: boolean;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetBatchTaskReportOverallReq {
  space_id: string;
  bot_id: string;
  /** 评测历史使用 */
  batch_task_id?: string;
  /** 点击评测用例，查看该用例最新的一个BatchTask时使用 */
  scene_id?: string;
  Base?: base.Base;
}

export interface GetBatchTaskReportOverallResp {
  overall?: task.BatchTaskReportOverall;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetBatchTaskScoreReq {
  space_id: string;
  bot_id: string;
  batch_task_id?: string;
  Base?: base.Base;
}

export interface GetBatchTaskScoreResp {
  score?: task.BatchTaskScore;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetCozeEvalGrayReq {
  space_id: string;
  bot_id: string;
  Base?: base.Base;
}

export interface GetCozeEvalGrayResp {
  is_hit_gray: boolean;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetTaskReportFilterMetaReq {
  space_id: string;
  bot_id: string;
  Base?: base.Base;
}

export interface GetTaskReportFilterMetaResp {
  /** key: field名称，如"GroupID"，"Input"，"Rules"等，对应 GetBatchTaskReportDetailReq.filters 的key */
  field_metas?: Record<string, task.FieldMeta>;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface KillTaskReq {
  space_id: string;
  batch_task_id: string;
  bot_id?: string;
  Base?: base.Base;
}

export interface KillTaskResp {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListBatchTaskReq {
  space_id: string;
  bot_id: string;
  scene_id?: string;
  offset?: number;
  limit?: number;
  Base?: base.Base;
}

export interface ListBatchTaskResp {
  batch_tasks?: Array<task.BatchTask>;
  has_more?: boolean;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListEvalVersionReq {
  space_id: string;
  bot_id: string;
  Base?: base.Base;
}

export interface ListEvalVersionResp {
  bot_snapshot_versions?: Array<bot.BotSnapshotVersion>;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface ListSceneReq {
  space_id: string;
  bot_id: string;
  Base?: base.Base;
}

export interface ListSceneResp {
  scenes?: Array<task.Scene>;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface RetryTaskReq {
  space_id: string;
  batch_task_id: string;
  bot_id?: string;
  Base?: base.Base;
}

export interface RetryTaskResp {
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface SetupReq {
  config?: setup.SetupConfig;
  Base?: base.Base;
}

export interface SetupResp {
  case_configs?: Array<setup.CaseConfig>;
  detail?: string;
  BaseResp?: base.BaseResp;
}

export interface SubmitTaskReq {
  /** Bot机器人ID */
  space_id: string;
  /** Bot机器人ID */
  bot_id: string;
  /** 评测场景ID */
  scene_id?: string;
  Base?: base.Base;
}

export interface SubmitTaskResp {
  /** 批量任务id */
  batch_task_id?: string;
  code: number;
  msg: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
