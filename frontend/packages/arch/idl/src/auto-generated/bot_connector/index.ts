/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as bot_common from './namespaces/bot_common';
import * as bot_connector from './namespaces/bot_connector';

export { base, bot_common, bot_connector };
export * from './namespaces/base';
export * from './namespaces/bot_common';
export * from './namespaces/bot_connector';

export type Int64 = string | number;

export default class BotConnectorService<T> {
  private request: any = () => {
    throw new Error('BotConnectorService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /** GET /v3/chat/retrieve */
  RetrieveChatOpen(
    req: bot_connector.RetrieveChatOpenRequest,
    options?: T,
  ): Promise<bot_connector.RetrieveChatOpenResponse> {
    const _req = req;
    const url = this.genBaseURL('/v3/chat/retrieve');
    const method = 'GET';
    const params = {
      conversation_id: _req['conversation_id'],
      chat_id: _req['chat_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v1/conversation/message/retrieve
   *
   * 查询单条消息详情
   */
  RetrieveMessageApi(
    req: bot_connector.RetrieveMessageApiRequest,
    options?: T,
  ): Promise<bot_connector.RetrieveMessageApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/conversation/message/retrieve');
    const method = 'GET';
    const params = {
      conversation_id: _req['conversation_id'],
      message_id: _req['message_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /v1/conversation/message/create
   *
   * 创建单条消息
   */
  CreateMessageApi(
    req: bot_connector.CreateMessageApiRequest,
    options?: T,
  ): Promise<bot_connector.CreateMessageApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/conversation/message/create');
    const method = 'POST';
    const data = {
      role: _req['role'],
      content: _req['content'],
      meta_data: _req['meta_data'],
      content_type: _req['content_type'],
      Base: _req['Base'],
    };
    const params = { conversation_id: _req['conversation_id'] };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * POST /v1/conversation/message/modify
   *
   * 修改单条消息
   */
  ModifyMessageApi(
    req: bot_connector.ModifyMessageApiRequest,
    options?: T,
  ): Promise<bot_connector.ModifyMessageApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/conversation/message/modify');
    const method = 'POST';
    const data = {
      meta_data: _req['meta_data'],
      content: _req['content'],
      content_type: _req['content_type'],
      Base: _req['Base'],
    };
    const params = {
      conversation_id: _req['conversation_id'],
      message_id: _req['message_id'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /**
   * GET /v1/conversation/retrieve
   *
   * 查询会话详情
   */
  RetrieveConversationApi(
    req: bot_connector.RetrieveConversationApiRequest,
    options?: T,
  ): Promise<bot_connector.RetrieveConversationApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/conversation/retrieve');
    const method = 'GET';
    const params = {
      conversation_id: _req['conversation_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * GET /v3/chat/message/list
   *
   * 查询单次运行的消息列表
   */
  ListChatMessageApi(
    req: bot_connector.ListChatMessageApiRequest,
    options?: T,
  ): Promise<bot_connector.ListChatMessageApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v3/chat/message/list');
    const method = 'GET';
    const params = {
      conversation_id: _req['conversation_id'],
      chat_id: _req['chat_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /v1/conversation/create
   *
   * 能力开放接口
   *
   * 创建会话
   */
  CreateConversationApi(
    req?: bot_connector.CreateConversationApiRequest,
    options?: T,
  ): Promise<bot_connector.CreateConversationApiResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/v1/conversation/create');
    const method = 'POST';
    const data = {
      meta_data: _req['meta_data'],
      messages: _req['messages'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      app_id: _req['app_id'],
      conversation_name: _req['conversation_name'],
      get_or_create: _req['get_or_create'],
      draft_mode: _req['draft_mode'],
      workflow_id: _req['workflow_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /v1/conversation/message/list
   *
   * 查询消息列表
   */
  ListMessageApi(
    req: bot_connector.ListMessageApiRequest,
    options?: T,
  ): Promise<bot_connector.ListMessageApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/conversation/message/list');
    const method = 'POST';
    const data = {
      limit: _req['limit'],
      order: _req['order'],
      chat_id: _req['chat_id'],
      before_id: _req['before_id'],
      after_id: _req['after_id'],
      Base: _req['Base'],
    };
    const params = { conversation_id: _req['conversation_id'] };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /v3/chat/cancel */
  CancelChatApi(
    req: bot_connector.CancelChatApiRequest,
    options?: T,
  ): Promise<bot_connector.CancelChatApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v3/chat/cancel');
    const method = 'POST';
    const data = {
      chat_id: _req['chat_id'],
      conversation_id: _req['conversation_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /v1/conversation/message/delete */
  DeleteMessageApi(
    req: bot_connector.DeleteMessageApiRequest,
    options?: T,
  ): Promise<bot_connector.DeleteMessageApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/conversation/message/delete');
    const method = 'POST';
    const data = { Base: _req['Base'] };
    const params = {
      message_id: _req['message_id'],
      conversation_id: _req['conversation_id'],
    };
    return this.request({ url, method, data, params }, options);
  }

  /** POST /v1/conversations/:conversation_id/clear */
  ClearConversationApi(
    req: bot_connector.ClearConversationApiRequest,
    options?: T,
  ): Promise<bot_connector.ClearConversationApiResponse> {
    const _req = req;
    const url = this.genBaseURL(
      `/v1/conversations/${_req['conversation_id']}/clear`,
    );
    const method = 'POST';
    const data = { Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** GET /v1/conversations */
  ListConversationsApi(
    req: bot_connector.ListConversationsApiRequest,
    options?: T,
  ): Promise<bot_connector.ListConversationsApiResponse> {
    const _req = req;
    const url = this.genBaseURL('/v1/conversations');
    const method = 'GET';
    const params = {
      page_num: _req['page_num'],
      page_size: _req['page_size'],
      sort_order: _req['sort_order'],
      sort_field: _req['sort_field'],
      bot_id: _req['bot_id'],
      connector_id: _req['connector_id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, params }, options);
  }
}
/* eslint-enable */
