/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';
import * as bot_common from './bot_common';

export type Int64 = string | number;

/** ===================================消息接口开始===================================== */
export enum ChatMode {
  Normal = 0,
  Group = 1,
}

/** 上下文允许传输的类型 */
export enum ContextMode {
  Chat = 0,
  FunctionCall_1 = 1,
  FunctionCall_2 = 2,
  FunctionCall_3 = 3,
}

export enum FileType {
  file = 1,
  image = 2,
}

export enum LoadDirection {
  Unknown = 0,
  Prev = 1,
  Next = 2,
}

export enum MessageSource {
  /** 普通聊天消息 */
  Chat = 0,
  /** 定时任务 */
  TaskManualTrigger = 1,
  /** 通知 */
  Notice = 2,
  /** 异步结果 */
  AsyncResult = 3,
}

export enum MetaType {
  /** Compatible value */
  Default_0 = 0,
  /** 端侧直接替换 */
  Replaceable = 1,
  /** 插入引用 */
  Insertable = 2,
  /** 文档引用 */
  DocumentRef = 3,
  /** 知识库引用卡片 本次新增 */
  KnowledgeCard = 4,
  /** 嵌入的多媒体信息，只是alice给端上用的，因为全链路复用这一个字段，所以在这儿改了 */
  EmbeddedMultimedia = 100,
}

export enum RoleType {
  user = 1,
  assistant = 2,
}

export enum SuggestReplyMode {
  System = 0,
  Custom = 1,
  Disable = 2,
  /** (Agent)使用源bot的配置 */
  OriBot = 3,
}

export interface BotInfo {
  /** bot id */
  Id: Int64;
  /** bot 名称 */
  Name?: string;
  /** bot 描述 */
  Description?: string;
  /** bot 头像 */
  Icon?: string;
  /** prompt */
  PromptInfo?: PromptInfo;
  /** 模型配置 */
  ModelInfo?: ModelInfo;
  /** plugin列表 */
  PluginList?: Array<PluginInfo>;
  /** 开场白配置 */
  OnboardingInfo?: OnboardingInfo;
  /** workflow 列表 */
  WorkFlowList?: Array<WorkFlowInfo>;
  /** suggest配置 */
  SuggestReplyInfo?: SuggestReplyInfo;
  CreateTime?: Int64;
  UpdateTime?: Int64;
}

export interface BotUserConversation {
  BotID?: Int64;
  UserID?: string;
  SessionName?: string;
  ConnectorID?: Int64;
  ConversationID?: Int64;
  AccountID?: Int64;
  CreatorID?: string;
}

export interface CancelChatApiRequest {
  chat_id: string;
  conversation_id: string;
  Base?: base.Base;
}

export interface CancelChatApiResponse {
  data?: bot_common.ChatV3ChatDetail;
  BaseResp?: base.BaseResp;
}

export interface ChatMessage {
  role?: string;
  type?: string;
  content?: string;
  content_type?: string;
  msg_id?: string;
  content_time?: Int64;
  message_id?: Int64;
  reply_id?: Int64;
  section_id?: Int64;
  status?: string;
  ext?: string;
  broken_position?: number;
  display_content?: string;
  bot_id?: Int64;
  user_id?: string;
  exclude_context?: boolean;
  message_index?: Int64;
  source?: MessageSource;
  meta_infos?: Array<MetaInfo>;
  query_snap?: string;
  card_status?: Record<string, string>;
  reasoning_content?: string;
}

export interface ClearConversationApiRequest {
  conversation_id: string;
  Base?: base.Base;
}

export interface ClearConversationApiResponse {
  /** 错误code */
  code?: Int64;
  /** 错误消息 */
  msg?: string;
  /** section 信息 */
  data?: Section;
  BaseResp?: base.BaseResp;
}

export interface ConversationData {
  id?: string;
  created_at?: Int64;
  meta_data?: Record<string, string>;
  creator_d?: string;
  connector_id?: string;
  last_section_id?: string;
  account_id?: Int64;
}

export interface CreateConversationApiRequest {
  meta_data?: Record<string, string>;
  /** 校验最多16个 */
  messages?: Array<EnterMessage>;
  bot_id?: string;
  connector_id?: string;
  app_id?: string;
  conversation_name?: string;
  get_or_create?: boolean;
  draft_mode?: boolean;
  workflow_id?: string;
  Base?: base.Base;
}

export interface CreateConversationApiResponse {
  data?: ConversationData;
  BaseResp: base.BaseResp;
}

export interface CreateMessageApiRequest {
  conversation_id: Int64;
  /** 已TODO 字段打平 */
  role: string;
  /** 内容 */
  content?: string;
  meta_data?: Record<string, string>;
  content_type?: string;
  Base?: base.Base;
}

export interface CreateMessageApiResponse {
  data?: OpenMessageApi;
  BaseResp: base.BaseResp;
}

export interface DatasetData {
  Name?: string;
  ID?: string;
}

/** dataset */
export interface DatasetInfo {
  Dataset?: Array<DatasetData>;
  TopK?: number;
  MinScore?: number;
  Auto?: boolean;
}

export interface DeleteMessageApiRequest {
  message_id: string;
  conversation_id: string;
  Base?: base.Base;
}

export interface DeleteMessageApiResponse {
  data?: OpenMessageApi;
  BaseResp?: base.BaseResp;
}

/** 批量删除message */
export interface DeleteMessageBody {
  MessageId: Int64;
  ConversationId: Int64;
}

export interface DraftMessageIdInfo {
  PushUuid?: string;
  Id?: string;
}

export interface EnterMessage {
  role: string;
  /** 内容 */
  content?: string;
  meta_data?: Record<string, string>;
  /** text/card/object_string */
  content_type?: string;
  type?: string;
}

export interface IdempotentInfo {
  source: MessageSource;
  BatchId: string;
  UniqueId: string;
}

export interface ListChatMessageApiRequest {
  /** connector层的会话id */
  conversation_id: string;
  /** 运行id */
  chat_id: string;
  Base?: base.Base;
}

export interface ListChatMessageApiResponse {
  data?: Array<bot_common.ChatV3MessageDetail>;
  BaseResp?: base.BaseResp;
}

export interface ListConversationData {
  conversations?: Array<ConversationData>;
  has_more?: boolean;
}

export interface ListConversationsApiRequest {
  page_num?: Int64;
  page_size?: Int64;
  /** 可选值：ASC、DESC */
  sort_order?: string;
  /** 可选值：created_at创建时间 */
  sort_field?: string;
  bot_id: string;
  connector_id?: string;
  Base?: base.Base;
}

export interface ListConversationsApiResponse {
  /** 错误code */
  code?: Int64;
  /** 错误消息 */
  msg?: string;
  data?: ListConversationData;
  BaseResp?: base.BaseResp;
}

export interface ListMessageApiRequest {
  /** connector层的会话id */
  conversation_id: Int64;
  /** 每页限制条数  TODO 限制50条 */
  limit?: Int64;
  /** 查询顺序  desc倒序 asc正序 TODO 默认倒序 */
  order?: string;
  /** 运行id */
  chat_id?: string;
  /** 前序消息游标ID  已TODO str */
  before_id?: string;
  /** 后序消息游标ID  已TODO str */
  after_id?: string;
  Base?: base.Base;
}

export interface ListMessageApiResponse {
  data?: Array<OpenMessageApi>;
  has_more?: boolean;
  first_id?: string;
  last_id?: string;
  BaseResp: base.BaseResp;
}

export interface ListMessageQueryOption {
  NeedMessageContext?: boolean;
}

export interface Message {
  /** user or assistant */
  Role: string;
  /** the content of the message */
  Content: string;
  ConversationId: Int64;
  /** customer meta data */
  CustomerData?: Record<string, string>;
  /** 创建时间 */
  CreateTime?: Int64;
}

export interface MetaInfo {
  type?: MetaType;
  info?: string;
}

export interface ModelInfo {
  ModelType?: Int64;
  Temperature?: number;
  MaxTokens?: number;
  TopP?: number;
  FrequencyPenalty?: number;
  PresencePenalty?: number;
  ShortMemoryPolicy?: ShortMemoryPolicy;
}

export interface ModifyMessageApiRequest {
  conversation_id: Int64;
  message_id: Int64;
  meta_data?: Record<string, string>;
  /** 内容 */
  content?: string;
  content_type?: string;
  Base?: base.Base;
}

export interface ModifyMessageApiResponse {
  message?: OpenMessageApi;
  BaseResp: base.BaseResp;
}

export interface MultiData {
  Url: string;
  Name?: string;
  /** 文件/图片/audio等 */
  Type: FileType;
}

export interface OnboardingInfo {
  /** 开场白 */
  Prologue?: string;
  /** 建议问题 */
  SuggestedQuestions?: Array<string>;
}

export interface OpenMessage {
  /** 主键ID */
  Id?: Int64;
  /** bot id */
  BotId?: Int64;
  Role?: RoleType;
  /** 内容 */
  Content?: string;
  /** conversation id */
  ConversationId?: Int64;
  CustomerData?: Record<string, string>;
  /** 创建时间 */
  CreatedAt?: Int64;
  /** 更新时间 */
  UpdatedAt?: Int64;
  /** 多模态数据 */
  MultiData?: Array<MultiData>;
  ChatId?: string;
  ContentType?: number;
  CustomerDataApi?: Record<string, string>;
  Type?: number;
  SectionID?: Int64;
  ReasoningContent?: string;
}

export interface OpenMessageApi {
  /** 主键ID */
  id?: string;
  /** bot id //已TODO 所有的i64加注解str,入参和出参都要 */
  bot_id?: string;
  role?: string;
  /** 内容 */
  content?: string;
  /** conversation id */
  conversation_id?: string;
  meta_data?: Record<string, string>;
  /** 创建时间 */
  created_at?: Int64;
  /** 更新时间 //已TODO 时间改成int */
  updated_at?: Int64;
  chat_id?: string;
  content_type?: string;
  type?: string;
  section_id?: string;
  reasoning_content?: string;
}

export interface PlaygroundOriginMessage {
  Query: PoMessage;
  AnswerList: Array<PoMessage>;
  ContextList: Array<PoMessage>;
  SuggestList: Array<PoMessage>;
}

export interface PluginIdInfo {
  PluginId: Int64;
  ApiId?: Int64;
}

export interface PluginIdList {
  IdList?: Array<PluginIdInfo>;
}

export interface PluginInfo {
  PluginIdInfo: PluginIdInfo;
}

export interface PoMessage {
  ContentType: number;
  Content: string;
  Ext: string;
  DraftMessageIdInfo: DraftMessageIdInfo;
}

export interface PromptInfo {
  /** 文本prompt */
  Prompt?: string;
}

export interface RetrieveChatOpenRequest {
  conversation_id: string;
  chat_id: string;
  Base?: base.Base;
}

export interface RetrieveChatOpenResponse {
  data?: bot_common.ChatV3ChatDetail;
  /** 11: optional RequiredAction RequiredAction (api.body = "required_action") */
  BaseResp?: base.BaseResp;
}

export interface RetrieveConversationApiRequest {
  conversation_id: Int64;
  Base?: base.Base;
}

export interface RetrieveConversationApiResponse {
  data?: ConversationData;
  BaseResp: base.BaseResp;
}

export interface RetrieveMessageApiRequest {
  conversation_id: Int64;
  message_id: Int64;
  Base?: base.Base;
}

export interface RetrieveMessageApiResponse {
  data?: OpenMessageApi;
  BaseResp: base.BaseResp;
}

export interface Section {
  id?: string;
  conversation_id?: string;
}

export interface ShortMemoryPolicy {
  ContextMode?: ContextMode;
  HistoryRound?: number;
}

/** suggest */
export interface SuggestReplyInfo {
  SuggestReplyMode?: SuggestReplyMode;
}

export interface WorkFlowIdList {
  IdList?: Array<Int64>;
}

export interface WorkFlowInfo {
  WorkFlowId: Int64;
}
/* eslint-enable */
