/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as infra from './infra';

export type Int64 = string | number;

/** MockHitStatus Mock命中状态 */
export enum MockHitStatus {
  Undefined = 0,
  /** Mock成功 */
  Success = 1,
  /** MockSet的Schema不兼容 */
  Incompatible = 2,
  /** MockSet已被删除 */
  Deleted = 3,
  /** MockSet里没有Rule */
  EmptyRule = 4,
  /** 没有命中Mock的灰度策略 */
  NotHitGray = 5,
}

/** ========== Common Define  ===========  //
 RequestFilterType 请求过滤规则类型 */
export enum RequestFilterType {
  Undefined = 0,
  /** 不进行请求匹配 */
  ByPass = 1,
}

/** ResponseExpectType 返回值生成规则类型 */
export enum ResponseExpectType {
  Undefined = 0,
  /** 返回类型是JSON，会进行Schema校验 */
  JSON = 1,
}

/** MockRule Mock规则设定 */
export interface MockRule {
  /** MockRule ID */
  id?: Int64;
  /** 绑定的MockSet ID */
  mockSetID?: Int64;
  /** 名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 优先级 */
  priority?: Int64;
  /** 请求过滤规则 */
  requestFilter?: RequestFilter;
  /** 响应规则 */
  responseExpect?: ResponseExpect;
  /** 创建者 */
  creator?: infra.Creator;
  /** 创建时间 */
  createTimeInSec?: Int64;
  /** 更新时间 */
  updateTimeInSec?: Int64;
}

/** MockSet 实体信息 */
export interface MockSet {
  /** Mockset id */
  id?: Int64;
  /** Mockset 名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 包含的MockRule数量 */
  mockRuleQuantity?: number;
  /** 创建者 */
  creator?: infra.Creator;
  /** 创建时间 */
  createTimeInSec?: Int64;
  /** 更新时间 */
  updateTimeInSec?: Int64;
  /** Schema不兼容 */
  schemaIncompatible?: boolean;
  mockSubject?: infra.ComponentSubject;
}

/** MockSetBinding MockSet绑定信息 */
export interface MockSetBinding {
  /** 绑定的MockSetID，为0时代表不走Mock */
  mockSetID?: Int64;
  /** 绑定的组件 */
  mockSubject?: infra.ComponentSubject;
  /** 业务上下文 */
  bizCtx?: infra.BizCtx;
}

/** RequestFilter 请求过滤规则 */
export interface RequestFilter {
  /** 请求过滤规则类型 */
  requestFilterType?: RequestFilterType;
  /** 请求过滤规则 */
  requestFilterRule?: string;
}

/** ResponseExpect 响应规则 */
export interface ResponseExpect {
  /** 响应规则类型 */
  responseExpectType?: ResponseExpectType;
  /** 响应规则 */
  responseExpectRule?: string;
}
/* eslint-enable */
