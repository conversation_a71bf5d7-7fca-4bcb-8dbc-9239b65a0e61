/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as infra from './infra';

export type Int64 = string | number;

export interface CaseDataBase {
  /** 新增时不填，更新时填写 */
  caseID?: Int64;
  name?: string;
  description?: string;
  /** json格式的输入信息 */
  input?: string;
  isDefault?: boolean;
}

export interface CaseDataDetail {
  caseBase?: CaseDataBase;
  creatorID?: string;
  createTimeInSec?: Int64;
  updateTimeInSec?: Int64;
  /** schema不兼容 */
  schemaIncompatible?: boolean;
  updater?: infra.Creator;
}
/* eslint-enable */
