/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum StructureGenDataType {
  Undefined = 0,
  JSON = 1,
}

export enum StructureGenModelType {
  Undefined = 0,
  GPT = 1,
  Skylark = 2,
}

/** StructureGenTaskStatus 自动生成任务状态 */
export enum StructureGenTaskStatus {
  Undefined = 0,
  /** 已完成 */
  Finished = 1,
  /** 等待中 */
  Pending = 2,
  /** 进行中 */
  Running = 3,
  /** 已取消 */
  Canceled = 4,
  /** 失败 */
  Failed = 5,
}

/** StructureGenChoice 生成结果 */
export interface StructureGenChoice {
  /** 生成的数据 */
  content?: string;
  /** 停止生成的原因，如果生成成功则为finish，否则为其他错误信息 */
  stopReason?: string;
  /** 消耗 */
  usage?: StructureGenUsage;
}

/** StructureGenUsage 生成消耗 */
export interface StructureGenUsage {
  /** 输入Tokens */
  inputTokens?: Int64;
  /** 输出Tokens */
  outputTokens?: Int64;
  /** 生成耗时（毫秒） */
  latencyInMs?: Int64;
}
/* eslint-enable */
