/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** 消息的发送方 */
export enum MessageRole {
  System = 1,
  User = 2,
  Assistant = 3,
  Function = 4,
}

/** LLM Message 场景 */
export enum MessageScene {
  Default = 1,
  /** 历史对话 */
  History = 2,
  /** System Prompt */
  SystemPrompt = 3,
  /** 大模型输出 */
  Output = 4,
  /** 用户输入 */
  Input = 5,
}

/** LLM消息 */
export interface LLMMessageItem {
  /** 消息场景 */
  messageScene?: MessageScene;
  /** 消息角色 */
  messageRole?: MessageRole;
  /** 消息内容 */
  content?: string;
}

/** Coze Run Event 实体信息 */
export interface RunEvent {
  /** 唯一标识 */
  id?: string;
  /** botID */
  botID?: string;
  /** 用户ID */
  userID?: string;
  /** 父节点ID */
  parentRunID?: string;
  /** 根节点ID */
  rootID?: string;
  /** 事件名 */
  name?: string;
  /** 事件开始时间, 毫秒时间戳 */
  startTimeMS?: Int64;
  /** 事件结束时间, 毫秒时间戳 */
  endTimeMS?: Int64;
  /** 耗时 */
  duration?: number;
  /** 事件类型 */
  runType?: string;
  /** 输入 */
  inputs?: string;
  /** 输出 */
  outputs?: string;
  /** 日志ID */
  logID?: string;
  /** 错误信息 */
  errorMessage?: string;
  /** 大模型内容 */
  llmMessages?: Array<LLMMessageItem>;
  /** connectorID */
  connectorID?: string;
  /** msgID */
  msgID?: string;
  /** deviceID */
  deviceID?: string;
  /** spaceID */
  spaceID?: string;
}
/* eslint-enable */
