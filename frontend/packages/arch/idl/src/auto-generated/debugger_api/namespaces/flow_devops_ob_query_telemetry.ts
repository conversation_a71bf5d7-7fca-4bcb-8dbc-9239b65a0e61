/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_ob_query_telemetry_span from './flow_devops_ob_query_telemetry_span';

export type Int64 = string | number;

export interface ListTracesData {
  spans: Array<flow_devops_ob_query_telemetry_span.Span>;
  /** 下一页的分页token，前端拉取下一页数据时回传。 */
  next_page_token: string;
  /** 是否有更多数据 */
  has_more: boolean;
}
/* eslint-enable */
