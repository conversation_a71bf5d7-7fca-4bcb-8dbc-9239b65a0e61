/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as flow_devops_evaluation_callback_common from './flow_devops_evaluation_callback_common';

export type Int64 = string | number;

/** https://lilianweng.github.io/posts/2023-06-23-agent/agent-overview.png
 Agent 包含：Planning、Memory、Tools 等。
 这里 Action 泛指 Agent 下的各种子组件的类型的执行 */
export enum ActionType {
  Unknown = 0,
  LLMPlanning = 1,
  ToolCall = 2,
  /** coze agent 思考过程 */
  CozeVerbose = 100,
}

export enum BuiltinEvaluatorType {
  /** 以用户定制输入的 PromptTemplate 作为评估器的配置信息，对评估对象的输入输出进行测评 */
  Prompt = 1,
  /** 以用户定制输入的 Python Code 作为评估器的配置信息，对评估对象的输入输出进行测评 */
  PythonCode = 2,
  /** 以用户定制输入的 JS Code 作为评估器的配置信息，对评估对象的输入输出进行测评 */
  JSCode = 3,
  /** 人工评测 */
  Manual = 7,
  /** prompt开发中的prompt */
  FornaxPrompt = 10,
  /** coze2.0 裁判模型评估器 */
  CozePrompt = 11,
  /** 以用户定制输入的 Func 静态参数 作为评估器的配置信息，对评估对象的输入输出进行测评 */
  BuiltinEquals = 10000,
  BuiltinNotEquals = 10001,
  BuiltinContains = 10002,
  BuiltinNotContains = 10003,
  BuiltinIContains = 10004,
  BuiltinNotIContains = 10005,
  BuiltinRegex = 10006,
  BuiltinNotRegex = 10007,
  BuiltinStartsWith = 10008,
  BuiltinNotStartsWith = 10009,
  BuiltinContainsAny = 10010,
  BuiltinNotContainsAny = 10011,
  BuiltinContainsAll = 10012,
  BuiltinNotContainsAll = 10013,
  BuiltinIContainsAny = 10014,
  BuiltinNotIContainsAny = 10015,
  BuiltinIContainsAll = 10016,
  BuiltinNotIContainsAll = 10017,
  BuiltinIsJSON = 10018,
  BuiltinNotIsJSON = 10019,
  /** deprecated */
  BuiltinContainsJSON = 10020,
  /** deprecated */
  BuiltinNotContainsJSON = 10021,
  BuiltinIsValidJSONObject = 10022,
  BuiltinNotIsValidJSONObject = 10023,
  /** single criteria eval (auto check label / unlabeled) */
  BuiltinConcisenessCriteriaEval = 20001,
  BuiltinRelevanceCriteriaEval = 20002,
  BuiltinHarmfulnessCriteriaEval = 20003,
  BuiltinMaliciousnessCriteriaEval = 20004,
  BuiltinHelpfulnessCriteriaEval = 20005,
  BuiltinControversialityCriteriaEval = 20006,
  BuiltinMisogynyCriteriaEval = 20007,
  BuiltinCriminalityCriteriaEval = 20008,
  BuiltinInsensitivityCriteriaEval = 20009,
  BuiltinDepthCriteriaEval = 20010,
  BuiltinCreativityCriteriaEval = 20011,
  BuiltinDetailCriteriaEval = 20012,
  /** must labeled, CotQA */
  BuiltinCorrectnessEval = 20013,
  /** 语言一致性 */
  BuiltinSpecTestLanguageConsistency = 20014,
  /** 回复拒答检查 */
  BuiltinSpecTestResponseDenialCheck = 20015,
  /** 内容真实性 */
  BuiltinSpecTestContentAuthenticity = 20016,
  /** 内容准确性 */
  BuiltinSpecTestContentAccuracy = 20017,
  /** 满足需求性 */
  BuiltinSpecTestNeedFulfillment = 20018,
  /** 回复时效性 */
  BuiltinSpecTestResponseTimeliness = 20019,
  /** 回复冗余性 */
  BuiltinSpecTestResponseRedundancy = 20020,
  /** 符合人设 */
  BuiltinSpecTestCharacterConsistency = 20021,
  /** 拟人程度 */
  BuiltinSpecTestAnthropomorphismLevel = 20022,
  /** 输入-输出语义相似度 */
  BuiltinSpecTestIOSematicSimilarity = 20023,
  /** 答案-输出语义相似度 */
  BuiltinSpecTestAOSematicSimilarity = 20024,
  /** 生图一致性 */
  BuiltinSpecTestImageGenerationConsistency = 20025,
  /** 图片美观性 */
  BuiltinSpecTestImageAesthetics = 20026,
  /** 回复完整性 */
  BuiltinSpecTestResponseCompleteness = 20027,
  /** 文生图完整性 */
  BuiltinSpecTestTextToImageGenerationCompleteness = 20028,
  /** 代码生成质量 */
  BuiltinSpecTestCodeGenerationScoring = 20029,
  /** 插件调用正确性 for coze bot */
  BuiltinSpecTestPluginCallingCorrectness = 20030,
  /** 插件入参正确性 for coze bot */
  BuiltinSpecTestPluginParametersCorrectness = 20031,
  /** Workflow调用正确性 for coze bot */
  BuiltinSpecTestWorkflowCallingCorrectness = 20032,
  /** Workflow入参正确性 for coze bot */
  BuiltinSpecTestWorkflowParametersCorrectness = 20033,
  /** 触发器调用正确性 for coze bot */
  BuiltinSpecTestTriggerCallingCorrectness = 20034,
  /** 触发器入参正确性 for coze bot */
  BuiltinSpecTestTriggerParametersCorrectness = 20035,
  /** 流程编排准确性 for coze bot */
  BuiltinSpecTestChoreographyAccuracy = 20036,
  /** Fornax prompt 泄露检测 */
  BuiltinFornaxPromptLeakDetection = 20200,
  /** 自定义指标
系统内置指标 */
  BuiltinDefaultMetric = 30001,
  /** 用户上报自定义指标 */
  BuiltinCustomMetric = 30002,
}

export enum EvaluatorState {
  Success = 1,
  Fail = 2,
}

/** A full description of an action for an Agent to execute. */
export interface AgentAction {
  agent_type?: ActionType;
  /** 本次执行工具的标识、名称等信息。由评测对象接入方自主定义其格式 */
  action_meta?: string;
  /** Additional information to log about the action.
This log can be used in a few ways. First, it can be used to audit
what exactly the LLM predicted to lead to this (Name, Input).
Second, it can be used in future iterations to show the LLMs prior
thoughts. This is useful when (Name, Input) does not contain
full information about the LLM prediction (for example, any `thought`
before the tool/tool_input).
this field is mainly used to show more information for human */
  log?: string;
  /** the input to pass in to the Tool. */
  input?: flow_devops_evaluation_callback_common.Content;
  /** the output of the Agent */
  output?: flow_devops_evaluation_callback_common.Content;
  /** extend information */
  ext?: Record<string, string>;
}

export interface Evaluator {
  type?: Int64;
  /** 在评估器管理平台上注册时，提供了需要透传的 RuleMeta 信息。 建议采用 JSON 序列化 */
  evaluator_meta?: string;
}

/** 以一行数据集为例：
列名：      input             output         context              person
列值：  "我适合什么样的工作"   "你适合休息"   "不喜欢挑战、不喜欢出力"   "{性别：男， 年龄：18， 文凭：名牌大学毕业}"
Input 参数构建：
  Input: "我适合什么样的工作"
  Variables: map{ context: "不喜欢挑战、不喜欢出力", person: "{性别：男， 年龄：18， 文凭：名牌大学毕业}" }
  Histories: null */
export interface Input {
  /** 数据集中的 input 列，一般代表评测Case中的用户输入 */
  input?: string;
  /** 数据集中，除 input、output 列之外，其他所有的列均视为是 Variable，列名作为 key、列值作为 value */
  variables?: Record<string, flow_devops_evaluation_callback_common.Content>;
  /** 多轮评测场景中，数据集中的一行数据中又可拆分成 n 轮评测输入。
在第 n 轮的评测中，Histories 传入 [1 ~ n-1] 的信息，采用 Json 序列化。 第 n 轮的信息由 Input 字段传入
此处前 n-1 轮的信息，采用 Json 序列化。序列化的 Schema 由评测任务制定，由评估器进行解析使用
例如：
Input: "我今天出门适合什么穿搭？"
Histories：[{ "human": "我在XX市XX区，今天天气怎么样", "assistant": "经过查询天气API，今天有雷阵雨，5级大风，温度5度左右" }] */
  histories?: Array<flow_devops_evaluation_callback_common.Message>;
  /** 数据集中的 output 列，一般代表评测Case中, 预期评测对象要产生的输出，通常作为评测的 Reference。 可以是 string，也可以是 Json 序列化 */
  output?: string;
  /** 评估对象的输出信息。评估器会以数据集中 output 列为基准，对评估对象输出的 Prediction 进行评测
Prediction 可以是 string、也可以是 JSON 结构体，需要与评估器对齐解析方式 */
  prediction?: string;
  /** 非文本模态时，评估器会以 output_v2 为基准，对评估对象输出的 prediction_v2 进行评测
文本模态时，可以继续使用 1~5 号字段 */
  input_v2?: flow_devops_evaluation_callback_common.Content;
  output_v2?: flow_devops_evaluation_callback_common.Content;
  prediction_v2?: flow_devops_evaluation_callback_common.Content;
  trajectory?: Trajectory;
}

export interface Metrics {
  /** 运行开始时间 */
  start_time?: Int64;
  /** 运行结束时间 */
  end_time?: Int64;
}

export interface Result {
  /** 规则运行状态 */
  state: EvaluatorState;
  /** 报错时的信息 */
  err_msg?: string;
  /** 打分结果 */
  score?: number;
  /** 打分过程与结果相关信息 */
  reasoning?: string;
  usage?: Usage;
  metrics?: Metrics;
  /** 冗余，暂无使用场景 */
  ext?: Record<string, string>;
}

export interface RuleConfig {
  /** ID 用于定位一个 JSON Schema, 来解析下面的 string JSON content */
  id: Int64;
  /** 用户选定评估器时，需要提供该评估器的配置内容，方能组合成一条可正常运行的 Rule
每个评估器的配置内容，是由评估器提供方定义的。评估器管理平台回调评估器时，需要根据评估器要求的 JSON Schema，构造配置内容 */
  data: string;
}

export interface Trajectory {
  /** 端到端测试时，评测对象内部执行的每一步的 Action 信息
list 中的 index 代表评测对象执行时，观测到的 Step
注：随着观测节点的增加，一个 AgentAction 所处的 step 会发生变化 */
  actions?: Array<AgentAction>;
}

export interface Usage {
  /** 计费信息。一次评估对象Playground执行时，内部总的输入、输出的Tokens的消耗 */
  input_tokens?: Int64;
  output_tokens?: Int64;
}
/* eslint-enable */
