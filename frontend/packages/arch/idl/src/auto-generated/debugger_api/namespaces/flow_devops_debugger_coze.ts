/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as infra from './infra';
import * as base from './base';
import * as testcase from './testcase';
import * as structure_gen from './structure_gen';
import * as mockset from './mockset';

export type Int64 = string | number;

export interface AutoGenerateCaseDataReq {
  /** 业务信息 */
  bizCtx?: infra.BizCtx;
  bizComponentSubject?: infra.ComponentSubject;
  /** 生成数量，默认1个 */
  count?: number;
  Base?: base.Base;
}

export interface AutoGenerateCaseDataResp {
  genCaseData?: Array<testcase.CaseDataBase>;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface BindMockSetRequest {
  /** 被选中的MockSet ID */
  mockSetID?: Int64;
  /** 业务上下文 */
  bizCtx?: infra.BizCtx;
  /** 被Mock的组件 */
  mockSubject?: infra.ComponentSubject;
  Base?: base.Base;
}

export interface BindMockSetResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface CancelMockDataAutoGenTaskRequest {
  taskID?: Int64;
  Base?: base.Base;
}

export interface CancelMockDataAutoGenTaskResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface CheckCaseDuplicateReq {
  bizCtx?: infra.BizCtx;
  /** case名称 */
  caseName?: string;
  bizComponentSubject?: infra.ComponentSubject;
  Base?: base.Base;
}

export interface CheckCaseDuplicateResp {
  isPass?: boolean;
  /** 当pass=false时，给出具体的校验不通过的原因 */
  failReason?: string;
  failCode?: number;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface CreateMockDataAutoGenTaskRequest {
  mockSetID?: Int64;
  /** 最大为5 */
  quantity?: number;
  /** 传空则使用mockSet的descraption */
  desc?: string;
  Base?: base.Base;
}

export interface CreateMockDataAutoGenTaskResponse {
  taskID?: Int64;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteCaseDataReq {
  /** 业务信息 */
  bizCtx?: infra.BizCtx;
  /** 单次上限20个 */
  caseIDs?: Array<Int64>;
  Base?: base.Base;
}

export interface DeleteCaseDataResp {
  deletedCaseIDS?: Array<Int64>;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteMockRuleRequest {
  /** 要删除的MockRule ID */
  id?: Int64;
  /** 业务上下文 */
  bizCtx?: infra.BizCtx;
  Base?: base.Base;
}

export interface DeleteMockRuleResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface DeleteMockSetRequest {
  /** 需要删除的MockSet ID */
  id?: Int64;
  /** 业务上下文 */
  bizCtx?: infra.BizCtx;
  Base?: base.Base;
}

export interface DeleteMockSetResponse {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetMockDataAutoGenTaskChoicesRequest {
  taskID?: Int64;
  Base?: base.Base;
}

export interface GetMockDataAutoGenTaskChoicesResponse {
  status?: structure_gen.StructureGenTaskStatus;
  choices?: Array<structure_gen.StructureGenChoice>;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetMockSetUsageInfoRequest {
  mockSetID?: Int64;
  spaceID?: string;
  Base?: base.Base;
}

export interface GetMockSetUsageInfoResponse {
  /** 该MockSet被使用的用户数 */
  usersUsageCount?: Int64;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface GetSchemaByIDReq {
  /** 业务信息 */
  bizCtx?: infra.BizCtx;
  bizComponentSubject?: infra.ComponentSubject;
  Base?: base.Base;
}

export interface GetSchemaByIDResp {
  /** Json格式的组件input信息，与Input Json Schema保持一致，不包含Value值信息 */
  schemaJson?: string;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface MGetCaseDataReq {
  /** 业务信息 */
  bizCtx?: infra.BizCtx;
  bizComponentSubject?: infra.ComponentSubject;
  pageLimit?: number;
  nextToken?: string;
  /** 按照case名称搜索 */
  caseName?: string;
  Base?: base.Base;
}

export interface MGetCaseDataResp {
  cases?: Array<testcase.CaseDataDetail>;
  hasNext?: boolean;
  nextToken?: string;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface MGetDevopsFeatureGatesRequest {
  /** fgName 需要注册在Bot Studio NameSpace下  */
  fgNames?: Array<string>;
  /** 对应 ByteGate Custom Key space_id */
  spaceID?: Int64;
  /** 对应 ByteGate Custom Key bot_id */
  botID?: Int64;
  /** 前端调用可以不传， 从ctx兜底取值对应 ByteGate Custom Key coze_uid */
  userID?: Int64;
  Base?: base.Base;
}

export interface MGetDevopsFeatureGatesResponse {
  /** key： fgName value: 是否放行 */
  featgates?: Record<string, boolean>;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface MGetMockRuleRequest {
  bizCtx?: infra.BizCtx;
  mockSetID?: Int64;
  creatorID?: string;
  /** 单次获取的记录条数，默认30，最大50 */
  pageLimit?: number;
  /** 分页游标 */
  pageToken?: string;
  /** 根据ID获取单个Component下的mockrule */
  ids?: Array<Int64>;
  /** 排序 */
  orderBy?: infra.OrderBy;
  /** 是否降序 */
  desc?: boolean;
  Base?: base.Base;
}

export interface MGetMockRuleResponse {
  /** MockRule信息 */
  mockRules?: Array<mockset.MockRule>;
  /** 是否还有下一页 */
  hasMore?: boolean;
  /** 本页最后一条记录的游标 */
  pageToken?: string;
  /** 总数 */
  count?: Int64;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface MGetMockSetBindingRequest {
  /** 业务上下文 */
  bizCtx?: infra.BizCtx;
  /** 被Mock的组件 */
  mockSubject?: infra.ComponentSubject;
  /** 需要mockSet详情 */
  needMockSetDetail?: boolean;
  Base?: base.Base;
}

export interface MGetMockSetBindingResponse {
  /** 为空表示没有命中任何mock */
  mockSetBindings?: Array<mockset.MockSetBinding>;
  /** mockSet详情（仅当needMockSetDetail为true时返回） */
  mockSetDetails?: Record<Int64, mockset.MockSet>;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface MGetMockSetRequest {
  /** 业务上下文 */
  bizCtx?: infra.BizCtx;
  /** 被mock的组件 */
  mockSubject?: infra.ComponentSubject;
  /** 创建者ID */
  creatorID?: string;
  /** 单次获取的记录条数，默认30，最大50 */
  pageLimit?: number;
  /** 分页游标 */
  pageToken?: string;
  /** 根据ID获取单个Component下的mockset */
  ids?: Array<Int64>;
  /** 排序 */
  orderBy?: infra.OrderBy;
  /** 是否降序 */
  desc?: boolean;
  Base?: base.Base;
}

export interface MGetMockSetResponse {
  /** MockSet信息 */
  mockSets?: Array<mockset.MockSet>;
  /** 与该Mockset绑定的组件的Schema */
  schema?: string;
  /** 是否还有下一页 */
  hasMore?: boolean;
  /** 本页最后一条记录的游标 */
  pageToken?: string;
  /** 总数 */
  count?: Int64;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface SaveCaseDataReq {
  /** 业务信息 */
  bizCtx?: infra.BizCtx;
  bizComponentSubject?: infra.ComponentSubject;
  /** case基本数据 */
  caseBase?: testcase.CaseDataBase;
  Base?: base.Base;
}

export interface SaveCaseDataResp {
  caseDetail?: testcase.CaseDataDetail;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface SaveMockRuleRequest {
  /** 名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 所属MockSet */
  mocksetID?: Int64;
  /** 业务上下文 */
  bizCtx?: infra.BizCtx;
  /** 优先级 */
  priority?: Int64;
  /** mockRule ID，为0表示创建，不为0代表更新 */
  id?: Int64;
  /** 请求过滤规则 */
  requestFilter?: mockset.RequestFilter;
  /** mock数据生成规则 */
  responseExpect?: mockset.ResponseExpect;
  Base?: base.Base;
}

export interface SaveMockRuleResponse {
  id?: Int64;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface SaveMockSetRequest {
  /** 名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 被mock组件 */
  mockSubject?: infra.ComponentSubject;
  /** 业务信息 */
  bizCtx?: infra.BizCtx;
  /** MocksetID，如果为0则为create，不为0代表update */
  id?: Int64;
  Base?: base.Base;
}

export interface SaveMockSetResponse {
  /** 被操作的MockSetID */
  id?: Int64;
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface SetDefaultTestCaseReq {
  /** 业务信息 */
  bizCtx?: infra.BizCtx;
  bizComponentSubject?: infra.ComponentSubject;
  caseID?: Int64;
  Base?: base.Base;
}

export interface SetDefaultTestCaseResp {
  code?: number;
  msg?: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
