/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './namespaces/base';
import * as flow_devops_debugger_coze from './namespaces/flow_devops_debugger_coze';
import * as flow_devops_debugger_ping from './namespaces/flow_devops_debugger_ping';
import * as flow_devops_evaluation_callback_common from './namespaces/flow_devops_evaluation_callback_common';
import * as flow_devops_evaluation_entity from './namespaces/flow_devops_evaluation_entity';
import * as flow_devops_evaluation_evaluator_callback from './namespaces/flow_devops_evaluation_evaluator_callback';
import * as flow_devops_evaluation_object_callback from './namespaces/flow_devops_evaluation_object_callback';
import * as flow_devops_ob_query_telemetry from './namespaces/flow_devops_ob_query_telemetry';
import * as flow_devops_ob_query_telemetry_common from './namespaces/flow_devops_ob_query_telemetry_common';
import * as flow_devops_ob_query_telemetry_field_filter from './namespaces/flow_devops_ob_query_telemetry_field_filter';
import * as flow_devops_ob_query_telemetry_span from './namespaces/flow_devops_ob_query_telemetry_span';
import * as infra from './namespaces/infra';
import * as mockset from './namespaces/mockset';
import * as run_event from './namespaces/run_event';
import * as structure_gen from './namespaces/structure_gen';
import * as testcase from './namespaces/testcase';

export {
  base,
  flow_devops_debugger_coze,
  flow_devops_debugger_ping,
  flow_devops_evaluation_callback_common,
  flow_devops_evaluation_entity,
  flow_devops_evaluation_evaluator_callback,
  flow_devops_evaluation_object_callback,
  flow_devops_ob_query_telemetry,
  flow_devops_ob_query_telemetry_common,
  flow_devops_ob_query_telemetry_field_filter,
  flow_devops_ob_query_telemetry_span,
  infra,
  mockset,
  run_event,
  structure_gen,
  testcase,
};
export * from './namespaces/base';
export * from './namespaces/flow_devops_debugger_coze';
export * from './namespaces/flow_devops_debugger_ping';
export * from './namespaces/flow_devops_evaluation_callback_common';
export * from './namespaces/flow_devops_evaluation_entity';
export * from './namespaces/flow_devops_evaluation_evaluator_callback';
export * from './namespaces/flow_devops_evaluation_object_callback';
export * from './namespaces/flow_devops_ob_query_telemetry';
export * from './namespaces/flow_devops_ob_query_telemetry_common';
export * from './namespaces/flow_devops_ob_query_telemetry_field_filter';
export * from './namespaces/flow_devops_ob_query_telemetry_span';
export * from './namespaces/infra';
export * from './namespaces/mockset';
export * from './namespaces/run_event';
export * from './namespaces/structure_gen';
export * from './namespaces/testcase';

export type Int64 = string | number;

export default class DebuggerApiService<T> {
  private request: any = () => {
    throw new Error('DebuggerApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * GET /api/devops/debugger/v1/ping
   *
   * KitexThrift
   */
  Ping(
    req: flow_devops_debugger_ping.PingReq,
    options?: T,
  ): Promise<flow_devops_debugger_ping.PingResp> {
    const _req = req;
    const url = this.genBaseURL('/api/devops/debugger/v1/ping');
    const method = 'GET';
    const params = { ping_message: _req['ping_message'], Base: _req['Base'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/mockSetBindingInfos
   *
   * BindingInfo
   */
  BindMockSet(
    req?: flow_devops_debugger_coze.BindMockSetRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.BindMockSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/mockSetBindingInfos',
    );
    const method = 'POST';
    const data = {
      mockSetID: _req['mockSetID'],
      bizCtx: _req['bizCtx'],
      mockSubject: _req['mockSubject'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/mockSetBindingInfos/mget */
  MGetMockSetBinding(
    req?: flow_devops_debugger_coze.MGetMockSetBindingRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.MGetMockSetBindingResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/mockSetBindingInfos/mget',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      mockSubject: _req['mockSubject'],
      needMockSetDetail: _req['needMockSetDetail'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/mockRules
   *
   * MockRule
   */
  SaveMockRule(
    req?: flow_devops_debugger_coze.SaveMockRuleRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.SaveMockRuleResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/debugger/v1/coze/mockRules');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      mocksetID: _req['mocksetID'],
      bizCtx: _req['bizCtx'],
      priority: _req['priority'],
      id: _req['id'],
      requestFilter: _req['requestFilter'],
      responseExpect: _req['responseExpect'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/mockRules/mget */
  MGetMockRule(
    req?: flow_devops_debugger_coze.MGetMockRuleRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.MGetMockRuleResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/debugger/v1/coze/mockRules/mget');
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      mockSetID: _req['mockSetID'],
      creatorID: _req['creatorID'],
      pageLimit: _req['pageLimit'],
      pageToken: _req['pageToken'],
      ids: _req['ids'],
      orderBy: _req['orderBy'],
      desc: _req['desc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/mockSets/mget */
  MGetMockSet(
    req?: flow_devops_debugger_coze.MGetMockSetRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.MGetMockSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/debugger/v1/coze/mockSets/mget');
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      mockSubject: _req['mockSubject'],
      creatorID: _req['creatorID'],
      pageLimit: _req['pageLimit'],
      pageToken: _req['pageToken'],
      ids: _req['ids'],
      orderBy: _req['orderBy'],
      desc: _req['desc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/mockSets/usage/get */
  GetMockSetUsageInfo(
    req?: flow_devops_debugger_coze.GetMockSetUsageInfoRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.GetMockSetUsageInfoResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/mockSets/usage/get',
    );
    const method = 'POST';
    const data = {
      mockSetID: _req['mockSetID'],
      spaceID: _req['spaceID'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/mockSets
   *
   * Manager
   *
   * MockSet
   */
  SaveMockSet(
    req?: flow_devops_debugger_coze.SaveMockSetRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.SaveMockSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/debugger/v1/coze/mockSets');
    const method = 'POST';
    const data = {
      name: _req['name'],
      description: _req['description'],
      mockSubject: _req['mockSubject'],
      bizCtx: _req['bizCtx'],
      id: _req['id'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/mockRules/delete */
  DeleteMockRule(
    req?: flow_devops_debugger_coze.DeleteMockRuleRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.DeleteMockRuleResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/mockRules/delete',
    );
    const method = 'POST';
    const data = { id: _req['id'], bizCtx: _req['bizCtx'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/mockSets/delete */
  DeleteMockSet(
    req?: flow_devops_debugger_coze.DeleteMockSetRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.DeleteMockSetResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/debugger/v1/coze/mockSets/delete');
    const method = 'POST';
    const data = { id: _req['id'], bizCtx: _req['bizCtx'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/testcase/casedata/delete */
  DeleteCaseData(
    req?: flow_devops_debugger_coze.DeleteCaseDataReq,
    options?: T,
  ): Promise<flow_devops_debugger_coze.DeleteCaseDataResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/testcase/casedata/delete',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      caseIDs: _req['caseIDs'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/testcase/casedata/check */
  CheckCaseDuplicate(
    req?: flow_devops_debugger_coze.CheckCaseDuplicateReq,
    options?: T,
  ): Promise<flow_devops_debugger_coze.CheckCaseDuplicateResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/testcase/casedata/check',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      caseName: _req['caseName'],
      bizComponentSubject: _req['bizComponentSubject'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/testcase/casedata/autogen
   *
   * case generate
   */
  AutoGenerateCaseData(
    req?: flow_devops_debugger_coze.AutoGenerateCaseDataReq,
    options?: T,
  ): Promise<flow_devops_debugger_coze.AutoGenerateCaseDataResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/testcase/casedata/autogen',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      bizComponentSubject: _req['bizComponentSubject'],
      count: _req['count'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/testcase/casedata/save
   *
   * ========================== Test Case ==============================
   *
   * case manage
   */
  SaveCaseData(
    req?: flow_devops_debugger_coze.SaveCaseDataReq,
    options?: T,
  ): Promise<flow_devops_debugger_coze.SaveCaseDataResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/testcase/casedata/save',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      bizComponentSubject: _req['bizComponentSubject'],
      caseBase: _req['caseBase'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/testcase/casedata/schema
   *
   * case schema
   */
  GetSchemaByID(
    req?: flow_devops_debugger_coze.GetSchemaByIDReq,
    options?: T,
  ): Promise<flow_devops_debugger_coze.GetSchemaByIDResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/testcase/casedata/schema',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      bizComponentSubject: _req['bizComponentSubject'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/testcase/casedata/mget */
  MGetCaseData(
    req?: flow_devops_debugger_coze.MGetCaseDataReq,
    options?: T,
  ): Promise<flow_devops_debugger_coze.MGetCaseDataResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/testcase/casedata/mget',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      bizComponentSubject: _req['bizComponentSubject'],
      pageLimit: _req['pageLimit'],
      nextToken: _req['nextToken'],
      caseName: _req['caseName'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/featureGates
   *
   * ========================== FeautureGates ==============================
   */
  MGetDevopsFeatureGates(
    req?: flow_devops_debugger_coze.MGetDevopsFeatureGatesRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.MGetDevopsFeatureGatesResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/devops/debugger/v1/coze/featureGates');
    const method = 'POST';
    const data = {
      fgNames: _req['fgNames'],
      spaceID: _req['spaceID'],
      botID: _req['botID'],
      userID: _req['userID'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/autogenMockDataTask/cancel */
  CancelMockDataAutoGenTask(
    req?: flow_devops_debugger_coze.CancelMockDataAutoGenTaskRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.CancelMockDataAutoGenTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/autogenMockDataTask/cancel',
    );
    const method = 'POST';
    const data = { taskID: _req['taskID'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/devops/debugger/v1/coze/autogenMockDataTask
   *
   * AutoGen
   */
  CreateMockDataAutoGenTask(
    req?: flow_devops_debugger_coze.CreateMockDataAutoGenTaskRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.CreateMockDataAutoGenTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/autogenMockDataTask',
    );
    const method = 'POST';
    const data = {
      mockSetID: _req['mockSetID'],
      quantity: _req['quantity'],
      desc: _req['desc'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/autogenMockDataTask/get */
  GetMockDataAutoGenTaskChoices(
    req?: flow_devops_debugger_coze.GetMockDataAutoGenTaskChoicesRequest,
    options?: T,
  ): Promise<flow_devops_debugger_coze.GetMockDataAutoGenTaskChoicesResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/autogenMockDataTask/get',
    );
    const method = 'POST';
    const data = { taskID: _req['taskID'], Base: _req['Base'] };
    return this.request({ url, method, data }, options);
  }

  /** POST /api/devops/debugger/v1/coze/testcase/casedata/setDefault */
  SetDefaultTestCase(
    req?: flow_devops_debugger_coze.SetDefaultTestCaseReq,
    options?: T,
  ): Promise<flow_devops_debugger_coze.SetDefaultTestCaseResp> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/devops/debugger/v1/coze/testcase/casedata/setDefault',
    );
    const method = 'POST';
    const data = {
      bizCtx: _req['bizCtx'],
      bizComponentSubject: _req['bizComponentSubject'],
      caseID: _req['caseID'],
      Base: _req['Base'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
