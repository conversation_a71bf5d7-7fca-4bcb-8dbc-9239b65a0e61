/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bot_common from './bot_common';
import * as intelligence_common_struct from './intelligence_common_struct';
import * as base from './base';
import * as common_struct from './common_struct';
import * as task_common from './task_common';

export type Int64 = string | number;

export enum AttributeValueType {
  Unknown = 0,
  String = 1,
  Boolean = 2,
  StringList = 11,
  BooleanList = 12,
}

/** ************************** collaboration  ************************************** */
export enum CollaborationRole {
  ProjectOwner = 1,
  ProjectEditor = 2,
  BotEditor = 3,
  BotDeveloper = 4,
  BotOperator = 5,
  BotOwner = 6,
}

export enum DiffMode {
  PromptDiff = 1,
  ModelDiff = 2,
}

/** ************************** DiffMode  ************************************** */
export enum DiffModeTargetType {
  DraftBot = 1,
}

export enum IntelligenceTaskStatus {
  Success = 1,
  Processing = 2,
  Failed = 3,
  Canceled = 4,
}

export enum IntelligenceTaskType {
  /** 存档 */
  Archive = 1,
  /** 回滚 */
  Rollback = 2,
}

export enum PermissionResourceType {
  Account = 1,
  Workspace = 2,
  Bot = 4,
  Enterprise = 15,
  Project = 19,
  OceanProject = 24,
  FinetuneTask = 25,
}

export enum TaskAction {
  ProjectCopyCancel = 1,
  ProjectCopyRetry = 2,
}

export enum TaskType {
  ProjectCopy = 1,
  BotCopy = 2,
}

export interface AttributeValue {
  Type: AttributeValueType;
  Value: string;
}

export interface DiffModeConfig {
  prompt?: string;
  model_info?: bot_common.ModelInfo;
  conversation_id?: string;
}

export interface DiffModeInfo {
  diff_mode: DiffMode;
  diff_mode_config_a: DiffModeConfig;
  diff_mode_config_b: DiffModeConfig;
  create_time?: Int64;
  update_time?: Int64;
}

/** ************************** task  ************************************** */
export interface EntityTaskData {
  /** 实体ID */
  entity_id?: string;
  /** 实体状态 */
  entity_status?: intelligence_common_struct.IntelligenceStatus;
}

export interface EntityTaskListRequest {
  space_id: string;
  task_id_list: Array<string>;
}

export interface EntityTaskListResponse {
  data?: EntityTaskListResponseData;
  code: Int64;
  msg: string;
}

export interface EntityTaskListResponseData {
  task_list?: Array<TaskData>;
}

export interface EntityTaskSearchRequest {
  task_list?: Array<TaskStruct>;
}

export interface EntityTaskSearchResponse {
  data?: EntityTaskSearchResponseData;
  code: Int64;
  msg: string;
}

export interface EntityTaskSearchResponseData {
  /** key: entity_id */
  entity_task_map?: Record<Int64, EntityTaskData>;
}

export interface GetDiffModeInfoData {
  diff_mode_info?: DiffModeInfo;
  /** 是否有对比中数据 */
  has_comparison_data?: boolean;
}

export interface GetDiffModeInfoRequest {
  target_type: DiffModeTargetType;
  target_id: string;
  Base?: base.Base;
}

export interface GetDiffModeInfoResponse {
  data?: GetDiffModeInfoData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}

export interface GetIntelligenceTaskInfoRequest {
  task_id: string;
}

export interface GetIntelligenceTaskInfoResponse {
  data?: IntelligenceTaskInfo;
  code: Int64;
  msg: string;
}

export interface IntelligenceTaskInfo {
  task_id?: string;
  task_type?: IntelligenceTaskType;
  task_status?: IntelligenceTaskStatus;
}

export interface ListIntelligenceCollaborationData {
  owner_info?: common_struct.User;
  collaborator_info?: Array<common_struct.User>;
  /** key: uid, value: 角色列表 */
  collaboration_role_map?: Record<string, Array<CollaborationRole>>;
}

export interface ListIntelligenceCollaborationRequest {
  intelligence_id: string;
  intelligence_type: intelligence_common_struct.IntelligenceType;
}

export interface ListIntelligenceCollaborationResponse {
  data: ListIntelligenceCollaborationData;
  code: Int64;
  msg: string;
}

export interface PingRequest {
  Base?: base.Base;
}

export interface PingResponse {
  BaseResp?: base.BaseResp;
}

export interface ProcessEntityTaskRequest {
  /** 实体ID */
  entity_id?: string;
  /** 任务动作 */
  action?: TaskAction;
  /** 批量实体ID */
  task_id_list?: Array<string>;
}

export interface ProcessEntityTaskResponse {
  data?: ProcessEntityTaskResponseData;
  code: Int64;
  msg: string;
}

export interface ProcessEntityTaskResponseData {
  entity_task?: EntityTaskData;
}

export interface ResourceIdentifier {
  /** 资源类型 */
  Type: PermissionResourceType;
  /** 资源Id */
  Id: string;
}

export interface TaskData {
  task_info?: task_common.IntelligenceTaskInfo;
  task_logs?: Array<task_common.IntelligenceTaskLog>;
  name?: string;
  icon_url?: string;
}

export interface TaskStruct {
  /** 实体ID */
  entity_id?: string;
  /** task类型 */
  task_type?: TaskType;
}

export interface UpdateDiffModeInfoData {
  diff_mode_info?: DiffModeInfo;
}

export interface UpdateDiffModeInfoRequest {
  target_type: DiffModeTargetType;
  target_id: string;
  /** 更新/初始化时传 */
  diff_mode_info?: DiffModeInfo;
  /** 退出并保存时传 */
  exit_and_save?: boolean;
  /** 退出并丢弃时传 */
  exit_and_discard?: boolean;
  Base?: base.Base;
}

export interface UpdateDiffModeInfoResponse {
  data?: UpdateDiffModeInfoData;
  code: Int64;
  msg: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
