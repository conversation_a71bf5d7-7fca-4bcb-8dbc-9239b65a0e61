/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

/** tpm火山操作记录聚合领域结构体 * */
export enum TpmExpansionStatus {
  OrderSuccess = 0,
  UpdateTpmThresholdSuccess = 1,
  UpdateTpmThresholdFailed = 2,
}

export enum TpmOperateStatus {
  UnKnown = 0,
  Success = 1,
  Failed = 2,
}

export enum TpmVolcaOperateType {
  UnKnown = 0,
  VolcaPush = 1,
  VolcaCallBack = 2,
}

export enum TpmVolcaPushType {
  Input = 1,
  Output = 2,
}
/* eslint-enable */
