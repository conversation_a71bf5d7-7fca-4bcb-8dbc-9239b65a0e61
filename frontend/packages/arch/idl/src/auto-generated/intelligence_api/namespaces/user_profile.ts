/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum BotMode {
  SingleMode = 0,
  MultiMode = 1,
  WorkflowMode = 2,
}

export enum DownloadStatus {
  Packing = 0,
  PackSuccess = 1,
  PackFail = 2,
}

/** 订单逆向状态 */
export enum OrderReverseStatus {
  Unknown = 0,
  RefundProcessing = 1,
  RefundSuccess = 2,
  RefundFailed = 3,
  Chargeback = 11,
  ChargebackReverse = 12,
}

/** 订单状态 */
export enum OrderStatus {
  Init = 0,
  Success = 1,
  Failed = 2,
  Closed = 3,
}

/** 订单类型 */
export enum OrderType {
  Unknown = 0,
  TokenCharge = 1,
  TokenAutoCharge = 2,
  SubMessageCredit = 11,
  MessageCredit = 12,
  PurchaseTemplate = 21,
}

/** 支付状态 */
export enum PayStatus {
  Init = 0,
  Success = 1,
  Closed = 2,
}

export enum ResourceType {
  Workflow = 1,
  Plugin = 2,
  Data = 3,
}

/** 业务场景 */
export enum Scene {
  /** 未知 */
  Unknown = 0,
  /** Token充值 */
  TokenCharge = 1,
  /** Token自动充值 */
  TokenAutoCharge = 2,
  /** MessageCredit订阅 */
  SubMessageCredit = 11,
  /** MessageCredit充值 */
  MessageCredit = 12,
  /** 模板购买 */
  PurchaseTemplate = 21,
}

export interface DownloadRecord {
  /** 下载请求发起时间(秒) */
  request_time?: Int64;
  /** 下载链接 */
  url?: string;
  status?: DownloadStatus;
}

export interface DownloadUserProfileRequest {
  Cookie?: string;
}

export interface DownloadUserProfileResponse {
  code?: Int64;
  msg?: string;
}

export interface GetUserCompleteProfileRecordRequest {}

export interface GetUserCompleteProfileRecordResponse {
  data: DownloadRecord;
  code?: Int64;
  msg?: string;
}
/* eslint-enable */
