/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common_struct from './common_struct';

export type Int64 = string | number;

export enum FinetuneParamType {
  BatchSize = 1,
  LearningRate = 2,
  Epochs = 3,
}

export enum ModelClass {
  GPT = 1,
  SEED = 2,
  Claude = 3,
  /** name: MiniMax */
  MiniMax = 4,
  Plugin = 5,
  StableDiffusion = 6,
  ByteArtist = 7,
  Maas = 9,
  /** 废弃：千帆(百度云) */
  QianFan = 10,
  /** name：Google Gemini */
  Gemini = 11,
  /** name: Moonshot */
  Moonshot = 12,
  /** name：智谱 */
  GLM = 13,
  /** name: 火山方舟 */
  MaaSAutoSync = 14,
  /** name：通义千问 */
  QWen = 15,
  /** name: Cohere */
  Cohere = 16,
  /** name: 百川智能 */
  Baichuan = 17,
  /** name：文心一言 */
  Ernie = 18,
  /** name: 幻方 */
  DeekSeek = 19,
  /** name: Llama */
  Llama = 20,
  StepFun = 23,
  Other = 999,
}

export enum ModelParamType {
  Float = 1,
  Int = 2,
  Boolean = 3,
  String = 4,
}

export enum ModelStatus {
  Online = 1,
  Offline = 2,
  Deleted = 3,
  FineTuneInQueue = 4,
  FineTuneRunning = 5,
  FineTuneFailed = 6,
  FineTuneTeminated = 7,
}

/** enum ModelTypeTag {
    MultiModal = 1 // 多模态
    TextGenerate = 2 // 文本生成
    Finetune = 3 // 微调
}

enum ModelUserRightTag {
    Free = 1 // 免费
    New = 2 // 新模型
    Expiring = 3 // 即将下架
    Premium = 4 // 高级
}

enum ModelFeatureTag {
    HighSpeed = 1 // 高速
    CostEffective = 2 // 性价比
    LongText = 3 // 长文本
    CodeExpert = 4 // 代码专精
    ImageUnderstanding = 5 // 图像理解
    AudioUnderstanding = 6 // 音频理解
    VideoUnderstanding = 7 // 视频理解
}

enum ModelFunctionTag {
    TextGenerate = 1 // 文本生成
    CodeGenerate = 2 // 代码生成
    CodeUnderstanding = 3 // 代码理解
    ImageUnderstanding = 4 // 图像理解
    AudioUnderstanding = 5 // 音频理解
    VideoUnderstanding = 6 // 视频理解
} */
export enum ModelTagClass {
  ModelType = 1,
  ModelUserRight = 2,
  ModelFeature = 3,
  ModelFunction = 4,
  ModelScenario = 5,
  ModelPayment = 15,
  /** 模型运行时能力 */
  ModelAbility = 16,
  /** 本期不做 */
  Custom = 20,
}

export enum OperateFinetuneTaskAction {
  stop = 1,
  resume = 2,
  delete = 3,
}

export enum OrderBy {
  /** 综合推荐 */
  Recommend = 1,
}

export enum ValidatingType {
  /** 上传 */
  upload = 1,
  /** 训练集分割 */
  TrainingSetSplit = 2,
}

export interface CreateFinetuneTaskData {
  task_id?: string;
}

export interface CreateFinetuneTaskRequest {
  space_id?: string;
  base_model_id?: string;
  /** 训练集 */
  training_dataset_id?: string;
  /** 验证集 */
  validating_dataset?: ValidatingDataset;
  /** 训练配置 */
  finetune_configuration?: FinetuneConfiguration;
  description?: string;
  name?: string;
}

export interface CreateFinetuneTaskResponse {
  data?: CreateFinetuneTaskData;
  code?: Int64;
  msg?: string;
}

export interface DeleteFinetuneModelRequest {
  model_id?: string;
  space_id?: string;
}

export interface DeleteFinetuneModelResponse {
  code?: Int64;
  msg?: string;
}

export interface ExpansionTpmData {
  record_id?: string;
}

export interface ExpansionTpmRequest {
  model_id?: string;
  enterprise_id?: string;
  organization_id?: string;
  tpm_input_expansion?: string;
  tpm_output_expansion?: string;
  /** 开始生效时间 */
  start_time?: string;
  /** 结束生效时间 */
  end_time?: string;
}

export interface ExpansionTpmResponse {
  data?: ExpansionTpmData;
  code?: Int64;
  msg?: string;
}

export interface FinetuneConfiguration {
  /** 批次大小 */
  batch_size?: Int64;
  /** 训练轮数 */
  epochs?: Int64;
  /** 学习率 */
  learning_rate?: number;
}

export interface FinetuneParam {
  type?: FinetuneParamType;
  name?: string;
  min?: string;
  max?: string;
  option_values?: Array<string>;
  is_activated?: boolean;
}

export interface FinetuneTemplateDatasets {
  zip_files?: string;
}

export interface FinetuneTrainingInfo {
  /** 具体数据 */
  metric_data?: Array<FinetuneTrainingInfoItem>;
  /** 指标名 */
  metric_name?: string;
}

export interface FinetuneTrainingInfoItem {
  /** 横坐标 */
  step?: Int64;
  /** 纵坐标 */
  value?: number;
}

export interface GetEstimatedTpmExpansionCostData {
  estimated_cost?: number;
}

export interface GetEstimatedTpmExpansionCostRequest {
  model_id?: string;
  enterprise_id?: string;
  input_tpm?: string;
  output_tpm?: string;
}

export interface GetEstimatedTpmExpansionCostResponse {
  data?: GetEstimatedTpmExpansionCostData;
  code?: Int64;
  msg?: string;
}

export interface GetEstimatedTpmExpansionData {
  /** 输入TPM扩容值 */
  tpm_input_expansion?: Int64;
  /** 输出TPM扩容值 */
  tpm_output_expansino?: Int64;
}

export interface GetEstimatedTpmExpansionRequest {
  model_id?: string;
  enterprise_id?: string;
  estimated_rpm?: string;
  /** 开始生效时间 */
  StartTime?: string;
  /** 结束生效时间 */
  EndTime?: string;
}

export interface GetEstimatedTpmExpansionResponse {
  data?: GetEstimatedTpmExpansionData;
  code?: Int64;
  msg?: string;
}

export interface GetEstimatedTrainingCostData {
  cost?: number;
  token?: Int64;
  unit_cost?: number;
  epocs?: Int64;
  has_finish?: boolean;
}

export interface GetEstimatedTrainingCostRequest {
  task_id?: string;
  space_id?: string;
  base_model_id?: string;
  epochs?: Int64;
}

export interface GetEstimatedTrainingCostResponse {
  data?: GetEstimatedTrainingCostData;
  code?: Int64;
  msg?: string;
}

export interface GetFinetuneTemplateDatasetRequest {}

export interface GetFinetuneTemplateDatasetResponse {
  data?: FinetuneTemplateDatasets;
  code?: Int64;
  msg?: string;
}

export interface GetFinetuneTrainingInfoData {
  data_list?: Array<FinetuneTrainingInfo>;
}

export interface GetFinetuneTrainingInfoRequest {
  space_id?: string;
  model_id?: string;
}

export interface GetFinetuneTrainingInfoResponse {
  data?: GetFinetuneTrainingInfoData;
  code?: Int64;
  msg?: string;
}

export interface GetModelConcurrencyPerformanceDataRequest {
  space_id?: string;
  model_id?: string;
}

export interface GetModelConcurrencyPerformanceDataResponse {
  data?: ModelConcurrencyPerformance;
  code?: Int64;
  msg?: string;
}

export interface GetModelInfoData {
  model_info?: ModelInfo;
  model_owner?: common_struct.User;
}

export interface GetModelInfoRequest {
  space_id?: string;
  model_id?: string;
  is_finetuning?: boolean;
}

export interface GetModelInfoResponse {
  data?: GetModelInfoData;
  code?: Int64;
  msg?: string;
}

export interface GetModelListData {
  model_list?: Array<ModelListCard>;
  next_cursor_id?: string;
  has_more?: boolean;
  total?: Int64;
}

export interface GetModelListFilterParamsData {
  tag_filters?: Partial<Record<ModelTagClass, Array<string>>>;
  context_len_min?: Int64;
  context_len_max?: Int64;
  model_cost_min?: Int64;
  model_cost_max?: Int64;
  model_vendors?: Array<string>;
  model_ability?: ModelAbility;
  model_show_family_list?: Array<ModelShowFamily>;
}

export interface GetModelListFilterParamsRequest {}

export interface GetModelListFilterParamsResponse {
  data?: GetModelListFilterParamsData;
  code?: Int64;
  msg?: string;
}

export interface GetModelListRequest {
  space_id?: string;
  name?: string;
  tag_filters?: Partial<Record<ModelTagClass, Array<string>>>;
  context_len_min?: Int64;
  context_len_max?: Int64;
  model_cost_min?: Int64;
  model_cost_max?: Int64;
  model_vendor?: string;
  statusList?: Array<ModelStatus>;
  model_show_family_id?: string;
  order_by?: OrderBy;
  cursor_id?: string;
  limit?: number;
}

export interface GetModelListResponse {
  data?: GetModelListData;
  code?: Int64;
  msg?: string;
}

export interface GetModelPerformanceData {
  output_speed?: Array<ModelPerformance>;
  latency?: Array<ModelPerformance>;
  accuracy?: Array<ModelPerformance>;
}

export interface GetModelPerformanceDataRequest {
  space_id?: string;
  model_id?: string;
}

export interface GetModelPerformanceDataResponse {
  data?: GetModelPerformanceData;
  code?: Int64;
  msg?: string;
}

export interface GetModelUsageDataRequest {
  space_id?: string;
  model_id?: string;
}

export interface GetModelUsageDataResponse {
  data?: ModelUsageData;
  code?: Int64;
  msg?: string;
}

export interface GetSpaceModelUserConfigData {
  space_model_user_config_list?: Array<SpaceModelUserConfig>;
}

export interface GetSpaceModelUserConfigRequest {
  space_id?: string;
}

export interface GetSpaceModelUserConfigResponse {
  data?: GetSpaceModelUserConfigData;
  code?: Int64;
  msg?: string;
}

export interface ModelAbility {
  /** 是否展示cot */
  cot_display?: boolean;
  /** 是否支持function call */
  function_call?: boolean;
  /** 是否支持图片理解 */
  image_understanding?: boolean;
  /** 是否支持视频理解 */
  video_understanding?: boolean;
  /** 是否支持音频理解 */
  audio_understanding?: boolean;
  /** 是否支持续写 */
  prefill_resp?: boolean;
}

export interface ModelBasicInfo {
  name?: string;
  model_id?: string;
  model_class?: ModelClass;
  /** domain_model.thrift icon的url */
  model_icon?: string;
  model_input_price?: number;
  model_output_price?: number;
  token_limit?: number;
  model_name?: string;
  model_class_name?: string;
  model_desc?: string;
  param_size?: string;
  /** 方舟模型节点名称 */
  endpoint_name?: string;
  model_tags?: Partial<Record<ModelTagClass, Array<string>>>;
  /** 模型厂商 */
  model_vendor?: string;
  /** 模型参数 */
  model_params?: Array<ModelParameter>;
  show_family_id?: string;
  model_ability?: ModelAbility;
  default_model_flag?: boolean;
  /** 0-用户可见 1-用户不可见 */
  model_config_type?: number;
  online_time?: string;
  offline_time?: string;
  status?: ModelStatus;
  update_time?: string;
}

export interface ModelConcurrencyPerformance {
  input_tmp_1d?: Array<ModelPerformance>;
  output_tmp_1d?: Array<ModelPerformance>;
  rpm_1d?: Array<ModelPerformance>;
  input_tmp_30d?: Array<ModelPerformance>;
  output_tmp_30d?: Array<ModelPerformance>;
  rpm_30d?: Array<ModelPerformance>;
  /** 一个月内每日峰值超出rpm阈值的次数 */
  exceed_rpm_threshold_num?: Int64;
  /** 一个月内每日峰值超出tpm阈值的次数 */
  exceed_tpm_threshold_num?: Int64;
}

export interface ModelConcurrentInfo {
  /** TPM扩容是否生效 */
  has_model_tpm_expansion?: boolean;
  /** 是否可以TPM扩容（只有可以TPM扩容的模型才能） */
  can_model_tpm_expansion?: boolean;
  /** 是否展示并发模型监控 */
  show_model_concurrent_monitor?: boolean;
}

export interface ModelInfo {
  model_basic_info?: ModelBasicInfo;
  model_rate_limit?: ModelRateLimit;
  model_speed?: Int64;
  model_permission_info?: ModelPermissionInfo;
  finetune_params?: Partial<Record<FinetuneParamType, FinetuneParam>>;
  model_concurrent_info?: ModelConcurrentInfo;
}

export interface ModelListCard {
  model_basic_info?: ModelBasicInfo;
  model_permission_info?: ModelPermissionInfo;
  model_speed?: Int64;
  is_finetuning?: boolean;
}

export interface ModelParamClass {
  /** 1="Generation diversity", 2="Input and output length", 3="Output format" */
  class_id?: number;
  label?: string;
}

export interface ModelParamDefaultValue {
  default_val: string;
  creative?: string;
  balance?: string;
  precise?: string;
}

export interface ModelParameter {
  /** 配置字段，如max_tokens */
  name: string;
  /** 配置字段展示名称 */
  label?: string;
  /** 配置字段详情描述 */
  desc?: string;
  /** 类型 */
  type: ModelParamType;
  /** 数值类型参数，允许设置的最小值 */
  min?: string;
  /** 数值类型参数，允许设置的最大值 */
  max?: string;
  /** float类型参数的精度 */
  precision?: number;
  /** 参数默认值{"default": xx, "creative":xx} */
  default_val: ModelParamDefaultValue;
  /** 枚举值，如response_format支持text,markdown,json */
  options?: Array<Option>;
  /** 参数分类，"Generation diversity", "Input and output length", "Output format" */
  param_class?: ModelParamClass;
}

export interface ModelPerformance {
  data?: number;
  time?: string;
}

export interface ModelPermissionInfo {
  can_delete?: boolean;
  can_finetune?: boolean;
  can_stop_tune_task?: boolean;
  can_retry_tune_task?: boolean;
  need_show_finetune_attention?: boolean;
}

export interface ModelRateLimit {
  qpm?: number;
  tpm?: number;
}

export interface ModelShowFamily {
  id?: string;
  icon?: string;
  iconUrl?: string;
  name?: string;
  ranking?: number;
}

export interface ModelUsage {
  BotTotalInput?: Int64;
  BotTotalOutput?: Int64;
  WorkflowTotalInput?: Int64;
  WorkflowTotalOutput?: Int64;
  date?: string;
}

export interface ModelUsageData {
  usage?: Array<ModelUsage>;
}

export interface OperateFinetuneTaskData {
  id?: string;
  status?: ModelStatus;
}

export interface OperateFinetuneTaskRequest {
  id?: string;
  /** 操作，stop, resume, delete */
  action?: OperateFinetuneTaskAction;
}

export interface OperateFinetuneTaskResponse {
  data?: OperateFinetuneTaskData;
  code?: Int64;
  msg?: string;
}

export interface Option {
  /** option展示的值 */
  label?: string;
  /** 填入的值 */
  value?: string;
}

export interface SpaceModelUserConfig {
  space_id?: string;
  model_id?: string;
  config_type?: number;
}

export interface StartEstimatedTrainingCostData {
  task_id?: string;
}

export interface StartEstimatedTrainingCostRequest {
  base_model_id?: string;
  space_id?: string;
  training_dataset_id?: string;
  epochs?: Int64;
}

export interface StartEstimatedTrainingCostResponse {
  data?: StartEstimatedTrainingCostData;
  code?: Int64;
  msg?: string;
}

export interface UpdateModelConfigTypeRequest {
  space_id?: string;
  model_id?: string;
  /** 0-用户可见 1-用户不可见 */
  config_type?: number;
}

export interface UpdateModelConfigTypeResponse {
  code?: Int64;
  msg?: string;
}

export interface UploadFinetuneDatasetData {
  dataset_id?: string;
}

export interface UploadFinetuneDatasetRequest {
  space_id?: string;
  /** 文件类型，后缀 */
  fileType?: string;
  /** 文件名 */
  fileName?: string;
  data?: string;
  testing_data?: Blob;
}

export interface UploadFinetuneDatasetResponse {
  data?: UploadFinetuneDatasetData;
  code?: Int64;
  msg?: string;
}

export interface ValidatingDataset {
  validating_type?: ValidatingType;
  /** 分割比例，如果是训练集分割传递 0 -100 */
  split_ratio?: Int64;
  validating_dataset_id?: string;
}
/* eslint-enable */
