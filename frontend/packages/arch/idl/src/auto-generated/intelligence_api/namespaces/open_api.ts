/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as base from './base';

export type Int64 = string | number;

export interface AppSimpleInfo {
  id?: string;
  name?: string;
  description?: string;
  icon_url?: string;
  is_published?: boolean;
  updated_at?: Int64;
  published_at?: Int64;
  owner_user_id?: string;
  folder_id?: string;
}

export interface BotSimpleInfo {
  id?: string;
  name?: string;
  description?: string;
  icon_url?: string;
  is_published?: boolean;
  /** 草稿返回update_time */
  updated_at?: Int64;
  /** 发布态返回publish_time */
  published_at?: Int64;
  owner_user_id?: string;
  folder_id?: string;
}

export interface FolderSimpleInfo {
  id?: string;
  name?: string;
  description?: string;
  workspace_id?: string;
  creator_user_id?: string;
  parent_folder_id?: string;
  /** OpenGetSpaceFolder 接口才返回 */
  children_count?: number;
}

export interface OpenGetBotData {
  total?: Int64;
  items?: Array<BotSimpleInfo>;
}

export interface OpenGetBotListRequest {
  workspace_id?: string;
  publish_status?: string;
  connector_id?: string;
  page_num?: Int64;
  page_size?: Int64;
}

export interface OpenGetBotListResponse {
  data?: OpenGetBotData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface OpenGetFolderInfoRequest {
  folder_id?: string;
}

export interface OpenGetFolderInfoResponse {
  data?: FolderSimpleInfo;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface OpenGetProjectData {
  total?: Int64;
  items?: Array<AppSimpleInfo>;
}

export interface OpenGetProjectListRequest {
  workspace_id?: string;
  publish_status?: string;
  /** 只在PublishedOnline传入 */
  connector_id?: string;
  page_num?: Int64;
  page_size?: Int64;
}

export interface OpenGetProjectListResponse {
  data?: OpenGetProjectData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}

export interface OpenGetSpaceFolderData {
  items?: Array<FolderSimpleInfo>;
  total_count?: Int64;
  has_more?: boolean;
}

export interface OpenGetSpaceFolderRequest {
  workspace_id?: string;
  folder_type?: string;
  /** 不传/传0表示获取根文件夹 */
  parent_folder_id?: string;
  page_num?: number;
  page_size?: number;
}

export interface OpenGetSpaceFolderResponse {
  data?: OpenGetSpaceFolderData;
  code?: Int64;
  msg?: string;
  BaseResp?: base.BaseResp;
}
/* eslint-enable */
