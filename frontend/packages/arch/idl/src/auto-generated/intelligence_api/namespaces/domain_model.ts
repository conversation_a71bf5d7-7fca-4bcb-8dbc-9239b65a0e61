/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum DomainModelClass {
  GPT = 1,
  SEED = 2,
  Claude = 3,
  /** name: MiniMax */
  MiniMax = 4,
  Plugin = 5,
  StableDiffusion = 6,
  ByteArtist = 7,
  Maas = 9,
  /** 废弃：千帆(百度云) */
  QianFan = 10,
  /** name：Google Gemini */
  Gemini = 11,
  /** name: Moonshot */
  Moonshot = 12,
  /** name：智谱 */
  GLM = 13,
  /** name: 火山方舟 */
  MaaSAutoSync = 14,
  /** name：通义千问 */
  QWen = 15,
  /** name: Cohere */
  Cohere = 16,
  /** name: 百川智能 */
  Baichuan = 17,
  /** name：文心一言 */
  Ernie = 18,
  /** name: 幻方 */
  DeekSeek = 19,
  /** name: Llama */
  Llama = 20,
  StepFun = 23,
  Other = 999,
}

export enum DomainModelParamType {
  Float = 1,
  Int = 2,
  Boolean = 3,
  String = 4,
}

export enum DomainModelStatus {
  Online = 1,
  Offline = 2,
  Deleted = 3,
  /** 排队中 */
  FineTuneInQueue = 5,
  /** 执行中 */
  FineTuneRunning = 6,
  /** 终止 */
  FineTuneTerminated = 7,
  /** 失败 */
  FineTuneFailed = 8,
  /** 成功 */
  FineTuneSuccess = 9,
}

/** 模型聚合 */
export enum DomainModelTagClass {
  ModelType = 1,
  ModelUserRight = 2,
  ModelFeature = 3,
  ModelFunction = 4,
  ModelScenario = 5,
  ModelPaid = 15,
  /** 模型运行时能力 */
  ModelAbility = 16,
  /** 本期不做 */
  Custom = 20,
}
/* eslint-enable */
