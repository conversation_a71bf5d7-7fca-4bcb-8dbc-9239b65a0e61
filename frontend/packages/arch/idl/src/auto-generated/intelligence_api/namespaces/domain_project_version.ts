/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export enum ProjectVersionAuditStatus {
  /** 未审核/审核中 */
  Default = 0,
  /** 审核通过 */
  AuditPass = 1,
  /** 审核不通过 */
  AuditNotPass = 2,
}

export enum ProjectVersionStatus {
  /** 版本创建中 */
  Default = 0,
  /** 版本可用（创建成功） */
  Available = 1,
  /** 版本不可用（创建失败） */
  Unavailable = 2,
}

export enum ProjectVersionType {
  /** 发布 */
  Publish = 0,
  /** 存档 */
  Archive = 1,
}
/* eslint-enable */
