/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common_struct from './common_struct';
import * as intelligence_common_struct from './intelligence_common_struct';
import * as publish from './publish';

export type Int64 = string | number;

export enum HistoryType {
  /** 发布 */
  Publish = 0,
  /** 存档 */
  Archive = 1,
}

export enum OperateScene {
  /** 用户页面操作 */
  User = 0,
  /** NL2App场景 */
  NL2App = 1,
}

export interface AppData {
  app_id?: string;
  version?: string;
  name?: string;
  description?: string;
  icon_url?: string;
  /** 变量列表 */
  variables?: Array<common_struct.Variable>;
}

export interface ArchiveProjectData {
  task_id?: string;
  project_id?: string;
  version?: string;
}

export interface ArchiveProjectRequest {
  project_id: string;
  /** 描述 */
  description: string;
  /** 操作场景 */
  scene: OperateScene;
}

export interface ArchiveProjectResponse {
  data?: ArchiveProjectData;
  code?: Int64;
  msg?: string;
}

export interface DraftProjectCopyRequest {
  project_id?: string;
  to_space_id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
}

export interface DraftProjectCopyResponse {
  data?: DraftProjectCopyResponseData;
  code: Int64;
  msg: string;
}

export interface DraftProjectCopyResponseData {
  basic_info?: intelligence_common_struct.IntelligenceBasicInfo;
  audit_data?: common_struct.AuditData;
  user_info?: common_struct.User;
}

export interface DraftProjectCreateData {
  project_id?: string;
  audit_data?: common_struct.AuditData;
}

export interface DraftProjectCreateRequest {
  space_id?: string;
  name?: string;
  description?: string;
  icon_uri?: string;
  monetization_conf?: MonetizationConf;
  /** 创建来源  navi:导航栏 space:空间 */
  create_from?: string;
  folder_id?: string;
}

export interface DraftProjectCreateResponse {
  data?: DraftProjectCreateData;
  code: Int64;
  msg: string;
}

export interface DraftProjectCrossSpaceCopyData {
  task_id?: string;
}

export interface DraftProjectCrossSpaceCopyRequest {
  project_id?: string;
  to_space_id?: string;
}

export interface DraftProjectCrossSpaceCopyResponse {
  data?: DraftProjectCrossSpaceCopyData;
  code: Int64;
  msg: string;
}

export interface DraftProjectDeleteRequest {
  project_id: string;
}

export interface DraftProjectDeleteResponse {
  code: Int64;
  msg: string;
}

export interface DraftProjectUpdateData {
  audit_data?: common_struct.AuditData;
}

export interface DraftProjectUpdateRequest {
  project_id: string;
  name?: string;
  description?: string;
  icon_uri?: string;
}

export interface DraftProjectUpdateResponse {
  data?: DraftProjectUpdateData;
  code: Int64;
  msg: string;
}

export interface GetConnectorPublishConfigData {
  associated_workflow_ids?: Array<Int64>;
}

export interface MonetizationConf {
  is_enable?: boolean;
}

export interface OnlineProjectCopyResponseData {
  basic_info?: intelligence_common_struct.IntelligenceBasicInfo;
  audit_data?: common_struct.AuditData;
}

export interface ProjectData {
  project_id?: Int64;
  space_id?: Int64;
  version?: Int64;
  name?: string;
  description?: string;
  icon_uri?: string;
  icon_url?: string;
  owner_id?: Int64;
  resource_info?: ProjectResourceInfo;
  template_relation?: TemplateRelation;
}

export interface ProjectHistory {
  project_id?: string;
  version?: string;
  history_type?: HistoryType;
  description?: string;
  /** 操作人 */
  operator?: common_struct.User;
  /** 操作时间 */
  operate_time?: Int64;
  /** 历史记录为发布类型时有值 */
  publish_detail?: publish.PublishRecordDetail;
}

export interface ProjectHistoryListData {
  history_list?: Array<ProjectHistory>;
  next_cursor?: string;
  has_more?: boolean;
}

export interface ProjectHistoryListRequest {
  project_id: string;
  history_type?: HistoryType;
  /** 游标，首次调用不传 */
  cursor?: string;
  /** 默认20，最大100 */
  size?: number;
}

export interface ProjectHistoryListResponse {
  data?: ProjectHistoryListData;
  code?: Int64;
  msg?: string;
}

export interface ProjectResourceInfo {
  plugin_list?: Array<ResourceInfo>;
  workflow_list?: Array<ResourceInfo>;
  image_flow_list?: Array<ResourceInfo>;
  knowledge_list?: Array<ResourceInfo>;
  ui_list?: Array<ResourceInfo>;
  database_list?: Array<ResourceInfo>;
}

export interface ResourceInfo {
  resource_id?: Int64;
  resource_type?: common_struct.ResourceType;
  /** A-B, A-C-B, B可以同时在A和C的Children出现 */
  resource_children?: Array<ResourceInfo>;
}

export interface RollbackProjectRequest {
  project_id: string;
  /** 回退到的版本 */
  rollback_version: string;
  /** 操作场景 */
  scene: OperateScene;
}

export interface RollbackProjectResponse {
  task_id?: string;
  code?: Int64;
  msg?: string;
}

export interface TemplateRelation {
  /** 源ProjectID */
  origin_project_id?: Int64;
  /** 源Project版本 */
  origin_project_version?: Int64;
  /** 模板ProjectID */
  template_project_id?: Int64;
  /** 模板Project版本 */
  template_project_version?: Int64;
}
/* eslint-enable */
