/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as bot_operation_common from './bot_operation_common';
import * as dist_reward_task from './dist_reward_task';

export type Int64 = string | number;

export interface AdminGetRefundOrderByOrderRequest {
  order_id: string;
}

export interface AdminGetRefundOrderByOrderResponse {
  code?: number;
  message?: string;
  refund_order_list?: Array<RefundOrder>;
}

export interface AdminListOrderRequest {
  /** 页码, >=1 */
  page: string;
  /** 每页数量, 1-200 */
  count: string;
  /** 用户 id */
  user_id?: string;
  /** 订单 id */
  order_id?: string;
}

export interface AdminListOrderResponse {
  code?: number;
  message?: string;
  data?: AdminListOrderResponseData;
}

export interface AdminListOrderResponseData {
  /** 订单列表 */
  order_list?: Array<OpOrder>;
  /** 总数 */
  total?: Int64;
}

export interface AdminRefundOrderRequest {
  order_id: string;
  refund_type?: bot_operation_common.RefundType;
  reason: string;
  is_skip_check?: boolean;
}

export interface AdminRefundOrderResponse {
  code?: number;
  message?: string;
}

export interface OpOrder {
  order_id?: string;
  /** 时间戳，单位秒 */
  order_timestamp?: string;
  /** 下单国家，可能为空 */
  submit_country?: string;
  /** 商品类型，可能为 Premium lite,Premium,Premium plus,Coze token,Message credits, AutoCharge Coze Token */
  goods_type_display?: string;
  /** 订单类型，1 为 TokenAutoCharge，2 为 TokenAutoCharge，3 为 SubMessageCredit */
  order_type?: bot_operation_common.OrderType;
  /** 支付金额 */
  purchase_amount?: string;
  /** 退款金额 */
  refund_amount?: string;
  /** 退款单 id */
  refund_order_id?: string;
  /** 订单状态 */
  order_status?: bot_operation_common.OrderStatus;
  /** 能否退款 */
  can_refund?: boolean;
  /** 是否存在退款单 */
  has_refund_order?: boolean;
  /** 不能退款原因 */
  cant_refund_reason?: string;
  /** 审批单列表 */
  approve_task_list?: Array<SimpleApproveTask>;
}

export interface RefundOrder {
  refund_order_id?: string;
  refund_reason?: string;
}

export interface SimpleApproveTask {
  ID?: Int64;
  Status?: dist_reward_task.DistRewardTaskStatus;
}
/* eslint-enable */
