/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as auto_charge from './namespaces/auto_charge';
import * as bot_operation_common from './namespaces/bot_operation_common';
import * as common from './namespaces/common';
import * as coze_token_task from './namespaces/coze_token_task';
import * as dist_reward_task from './namespaces/dist_reward_task';
import * as fulfill_common from './namespaces/fulfill_common';
import * as fulfillment from './namespaces/fulfillment';
import * as order from './namespaces/order';
import * as service from './namespaces/service';

export {
  auto_charge,
  bot_operation_common,
  common,
  coze_token_task,
  dist_reward_task,
  fulfill_common,
  fulfillment,
  order,
  service,
};
export * from './namespaces/auto_charge';
export * from './namespaces/bot_operation_common';
export * from './namespaces/common';
export * from './namespaces/coze_token_task';
export * from './namespaces/dist_reward_task';
export * from './namespaces/fulfill_common';
export * from './namespaces/fulfillment';
export * from './namespaces/order';
export * from './namespaces/service';

export type Int64 = string | number;

export default class FulfillOperationService<T> {
  private request: any = () => {
    throw new Error('FulfillOperationService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/marketplace/opt_fulfill/coze_token_task/create
   *
   * == BOT 运营后台 - coze token ==
   *
   * 创建 coze token 任务
   */
  BotOpCreateCozeTokenTask(
    req?: coze_token_task.BotOpCreateCozeTokenTaskRequest,
    options?: T,
  ): Promise<coze_token_task.BotOpCreateCozeTokenTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/coze_token_task/create',
    );
    const method = 'POST';
    const data = {
      to_uid_list: _req['to_uid_list'],
      single_user_token: _req['single_user_token'],
      reason: _req['reason'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/coze_token_task/list
   *
   * 获取 coze token 任务
   */
  BotOpListCozeTokenTask(
    req?: coze_token_task.BotOpListCozeTokenTaskRequest,
    options?: T,
  ): Promise<coze_token_task.BotOpListCozeTokenTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/coze_token_task/list',
    );
    const method = 'POST';
    const data = {
      page: _req['page'],
      count: _req['count'],
      status_in: _req['status_in'],
      created_at_begin: _req['created_at_begin'],
      uid: _req['uid'],
      disable_env: _req['disable_env'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/coze_token_task/list_fulfillment
   *
   * 获取 coze token 任务对应的履约单
   */
  BotOpListCozeTokenTaskFulfillment(
    req?: coze_token_task.BotOpListCozeTokenTaskFulfillmentRequest,
    options?: T,
  ): Promise<coze_token_task.BotOpListCozeTokenTaskFulfillmentResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/coze_token_task/list_fulfillment',
    );
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/coze_token_task/cancel
   *
   * 取消 coze token 任务
   */
  BotOpCancelCozeTokenTask(
    req?: coze_token_task.BotOpCancelCozeTokenTaskRequest,
    options?: T,
  ): Promise<coze_token_task.BotOpCancelCozeTokenTaskResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/coze_token_task/cancel',
    );
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * GET /api/marketplace/fulfill/auto_charge/get_config
   *
   * == 自动充值 ==
   *
   * 获取自动充值配置
   */
  GetAutoChargeConfig(
    req?: auto_charge.GetAutoChargeConfigRequest,
    options?: T,
  ): Promise<auto_charge.GetAutoChargeConfigResponse> {
    const _req = req || {};
    const url = this.genBaseURL(
      '/api/marketplace/fulfill/auto_charge/get_config',
    );
    const method = 'GET';
    const params = { UserID: _req['UserID'] };
    return this.request({ url, method, params }, options);
  }

  /**
   * POST /api/marketplace/fulfill/auto_charge/cancel
   *
   * 解约
   */
  CancelAutoCharge(
    req?: auto_charge.CancelAutoChargeRequest,
    options?: T,
  ): Promise<auto_charge.CancelAutoChargeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/fulfill/auto_charge/cancel');
    const method = 'POST';
    const data = { UserID: _req['UserID'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/fulfill/auto_charge/sign
   *
   * 签约并保存配置
   */
  SignAutoCharge(
    req?: auto_charge.SignAutoChargeRequest,
    options?: T,
  ): Promise<auto_charge.SignAutoChargeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/fulfill/auto_charge/sign');
    const method = 'POST';
    const data = {
      UserID: _req['UserID'],
      threshold_amount: _req['threshold_amount'],
      charge_amount: _req['charge_amount'],
      max_charge_amount_per_day: _req['max_charge_amount_per_day'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/dist_reward_task/list_fulfillment
   *
   * 获取发放奖励任务对应的履约单
   */
  AdminListDistRewardTaskFulfillment(
    req: dist_reward_task.AdminListDistRewardTaskFulfillmentRequest,
    options?: T,
  ): Promise<dist_reward_task.AdminListDistRewardTaskFulfillmentResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/dist_reward_task/list_fulfillment',
    );
    const method = 'POST';
    const data = { task_id: _req['task_id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/dist_reward_task/create
   *
   * 创建发放奖励任务
   */
  AdminCreateDistRewardTask(
    req: dist_reward_task.AdminCreateDistRewardTaskRequest,
    options?: T,
  ): Promise<dist_reward_task.AdminCreateDistRewardTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/dist_reward_task/create',
    );
    const method = 'POST';
    const data = {
      to_uid_list: _req['to_uid_list'],
      dist_reward_type: _req['dist_reward_type'],
      reward_detail: _req['reward_detail'],
      reason: _req['reason'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/dist_reward_task/list
   *
   * 列出发放奖励任务
   */
  AdminListDistRewardTask(
    req: dist_reward_task.AdminListDistRewardTaskRequest,
    options?: T,
  ): Promise<dist_reward_task.AdminListDistRewardTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/dist_reward_task/list',
    );
    const method = 'POST';
    const data = {
      page: _req['page'],
      count: _req['count'],
      status_in: _req['status_in'],
      created_at_begin: _req['created_at_begin'],
      uid: _req['uid'],
      disable_env: _req['disable_env'],
      dist_reward_type: _req['dist_reward_type'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/dist_reward_task/cancel
   *
   * 取消发放奖励任务
   */
  AdminCancelDistRewardTask(
    req: dist_reward_task.AdminCancelDistRewardTaskRequest,
    options?: T,
  ): Promise<dist_reward_task.AdminCancelDistRewardTaskResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/dist_reward_task/cancel',
    );
    const method = 'POST';
    const data = { id: _req['id'] };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/order/list
   *
   * 列出订单列表
   */
  AdminListOrder(
    req: order.AdminListOrderRequest,
    options?: T,
  ): Promise<order.AdminListOrderResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/opt_fulfill/order/list');
    const method = 'POST';
    const data = {
      page: _req['page'],
      count: _req['count'],
      user_id: _req['user_id'],
      order_id: _req['order_id'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/order/refund
   *
   * 对订单进行退款
   */
  AdminRefundOrder(
    req: order.AdminRefundOrderRequest,
    options?: T,
  ): Promise<order.AdminRefundOrderResponse> {
    const _req = req;
    const url = this.genBaseURL('/api/marketplace/opt_fulfill/order/refund');
    const method = 'POST';
    const data = {
      order_id: _req['order_id'],
      refund_type: _req['refund_type'],
      reason: _req['reason'],
      is_skip_check: _req['is_skip_check'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/opt_fulfill/order/refund_order
   *
   * 获取订单下的退款单
   */
  AdminGetRefundOrderByOrder(
    req: order.AdminGetRefundOrderByOrderRequest,
    options?: T,
  ): Promise<order.AdminGetRefundOrderByOrderResponse> {
    const _req = req;
    const url = this.genBaseURL(
      '/api/marketplace/opt_fulfill/order/refund_order',
    );
    const method = 'POST';
    const data = { order_id: _req['order_id'] };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
