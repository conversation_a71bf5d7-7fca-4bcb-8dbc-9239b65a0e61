/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as api from './namespaces/api';
import * as common from './namespaces/common';

export { api, common };
export * from './namespaces/api';
export * from './namespaces/common';

export type Int64 = string | number;

export default class NotifyApiService<T> {
  private request: any = () => {
    throw new Error('NotifyApiService.request is undefined');
  };
  private baseURL: string | ((path: string) => string) = '';

  constructor(options?: {
    baseURL?: string | ((path: string) => string);
    request?<R>(
      params: {
        url: string;
        method: 'GET' | 'DELETE' | 'POST' | 'PUT' | 'PATCH';
        data?: any;
        params?: any;
        headers?: any;
      },
      options?: T,
    ): Promise<R>;
  }) {
    this.request = options?.request || this.request;
    this.baseURL = options?.baseURL || '';
  }

  private genBaseURL(path: string) {
    return typeof this.baseURL === 'string'
      ? this.baseURL + path
      : this.baseURL(path);
  }

  /**
   * POST /api/marketplace/notify/mobile_verify/send
   *
   * 发送短信验证码
   */
  SendMobileVerifyCode(
    req?: api.SendMobileVerifyCodeRequest,
    options?: T,
  ): Promise<api.SendMobileVerifyCodeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/notify/mobile_verify/send');
    const method = 'POST';
    const data = { mobile: _req['mobile'] };
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/marketplace/notify/email_verify/send
   *
   * ------------------------------------ HTTP 接口 ------------------------------------
   *
   * 发送邮件验证码
   */
  SendEmailVerifyCode(
    req?: api.SendEmailVerifyCodeRequest,
    options?: T,
  ): Promise<api.SendEmailVerifyCodeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/notify/email_verify/send');
    const method = 'POST';
    const data = { email_address: _req['email_address'] };
    const headers = { 'Tt-Agw-Client-Ip': _req['Tt-Agw-Client-Ip'] };
    return this.request({ url, method, data, headers }, options);
  }

  /**
   * POST /api/marketplace/notify/email_verify/check
   *
   * 校验邮件验证码
   */
  CheckEmailVerifyCode(
    req?: api.CheckEmailVerifyCodeRequest,
    options?: T,
  ): Promise<api.CheckEmailVerifyCodeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/notify/email_verify/check');
    const method = 'POST';
    const data = {
      message_id: _req['message_id'],
      verify_code: _req['verify_code'],
      email_address: _req['email_address'],
    };
    return this.request({ url, method, data }, options);
  }

  /**
   * POST /api/marketplace/notify/mobile_verify/check
   *
   * 校验短信验证码
   */
  CheckMobileVerifyCode(
    req?: api.CheckMobileVerifyCodeRequest,
    options?: T,
  ): Promise<api.CheckMobileVerifyCodeResponse> {
    const _req = req || {};
    const url = this.genBaseURL('/api/marketplace/notify/mobile_verify/check');
    const method = 'POST';
    const data = {
      message_id: _req['message_id'],
      verify_code: _req['verify_code'],
      mobile: _req['mobile'],
    };
    return this.request({ url, method, data }, options);
  }
}
/* eslint-enable */
