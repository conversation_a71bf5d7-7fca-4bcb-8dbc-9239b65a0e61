/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

import * as common from './common';

export type Int64 = string | number;

export interface CheckEmailVerifyCodeData {
  status?: common.VerifyStatus;
  /** 验证票据，用于验证 Email 是否真正完成了校验 */
  ticket?: string;
}

export interface CheckEmailVerifyCodeRequest {
  message_id?: string;
  verify_code?: string;
  email_address?: string;
}

export interface CheckEmailVerifyCodeResponse {
  code?: number;
  message?: string;
  data?: CheckEmailVerifyCodeData;
}

export interface CheckMobileVerifyCodeData {
  status?: common.VerifyStatus;
  /** 验证票据，用于验证 Mobile 是否真正完成了校验 */
  ticket?: string;
}

export interface CheckMobileVerifyCodeRequest {
  message_id?: string;
  verify_code?: string;
  mobile?: string;
}

export interface CheckMobileVerifyCodeResponse {
  code?: number;
  message?: string;
  data?: CheckMobileVerifyCodeData;
}

export interface SendEmailVerifyCodeRequest {
  'Tt-Agw-Client-Ip'?: string;
  email_address?: string;
}

export interface SendEmailVerifyCodeResponse {
  code?: number;
  message?: string;
  data?: SendEmailVerifyData;
}

export interface SendEmailVerifyData {
  message_id?: string;
}

export interface SendMobileVerifyCodeRequest {
  'Tt-Agw-Client-Ip'?: string;
  mobile?: string;
}

export interface SendMobileVerifyCodeResponse {
  code?: number;
  message?: string;
  data?: SendMobileVerifyData;
}

export interface SendMobileVerifyData {
  message_id?: string;
}
/* eslint-enable */
