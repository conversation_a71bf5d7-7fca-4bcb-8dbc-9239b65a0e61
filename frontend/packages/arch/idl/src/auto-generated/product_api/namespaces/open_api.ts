/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck

export type Int64 = string | number;

export interface DuplicateTemplateData {
  /** 复制后的实体ID，如果复制的是智能体模板，对应复制后的智能体ID */
  entity_id?: string;
  /** 枚举类型，目前只有 agent（智能体） */
  entity_type?: string;
}

export interface DuplicateTemplateRequest {
  /** 模板ID（目前仅支持复制智能体） */
  template_id?: string;
  /** 工作空间ID（预期将模板复制该空间） */
  workspace_id?: string;
  /** 复制后的实体名称（对于复制智能体来说，未指定则默认用复制的智能体的名称） */
  name?: string;
}

export interface DuplicateTemplateResponse {
  code?: number;
  msg?: string;
  data?: DuplicateTemplateData;
}
/* eslint-enable */
