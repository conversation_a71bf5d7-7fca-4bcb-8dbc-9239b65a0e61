/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/* eslint-disable @typescript-eslint/no-explicit-any */
export const loader = {
  async init(): Promise<any> {
    const load = await this.config();
    return load.init();
  },

  async config(config = {}): Promise<any> {
    const monaco = await import('monaco-editor');
    const { loader: load } = await import('@monaco-editor/react');
    load.config({
      monaco,
      ...config,
    });
    return load;
  },
};
