{"name": "@coze-arch/bot-space-api", "version": "0.0.1", "description": "bot space api instance that extracts from apps/bot/src/services/api/space-api.ts", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-arch/web-context": "workspace:*", "axios": "^1.4.0"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@vitest/coverage-v8": "~3.0.5", "debug": "^4.3.4", "vitest": "~3.0.5"}, "// deps": "debug@^4.3.4 为脚本自动补齐，请勿改动"}