{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.node.json", "compilerOptions": {"types": [], "moduleResolution": "Node10", "module": "CommonJS", "lib": ["ES2022", "DOM"], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}]}