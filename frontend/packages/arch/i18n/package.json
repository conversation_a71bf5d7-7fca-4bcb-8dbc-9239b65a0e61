{"name": "@coze-arch/i18n", "version": "0.0.1", "author": "<EMAIL>", "exports": {".": "./src/index.ts", "./raw": "./src/raw/index.ts", "./locales": "./src/resource.ts", "./i18n-provider": "./src/i18n-provider/index.tsx", "./intl": "./src/intl/index.ts"}, "main": "./src/index.ts", "typesVersions": {"*": {"raw": ["./src/raw/index.ts"], "locales": ["./src/resource.ts"], "i18n-provider": ["./src/i18n-provider/index.tsx"], "intl": ["./src/intl/index.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-studio/studio-i18n-resource-adapter": "workspace:*", "i18next": ">= 19.0.0", "i18next-browser-languagedetector": "8.0.4", "i18next-icu": "2.3.0"}, "devDependencies": {"@babel/core": "^7.20.2", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@rsbuild/core": "1.1.13", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "react-is": ">= 16.8.0", "typescript": "~5.8.2", "vitest": "~3.0.5"}, "peerDependencies": {"react": "~18.2.0", "react-dom": "~18.2.0"}}