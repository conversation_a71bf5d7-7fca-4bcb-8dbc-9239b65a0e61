/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { uniqueId } from 'lodash-es';

import {
  defaultConversationKey,
  defaultConversationUniqId,
} from '../../src/const/community';

// 模拟 lodash-es 的 uniqueId 函数
vi.mock('lodash-es', () => ({
  uniqueId: vi.fn().mockReturnValue('mocked-unique-id'),
}));

describe('const/community', () => {
  test('defaultConversationKey should be -1', () => {
    expect(defaultConversationKey).toBe(-1);
  });

  test('defaultConversationUniqId should be a unique ID generated by lodash-es', () => {
    // 验证 uniqueId 函数被调用
    expect(uniqueId).toHaveBeenCalled();
    // 验证 defaultConversationUniqId 的值是由模拟的 uniqueId 函数返回的
    expect(defaultConversationUniqId).toBe('mocked-unique-id');
  });
});
