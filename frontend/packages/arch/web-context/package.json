{"name": "@coze-arch/web-context", "version": "0.0.1", "description": "Global Context for whole hot studio web app. You should keep using this package instead of call `window.xxx` directly", "author": "<EMAIL>", "main": "./src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "NODE_OPTIONS='--max_old_space_size=2048' NODE_ENV=test vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"eventemitter3": "^5.0.1", "lodash-es": "^4.17.21"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@vitest/coverage-v8": "~3.0.5", "sucrase": "^3.32.0", "tsconfig-paths": "4.1.0", "vitest": "~3.0.5"}}