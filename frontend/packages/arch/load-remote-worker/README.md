# @coze-arch/load-remote-worker

load remote worker inspire by https://github.com/jantimon/remote-web-worker/

## Overview

This package is part of the Coze Studio monorepo and provides architecture functionality. It includes api.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/load-remote-worker": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/load-remote-worker';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Api

## API Reference

Please refer to the TypeScript definitions for detailed API documentation.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
