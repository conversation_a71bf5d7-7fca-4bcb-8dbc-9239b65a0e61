{"name": "@coze-foundation/space-ui-base", "version": "0.0.1", "description": "工作空间通用UI组件", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/idl": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-common/auth": "workspace:*", "@coze-common/auth-adapter": "workspace:*", "@coze-common/coze-mitt": "workspace:*", "@coze-foundation/local-storage": "workspace:*", "@coze-foundation/space-store": "workspace:*", "ahooks": "^3.7.8", "classnames": "^2.3.2", "react-router-dom": "^6.11.1", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}