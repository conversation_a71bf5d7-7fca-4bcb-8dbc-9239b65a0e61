# @coze-foundation/space-store

基座中的空间store

## Overview

This package is part of the Coze Studio monorepo and provides state management functionality. It includes hook, adapter, store.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-foundation/space-store": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-foundation/space-store';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook
- Adapter
- Store

## API Reference

### Exports

- `useSpaceStore`
- `useSpace, useSpaceList, useRefreshSpaces`
- `useSpaceApp`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
