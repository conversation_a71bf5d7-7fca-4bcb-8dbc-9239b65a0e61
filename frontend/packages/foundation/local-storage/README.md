# @coze-foundation/local-storage

global local storage service

## Overview

This package is part of the Coze Studio monorepo and provides architecture functionality. It includes hook, service.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-foundation/local-storage": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-foundation/local-storage';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook
- Service

## API Reference

### Exports

- `localStorageService`
- `useValue as useLocalStorageValue`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
