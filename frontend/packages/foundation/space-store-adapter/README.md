# @coze-foundation/space-store-adapter

基座中的空间store

## Overview

This package is part of the Coze Studio monorepo and provides state management functionality. It includes store.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-foundation/space-store-adapter": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-foundation/space-store-adapter';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Store

## API Reference

### Exports

- `useSpaceStore`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
