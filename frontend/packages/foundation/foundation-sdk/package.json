{"name": "@coze-foundation/foundation-sdk", "version": "0.0.1", "description": "基座提供sdk的具体实现package", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-foundation/account-adapter": "workspace:*", "@coze-foundation/layout": "workspace:*", "@coze-foundation/space-store": "workspace:*"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/foundation-sdk": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@vitest/coverage-v8": "~3.0.5", "sucrase": "^3.32.0", "vitest": "~3.0.5"}}