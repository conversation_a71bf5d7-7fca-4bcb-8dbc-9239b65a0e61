/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect } from 'react';

import { localStorageService } from '@coze-foundation/local-storage';

import { useLoginStatus, useUserInfo } from './index';

export const useSyncLocalStorageUid = () => {
  const userInfo = useUserInfo();
  const loginStatus = useLoginStatus();

  useEffect(() => {
    if (loginStatus === 'logined') {
      localStorageService.setUserId(userInfo?.user_id_str);
    }
    if (loginStatus === 'not_login') {
      localStorageService.setUserId();
    }
  }, [loginStatus, userInfo?.user_id_str]);
};
