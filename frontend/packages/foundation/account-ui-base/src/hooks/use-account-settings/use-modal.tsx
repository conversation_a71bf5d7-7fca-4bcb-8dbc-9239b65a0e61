/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useState } from 'react';

import { Modal, type ModalProps } from '@coze-arch/coze-design';

export type UseModalParams = Omit<ModalProps, 'visible'>;

export interface UseModalReturnValue {
  modal: (inner: JSX.Element) => JSX.Element;
  open: () => void;
  close: () => void;
}

export const useModal = (params: UseModalParams): UseModalReturnValue => {
  const [visible, setVisible] = useState(false);

  return {
    modal: inner => (
      <Modal {...params} visible={visible}>
        {inner}
      </Modal>
    ),
    open: () => setVisible(true),
    close: () => setVisible(false),
  };
};
