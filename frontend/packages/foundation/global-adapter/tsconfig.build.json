{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"paths": {"@/*": ["./src/*"]}, "outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "target": "ES2020", "moduleResolution": "bundler", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../account-adapter/tsconfig.build.json"}, {"path": "../account-ui-adapter/tsconfig.build.json"}, {"path": "../account-ui-base/tsconfig.build.json"}, {"path": "../../agent-ide/space-bot/tsconfig.build.json"}, {"path": "../../arch/bot-error/tsconfig.build.json"}, {"path": "../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/default-slardar/tsconfig.build.json"}, {"path": "../../arch/foundation-sdk/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/idl/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../browser-upgrade-banner/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../global-store/tsconfig.build.json"}, {"path": "../global/tsconfig.build.json"}, {"path": "../layout/tsconfig.build.json"}, {"path": "../../studio/open-platform/open-auth/tsconfig.build.json"}, {"path": "../../studio/user-store/tsconfig.build.json"}]}