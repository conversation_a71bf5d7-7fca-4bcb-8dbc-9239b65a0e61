/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect } from 'react';

import { userStoreService } from '@coze-studio/user-store';
import { useBotListFilterStore } from '@coze-agent-ide/space-bot/store';
import { useSpaceStore } from '@coze-arch/bot-studio-store';

export const useResetStoreOnLogout = () => {
  const isSettled = userStoreService.useIsSettled();
  const isLogined = userStoreService.useIsLogined();
  useEffect(() => {
    if (isSettled && !isLogined) {
      useSpaceStore.getState().reset();
      useBotListFilterStore.getState().reset();
    }
  }, [isLogined, isSettled]);
};
