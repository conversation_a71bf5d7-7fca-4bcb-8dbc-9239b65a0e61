/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useLocation } from 'react-router-dom';
import { useEffect } from 'react';

import { logger } from '@coze-arch/logger';

export enum CustomPerfMarkNames {
  RouteChange = 'route_change',
}

export const useTrackRouteChange = () => {
  const location = useLocation();

  useEffect(() => {
    performance.mark(CustomPerfMarkNames.RouteChange, {
      detail: {
        location,
      },
    });
    logger.info({
      message: 'location change',
      meta: { location },
    });
  }, [location.pathname]);
};
