.flex-helper {
  /* stylelint-disable-next-line value-no-vendor-prefix */
  display: -webkit-flex;  /* 新版本语法: Chrome 21+ */
  display: flex;          /* 新版本语法: Opera 12.1, Firefox 22+ */
  display: -webkit-box;   /* 老版本语法: Safari, iOS, Android browser, older WebKit browsers. */
  display: -moz-box;      /* 老版本语法: Firefox (buggy) */
  /* stylelint-disable-next-line value-no-vendor-prefix */
  display: -ms-flexbox;   /* 混合版本语法: IE 10 */
}

.flex-1-helper {
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-flex: 1;        /* Chrome */
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -ms-flex: 1;             /* IE 10 */
  flex: 1;                /* NEW, Spec - Opera 12.1, Firefox 20+ */

  -webkit-box-flex: 1;     /* OLD - iOS 6-, Safari 3.1-6 */
  -moz-box-flex: 1;       /* OLD - Firefox 19- */
}

.flex-direction-row-helper {
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-flex-direction: row;
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -ms-flex-direction: row;
  flex-direction: row;

  -webkit-box-direction: normal;
  -moz-box-direction: normal;
  -webkit-box-orient: horizontal;
  -moz-box-orient: horizontal;
}

.flex-items-center {
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-align-items: center; /* Chrome 21+, Safari 6.1+, Opera 15+ */
  align-items: center;         /* 新语法 */

  -ms-flex-align: center;      /* IE 10 */
}

.flex-justify-center {
  /* stylelint-disable-next-line property-no-vendor-prefix */
  -webkit-justify-content: center; /* Chrome 21+, Safari 6.1+ */
  justify-content: center;   /* 新版浏览器 */

  -webkit-box-pack: center;  /* iOS 6-, Safari 3.1-6 */
  -moz-box-pack: center;     /* 早期版本的 Firefox */
  -ms-flex-pack: center;     /* IE 10 */
}

.banner-wrapper {
  width: 100%;
  min-height: 48px;
  padding: 0 64px;
  background: #E5B65C;
}

.banner-item {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #FFF;
  text-align: center;
}

.banner-upgrade-button {
  cursor: pointer;

  margin-left: 6px;

  font-weight: bold;
  text-decoration-line: underline;
  text-underline-offset: 4px;
}

.close {
  cursor: pointer;
  color: #FFF;
}
