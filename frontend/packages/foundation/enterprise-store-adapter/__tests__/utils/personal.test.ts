/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { describe, it, expect } from 'vitest';

import { isPersonalEnterprise } from '../../src/utils/personal';
import { PERSONAL_ENTERPRISE_ID } from '../../src/constants';

describe('isPersonalEnterprise', () => {
  it('should return true for personal enterprise id', () => {
    expect(isPersonalEnterprise(PERSONAL_ENTERPRISE_ID)).toBe(true);
  });

  it('should return false for non-personal enterprise id', () => {
    expect(isPersonalEnterprise('enterprise-1')).toBe(false);
  });
});
