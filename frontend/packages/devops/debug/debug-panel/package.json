{"name": "@coze-devops/debug-panel", "version": "0.0.1", "description": "coze debug panel", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "typings": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-devops/json-link-preview": "workspace:*", "immer": "^10.0.3", "json-bigint": "~1.0.0", "qs": "^6.11.2", "re-resizable": "~6.9.11", "react-json-view": "~1.21.3"}, "devDependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-devops/common-modules": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/json-bigint": "~1.0.4", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "copy-to-clipboard": "^3.3.3", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-devops/common-modules": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@douyinfe/semi-illustrations": "^2.36.0", "ahooks": "^3.7.8", "classnames": "^2.3.2", "dayjs": "^1.11.7", "lodash-es": "^4.17.21", "react": ">=18.2.0", "react-dom": ">=18.2.0", "zustand": "^4.4.7"}}