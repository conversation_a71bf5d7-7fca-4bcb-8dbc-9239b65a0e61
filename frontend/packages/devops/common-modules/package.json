{"name": "@coze-devops/common-modules", "version": "0.0.1", "private": true, "description": "common business modules for devops", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./query-trace": "./src/modules/query-trace/index.ts", "./tree": "./src/modules/query-trace/components/tree/index.tsx"}, "main": "src/index.tsx", "typesVersions": {"*": {"query-trace": ["./src/modules/query-trace/index.ts"], "tree": ["./src/modules/query-trace/components/tree/index.tsx"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@dagrejs/dagre": "^1.0.2", "@visactor/vgrammar": "0.12.5-alpha.4", "ahooks": "^3.7.8", "classnames": "^2.3.2", "dayjs": "^1.11.7", "lodash-es": "^4.17.21", "reactflow": "^11.8.2"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}