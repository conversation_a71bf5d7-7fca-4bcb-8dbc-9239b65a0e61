.wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;

  width: 640px;
  min-width: 640px;
  max-width: 640px;
  min-height: 480px;
  padding: 16px 24px 24px;

  background: var(--coz-bg-plus, #F9F9F9);
  border-radius: var(--default, 8px)
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  height: 32px;
  min-height: 32px;
}

.body {
  display: flex;
  flex: 1 0 0;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.footer {
  @apply flex items-center justify-end w-full;
}

.title {
  font-size: 20px;
  font-weight: 500;
  font-style: normal;
  line-height: 28px; /* 140% */
  color: var(--coz-fg-plus, rgba(6, 7, 9, 96%));
}

.error-txt {
  margin-top: 16px;

  font-size: 14px;
  font-weight: 400;
  font-style: normal;
  line-height: normal;
  color: var(--Light-usage-text---color-text-1, rgba(28, 31, 35, 80%));
}
