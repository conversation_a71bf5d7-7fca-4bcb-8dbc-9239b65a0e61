{"name": "@coze-devops/json-link-preview", "version": "0.0.1", "description": "预览json里面的链接", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.tsx", "unpkg": "./dist/umd/index.js", "module": "./dist/esm/index.js", "types": "./src/index.tsx", "files": ["dist", "README.md"], "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test --coverage"}, "dependencies": {"@coze-arch/bot-tea": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/pdfjs-shadow": "workspace:*", "@uiw/react-json-view": "~2.0.0-alpha.26", "classnames": "^2.3.2", "lodash-es": "^4.17.21"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@swc/core": "^1.3.35", "@swc/helpers": "^0.4.12", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.16", "less-loader": "~11.1.3", "postcss": "^8.4.32", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}