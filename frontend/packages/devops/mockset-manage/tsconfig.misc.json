{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "stories", "vitest.config.ts"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDir": "./", "outDir": "./dist", "jsx": "react-jsx", "types": ["react", "react-dom", "vitest/globals"], "strictNullChecks": true, "noImplicitAny": true, "paths": {"@/*": ["./src/*"]}}}