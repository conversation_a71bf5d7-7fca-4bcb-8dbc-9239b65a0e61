# @coze-devops/testset-manage

Testset manage for coze

## Overview

This package is part of the Coze Studio monorepo and provides library functionality. It includes component, hook, store.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-devops/testset-manage": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-devops/testset-manage';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component
- Hook
- Store

## API Reference

### Exports

- `TestsetSideSheet,
  type TestsetSideSheetProps,`
- `TestsetSelect,
  type TestsetSelectProps,`
- `getTestsetNameRules`
- `TestsetManageProvider`
- `useTestsetManageStore, useCheckSchema`
- `FormItemSchemaType,
  type NodeFormItem,
  type NodeFormSchema,
  type TestsetData,
  type TestsetDatabase,`
- `TestsetManageEventName`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript

- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
