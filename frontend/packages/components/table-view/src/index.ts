/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import './main.css';

export { TableView, type TableViewMethods } from './components/table-view';
export {
  TextR<PERSON>,
  EditHeaderRender,
  TagRender,
  ActionsRender,
  ImageRender,
} from './components/renders';

export {
  TableViewValue,
  TableViewColumns,
  TableViewRecord,
} from './components/types';

export { colWidthCacheService } from './components/table-view/service';
