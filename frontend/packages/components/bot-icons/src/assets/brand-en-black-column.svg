<svg width="116" height="116" viewBox="0 0 116 116" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="25.6167" y="2" width="64.3402" height="64.3402" rx="7.14891" fill="#4D53E8"/>
    <g filter="url(#filter0_ddiii_593_519)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M58.056 12.137C48.7545 12.137 41.2142 19.6804 41.2142 28.9858V36.0514H37.1707C32.5284 36.0514 31.4785 42.5639 35.8854 44.0239L41.2142 45.7893V48.8398C41.2142 51.855 44.386 53.8161 47.0817 52.4677L50.0332 50.9913C50.1699 50.9229 50.3353 50.9926 50.3819 51.1381C52.7746 58.6065 63.3374 58.6065 65.7301 51.1381C65.7767 50.9926 65.9421 50.9229 66.0788 50.9913L69.0303 52.4677C71.726 53.8161 74.8978 51.855 74.8978 48.8398V45.7893L80.2266 44.0239C84.6335 42.5639 83.5836 36.0514 78.9413 36.0514H74.8978V28.9858C74.8978 19.6804 67.3575 12.137 58.056 12.137Z" fill="white"/>
    </g>
    <g filter="url(#filter1_dd_593_519)">
        <path d="M63.7617 37.6806C63.7617 36.7801 64.4914 36.05 65.3916 36.05C66.2917 36.05 67.0214 36.7801 67.0214 37.6806C67.0214 38.5811 66.2917 39.3111 65.3916 39.3111C64.4914 39.3111 63.7617 38.5811 63.7617 37.6806Z" fill="url(#paint0_linear_593_519)"/>
    </g>
    <g filter="url(#filter2_dd_593_519)">
        <path d="M52.868 43.6842C52.2315 43.0475 52.2315 42.0151 52.868 41.3783C53.5045 40.7416 54.5365 40.7416 55.173 41.3783C56.7642 42.9702 59.3441 42.9702 60.9353 41.3783C61.5718 40.7416 62.6038 40.7416 63.2403 41.3783C63.8768 42.0151 63.8768 43.0475 63.2403 43.6842C60.3761 46.5497 55.7322 46.5497 52.868 43.6842Z" fill="url(#paint1_linear_593_519)"/>
    </g>
    <g filter="url(#filter3_dd_593_519)">
        <path d="M50.7216 34.4197C49.8215 34.4197 49.0918 35.1497 49.0918 36.0502V39.3113C49.0918 40.2118 49.8215 40.9418 50.7216 40.9418C51.6218 40.9418 52.3515 40.2118 52.3515 39.3113V36.0502C52.3515 35.1497 51.6218 34.4197 50.7216 34.4197Z" fill="url(#paint2_linear_593_519)"/>
    </g>
    <path d="M23.7098 110.032C23.8999 110.362 23.8266 110.782 23.5237 111.012C21.0554 112.885 17.9566 114 14.5917 114C6.53295 114 0 107.604 0 99.7151C0 91.8258 6.53295 85.4303 14.5917 85.4303C18.0799 85.4303 21.2821 86.6285 23.7925 88.6273C24.0848 88.8601 24.152 89.2726 23.9651 89.5962L21.8307 93.293C21.5471 93.7843 20.8459 93.8206 20.4546 93.4099C18.9479 91.8282 16.8772 90.8517 14.5917 90.8517C9.98452 90.8517 6.24962 94.82 6.24962 99.7151C6.24962 104.61 9.98452 108.578 14.5917 108.578C16.7732 108.578 18.7592 107.689 20.2456 106.232C20.6414 105.845 21.3194 105.892 21.5964 106.372L23.7098 110.032Z" fill="#1E1E1E"/>
    <path d="M61.6971 87.1475C61.6971 86.7161 62.0469 86.3663 62.4783 86.3663H84.1805C84.6119 86.3663 84.9617 86.7161 84.9617 87.1475V90.7745V90.7745C84.9607 90.7745 84.9597 90.775 84.959 90.7757L68.9915 108.114H84.1618C84.5932 108.114 84.943 108.464 84.943 108.896V112.355C84.943 112.787 84.5932 113.136 84.1618 113.136H61.9882C61.5568 113.136 61.207 112.787 61.207 112.355V109.061C61.207 108.865 61.2808 108.676 61.4136 108.532L77.2007 91.3883H62.4783C62.0469 91.3883 61.6971 91.0386 61.6971 90.6071V87.1475Z" fill="#1E1E1E"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M42.5224 113.974C50.5812 113.974 57.1142 107.578 57.1142 99.6888C57.1142 91.7995 50.5812 85.4039 42.5224 85.4039C34.4636 85.4039 27.9307 91.7995 27.9307 99.6888C27.9307 107.578 34.4636 113.974 42.5224 113.974ZM34.1817 99.6885C34.1817 104.584 37.9166 108.552 42.5239 108.552C47.1311 108.552 50.866 104.584 50.866 99.6885C50.866 94.7934 47.1311 90.8252 42.5239 90.8252C37.9166 90.8252 34.1817 94.7934 34.1817 99.6885Z" fill="#1E1E1E"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M89.1108 99.7924C89.1108 108.233 95.2212 113.981 103.144 113.981C107.852 113.981 111.287 112.206 113.831 109.425C114.116 109.113 114.078 108.632 113.762 108.352L111.196 106.075C110.89 105.803 110.426 105.816 110.122 106.09C108.118 107.893 106.054 108.854 103.247 108.854C99.2084 108.854 96.0497 106.369 95.3765 101.915H114.749C115.156 101.915 115.492 101.604 115.523 101.199C115.531 101.085 115.537 101.005 115.537 101.005C115.572 100.666 115.572 100.38 115.572 100.155C115.572 92.2839 111.17 85.4486 102.471 85.4486C94.6516 85.4486 89.1108 91.8696 89.1108 99.6888V99.7924ZM95.4836 96.8618H109.228C108.569 93.2687 106.244 90.5751 102.419 90.5751C98.8676 90.5751 96.2947 93.1104 95.4836 96.8618Z" fill="#1E1E1E"/>
    <defs>
        <filter id="filter0_ddiii_593_519" x="30.9339" y="9.99229" width="54.2439" height="49.9642" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="1.07234"/>
            <feGaussianBlur stdDeviation="1.07234"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.165547 0 0 0 0 0.177065 0 0 0 0 0.463086 0 0 0 0.2 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_593_519"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="0.536168"/>
            <feGaussianBlur stdDeviation="0.536168"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.164706 0 0 0 0 0.176471 0 0 0 0 0.462745 0 0 0 0.1 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_593_519" result="effect2_dropShadow_593_519"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_593_519" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="2.14467"/>
            <feGaussianBlur stdDeviation="2.14467"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.301961 0 0 0 0 0.32549 0 0 0 0 0.909804 0 0 0 0.1 0"/>
            <feBlend mode="normal" in2="shape" result="effect3_innerShadow_593_519"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-2.14467"/>
            <feGaussianBlur stdDeviation="2.14467"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.301961 0 0 0 0 0.32549 0 0 0 0 0.909804 0 0 0 0.15 0"/>
            <feBlend mode="normal" in2="effect3_innerShadow_593_519" result="effect4_innerShadow_593_519"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-2.14467"/>
            <feGaussianBlur stdDeviation="1.6085"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.301961 0 0 0 0 0.32549 0 0 0 0 0.909804 0 0 0 0.15 0"/>
            <feBlend mode="normal" in2="effect4_innerShadow_593_519" result="effect5_innerShadow_593_519"/>
        </filter>
        <filter id="filter1_dd_593_519" x="62.1532" y="34.7096" width="6.47677" height="7.01428" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="0.804252"/>
            <feGaussianBlur stdDeviation="0.804252"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_593_519"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-0.268084"/>
            <feGaussianBlur stdDeviation="0.536168"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.301961 0 0 0 0 0.32549 0 0 0 0 0.909804 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_593_519" result="effect2_dropShadow_593_519"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_593_519" result="shape"/>
        </filter>
        <filter id="filter2_dd_593_519" x="50.7821" y="39.5603" width="14.5442" height="8.68579" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="0.804252"/>
            <feGaussianBlur stdDeviation="0.804252"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_593_519"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-0.268084"/>
            <feGaussianBlur stdDeviation="0.536168"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.301961 0 0 0 0 0.32549 0 0 0 0 0.909804 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_593_519" result="effect2_dropShadow_593_519"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_593_519" result="shape"/>
        </filter>
        <filter id="filter3_dd_593_519" x="47.4833" y="33.0793" width="6.47677" height="10.2753" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="0.804252"/>
            <feGaussianBlur stdDeviation="0.804252"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_593_519"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="-0.268084"/>
            <feGaussianBlur stdDeviation="0.536168"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.301961 0 0 0 0 0.32549 0 0 0 0 0.909804 0 0 0 0.4 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow_593_519" result="effect2_dropShadow_593_519"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_593_519" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_593_519" x1="65.4872" y1="36.5955" x2="65.2318" y2="47.0445" gradientUnits="userSpaceOnUse">
            <stop stop-color="#272DCC"/>
            <stop offset="1" stop-color="#9A9DF2"/>
        </linearGradient>
        <linearGradient id="paint1_linear_593_519" x1="57.5177" y1="42.4807" x2="56.4454" y2="46.502" gradientUnits="userSpaceOnUse">
            <stop stop-color="#2B33E6"/>
            <stop offset="1" stop-color="#A19AF2"/>
        </linearGradient>
        <linearGradient id="paint2_linear_593_519" x1="50.8173" y1="35.5105" x2="49.7974" y2="56.3712" gradientUnits="userSpaceOnUse">
            <stop stop-color="#272DCC"/>
            <stop offset="1" stop-color="#9A9DF2"/>
        </linearGradient>
    </defs>
</svg>
