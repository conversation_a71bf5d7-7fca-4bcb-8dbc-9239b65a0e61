/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/* eslint-disable max-lines */
import { IconFactory } from './factory';
import { ReactComponent as SvtPlaygroundIcon } from './assets/playground-icon.svg';
import { ReactComponent as SvtPlaygroundIconFill } from './assets/playground-icon-fill.svg';
import { ReactComponent as SvgMarIcon } from './assets/mar.svg';
import { ReactComponent as SvgMarColorIcon } from './assets/mar-c.svg';
import { ReactComponent as SvgLocalPluginIcon } from './assets/localIcon.svg';
import { ReactComponent as SvgWeixinIcon } from './assets/icons/third-party-icons/weixin.svg';
import { ReactComponent as SvgTextFileIcon } from './assets/icons/third-party-icons/text-file.svg';
import { ReactComponent as SvgSheetFileIcon } from './assets/icons/third-party-icons/sheet-file.svg';
import { ReactComponent as SvgNotionIcon } from './assets/icons/third-party-icons/notion.svg';
import { ReactComponent as SvgGoogleIcon } from './assets/icons/third-party-icons/google.svg';
import { ReactComponent as SvgFolderIcon } from './assets/icons/third-party-icons/folder.svg';
import { ReactComponent as SvgFeishuIcon } from './assets/icons/third-party-icons/feishu.svg';
import { ReactComponent as SvgStoreMenuWorkflow } from './assets/icons/store-menu-workflow.svg';
import { ReactComponent as SvgStoreMenuWorkflowSelected } from './assets/icons/store-menu-workflow-selected.svg';
import { ReactComponent as SvgStoreIcon } from './assets/icons/store-icon.svg';
import { ReactComponent as SvgSpanWorkflow } from './assets/icons/span-type/icon-workflow.svg';
import { ReactComponent as SvgSpanWorkflowStart } from './assets/icons/span-type/icon-workflow-start.svg';
import { ReactComponent as SvgSpanWorkflowEnd } from './assets/icons/span-type/icon-workflow-end.svg';
import { ReactComponent as SvgSpanVar } from './assets/icons/span-type/icon-var.svg';
import { ReactComponent as SvgSpanUnknown } from './assets/icons/span-type/icon-unknown.svg';
import { ReactComponent as SvgSpanPluginTool } from './assets/icons/span-type/icon-plugin-tool.svg';
import { ReactComponent as SvgSpanNodeDamaged } from './assets/icons/span-type/icon-node-damaged.svg';
import { ReactComponent as SvgSpanMessage } from './assets/icons/span-type/icon-message.svg';
import { ReactComponent as SvgSpanLLMCall } from './assets/icons/span-type/icon-llm-call.svg';
import { ReactComponent as SvgSpanKnowledge } from './assets/icons/span-type/icon-knowledge.svg';
import { ReactComponent as SvgSpanHook } from './assets/icons/span-type/icon-hook.svg';
import { ReactComponent as SvgSpanDatabase } from './assets/icons/span-type/icon-database.svg';
import { ReactComponent as SvgSpanCondition } from './assets/icons/span-type/icon-condition.svg';
import { ReactComponent as SvgSpanCode } from './assets/icons/span-type/icon-code.svg';
import { ReactComponent as SvgSpanCard } from './assets/icons/span-type/icon-card.svg';
import { ReactComponent as SvgSpanBot } from './assets/icons/span-type/icon-bot.svg';
import { ReactComponent as SvgSpanBMParallel } from './assets/icons/span-type/icon-bm-parallel.svg';
import { ReactComponent as SvgSpanBMConnector } from './assets/icons/span-type/icon-bm-connector.svg';
import { ReactComponent as SvgSpanBMBatch } from './assets/icons/span-type/icon-bm-batch.svg';
import { ReactComponent as SvgSpanAgent } from './assets/icons/span-type/icon-agent.svg';
import { ReactComponent as SvgTemplateShortcutTransparency } from './assets/icons/shortcut-icons/top_panel_close_transparency.svg';
import { ReactComponent as SvgLoadMoreShortcuts } from './assets/icons/shortcut-icons/icon_more.svg';
import { ReactComponent as SvgShortcutTrash } from './assets/icons/shortcut-icons/icon-trash.svg';
import { ReactComponent as SvgTemplateShortcut } from './assets/icons/shortcut-icons/icon-template-shortcut.svg';
import { ReactComponent as SvgShortcutEdit } from './assets/icons/shortcut-icons/icon-edit.svg';
import { ReactComponent as SvgShortcutDrag } from './assets/icons/shortcut-icons/icon-drag.svg';
import { ReactComponent as SvgShortcutDisorder } from './assets/icons/shortcut-icons/icon-disorder.svg';
import { ReactComponent as SvgShortcutComponentTag } from './assets/icons/shortcut-icons/icon-comp-tag.svg';
import { ReactComponent as SvgTemplateShortcutClose } from './assets/icons/shortcut-icons/icon-close.svg';
import { ReactComponent as SvgSceneListEmptyIcon } from './assets/icons/scene-list-empty.svg';
import { ReactComponent as SvgExampleNormalIcon } from './assets/icons/plugin-example/icon-example-normal.svg';
import { ReactComponent as SvgExampleNoneIcon } from './assets/icons/plugin-example/icon-example-none.svg';
import { ReactComponent as SvgExampleInvalidIcon } from './assets/icons/plugin-example/icon-example-invalid.svg';
import { ReactComponent as MenuCollapse } from './assets/icons/menu-collapse.svg';
import { ReactComponent as SvgValcanoColored } from './assets/icons/logo-volcano-colored.svg';
import { ReactComponent as SvgLargeEmptyIcon } from './assets/icons/large-empty.svg';
import { ReactComponent as SvgKnowledgeTableAvatar } from './assets/icons/knowledge/table-type-avatar.svg';
import { ReactComponent as SvgKnowledgeImgAvatar } from './assets/icons/knowledge/img-type-avatar.svg';
import { ReactComponent as SvgUpdateFrequency } from './assets/icons/knowledge/icon_forward-15s_outlined.svg';
import { ReactComponent as SvgKnowledgeEdit } from './assets/icons/knowledge/icon_edit_round_outlined.svg';
import { ReactComponent as SvgArrowKnowledgeIcon } from './assets/icons/knowledge/icon_arrow-right_fill.svg';
import { ReactComponent as SvgViewContentOutlinedIcon } from './assets/icons/knowledge/icon-view-content-outlined.svg';
import { ReactComponent as SvgUpdateTableConfig } from './assets/icons/knowledge/icon-update-table-config.svg';
import { ReactComponent as SvgKnowledgeSetting } from './assets/icons/knowledge/icon-knwoledge-setting.svg';
import { ReactComponent as SvgKnowledgeSettingOutlinedIcon } from './assets/icons/knowledge/icon-knowledge-setting-outlined.svg';
import { ReactComponent as SvgDragOutlined } from './assets/icons/knowledge/icon-drag-outlined.svg';
import { ReactComponent as SvgDocResegment } from './assets/icons/knowledge/icon-doc-resegment.svg';
import { ReactComponent as SvgDocRefreshOutlinedIcon } from './assets/icons/knowledge/icon-doc-refresh-outlined.svg';
import { ReactComponent as SvgDocLinkOutlinedIcon } from './assets/icons/knowledge/icon-doc-link-outlined.svg';
import { ReactComponent as SvgAddIcon } from './assets/icons/knowledge/icon-add-middle-outlined.svg';
import { ReactComponent as SvgKnowledgeDocAvatar } from './assets/icons/knowledge/document-type-avatar.svg';
import { ReactComponent as SvgXColorful } from './assets/icons/icon_x_colorful.svg';
import { ReactComponent as SvgWeiboColorful } from './assets/icons/icon_weibo_colorful.svg';
import { ReactComponent as SvgWeChatColorful } from './assets/icons/icon_wechat_colorful.svg';
import { ReactComponent as SvgWarningSize24 } from './assets/icons/icon_warning_size_24.svg';
import { ReactComponent as SvgWaringOrangeIcon } from './assets/icons/icon_warning_colorful-dm.svg';
import { ReactComponent as SvgWarning } from './assets/icons/icon_warning.svg';
import { ReactComponent as SvgWaringRedIcon } from './assets/icons/icon_warning-red_colorful-dm.svg';
import { ReactComponent as SvgViewinchatOutlined } from './assets/icons/icon_viewinchat_outlined.svg';
import { ReactComponent as SvgUpOutlinedBold } from './assets/icons/icon_up-bold_outlined.svg';
import { ReactComponent as SvgTrashCan } from './assets/icons/icon_trash_can.svg';
import { ReactComponent as SvgTimeOutlinedIcon } from './assets/icons/icon_time_outlined.svg';
import { ReactComponent as SvgThumbupOutlined } from './assets/icons/icon_thumbsup_outlined.svg';
import { ReactComponent as SvgIconThumbupFilled } from './assets/icons/icon_thumbsup_filled.svg';
import { ReactComponent as SvgThumbdownOutlined } from './assets/icons/icon_thumbdown_outlined.svg';
import { ReactComponent as SvgIconThumbdownFilled } from './assets/icons/icon_thumbdown_filled.svg';
import { ReactComponent as SvgSucceed } from './assets/icons/icon_succeed.svg';
import { ReactComponent as SvgStrikethrough } from './assets/icons/icon_strikethrough.svg';
import { ReactComponent as SvgAiStopGenIcon } from './assets/icons/icon_stop_ai_gen.svg';
import { ReactComponent as SvgSpeakerOutlined } from './assets/icons/icon_speaker_outlined.svg';
import { ReactComponent as SVGIconSideFoldOutlined } from './assets/icons/icon_side-fold_outlined.svg';
import { ReactComponent as SvgShareIcon } from './assets/icons/icon_share_outlined.svg';
import { ReactComponent as SvgHotIconSettingOutlined } from './assets/icons/icon_setting_outlined.svg';
import { ReactComponent as SvgSelectorIcon } from './assets/icons/icon_selector.svg';
import { ReactComponent as SvgSelectArrowIcon } from './assets/icons/icon_select_arrow.svg';
import { ReactComponent as SvgReplyOutlined } from './assets/icons/icon_reply_outlined.svg';
import { ReactComponent as SvgReplyCnOutlined } from './assets/icons/icon_reply-cn_outlined.svg';
import { ReactComponent as SvgRedditColorful } from './assets/icons/icon_reddit_colorful.svg';
import { ReactComponent as SvgReadInfoOutlined } from './assets/icons/icon_readinfo_outlined.svg';
import { ReactComponent as SvgQzoneColorful } from './assets/icons/icon_qzone_colorful.svg';
import { ReactComponent as SvgQuotation } from './assets/icons/icon_quotation.svg';
import { ReactComponent as SvgPlatformOutlined } from './assets/icons/icon_platform_outlined.svg';
import { ReactComponent as SvgPinOutlined } from './assets/icons/icon_pin_outlined.svg';
import { ReactComponent as SvgPhoneIcon } from './assets/icons/icon_phone.svg';
import { ReactComponent as SvgPCIcon } from './assets/icons/icon_pc.svg';
import { ReactComponent as SvgDatabaseTabIcon } from './assets/icons/icon_pa-cost_outlined.svg';
import { ReactComponent as SvgNewConversation } from './assets/icons/icon_new_conversation.svg';
import { ReactComponent as SvgLtmEditIcon } from './assets/icons/icon_more-round_outlined.svg';
import { ReactComponent as SvgMinimizeOutlined } from './assets/icons/icon_minimize_outlined.svg';
import { ReactComponent as SvgMemoryDownMenuIcon } from './assets/icons/icon_memory_down_menu.svg';
import { ReactComponent as SvgMemberOutlinedIcon } from './assets/icons/icon_member_outlined.svg';
import { ReactComponent as SvgLtmTabIcon } from './assets/icons/icon_ltm_tab.svg';
import { ReactComponent as SvgLongArrowUp } from './assets/icons/icon_long_arrow_up.svg';
import { ReactComponent as SvgLogOutIcon } from './assets/icons/icon_logout_outlined.svg';
import { ReactComponent as SvgLoading } from './assets/icons/icon_loading.svg';
import { ReactComponent as SvglistOrder } from './assets/icons/icon_list_order.svg';
import { ReactComponent as SvgListDisorder } from './assets/icons/icon_list_disorder.svg';
import { ReactComponent as SvgListFilter } from './assets/icons/icon_list-filter_outlined.svg';
import { ReactComponent as SvgLink } from './assets/icons/icon_link.svg';
import { ReactComponent as SvgFileLinkOutlined } from './assets/icons/icon_link-copy_outlined.svg';
import { ReactComponent as SvgLegacyIcon } from './assets/icons/icon_legacy.svg';
import { ReactComponent as SvgJuejinColorful } from './assets/icons/icon_juejin_colorful.svg';
import { ReactComponent as SvgItalic } from './assets/icons/icon_italic.svg';
import { ReactComponent as SvgIntelligentIcon } from './assets/icons/icon_intelligent_assistant_filled.svg';
import { ReactComponent as SvgStatusInfo } from './assets/icons/icon_info.svg';
import { ReactComponent as SvgImageColorful } from './assets/icons/icon_image_colorful.svg';
import { ReactComponent as SvgImage } from './assets/icons/icon_image.svg';
import { ReactComponent as SvgCopyLinkIcon } from './assets/icons/icon_hyperlink_outlined.svg';
import { ReactComponent as SvgHn } from './assets/icons/icon_hn.svg';
import { ReactComponent as SvgH3 } from './assets/icons/icon_h3.svg';
import { ReactComponent as SvgH2 } from './assets/icons/icon_h2.svg';
import { ReactComponent as SvgH1 } from './assets/icons/icon_h1.svg';
import { ReactComponent as SvgModelConfig } from './assets/icons/icon_ganttset_outlined.svg';
import { ReactComponent as SvgExpandOutlined } from './assets/icons/icon_expand_outlined.svg';
import { ReactComponent as SvgDownArrowBlack } from './assets/icons/icon_expand-up_filled.svg';
import { ReactComponent as SvgExitIcon } from './assets/icons/icon_exit.svg';
import { ReactComponent as SvgEmptyIcon } from './assets/icons/icon_empty.svg';
import { ReactComponent as SvgEmojiOutlined } from './assets/icons/icon_emoji_outlined.svg';
import { ReactComponent as SvgEffectsFilled } from './assets/icons/icon_effects_filled.svg';
import { ReactComponent as SvgDiscussOutlined } from './assets/icons/icon_discuss_outlined.svg';
import { ReactComponent as SvgDateListPickerIcon } from './assets/icons/icon_date_list_picker.svg';
import { ReactComponent as SvgVariableTabIcon } from './assets/icons/icon_data-sheet_outlined.svg';
import { ReactComponent as SvgCozeTxtEn } from './assets/icons/icon_coze_txt_en.svg';
import { ReactComponent as SvgCozeTxtCn } from './assets/icons/icon_coze_txt_cn.svg';
import { ReactComponent as SvgCozeLogo } from './assets/icons/icon_coze_logo.svg';
import { ReactComponent as SvgConversionOutlined } from './assets/icons/icon_conversion_outlined.svg';
import { ReactComponent as SvgConnect } from './assets/icons/icon_connect.svg';
import { ReactComponent as SvgCommunityTabOutlined } from './assets/icons/icon_community-tab_outlined.svg';
import { ReactComponent as SvgCollectionOutlinedIcon } from './assets/icons/icon_collection_outlined.svg';
import { ReactComponent as SvgCloseKnowledge } from './assets/icons/icon_close_knowledge.svg';
import { ReactComponent as SvgChatHashtag } from './assets/icons/icon_chat_hashtag.svg';
import { ReactComponent as SvgIconCcmNextOutlined } from './assets/icons/icon_ccm-next_outlined.svg';
import { ReactComponent as SvgBrace } from './assets/icons/icon_brace.svg';
import { ReactComponent as SvgBotstoreMemoryIcon } from './assets/icons/icon_bot_store_memory.svg';
import { ReactComponent as SvgBold } from './assets/icons/icon_bold.svg';
import { ReactComponent as SvgBitableFormOutlined } from './assets/icons/icon_bitableform_outlined.svg';
import { ReactComponent as SvgAvatarEditMask } from './assets/icons/icon_avatar_edit_mask.svg';
import { ReactComponent as SvgIconAtOutlined } from './assets/icons/icon_at_outlined.svg';
import { ReactComponent as SvgArrowDownFill } from './assets/icons/icon_arrow_down_fill.svg';
import { ReactComponent as SvgApiOutlined } from './assets/icons/icon_api_outlined.svg';
import { ReactComponent as SvgAddDesktopIcon } from './assets/icons/icon_add_desktop.svg';
import { ReactComponent as SvgAddEntryTopOutlined } from './assets/icons/icon_add-entry_top_outlined.svg';
import { ReactComponent as SvgAddEntryBottomOutlined } from './assets/icons/icon_add-entry_bottom_outlined.svg';
import { ReactComponent as SvgYesFilled } from './assets/icons/icon-yes-filled.svg';
import { ReactComponent as SvgWorkflows } from './assets/icons/icon-workflows.svg';
import { ReactComponent as SvgWorkflowsSelected } from './assets/icons/icon-workflows-selected.svg';
import { ReactComponent as SvgWorkflowRunning } from './assets/icons/icon-workflow-running.svg';
import { ReactComponent as SvgWorkflowRunSuccess } from './assets/icons/icon-workflow-run-success.svg';
import { ReactComponent as SvgWorkflowRunResultClose } from './assets/icons/icon-workflow-run-result-close.svg';
import { ReactComponent as SvgWorkflowRunFail } from './assets/icons/icon-workflow-run-fail.svg';
import { ReactComponent as SvgWorkflowCardMore } from './assets/icons/icon-workflow-card-more.svg';
import { ReactComponent as SvgWithdraw } from './assets/icons/icon-withdraw.svg';
import { ReactComponent as SvgWidgetUnpublished } from './assets/icons/icon-widget-unpublished.svg';
import { ReactComponent as SvgWidgetPublished } from './assets/icons/icon-widget-published.svg';
import { ReactComponent as SvgWarningInfoIcon } from './assets/icons/icon-warning.svg';
import { ReactComponent as SvgWarningStrongInfoIcon } from './assets/icons/icon-warning-strong.svg';
import { ReactComponent as SvgVisible } from './assets/icons/icon-visible.svg';
import { ReactComponent as SvgViewList } from './assets/icons/icon-view-list.svg';
import { ReactComponent as SvgUsePlugin } from './assets/icons/icon-use-plugin.svg';
import { ReactComponent as SvgUpload } from './assets/icons/icon-upload.svg';
import { ReactComponent as SvgUploadOutlined1 } from './assets/icons/icon-upload-outlined1.svg';
import { ReactComponent as SvgUploadOutlined } from './assets/icons/icon-upload-outlined.svg';
import { ReactComponent as SvgUploadOutlinedUp } from './assets/icons/icon-upload-outlined-up.svg';
import { ReactComponent as SvgUploadFileSuccess } from './assets/icons/icon-upload-file-success.svg';
import { ReactComponent as SvgUploadFileFail } from './assets/icons/icon-upload-file-fail.svg';
import { ReactComponent as SvgUploadCompletedIcon } from './assets/icons/icon-upload-completed.svg';
import { ReactComponent as SvgIconUpdateOutlined } from './assets/icons/icon-update-outlined.svg';
import { ReactComponent as SvgUpOutlined } from './assets/icons/icon-up-outlined.svg';
import { ReactComponent as SvgUpBoldIcon } from './assets/icons/icon-up-bold-outlined.svg';
import { ReactComponent as SvgUnitEditIcon } from './assets/icons/icon-unit-edit.svg';
import { ReactComponent as SvgUnBoundIcon } from './assets/icons/icon-unbound-group.svg';
import { ReactComponent as SvgTokenIcon } from './assets/icons/icon-token.svg';
import { ReactComponent as SvgTokenIconSelected } from './assets/icons/icon-token-selected.svg';
import { ReactComponent as SvgToastWarning } from './assets/icons/icon-toast-warning.svg';
import { ReactComponent as SvgToastSuccess } from './assets/icons/icon-toast-success.svg';
import { ReactComponent as SvgToastInfo } from './assets/icons/icon-toast-info.svg';
import { ReactComponent as SvgToastError } from './assets/icons/icon-toast-error.svg';
import { ReactComponent as SvgTime } from './assets/icons/icon-time.svg';
import { ReactComponent as SvgTimeCapsule } from './assets/icons/icon-time-capsule.svg';
import { ReactComponent as SvgTickStroked } from './assets/icons/icon-tick-stroked.svg';
import { ReactComponent as SvgThumbsUpMessage } from './assets/icons/icon-thumbs-up-message.svg';
import { ReactComponent as SvgThumbsUpMessageSelected } from './assets/icons/icon-thumbs-up-message-selected.svg';
import { ReactComponent as SvgTextFormat } from './assets/icons/icon-text-format.svg';
import { ReactComponent as SvgTextFormatActive } from './assets/icons/icon-text-format-active.svg';
import { ReactComponent as SvgTemplate } from './assets/icons/icon-template.svg';
import { ReactComponent as SvgTeam } from './assets/icons/icon-team.svg';
import { ReactComponent as SvgTeamTools } from './assets/icons/icon-team-tools.svg';
import { ReactComponent as SvgTeamTag } from './assets/icons/icon-team-tag.svg';
import { ReactComponent as SvgTeamSelected } from './assets/icons/icon-team-selected.svg';
import { ReactComponent as SvgTeamFilled } from './assets/icons/icon-team-filled.svg';
import { ReactComponent as SvgTeamDefault } from './assets/icons/icon-team-default.svg';
import { ReactComponent as SvgTeamAddOutlined } from './assets/icons/icon-team-add-outlined.svg';
import { ReactComponent as SvgTasklistOutlined } from './assets/icons/icon-tasklist-outlined.svg';
import { ReactComponent as SvgTaskTime } from './assets/icons/icon-task-time.svg';
import { ReactComponent as SvgTask } from './assets/icons/icon-task-outlined.svg';
import { ReactComponent as SvgTaskEvent } from './assets/icons/icon-task-event.svg';
import { ReactComponent as SvgTable } from './assets/icons/icon-table.svg';
import { ReactComponent as SvgTableFormat } from './assets/icons/icon-table-format.svg';
import { ReactComponent as SvgTableFormatActive } from './assets/icons/icon-table-format-active.svg';
import { ReactComponent as SvgTabDownOutlined } from './assets/icons/icon-tab-down-outlined.svg';
import { ReactComponent as SvgSuccessIcon } from './assets/icons/icon-success.svg';
import { ReactComponent as SvgStyleSet } from './assets/icons/icon-style-set.svg';
import { ReactComponent as SvgStoreListMoreIcon } from './assets/icons/icon-store-list-more.svg';
import { ReactComponent as SvgStopOutlined } from './assets/icons/icon-stop-outlined.svg';
import { ReactComponent as SvgStandardRefresh } from './assets/icons/icon-standard-refresh.svg';
import { ReactComponent as SvgStandardCopy } from './assets/icons/icon-standard-copy.svg';
import { ReactComponent as SvgSpaceDownOutlined } from './assets/icons/icon-space-down-outlined.svg';
import { ReactComponent as SvgSortDescendIcon } from './assets/icons/icon-sort-descend.svg';
import { ReactComponent as SvgSortDefaultIcon } from './assets/icons/icon-sort-default.svg';
import { ReactComponent as SvgSortAscendIcon } from './assets/icons/icon-sort-ascend.svg';
import { ReactComponent as SvgSmoothline } from './assets/icons/icon-smoothline.svg';
import { ReactComponent as SvgSlash } from './assets/icons/icon-slash.svg';
import { ReactComponent as SvgSingleMode } from './assets/icons/icon-single-mode.svg';
import { ReactComponent as SvgSetting } from './assets/icons/icon-setting.svg';
import { ReactComponent as SvgSettingStroked } from './assets/icons/icon-setting-stroked.svg';
import { ReactComponent as SvgSend } from './assets/icons/icon-send.svg';
import { ReactComponent as SvgSegmentEmptyIcon } from './assets/icons/icon-segment-empty.svg';
import { ReactComponent as SvgSearch } from './assets/icons/icon-search.svg';
import { ReactComponent as SvgSearchInput } from './assets/icons/icon-search-input.svg';
import { ReactComponent as SvgRobot } from './assets/icons/icon-robot.svg';
import { ReactComponent as SvgRightArrow } from './assets/icons/icon-right-arrow.svg';
import { ReactComponent as SvgReportIcon } from './assets/icons/icon-report.svg';
import { ReactComponent as SvgReplace } from './assets/icons/icon-replace.svg';
import { ReactComponent as SvgRefresh } from './assets/icons/icon-refresh.svg';
import { ReactComponent as SvgRefreshOutlined1 } from './assets/icons/icon-refresh-outlined1.svg';
import { ReactComponent as SvgRefreshOutlined } from './assets/icons/icon-refresh-outlined.svg';
import { ReactComponent as SvgRefreshOutlinedNormalized } from './assets/icons/icon-refresh-outlined-normalized.svg';
import { ReactComponent as SvgQueryEmpty } from './assets/icons/icon-query-empty.svg';
import { ReactComponent as SvgPullDown } from './assets/icons/icon-pull-down.svg';
import { ReactComponent as SvgProcessPlugin } from './assets/icons/icon-process-plugin.svg';
import { ReactComponent as SvgProcessJump } from './assets/icons/icon-process-jump.svg';
import { ReactComponent as SvgProcessDataSet } from './assets/icons/icon-process-data-set.svg';
import { ReactComponent as SvgPost } from './assets/icons/icon-post.svg';
import { ReactComponent as SvgPositionFilled } from './assets/icons/icon-position-filled.svg';
import { ReactComponent as SvgPolyline } from './assets/icons/icon-polyline.svg';
import { ReactComponent as SvgPlus } from './assets/icons/icon-plus.svg';
import { ReactComponent as SvgPlugins } from './assets/icons/icon-plugins.svg';
import { ReactComponent as SvgPluginsSelected } from './assets/icons/icon-plugins-selected.svg';
import { ReactComponent as SvgPlugin } from './assets/icons/icon-plugin.svg';
import { ReactComponent as SvgPluginShop } from './assets/icons/icon-plugin-shop.svg';
import { ReactComponent as SvgPlayground } from './assets/icons/icon-playground.svg';
import { ReactComponent as SvgPlayOutline } from './assets/icons/icon-play-round-outlined.svg';
import { ReactComponent as SvgPlaceholderImg } from './assets/icons/icon-placeholder-img.svg';
import { ReactComponent as SvgPerson } from './assets/icons/icon-person.svg';
import { ReactComponent as SvgPersonSelected } from './assets/icons/icon-person-selected.svg';
import { ReactComponent as SvgPersonFilled } from './assets/icons/icon-person-filled.svg';
import { ReactComponent as SvgParagraphCopy } from './assets/icons/icon-paragraph-copy.svg';
import { ReactComponent as SvgPaLogOutlined } from './assets/icons/icon-pa-log-outlined.svg';
import { ReactComponent as SvgOpenTranslateIcon } from './assets/icons/icon-open-translate.svg';
import { ReactComponent as SvgOpenInDefault } from './assets/icons/icon-open-in-default.svg';
import { ReactComponent as SvgOpenAPIIcon } from './assets/icons/icon-open-api.svg';
import { ReactComponent as SvgOfficialLabel } from './assets/icons/icon-official-label.svg';
import { ReactComponent as SvgNote } from './assets/icons/icon-note-filled.svg';
import { ReactComponent as SvgNo } from './assets/icons/icon-no.svg';
import { ReactComponent as SvgNoFilledIcon } from './assets/icons/icon-no-filled.svg';
import { ReactComponent as SvgNavBarBack } from './assets/icons/icon-navbar-back.svg';
import { ReactComponent as SvgNationalEmblem } from './assets/icons/icon-national-emblem.svg';
import { ReactComponent as SvgMyTools } from './assets/icons/icon-my-tools.svg';
import { ReactComponent as SvgMyPlugin } from './assets/icons/icon-my-plugin.svg';
import { ReactComponent as SvgMultipleMode } from './assets/icons/icon-multiple-mode.svg';
import { ReactComponent as SvgBotMultiModelBtn } from './assets/icons/icon-multi-model-btn.svg';
import { ReactComponent as SvgMore } from './assets/icons/icon-more.svg';
import { ReactComponent as SvgMoreVertical } from './assets/icons/icon-more-vertical.svg';
import { ReactComponent as SvgMoreRound } from './assets/icons/icon-more-round-outlined.svg';
import { ReactComponent as SvgMoreOperationsMessage } from './assets/icons/icon-more-operations-message.svg';
import { ReactComponent as SvgMoreOperationsDeleteMessage } from './assets/icons/icon-more-operations-delete-message.svg';
import { ReactComponent as SvgMoreAdd } from './assets/icons/icon-more-add.svg';
import { ReactComponent as SvgAddMoreNew } from './assets/icons/icon-more-add-new.svg';
import { ReactComponent as SvgModify } from './assets/icons/icon-modify.svg';
import { ReactComponent as SvgModalErrorIcon } from './assets/icons/icon-modal-error.svg';
import { ReactComponent as SvgIconMobileSound } from './assets/icons/icon-mobile-sound.svg';
import { ReactComponent as SvgIconMobileSoundNormal } from './assets/icons/icon-mobile-sound-normal.svg';
import { ReactComponent as SvgIconMobileSoundDisable } from './assets/icons/icon-mobile-sound-disable.svg';
import { ReactComponent as SvgIconMobileSoundClosed } from './assets/icons/icon-mobile-sound-closed.svg';
import { ReactComponent as SVGIconMobileShare } from './assets/icons/icon-mobile-share.svg';
import { ReactComponent as SvgIconMobileSetting } from './assets/icons/icon-mobile-setting.svg';
import { ReactComponent as SvgIconMobileMore } from './assets/icons/icon-mobile-more.svg';
import { ReactComponent as SvgIconMobileMenu } from './assets/icons/icon-mobile-menu.svg';
import { ReactComponent as SvgMobileFeishu } from './assets/icons/icon-mobile-feishu.svg';
import { ReactComponent as SvgMobileEmail } from './assets/icons/icon-mobile-email.svg';
import { ReactComponent as SvgMobileCommunity } from './assets/icons/icon-mobile-community.svg';
import { ReactComponent as SvgIconMobileCollect } from './assets/icons/icon-mobile-collect.svg';
import { ReactComponent as SvgIconMobileCollectFill } from './assets/icons/icon-mobile-collect-fill.svg';
import { ReactComponent as SvgIconMobileClose } from './assets/icons/icon-mobile-close.svg';
import { ReactComponent as SvgMinus } from './assets/icons/icon-minus.svg';
import { ReactComponent as SvgMinimizeWindow } from './assets/icons/icon-minimize-window.svg';
import { ReactComponent as SvgMenuIcon } from './assets/icons/icon-menu.svg';
import { ReactComponent as SvgIconMenuSocialMedia } from './assets/icons/icon-menu-social-media.svg';
import { ReactComponent as SvgIconMenuSocialMediaYoutube } from './assets/icons/icon-menu-social-media-youtube.svg';
import { ReactComponent as SvgIconMenuSocialMediaX } from './assets/icons/icon-menu-social-media-x.svg';
import { ReactComponent as SvgIconBotMenuPlus } from './assets/icons/icon-menu-plus.svg';
import { ReactComponent as SvgIconBotMenuLogo } from './assets/icons/icon-menu-logo.svg';
import { ReactComponent as SvgIconBotMenuLogoText } from './assets/icons/icon-menu-logo-text.svg';
import { ReactComponent as SvgIconBotMenuLogoTextCN } from './assets/icons/icon-menu-logo-text-cn.svg';
import { ReactComponent as SvgIconMenuFeedback } from './assets/icons/icon-menu-feedback.svg';
import { ReactComponent as SvgIconMenuDocument } from './assets/icons/icon-menu-document.svg';
import { ReactComponent as SvgIconMenuCommunity } from './assets/icons/icon-menu-community.svg';
import { ReactComponent as SvgIconMenuCommunityTelegram } from './assets/icons/icon-menu-community-telegram.svg';
import { ReactComponent as SvgIconMenuCommunityDiscord } from './assets/icons/icon-menu-community-discord.svg';
import { ReactComponent as SvgMenuArena } from './assets/icons/icon-menu-arena.svg';
import { ReactComponent as SvgMenuArenaSelected } from './assets/icons/icon-menu-arena-select.svg';
import { ReactComponent as SvgMemory } from './assets/icons/icon-memory.svg';
import { ReactComponent as SvgLogo } from './assets/icons/icon-logo.svg';
import { ReactComponent as SvgLogoWife } from './assets/icons/icon-logo-wife.svg';
import { ReactComponent as SvgListCheck } from './assets/icons/icon-list-check.svg';
import { ReactComponent as SvgLinkStroked } from './assets/icons/icon-link-stroked.svg';
import { ReactComponent as SvgLeftArrow } from './assets/icons/icon-left-arrow.svg';
import { ReactComponent as SvgLearnMoreIcon } from './assets/icons/icon-learn-more.svg';
import { ReactComponent as SvgInvisible } from './assets/icons/icon-invisible.svg';
import { ReactComponent as SvgInfo } from './assets/icons/icon-info.svg';
import { ReactComponent as SvgInfoOutlined } from './assets/icons/icon-info-outlined.svg';
import { ReactComponent as SvgInfoCircle } from './assets/icons/icon-info-circle.svg';
import { ReactComponent as SvgIconImportOutlined } from './assets/icons/icon-import-outlined.svg';
import { ReactComponent as SvgImageOverlay } from './assets/icons/icon-image-overlay.svg';
import { ReactComponent as SvgImageFailOutlined } from './assets/icons/icon-image-fail-outlined.svg';
import { ReactComponent as SvgIDEFilebox } from './assets/icons/icon-ide-filebox.svg';
import { ReactComponent as SvgHotBot } from './assets/icons/icon-hot-bot.svg';
import { ReactComponent as SvgHome } from './assets/icons/icon-home.svg';
import { ReactComponent as SvgHomeSelected } from './assets/icons/icon-home-selected.svg';
import { ReactComponent as SvgHistory } from './assets/icons/icon-history.svg';
import { ReactComponent as SvgHelpIcon } from './assets/icons/icon-help.svg';
import { ReactComponent as SvgHandIcon } from './assets/icons/icon-hand.svg';
import { ReactComponent as SvgGroupCardOutlined } from './assets/icons/icon-group-card-outlined.svg';
import { ReactComponent as SvgGroupCardNoBind } from './assets/icons/icon-group-card-no-bind.svg';
import { ReactComponent as SvgGoogle } from './assets/icons/icon-google.svg';
import { ReactComponent as SvgIconGoogle16 } from './assets/icons/icon-google-logo-16.svg';
import { ReactComponent as SvgGlobalIntentLogo } from './assets/icons/icon-global-intent-logo-orange.svg';
import { ReactComponent as SvgFullscreen } from './assets/icons/icon-fullscreen.svg';
import { ReactComponent as SvgFrownUponMessage } from './assets/icons/icon-frown-upon-message.svg';
import { ReactComponent as SvgFrownUponMessageSelected } from './assets/icons/icon-frown-upon-message-selected.svg';
import { ReactComponent as SvgFooterWeixinIcon } from './assets/icons/icon-footer-weixi.svg';
import { ReactComponent as SvgFooterMailIcon } from './assets/icons/icon-footer-mail.svg';
import { ReactComponent as SvgFooterFeishuIcon } from './assets/icons/icon-footer-feishu.svg';
import { ReactComponent as SvgFooterDocumentIcon } from './assets/icons/icon-footer-document.svg';
import { ReactComponent as SvgFooterDiscordIcon } from './assets/icons/icon-footer-discord.svg';
import { ReactComponent as SvgFlowArrow } from './assets/icons/icon-flow-arrow.svg';
import { ReactComponent as SvgFile } from './assets/icons/icon-file.svg';
import { ReactComponent as SvgSheetIcon } from './assets/icons/icon-file-sheet-color.svg';
import { ReactComponent as SvgFileLink } from './assets/icons/icon-file-link.svg';
import { ReactComponent as SvgFileIcon } from './assets/icons/icon-file-color.svg';
import { ReactComponent as SvgFeedback } from './assets/icons/icon-feedback.svg';
import { ReactComponent as SvgExplorePlugins } from './assets/icons/icon-explore-plugins.svg';
import { ReactComponent as SvgExplorePluginsSelected } from './assets/icons/icon-explore-plugins-selected.svg';
import { ReactComponent as SvgExploreBots } from './assets/icons/icon-explore-bots.svg';
import { ReactComponent as SvgExploreBotsSelected } from './assets/icons/icon-explore-bots-selected.svg';
import { ReactComponent as SvgErrorIcon } from './assets/icons/icon-error.svg';
import { ReactComponent as SvgEllipsis } from './assets/icons/icon-ellipsis.svg';
import { ReactComponent as SvgEffects } from './assets/icons/icon-effects.svg';
import { ReactComponent as SvgEdit } from './assets/icons/icon-edit.svg';
import { ReactComponent as SvgEditOutlined1 } from './assets/icons/icon-edit-outlined1.svg';
import { ReactComponent as SvgEditOutlined } from './assets/icons/icon-edit-outlined.svg';
import { ReactComponent as SvgEditOutline } from './assets/icons/icon-edit-outline.svg';
import { ReactComponent as SvgEditOptions } from './assets/icons/icon-edit-options.svg';
import { ReactComponent as SvgEditNew } from './assets/icons/icon-edit-new.svg';
import { ReactComponent as SvgEditKnowledgeIcon } from './assets/icons/icon-edit-knowledge.svg';
import { ReactComponent as SvgEditBlue } from './assets/icons/icon-edit-blue.svg';
import { ReactComponent as SvgDouyin } from './assets/icons/icon-dy.svg';
import { ReactComponent as SvgDouyinSquare } from './assets/icons/icon-dy-square.svg';
import { ReactComponent as SvgDropdownTrigger } from './assets/icons/icon-dropdown-trigger.svg';
import { ReactComponent as SvgDropdownMobile } from './assets/icons/icon-dropdown-mobile.svg';
import { ReactComponent as SvgDropdownClose } from './assets/icons/icon-dropdown-close.svg';
import { ReactComponent as SvgDrag } from './assets/icons/icon-drag.svg';
import { ReactComponent as SvgDraftsTag } from './assets/icons/icon-drafts-tag.svg';
import { ReactComponent as SvgDownBoldIcon } from './assets/icons/icon-down-bold-outlined.svg';
import { ReactComponent as SvgDownArrow } from './assets/icons/icon-down-arrow.svg';
import { ReactComponent as SvgDownArrowStroked } from './assets/icons/icon-down-arrow-stroked.svg';
import { ReactComponent as SvgDocuments } from './assets/icons/icon-documents.svg';
import { ReactComponent as SvgDocumentsLanding } from './assets/icons/icon-documents-landing.svg';
import { ReactComponent as SvgDiscover } from './assets/icons/icon-discover.svg';
import { ReactComponent as SvgDiff } from './assets/icons/icon-diff.svg';
import { ReactComponent as SvgDelete } from './assets/icons/icon-delete.svg';
import { ReactComponent as SvgDeleteOutline1 } from './assets/icons/icon-delete-outline1.svg';
import { ReactComponent as SvgDeleteOutline } from './assets/icons/icon-delete-outline.svg';
import { ReactComponent as SvgDeleteMessage } from './assets/icons/icon-delete-message.svg';
import { ReactComponent as SvgDebugTts } from './assets/icons/icon-debug-tts.svg';
import { ReactComponent as SvgDatasets } from './assets/icons/icon-datasets.svg';
import { ReactComponent as SvgDatasetsSelected } from './assets/icons/icon-datasets-selected.svg';
import { ReactComponent as SvgDataSheet } from './assets/icons/icon-data-sheet-outlined.svg';
import { ReactComponent as SvgDataSetting } from './assets/icons/icon-data-setting.svg';
import { ReactComponent as SvgDataSet } from './assets/icons/icon-data-set.svg';
import { ReactComponent as SvgCozeZHIcon } from './assets/icons/icon-coze-zh.svg';
import { ReactComponent as SvgCozeENIcon } from './assets/icons/icon-coze-en.svg';
import { ReactComponent as SvgCopyOutlined } from './assets/icons/icon-copy-outlined.svg';
import { ReactComponent as SvgConfig } from './assets/icons/icon-config.svg';
import { ReactComponent as SvgCommunityWeixin } from './assets/icons/icon-community-weixin.svg';
import { ReactComponent as SvgCommunityWeixinLoading } from './assets/icons/icon-community-weixin-loading.svg';
import { ReactComponent as SvgCommunityDiscord } from './assets/icons/icon-community-discord.svg';
import { ReactComponent as SvgCollectStroked } from './assets/icons/icon-collection-stroked.svg';
import { ReactComponent as SvgCollectFilled } from './assets/icons/icon-collect-filled.svg';
import { ReactComponent as SvgCollapse } from './assets/icons/icon-collapse.svg';
import { ReactComponent as SvgCodeOutlined } from './assets/icons/icon-code-outlined.svg';
import { ReactComponent as SvgClose } from './assets/icons/icon-close.svg';
import { ReactComponent as SvgCloseOutlined } from './assets/icons/icon-close-outlined.svg';
import { ReactComponent as SvgIconCloseNoCycle } from './assets/icons/icon-close-no-cycle.svg';
import { ReactComponent as SvgClear } from './assets/icons/icon-clear.svg';
import { ReactComponent as SvgClearStroked } from './assets/icons/icon-clear-stroked.svg';
import { ReactComponent as SvgCircleClose } from './assets/icons/icon-circle-close.svg';
import { ReactComponent as SvgChevronRightIcon } from './assets/icons/icon-chevron-right.svg';
import { ReactComponent as SvgChevronDownIcon } from './assets/icons/icon-chevron-down.svg';
import { ReactComponent as SvgChatboxOutlined } from './assets/icons/icon-chatbox-outlined.svg';
import { ReactComponent as SvgChatIcon } from './assets/icons/icon-chat.svg';
import { ReactComponent as SvgChatIconDisable } from './assets/icons/icon-chat-disable.svg';
import { ReactComponent as SvgCardSearchOutline } from './assets/icons/icon-card-search-outlined.svg';
import { ReactComponent as SvgCancelLinkOutlinedIcon } from './assets/icons/icon-cancel-link-outlined.svg';
import { ReactComponent as SvgCamera } from './assets/icons/icon-camera.svg';
import { ReactComponent as SvgCalendar } from './assets/icons/icon-calendar.svg';
import { ReactComponent as SvgBrowse } from './assets/icons/icon-browse.svg';
import { ReactComponent as SvgBots } from './assets/icons/icon-bots.svg';
import { ReactComponent as SvgBotsSelected } from './assets/icons/icon-bots-selected.svg';
import { ReactComponent as SvgBotStoreUser } from './assets/icons/icon-bot-store-user.svg';
import { ReactComponent as SvgIconBotStoreQuestion } from './assets/icons/icon-bot-store-question.svg';
import { ReactComponent as SvgBotStoreLink } from './assets/icons/icon-bot-store-link.svg';
import { ReactComponent as SvgBotStoreConversaion } from './assets/icons/icon-bot-store-conversation.svg';
import { ReactComponent as SvgBotPublished } from './assets/icons/icon-bot-published.svg';
import { ReactComponent as SvgBotMultiRightBtnIcon } from './assets/icons/icon-bot-multi-right-btn.svg';
import { ReactComponent as SvgBotMultiLeftBtnIcon } from './assets/icons/icon-bot-multi-left-btn.svg';
import { ReactComponent as SvgBotManage } from './assets/icons/icon-bot-manage.svg';
import { ReactComponent as SvgBotExit } from './assets/icons/icon-bot-exit.svg';
import { ReactComponent as SvgBotDatabase } from './assets/icons/icon-bot-database.svg';
import { ReactComponent as SvgBlogs } from './assets/icons/icon-blogs.svg';
import { ReactComponent as SvgCopy } from './assets/icons/icon-backup-verification.svg';
import { ReactComponent as SvgCopySuccess } from './assets/icons/icon-backup-verification-success.svg';
import { ReactComponent as SvgBackTop } from './assets/icons/icon-back-top.svg';
import { ReactComponent as SvgAuto } from './assets/icons/icon-auto.svg';
import { ReactComponent as SvgIconAutoShape } from './assets/icons/icon-auto-shape.svg';
import { ReactComponent as SvgAutoLayout } from './assets/icons/icon-auto-layout.svg';
import { ReactComponent as SvgAutoGenerationIcon } from './assets/icons/icon-auto-generation.svg';
import { ReactComponent as SvgIconAt } from './assets/icons/icon-at.svg';
import { ReactComponent as SvgArrow } from './assets/icons/icon-arrow.svg';
import { ReactComponent as SvgIconArrowRight } from './assets/icons/icon-arrow-right.svg';
import { ReactComponent as SvgIconArrowRightAdjustable } from './assets/icons/icon-arrow-right-adjustable.svg';
import { ReactComponent as SvgArrowLeft } from './assets/icons/icon-arrow-left.svg';
import { ReactComponent as SvgArenaModel } from './assets/icons/icon-arena-model.svg';
import { ReactComponent as SvgArenaDice } from './assets/icons/icon-arena-dice.svg';
import { ReactComponent as SvgAlertStroked } from './assets/icons/icon-alert-stroked.svg';
import { ReactComponent as SvgAlertFilled } from './assets/icons/icon-alert-filled.svg';
import { ReactComponent as SvgAi } from './assets/icons/icon-ai.svg';
import { ReactComponent as SvgAgentWorkflowSkill } from './assets/icons/icon-agent-workflow-skill.svg';
import { ReactComponent as SvgAgentToolsSkill } from './assets/icons/icon-agent-tools-skill.svg';
import { ReactComponent as SvgAgentLogo } from './assets/icons/icon-agent-logo.svg';
import { ReactComponent as SvgAgentKnowledgeSkill } from './assets/icons/icon-agent-knowledge-skill.svg';
import { ReactComponent as SvgAddedPlugin } from './assets/icons/icon-added-plugin.svg';
import { ReactComponent as SvgAdd } from './assets/icons/icon-add.svg';
import { ReactComponent as SvgAddSubItemsOutlined } from './assets/icons/icon-add-sub-items-outlined.svg';
import { ReactComponent as SvgAddFiled } from './assets/icons/icon-add-field.svg';
import { ReactComponent as SvgAddCircle } from './assets/icons/icon-add-circle.svg';
import { ReactComponent as SvgAddChildOutlined } from './assets/icons/icon-add-child-outlined.svg';
import { ReactComponent as SvgAddAgentLogo } from './assets/icons/icon-add-agent-logo.svg';
import { ReactComponent as SvgHotIcon } from './assets/icons/hot-icon.svg';
import { ReactComponent as SvgFilterIcon } from './assets/icons/filter.svg';
import { ReactComponent as SvgFileBox } from './assets/icons/filebox.svg';
import { ReactComponent as SvgUploadXLS } from './assets/icons/file-type/icon-upload-xls.svg';
import { ReactComponent as SvgUploadTxt } from './assets/icons/file-type/icon-upload-txt.svg';
import { ReactComponent as SvgUploadTextUrl } from './assets/icons/file-type/icon-upload-text-url.svg';
import { ReactComponent as SvgUploadTableUrl } from './assets/icons/file-type/icon-upload-table-url.svg';
import { ReactComponent as SvgUploadPdf } from './assets/icons/file-type/icon-upload-pdf.svg';
import { ReactComponent as SvgUploadMD } from './assets/icons/file-type/icon-upload-md.svg';
import { ReactComponent as SvgUploadDoc } from './assets/icons/file-type/icon-upload-doc.svg';
import { ReactComponent as SvgUploadCsv } from './assets/icons/file-type/icon-upload-csv.svg';
import { ReactComponent as SvgUploadAPI } from './assets/icons/file-type/icon-upload-api.svg';
import { ReactComponent as SvgUnknowFile } from './assets/icons/file-type/icon-unknow.svg';
import { ReactComponent as SvgUnitsTable } from './assets/icons/file-type/icon-units-table.svg';
import { ReactComponent as SvgUnitsFile } from './assets/icons/file-type/icon-units-file.svg';
import { ReactComponent as SvgTextFile } from './assets/icons/file-type/icon-text.svg';
import { ReactComponent as SvgTableApiOutlined } from './assets/icons/file-type/icon-table-api-outlined.svg';
import { ReactComponent as SvgPDFFile } from './assets/icons/file-type/icon-pdf.svg';
import { ReactComponent as SvgOnlineData } from './assets/icons/file-type/icon-online-data.svg';
import { ReactComponent as SvgLocalFile } from './assets/icons/file-type/icon-local-file.svg';
import { ReactComponent as SvgDocxFile } from './assets/icons/file-type/icon-docx.svg';
import { ReactComponent as SvgDeleteOutline2File } from './assets/icons/file-type/icon-delete-outline2.svg';
import { ReactComponent as SvgCustomData } from './assets/icons/file-type/icon-custom-data.svg';
import { ReactComponent as SvgCozeCross } from './assets/icons/coze_cross.svg';
import { ReactComponent as SvtUserGuideIconFill } from './assets/icons/coz_user_guide_fill.svg';
import { ReactComponent as SvtUserGuideIcon } from './assets/icons/coz_user_guide.svg';
import { ReactComponent as SvgTextStyle } from './assets/icons/coz_text_style.svg';
import { ReactComponent as SvgIconCozShare } from './assets/icons/coz_share.svg';
import { ReactComponent as SvtProGuideIconFill } from './assets/icons/coz_pro_guide_fill.svg';
import { ReactComponent as SvtProGuideIcon } from './assets/icons/coz_pro_guide.svg';
import { ReactComponent as SvgPin } from './assets/icons/coz_pin.svg';
import { ReactComponent as SvgCozOfficialFill } from './assets/icons/coz_official_fill.svg';
import { ReactComponent as SvgEyeClose } from './assets/icons/coz_eye_close.svg';
import { ReactComponent as SvgDocumentCode } from './assets/icons/coz_document_code.svg';
import { ReactComponent as SvgDiamondFill } from './assets/icons/coz_diamond_fill.svg';
import { ReactComponent as SvtDeveloperGuideIconFill } from './assets/icons/coz_developer_guide_fill.svg';
import { ReactComponent as SvtDeveloperGuideIcon } from './assets/icons/coz_developer_guide.svg';
import { ReactComponent as SvgCozCheckMark } from './assets/icons/coz_check_mark.svg';
import { ReactComponent as SvgArenaBattle } from './assets/icons/coz_battle.svg';
import { ReactComponent as SvgBan } from './assets/icons/coz_ban.svg';
import { ReactComponent as SvgCozArrowDownLeftIcon } from './assets/icons/coz_arrow_down_left.svg';
import { ReactComponent as SvgCozArrowDownIcon } from './assets/icons/coz_arrow_down.svg';
import { ReactComponent as SvgRankMath } from './assets/icons/arena/psychology.svg';
import { ReactComponent as SvgRankKnowledge } from './assets/icons/arena/menu_book.svg';
import { ReactComponent as SvgRankTool } from './assets/icons/arena/construction.svg';
import { ReactComponent as SvgRankRole } from './assets/icons/arena/comedy_mask.svg';
import { ReactComponent as SvgRankCode } from './assets/icons/arena/code_blocks.svg';
import { ReactComponent as SvgRankChat } from './assets/icons/arena/chat.svg';
import { ReactComponent as SvgRankCreation } from './assets/icons/arena/award_star.svg';
import { ReactComponent as SvgArenaRankIcon } from './assets/icons/arena/arena-rank-icon.svg';
import { ReactComponent as SvgAnalyticsRegionalIcon } from './assets/icons/analytics/analytics-regional.svg';
import { ReactComponent as SvgAnalyticsRankingIcon } from './assets/icons/analytics/analytics-ranking.svg';
import { ReactComponent as SvgAnalyticsGlobalIcon } from './assets/icons/analytics/analytics-global.svg';
import { ReactComponent as SvgBrandRoundIcon } from './assets/brand-round.svg';
import { ReactComponent as SvgBrandEnWhiteRow } from './assets/brand-en-white-row.svg';
import { ReactComponent as SvgBrandEnWhiteColumn } from './assets/brand-en-white-column.svg';
import { ReactComponent as SvgBrandEnBlackRow } from './assets/brand-en-black-row.svg';
import { ReactComponent as SvgBrandEnBlackColumn } from './assets/brand-en-black-column.svg';
import { ReactComponent as SvgBrandCnWhiteRow } from './assets/brand-cn-white-row.svg';
import { ReactComponent as SvgBrandCnWhiteColumn } from './assets/brand-cn-white-column.svg';
import { ReactComponent as SvgBrandCnBlackRow } from './assets/brand-cn-black-row.svg';
import { ReactComponent as SvgBrandCnBlackColumn } from './assets/brand-cn-black-column.svg';
import { ReactComponent as SvgBotIcon } from './assets/bot-icon.svg';

export const IconApiPlayground = IconFactory(<SvtPlaygroundIcon />);
export const IconApiPlaygroundFill = IconFactory(<SvtPlaygroundIconFill />);
export const IconUserGuide = IconFactory(<SvtUserGuideIcon />);
export const IconUserGuideFill = IconFactory(<SvtUserGuideIconFill />);
export const IconDeveloperGuide = IconFactory(<SvtDeveloperGuideIcon />);
export const IconDeveloperGuideFill = IconFactory(
  <SvtDeveloperGuideIconFill />,
);
export const IconProGuide = IconFactory(<SvtProGuideIcon />);
export const IconProGuideFill = IconFactory(<SvtProGuideIconFill />);
export const IconLegacy = IconFactory(<SvgLegacyIcon />);

export const IconAreanRankIcon = IconFactory(<SvgArenaRankIcon />);
export const IconOfficialLabel = IconFactory(<SvgOfficialLabel />);
export const IconRankKnowledge = IconFactory(<SvgRankKnowledge />);
export const IconRankCreation = IconFactory(<SvgRankCreation />);
export const IconRankMath = IconFactory(<SvgRankMath />);
export const IconRankRole = IconFactory(<SvgRankRole />);
export const IconRankTool = IconFactory(<SvgRankTool />);
export const IconRankChat = IconFactory(<SvgRankChat />);
export const IconRankCode = IconFactory(<SvgRankCode />);
export const IconModelConfig = IconFactory(<SvgModelConfig />);
export const IconCozShare = IconFactory(<SvgIconCozShare />);
export const IconArenaModel = IconFactory(<SvgArenaModel />);
export const IconBrandRound = IconFactory(<SvgBrandRoundIcon />);
export const IconMenuArena = IconFactory(<SvgMenuArena />);
export const IconMenuArenaSelected = IconFactory(<SvgMenuArenaSelected />);
export const IconReplyCnOutlined = IconFactory(<SvgReplyCnOutlined />);
export const IconThumbupOutlined = IconFactory(<SvgThumbupOutlined />);
export const IconThumbdownOutlined = IconFactory(<SvgThumbdownOutlined />);
export const IconSpaceDownOutlined = IconFactory(<SvgSpaceDownOutlined />);
export const IconArenaDice = IconFactory(<SvgArenaDice />);
export const IconCozeBattle = IconFactory(<SvgArenaBattle />);
export const IconAddDesktop = IconFactory(<SvgAddDesktopIcon />);
export const IconAvatarEditMask = IconFactory(<SvgAvatarEditMask />);
export const IconDeleteOutline1 = IconFactory(<SvgDeleteOutline1 />);
export const IconRefreshOutlined1 = IconFactory(<SvgRefreshOutlined1 />);
export const IconUploadOutlined1 = IconFactory(<SvgUploadOutlined1 />);
export const IconImageOverlay = IconFactory(<SvgImageOverlay />);
export const IconTextStyle = IconFactory(<SvgTextStyle />);
export const IconPin = IconFactory(<SvgPin />);
export const IconEffects = IconFactory(<SvgEffects />);
export const IconEditKnowledge = IconFactory(<SvgEditKnowledgeIcon />);
export const IconHot = IconFactory(<SvgHotIcon />);
export const IconEdit = IconFactory(<SvgEdit />);
export const IconEditBlue = IconFactory(<SvgEditBlue />);
export const IconInfoCircle = IconFactory(<SvgInfoCircle />);
export const IconInfoOutlined = IconFactory(<SvgInfoOutlined />);
export const IconFlowArrow = IconFactory(<SvgFlowArrow />);
export const IconSend = IconFactory(<SvgSend />);
export const IconAdd = IconFactory(<SvgAdd />);
export const IconAddSubItemsOutlined = IconFactory(<SvgAddSubItemsOutlined />);
export const IconAddMoreNew = IconFactory(<SvgAddMoreNew />);
export const IconRobot = IconFactory(<SvgRobot />);
export const IconStyleSet = IconFactory(<SvgStyleSet />);
export const IconSetting = IconFactory(<SvgSetting />);
export const IconDelete = IconFactory(<SvgDelete />);
export const IconReplace = IconFactory(<SvgReplace />);
export const IconAuto = IconFactory(<SvgAuto />);
export const IconCopy = IconFactory(<SvgCopy />);
export const IconCopySuccess = IconFactory(<SvgCopySuccess />);
export const IconInfo = IconFactory(<SvgInfo />);
export const IconNo = IconFactory(<SvgNo />);
export const IconAddFiled = IconFactory(<SvgAddFiled />);
export const IconCamera = IconFactory(<SvgCamera />);
export const IconImageFailOutlined = IconFactory(<SvgImageFailOutlined />);
export const IconBotManage = IconFactory(<SvgBotManage />);
export const IconDataSetting = IconFactory(<SvgDataSetting />);
export const IconTimeCapsule = IconFactory(<SvgTimeCapsule />);
export const IconPlugin = IconFactory(<SvgPlugin />);
export const IconPlayground = IconFactory(<SvgPlayground />);
export const IconPullDown = IconFactory(<SvgPullDown />);
export const IconNote = IconFactory(<SvgNote />);
export const IconMemory = IconFactory(<SvgMemory />);
export const IconStopOutlined = IconFactory(<SvgStopOutlined />);
export const IconDownArrow = IconFactory(<SvgDownArrow />);
export const IconDownArrowBlack = IconFactory(<SvgDownArrowBlack />);

export const IconRightArrow = IconFactory(<SvgRightArrow />);
export const IconDataSet = IconFactory(<SvgDataSet />);
export const IconSearch = IconFactory(<SvgSearch />);
export const IconBrowse = IconFactory(<SvgBrowse />);
export const IconUsePlugin = IconFactory(<SvgUsePlugin />);
export const IconDownArrowStroked = IconFactory(<SvgDownArrowStroked />);
export const IconViewList = IconFactory(<SvgViewList />);
export const IconClose = IconFactory(<SvgClose />);
export const IconModify = IconFactory(<SvgModify />);
export const IconTable = IconFactory(<SvgTable />);
export const IconTemplate = IconFactory(<SvgTemplate />);
export const IconAlertStroked = IconFactory(<SvgAlertStroked />);
export const IconAlertFilled = IconFactory(<SvgAlertFilled />);
export const IconTickStroked = IconFactory(<SvgTickStroked />);
export const IconFile = IconFactory(<SvgFile />);
export const IconPinOutlined = IconFactory(<SvgPinOutlined />);
export const IconTrashCan = IconFactory(<SvgTrashCan />);
export const IconBackTop = IconFactory(<SvgBackTop />);
export const IconCollectFilled = IconFactory(<SvgCollectFilled />);
export const IconCollectStroked = IconFactory(<SvgCollectStroked />);
export const IconConfig = IconFactory(<SvgConfig />);
export const IconArrow = IconFactory(<SvgArrow />);
export const IconCircleClose = IconFactory(<SvgCircleClose />);
export const IconWorkflowRunning = IconFactory(<SvgWorkflowRunning />);
export const IconWorkflowRunSuccess = IconFactory(<SvgWorkflowRunSuccess />);
export const IconWorkflowRunFail = IconFactory(<SvgWorkflowRunFail />);
export const IconWorkflowRunResultClose = IconFactory(
  <SvgWorkflowRunResultClose />,
);
export const IconLinkStroked = IconFactory(<SvgLinkStroked />);
export const IconBan = IconFactory(<SvgBan />);
export const IconEyeClose = IconFactory(<SvgEyeClose />);
export const IconSelector = IconFactory(<SvgSelectorIcon />);
export const IconDiscover = IconFactory(<SvgDiscover />);
export const IconClearStoked = IconFactory(<SvgClearStroked />);
export const IconAddedPlugin = IconFactory(<SvgAddedPlugin />);
export const IconMyPlugin = IconFactory(<SvgMyPlugin />);
export const IconMyTools = IconFactory(<SvgMyTools />);
export const IconTeamTools = IconFactory(<SvgTeamTools />);
export const IconTeamAddOutlined = IconFactory(<SvgTeamAddOutlined />);

export const IconSettingStroked = IconFactory(<SvgSettingStroked />);
export const IconMoreAdd = IconFactory(<SvgMoreAdd />);
export const IconMore = IconFactory(<SvgMore />);
export const IconMoreVertical = IconFactory(<SvgMoreVertical />);
export const IconFilter = IconFactory(<SvgFilterIcon />);
export const IconPlaceholderImg = IconFactory(<SvgPlaceholderImg />);
export const IconMenuCollapse = IconFactory(<MenuCollapse />);

export const IconBots = IconFactory(<SvgBots />);
export const IconBotsSelected = IconFactory(<SvgBotsSelected />);
export const IconPlugins = IconFactory(<SvgPlugins />);
export const IconPluginsSelected = IconFactory(<SvgPluginsSelected />);
export const IconWorkflows = IconFactory(<SvgWorkflows />);
export const IconWorkflowsSelected = IconFactory(<SvgWorkflowsSelected />);
export const IconDatasets = IconFactory(<SvgDatasets />);
export const IconDatasetsSelected = IconFactory(<SvgDatasetsSelected />);
export const IconTeam = IconFactory(<SvgTeam />);
export const IconTeamTag = IconFactory(<SvgTeamTag />);
export const IconTeamDefault = IconFactory(<SvgTeamDefault />);
export const IconTeamSelected = IconFactory(<SvgTeamSelected />);
export const IconExploreBots = IconFactory(<SvgExploreBots />);
export const IconExploreBotsSelected = IconFactory(<SvgExploreBotsSelected />);
export const IconExplorePlugins = IconFactory(<SvgExplorePlugins />);
export const IconExplorePluginsSelected = IconFactory(
  <SvgExplorePluginsSelected />,
);
export const IconHome = IconFactory(<SvgHome />);
export const IconLongArrowUp = IconFactory(<SvgLongArrowUp />);
export const IconHomeSelected = IconFactory(<SvgHomeSelected />);
export const IconThumbupFilled = IconFactory(<SvgIconThumbupFilled />);
export const IconThumbdownFilled = IconFactory(<SvgIconThumbdownFilled />);
export const IconPerson = IconFactory(<SvgPerson />);
export const IconPersonSelected = IconFactory(<SvgPersonSelected />);
export const IconDocuments = IconFactory(<SvgDocuments />);
export const IconDocumentsLanding = IconFactory(<SvgDocumentsLanding />);
export const IconBlogs = IconFactory(<SvgBlogs />);
export const IconFeedback = IconFactory(<SvgFeedback />);
export const IconFileLink = IconFactory(<SvgFileLink />);
export const IconTime = IconFactory(<SvgTime />);
export const IconDeleteOutline = IconFactory(<SvgDeleteOutline />);
export const IconDeleteOutline2 = IconFactory(<SvgDeleteOutline2File />);
export const IconDraftsTag = IconFactory(<SvgDraftsTag />);
export const IconMoreRound = IconFactory(<SvgMoreRound />);
export const IconUploadOutlined = IconFactory(<SvgUploadOutlined />);
export const IconEditOutline = IconFactory(<SvgEditOutline />);
export const IconEditOutlined = IconFactory(<SvgEditOutlined />);
export const IconEditOutlined1 = IconFactory(<SvgEditOutlined1 />);
export const IconSearchInput = IconFactory(<SvgSearchInput />);
export const IconAutoShape = IconFactory(<SvgIconAutoShape />);

export const IconMenuSocialMedia = IconFactory(<SvgIconMenuSocialMedia />);
export const IconMenuSocialMediaYoutube = IconFactory(
  <SvgIconMenuSocialMediaYoutube />,
);
export const IconMenuSocialMediaX = IconFactory(<SvgIconMenuSocialMediaX />);
export const IconMenuFeedback = IconFactory(<SvgIconMenuFeedback />);
export const IconMenuDocument = IconFactory(<SvgIconMenuDocument />);
export const IconMenuCommunity = IconFactory(<SvgIconMenuCommunity />);
export const IconMenuCommunityTelegram = IconFactory(
  <SvgIconMenuCommunityTelegram />,
);
export const IconMenuCommunityDiscord = IconFactory(
  <SvgIconMenuCommunityDiscord />,
);

export const IconMenuLogo = IconFactory(<SvgIconBotMenuLogo />);
export const IconChatHashtag = IconFactory(<SvgChatHashtag />);
export const IconMenuLogoText = IconFactory(<SvgIconBotMenuLogoText />);
export const IconMenuLogoTextCN = IconFactory(<SvgIconBotMenuLogoTextCN />);

export const IconMenuPlus = IconFactory(<SvgIconBotMenuPlus />);

export const IconQueryEmpty = IconFactory(<SvgQueryEmpty />);

export const IconCollapse = IconFactory(<SvgCollapse />);
export const IconLargeEmpty = IconFactory(<SvgLargeEmptyIcon />);
export const IconEmpty = IconFactory(<SvgEmptyIcon />);
export const IconPlayRoundOutlined = IconFactory(<SvgPlayOutline />);
export const IconCardSearchOutlined = IconFactory(<SvgCardSearchOutline />);
export const IconGroupCardOutlined = IconFactory(<SvgGroupCardOutlined />);
export const IconGroupCardNoBind = IconFactory(<SvgGroupCardNoBind />);
export const IconHistory = IconFactory(<SvgHistory />);
export const IconDataSheetOutlined = IconFactory(<SvgDataSheet />);
export const IconTaskOutlined = IconFactory(<SvgTask />);
export const IconPDFFile = IconFactory(<SvgPDFFile />);
export const IconDocxFile = IconFactory(<SvgDocxFile />);
export const IconTextFile = IconFactory(<SvgTextFile />);
export const IconUnitsFile = IconFactory(<SvgUnitsFile />);
export const IconUpdateOutlined = IconFactory(<SvgIconUpdateOutlined />);
export const IconUnitsTable = IconFactory(<SvgUnitsTable />);
export const IconUnknowFile = IconFactory(<SvgUnknowFile />);
export const IconRefresh = IconFactory(<SvgRefresh />);
export const IconDeleteMessage = IconFactory(<SvgDeleteMessage />);
export const IconMoreOperationsMessage = IconFactory(
  <SvgMoreOperationsMessage />,
);
export const IconMoreOperationsDeleteMessage = IconFactory(
  <SvgMoreOperationsDeleteMessage />,
);
export const IconFrownUponMessageSelected = IconFactory(
  <SvgFrownUponMessageSelected />,
);
export const IconFrownUponMessage = IconFactory(<SvgFrownUponMessage />);
export const IconThumbsUpMessageSelected = IconFactory(
  <SvgThumbsUpMessageSelected />,
);
export const IconThumbsUpMessage = IconFactory(<SvgThumbsUpMessage />);
export const IconUpload = IconFactory(<SvgUpload />);
export const IconLocalFile = IconFactory(<SvgLocalFile />);
export const IconOnlineData = IconFactory(<SvgOnlineData />);
export const IconTableApiOutlined = IconFactory(<SvgTableApiOutlined />);
export const IconCustomData = IconFactory(<SvgCustomData />);
export const IconStatusSucceed = IconFactory(<SvgSucceed />);
export const IconStatusWarning = IconFactory(<SvgWarning />);

export const IconStatusInfo = IconFactory(<SvgStatusInfo />);
export const IconNewConversation = IconFactory(<SvgNewConversation />);

export const IconBrandCnWhiteRow = IconFactory(<SvgBrandCnWhiteRow />);
export const IconBrandEnWhiteRow = IconFactory(<SvgBrandEnWhiteRow />);
export const IconBrandCnBlackRow = IconFactory(<SvgBrandCnBlackRow />);
export const IconBrandEnBlackRow = IconFactory(<SvgBrandEnBlackRow />);
export const IconBrandCnWhiteColumn = IconFactory(<SvgBrandCnWhiteColumn />);
export const IconBrandEnWhiteColumn = IconFactory(<SvgBrandEnWhiteColumn />);
export const IconBrandCnBlackColumn = IconFactory(<SvgBrandCnBlackColumn />);
export const IconBrandEnBlackColumn = IconFactory(<SvgBrandEnBlackColumn />);
export const IconBotIcon = IconFactory(<SvgBotIcon />);
export const IconGoogle = IconFactory(<SvgGoogle />);
export const IconDouyin = IconFactory(<SvgDouyin />);
export const IconDouyinSquare = IconFactory(<SvgDouyinSquare />);
export const IconFullscreen = IconFactory(<SvgFullscreen />);
export const IconPlus = IconFactory(<SvgPlus />);
export const IconMinus = IconFactory(<SvgMinus />);
export const IconAutoLayout = IconFactory(<SvgAutoLayout />);
export const IconWithdraw = IconFactory(<SvgWithdraw />);
export const IconPolyline = IconFactory(<SvgPolyline />);
export const IconSmoothline = IconFactory(<SvgSmoothline />);
export const IconArrowRight = IconFactory(<SvgIconArrowRight />);
export const IconImportOutlined = IconFactory(<SvgIconImportOutlined />);
export const IconArrowRightAdjustable = IconFactory(
  <SvgIconArrowRightAdjustable />,
);
export const IconParagraphCopy = IconFactory(<SvgParagraphCopy />);
export const IconCardMore = IconFactory(<SvgWorkflowCardMore />);
export const IconError = IconFactory(<SvgErrorIcon />);
export const IconModalError = IconFactory(<SvgModalErrorIcon />);
export const IconSuccess = IconFactory(<SvgSuccessIcon />);
export const IconCommunityWeixinLoading = IconFactory(
  <SvgCommunityWeixinLoading />,
);
export const IconCommunityWeixin = IconFactory(<SvgCommunityWeixin />);
export const IconCommunityDiscord = IconFactory(<SvgCommunityDiscord />);
export const IconGoogleLogo16 = IconFactory(<SvgIconGoogle16 />);
export const IconDropdownTrigger = IconFactory(<SvgDropdownTrigger />);
export const IconLogo = IconFactory(<SvgLogo />);
export const IconLogoWife = IconFactory(<SvgLogoWife />);
export const IconAddCircle = IconFactory(<SvgAddCircle />);
export const IconSlash = IconFactory(<SvgSlash />);
export const IconListCheck = IconFactory(<SvgListCheck />);
export const IconPersonFilled = IconFactory(<SvgPersonFilled />);
export const IconTeamFilled = IconFactory(<SvgTeamFilled />);
export const IconToastInfo = IconFactory(<SvgToastInfo />);
export const IconToastSuccess = IconFactory(<SvgToastSuccess />);
export const IconToastWarning = IconFactory(<SvgToastWarning />);
export const IconToastError = IconFactory(<SvgToastError />);
export const IconMultipleMode = IconFactory(<SvgMultipleMode />);
export const IconSingleMode = IconFactory(<SvgSingleMode />);
export const IconBotMultiModelBtn = IconFactory(<SvgBotMultiModelBtn />);
export const IconChat = IconFactory(<SvgChatIcon />);
export const IconChatDisable = IconFactory(<SvgChatIconDisable />);
export const IconAgentLogo = IconFactory(<SvgAgentLogo />);
export const IconAgentToolsSkill = IconFactory(<SvgAgentToolsSkill />);
export const IconAgentWorkflowSkill = IconFactory(<SvgAgentWorkflowSkill />);
export const IconAgentKnowledgeSkill = IconFactory(<SvgAgentKnowledgeSkill />);
export const IconBotMultiLeftBtnIcon = IconFactory(<SvgBotMultiLeftBtnIcon />);
export const IconBotMultiRightBtnIcon = IconFactory(
  <SvgBotMultiRightBtnIcon />,
);
export const IconBotExit = IconFactory(<SvgBotExit />);
export const IconValcanoColored = IconFactory(<SvgValcanoColored />);
export const IconBotPublished = IconFactory(<SvgBotPublished />);
export const IconNoFilled = IconFactory(<SvgNoFilledIcon />);
export const IconNationalEmblem = IconFactory(<SvgNationalEmblem />);
export const IconUploadOutlinedUp = IconFactory(<SvgUploadOutlinedUp />);
export const IconAi = IconFactory(<SvgAi />);
export const IconCloseOutlined = IconFactory(<SvgCloseOutlined />);
export const IconDropdownMobile = IconFactory(<SvgDropdownMobile />);
export const IconLearnMore = IconFactory(<SvgLearnMoreIcon />);
export const IconMobileCommunity = IconFactory(<SvgMobileCommunity />);
export const IconMobileFeishu = IconFactory(<SvgMobileFeishu />);
export const IconDropdownClose = IconFactory(<SvgDropdownClose />);
export const IconMobileEmail = IconFactory(<SvgMobileEmail />);
export const IconMobileSound = IconFactory(<SvgIconMobileSound />);
export const IconMobileSoundNormal = IconFactory(<SvgIconMobileSoundNormal />);
export const IconMobileSoundClosed = IconFactory(<SvgIconMobileSoundClosed />);
export const IconMobileSoundDisable = IconFactory(
  <SvgIconMobileSoundDisable />,
);
export const IconMobileShare = IconFactory(<SVGIconMobileShare />);
export const IconMobileSetting = IconFactory(<SvgIconMobileSetting />);
export const IconMobileCollect = IconFactory(<SvgIconMobileCollect />);
export const IconMobileCollectFill = IconFactory(<SvgIconMobileCollectFill />);
export const IconMobileMenu = IconFactory(<SvgIconMobileMenu />);
export const IconMobileClose = IconFactory(<SvgIconMobileClose />);
export const IconMobileMore = IconFactory(<SvgIconMobileMore />);
export const IconDebugTts = IconFactory(<SvgDebugTts />);
export const IconMinimizeWindow = IconFactory(<SvgMinimizeWindow />);
export const IconBotDatabase = IconFactory(<SvgBotDatabase />);
export const IconWarningInfo = IconFactory(<SvgWarningInfoIcon />);
export const IconCloseNoCycle = IconFactory(<SvgIconCloseNoCycle />);
export const IconWarningStrongInfo = IconFactory(<SvgWarningStrongInfoIcon />);
export const IconWarningSize24 = IconFactory(<SvgWarningSize24 />);
export const IconTextFormat = IconFactory(<SvgTextFormat />);
export const IconTextFormatActive = IconFactory(<SvgTextFormatActive />);
export const IconTableFormat = IconFactory(<SvgTableFormat />);
export const IconTableFormatActive = IconFactory(<SvgTableFormatActive />);
export const IconDocResegment = IconFactory(<SvgDocResegment />);
export const IconKnowledgeSetting = IconFactory(<SvgKnowledgeSetting />);
export const IconUpdateFrequency = IconFactory(<SvgUpdateFrequency />);
export const IconUpdateTableConfig = IconFactory(<SvgUpdateTableConfig />);
export const IconUploadAPI = IconFactory(<SvgUploadAPI />);
export const IconUploadPDF = IconFactory(<SvgUploadPdf />);
export const IconUploadCSV = IconFactory(<SvgUploadCsv />);
export const IconUploadDoc = IconFactory(<SvgUploadDoc />);
export const IconUploadTxt = IconFactory(<SvgUploadTxt />);
export const IconUploadTableUrl = IconFactory(<SvgUploadTableUrl />);
export const IconUploadTextUrl = IconFactory(<SvgUploadTextUrl />);
export const IconUploadXLS = IconFactory(<SvgUploadXLS />);
export const IconUploadMD = IconFactory(<SvgUploadMD />);
export const IconUploadFileSuccess = IconFactory(<SvgUploadFileSuccess />);
export const IconUploadFileFail = IconFactory(<SvgUploadFileFail />);
export const IconWaringRed = IconFactory(<SvgWaringRedIcon />);
export const IconSegmentEmpty = IconFactory(<SvgSegmentEmptyIcon />);
export const IconSvgUpBold = IconFactory(<SvgUpBoldIcon />);
export const IconSvgDownBold = IconFactory(<SvgDownBoldIcon />);
export const IconSvgUploadCompletedIcon = IconFactory(
  <SvgUploadCompletedIcon />,
);
// export const IconRefreshOutlined = IconFactory(<SvgRefreshIcon />);
export const IconUnitEditIcon = IconFactory(<SvgUnitEditIcon />);
export const IconUpOutlined = IconFactory(<SvgUpOutlined />);
export const IconChatboxOutlined = IconFactory(<SvgChatboxOutlined />);
export const IconGlobalIntentLogo = IconFactory(<SvgGlobalIntentLogo />);
export const IconAddAgentLogo = IconFactory(<SvgAddAgentLogo />);
export const IconExit = IconFactory(<SvgExitIcon />);
export const IconArrowLeft = IconFactory(<SvgArrowLeft />);
export const IconEditNew = IconFactory(<SvgEditNew />);
export const IconNavBarBack = IconFactory(<SvgNavBarBack />);
export const IconCloseKnowledge = IconFactory(<SvgCloseKnowledge />);

export const IconCopyOutlined = IconFactory(<SvgCopyOutlined />);
export const IconRefreshOutlined = IconFactory(<SvgRefreshOutlined />);
export const IconRefreshOutlinedNormalized = IconFactory(
  <SvgRefreshOutlinedNormalized />,
);
export const IconSvgLogOut = IconFactory(<SvgLogOutIcon />);
export const IconSvgFile = IconFactory(<SvgFileIcon />);
export const IconSvgSheet = IconFactory(<SvgSheetIcon />);
export const IconSvgUnbound = IconFactory(<SvgUnBoundIcon />);
export const IconTabDownOutlined = IconFactory(<SvgTabDownOutlined />);
export const IconCancelLinkOutlined = IconFactory(
  <SvgCancelLinkOutlinedIcon />,
);

export const IconMemberOutlined = IconFactory(<SvgMemberOutlinedIcon />);
export const IconLoading = IconFactory(<SvgLoading />);
export const IconTimeOutlined = IconFactory(<SvgTimeOutlinedIcon />);
export const IconCollectionOutlined = IconFactory(
  <SvgCollectionOutlinedIcon />,
);
export const IconViewinchatOutlined = IconFactory(<SvgViewinchatOutlined />);
export const IconReport = IconFactory(<SvgReportIcon />);
export const IconEffectsFilled = IconFactory(<SvgEffectsFilled />);
export const IconConversionOutlined = IconFactory(<SvgConversionOutlined />);
export const IconPlatformOutlined = IconFactory(<SvgPlatformOutlined />);
export const IconHotBot = IconFactory(<SvgHotBot />);
export const IconDrag = IconFactory(<SvgDrag />);
export const IconEllipsis = IconFactory(<SvgEllipsis />);
export const IconLeftArrow = IconFactory(<SvgLeftArrow />);
export const IconStore = IconFactory(<SvgStoreIcon />);
export const IconShare = IconFactory(<SvgShareIcon />);
export const IconClear = IconFactory(<SvgClear />);
export const IconAt = IconFactory(<SvgIconAt />);
export const IconAtOutlined = IconFactory(<SvgIconAtOutlined />);
export const IconCcmNextOutlined = IconFactory(<SvgIconCcmNextOutlined />);
export const IconToken = IconFactory(<SvgTokenIcon />);
export const IconTokenSelected = IconFactory(<SvgTokenIconSelected />);
export const IconTasklistOutlined = IconFactory(<SvgTasklistOutlined />);
export const IconIDEFilebox = IconFactory(<SvgIDEFilebox />);
export const IconCommunityTabOutlined = IconFactory(
  <SvgCommunityTabOutlined />,
);
export const IconDiscussOutlined = IconFactory(<SvgDiscussOutlined />);
export const IconApiOutlined = IconFactory(<SvgApiOutlined />);
export const IconOpenAPI = IconFactory(<SvgOpenAPIIcon />);
export const IconCopyLink = IconFactory(<SvgCopyLinkIcon />);
export const IconSpeakerOutlined = IconFactory(<SvgSpeakerOutlined />);
export const IconEditOptions = IconFactory(<SvgEditOptions />);
export const IconSvgNotion = IconFactory(<SvgNotionIcon />);
export const IconSvgGoogle = IconFactory(<SvgGoogleIcon />);
export const IconSvgFeishu = IconFactory(<SvgFeishuIcon />);
export const IconSvgWeixin = IconFactory(<SvgWeixinIcon />);
export const IconSvgFolder = IconFactory(<SvgFolderIcon />);
export const IconSvgTextFile = IconFactory(<SvgTextFileIcon />);
export const IconSvgSheetFile = IconFactory(<SvgSheetFileIcon />);
export const IconSvgWarning = IconFactory(<SvgWaringOrangeIcon />);
export const IconSvgSelectArrow = IconFactory(<SvgSelectArrowIcon />);

export const IconSvgProcessPlugin = IconFactory(<SvgProcessPlugin />);
export const IconSvgProcessDataSet = IconFactory(<SvgProcessDataSet />);
export const IconSvgProcessJump = IconFactory(<SvgProcessJump />);

export const IconSvgSvgTaskTime = IconFactory(<SvgTaskTime />);
export const IconSvgSvgTaskEvent = IconFactory(<SvgTaskEvent />);

export const IconPluginShop = IconFactory(<SvgPluginShop />);
export const IconOpenInDefault = IconFactory(<SvgOpenInDefault />);
export const IconCodeOutlined = IconFactory(<SvgCodeOutlined />);
export const IconPositionFilled = IconFactory(<SvgPositionFilled />);

export const IconDateListPicker = IconFactory(<SvgDateListPickerIcon />);
export const IconWidgetPublished = IconFactory(<SvgWidgetPublished />);
export const IconWidgetUnpublished = IconFactory(<SvgWidgetUnpublished />);

export const IconPaLogOutlined = IconFactory(<SvgPaLogOutlined />);
export const IconCalendar = IconFactory(<SvgCalendar />);

export const IconSpanBMParallel = IconFactory(<SvgSpanBMParallel />);
export const IconSpanBMConnector = IconFactory(<SvgSpanBMConnector />);
export const IconSpanBMBatch = IconFactory(<SvgSpanBMBatch />);
export const IconSpanAgent = IconFactory(<SvgSpanAgent />);
export const IconSpanBot = IconFactory(<SvgSpanBot />);
export const IconSpanCode = IconFactory(<SvgSpanCode />);
export const IconSpanHook = IconFactory(<SvgSpanHook />);
export const IconSpanCondition = IconFactory(<SvgSpanCondition />);
export const IconSpanDatabase = IconFactory(<SvgSpanDatabase />);
export const IconSpanKnowledge = IconFactory(<SvgSpanKnowledge />);
export const IconSpanLLMCall = IconFactory(<SvgSpanLLMCall />);
export const IconpanNodeDamaged = IconFactory(<SvgSpanNodeDamaged />);
export const IconSpanPluginTool = IconFactory(<SvgSpanPluginTool />);
export const IconSpanVar = IconFactory(<SvgSpanVar />);
export const IconSpanWorkflowEnd = IconFactory(<SvgSpanWorkflowEnd />);
export const IconSpanWorkflowStart = IconFactory(<SvgSpanWorkflowStart />);
export const IconSpanCard = IconFactory(<SvgSpanCard />);
export const IconSpanMessage = IconFactory(<SvgSpanMessage />);

export const IconSpanWorkflow = IconFactory(<SvgSpanWorkflow />);
export const IconSpanUnknown = IconFactory(<SvgSpanUnknown />);
export const IconAddChildOutlined = IconFactory(<SvgAddChildOutlined />);
export const IconAutoGeneration = IconFactory(<SvgAutoGenerationIcon />);

export const IconSvgFooterDocument = IconFactory(<SvgFooterDocumentIcon />);
export const IconSvgFooterMail = IconFactory(<SvgFooterMailIcon />);
export const IconSvgFooterWeixin = IconFactory(<SvgFooterWeixinIcon />);
export const IconSvgFooterFeishu = IconFactory(<SvgFooterFeishuIcon />);
export const IconSvgFooterDiscord = IconFactory(<SvgFooterDiscordIcon />);

export const IconSvgBotStoreUser = IconFactory(<SvgBotStoreUser />);
export const IconSvgBotStoreConversaion = IconFactory(
  <SvgBotStoreConversaion />,
);
export const IconSvgBotStoreLink = IconFactory(<SvgBotStoreLink />);
export const IconSvgIconBotStoreQuestion = IconFactory(
  <SvgIconBotStoreQuestion />,
);
export const IconHand = IconFactory(<SvgHandIcon />);
export const IconHelp = IconFactory(<SvgHelpIcon />);
export const IconOpenTranslate = IconFactory(<SvgOpenTranslateIcon />);
export const IconExpandOutlined = IconFactory(<SvgExpandOutlined />);
export const IconAiStopGen = IconFactory(<SvgAiStopGenIcon />);
export const IconViewDiff = IconFactory(<SvgDiff />);
export const IconYesFilled = IconFactory(<SvgYesFilled />);
export const IconSideFoldOutlined = IconFactory(<SVGIconSideFoldOutlined />);

export const IconSvgCozeTxtEn = IconFactory(<SvgCozeTxtEn />);
export const IconSvgCozeTxtCn = IconFactory(<SvgCozeTxtCn />);
export const IconSvgCozeLogo = IconFactory(<SvgCozeLogo />);

export const IconInvisible = IconFactory(<SvgInvisible />);
export const IconVisible = IconFactory(<SvgVisible />);
export const IconMenu = IconFactory(<SvgMenuIcon />);
export const IconChevronRight = IconFactory(<SvgChevronRightIcon />);
export const IconChevronDown = IconFactory(<SvgChevronDownIcon />);
export const IconSvgListFilter = IconFactory(<SvgListFilter />);
export const IconStoreListMore = IconFactory(<SvgStoreListMoreIcon />);
export const IconSvgSceneListEmpty = IconFactory(<SvgSceneListEmptyIcon />);

export const IconPhone = IconFactory(<SvgPhoneIcon />);
export const IconPC = IconFactory(<SvgPCIcon />);
export const IconIntelligent = IconFactory(<SvgIntelligentIcon />);
export const IconStandardRefresh = IconFactory(<SvgStandardRefresh />);
export const IconStandardCopy = IconFactory(<SvgStandardCopy />);
export const IconArrowDownFill = IconFactory(<SvgArrowDownFill />);
export const IconBlod = IconFactory(<SvgBold />);
export const IconHn = IconFactory(<SvgHn />);
export const IconItalic = IconFactory(<SvgItalic />);
export const IconStrikethrough = IconFactory(<SvgStrikethrough />);
export const IconListDisorder = IconFactory(<SvgListDisorder />);
export const IconListOrder = IconFactory(<SvglistOrder />);
export const IconQuotation = IconFactory(<SvgQuotation />);
export const IconLink = IconFactory(<SvgLink />);
export const IconImage = IconFactory(<SvgImage />);
export const IconH1 = IconFactory(<SvgH1 />);
export const IconH2 = IconFactory(<SvgH2 />);
export const IconH3 = IconFactory(<SvgH3 />);
export const IconEmojiOutlined = IconFactory(<SvgEmojiOutlined />);
export const IconReplyOutlined = IconFactory(<SvgReplyOutlined />);
export const IconMinimizeOutlined = IconFactory(<SvgMinimizeOutlined />);
export const IconBrace = IconFactory(<SvgBrace />);
export const IconPost = IconFactory(<SvgPost />);
export const IconKnowledgeDocAvatar = IconFactory(<SvgKnowledgeDocAvatar />);
export const IconKnowledgeTableAvatar = IconFactory(
  <SvgKnowledgeTableAvatar />,
);
export const IconKnowledgeArrow = IconFactory(<SvgArrowKnowledgeIcon />);

export const IconKnowledgeImgAvatar = IconFactory(<SvgKnowledgeImgAvatar />);

export const IconExampleInvalid = IconFactory(<SvgExampleInvalidIcon />);
export const IconExampleNone = IconFactory(<SvgExampleNoneIcon />);
export const IconExampleNormal = IconFactory(<SvgExampleNormalIcon />);
export const IconAnalyticsGlobal = IconFactory(<SvgAnalyticsGlobalIcon />);
export const IconAnalyticsRegional = IconFactory(<SvgAnalyticsRegionalIcon />);
export const IconAnalyticsRanking = IconFactory(<SvgAnalyticsRankingIcon />);

export const IconShortcutEdit = IconFactory(<SvgShortcutEdit />);
export const IconShortcutTrash = IconFactory(<SvgShortcutTrash />);
export const IconShortcutDisorder = IconFactory(<SvgShortcutDisorder />);
export const IconTemplateShortcut = IconFactory(<SvgTemplateShortcut />);
export const IconTemplateShortcutTransparency = IconFactory(
  <SvgTemplateShortcutTransparency />,
);
export const IconShortcutTemplateClose = IconFactory(
  <SvgTemplateShortcutClose />,
);
export const IconLoadMoreShortcuts = IconFactory(<SvgLoadMoreShortcuts />);
export const IconShortcutComponentTag = IconFactory(
  <SvgShortcutComponentTag />,
);
export const IconSvgShortcutDrag = IconFactory(<SvgShortcutDrag />);

export const IconMar = IconFactory(<SvgMarIcon />);
export const IconMarColor = IconFactory(<SvgMarColorIcon />);

export const IconSortDescend = IconFactory(<SvgSortDescendIcon />);
export const IconSortAscend = IconFactory(<SvgSortAscendIcon />);
export const IconSortDefault = IconFactory(<SvgSortDefaultIcon />);

export const IconWeChatColorful = IconFactory(<SvgWeChatColorful />);
export const IconWeiboColorful = IconFactory(<SvgWeiboColorful />);
export const IconQzoneColorful = IconFactory(<SvgQzoneColorful />);
export const IconJuejinColorful = IconFactory(<SvgJuejinColorful />);
export const IconXColorful = IconFactory(<SvgXColorful />);
export const IconRedditColorful = IconFactory(<SvgRedditColorful />);
export const IconImageColorful = IconFactory(<SvgImageColorful />);
export const IconStoreMenuWorkflow = IconFactory(<SvgStoreMenuWorkflow />);
export const IconStoreMenuWorkflowSelected = IconFactory(
  <SvgStoreMenuWorkflowSelected />,
);

export const IconCozeEN = IconFactory(<SvgCozeENIcon />);
export const IconCozeZH = IconFactory(<SvgCozeZHIcon />);
export const IconSvgBitableFormOutlined = IconFactory(
  <SvgBitableFormOutlined />,
);
export const IconCozOfficialFill = IconFactory(<SvgCozOfficialFill />);
export const IconConnect = IconFactory(<SvgConnect />);

export const IconSvgReadInfoOutlined = IconFactory(<SvgReadInfoOutlined />);
export const IconLtmEdit = IconFactory(<SvgLtmEditIcon />);
export const IconDatabaseTab = IconFactory(<SvgDatabaseTabIcon />);
export const IconLtmTab = IconFactory(<SvgLtmTabIcon />);
export const IconVariableTab = IconFactory(<SvgVariableTabIcon />);
export const IconMemoryDownMenu = IconFactory(<SvgMemoryDownMenuIcon />);
export const IconBotStoreMemoryMenu = IconFactory(<SvgBotstoreMemoryIcon />);
export const IconUpOutlinedBold = IconFactory(<SvgUpOutlinedBold />);
export const IconSvgAddIcon = IconFactory(<SvgAddIcon />);
export const IconKnowledgeEditOutlined = IconFactory(<SvgKnowledgeEdit />);
export const IconCozeCross = IconFactory(<SvgCozeCross />);
export const IconLocalPlugin = IconFactory(<SvgLocalPluginIcon />);
export const IconDocLinkOutlined = IconFactory(<SvgDocLinkOutlinedIcon />);
export const IconDocRefreshOutlined = IconFactory(
  <SvgDocRefreshOutlinedIcon />,
);
export const IconKnowledgeSettingOutlined = IconFactory(
  <SvgKnowledgeSettingOutlinedIcon />,
);
export const IconViewContentOutlined = IconFactory(
  <SvgViewContentOutlinedIcon />,
);
export const IconDragOutlined = IconFactory(<SvgDragOutlined />);

export const IconFileBox = IconFactory(<SvgFileBox />);
export const IconLinkOutlined = IconFactory(<SvgFileLinkOutlined />);
export const IconAddEntryBottom = IconFactory(<SvgAddEntryBottomOutlined />);
export const IconAddEntryTop = IconFactory(<SvgAddEntryTopOutlined />);

export const IconSettingOutlined = IconFactory(<SvgHotIconSettingOutlined />);
export const IconDocumentCode = IconFactory(<SvgDocumentCode />);
export const IconCozArrowDown = IconFactory(<SvgCozArrowDownIcon />);
export const IconCozArrowDownLeft = IconFactory(<SvgCozArrowDownLeftIcon />);
export const IconDiamondFill = IconFactory(<SvgDiamondFill />);
export const IconCheckMark = IconFactory(<SvgCozCheckMark />);
