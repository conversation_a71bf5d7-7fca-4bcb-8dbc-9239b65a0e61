{"name": "@coze-arch/bot-icons", "version": "0.0.1", "description": "Wrapper components for semi", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@douyinfe/semi-icons": "^2.36.0", "react": "~18.2.0", "react-dom": "~18.2.0"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "vitest": "~3.0.5"}}