.frame {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(173deg, #ecf4ff -0.79%, #d3e1ff 94.5%);

  .brand {
    position: absolute;
    top: 16px;
    left: 24px;
    display: flex;
    align-items: center;
    color: #000;
    font-size: 24px;
    font-weight: 600;
    line-height: 24px;

    .img {
      padding-right: 12px;

      >img {
        height: 32px;
        width: 32px;
      }
    }
  }
}

.panel {
  width: 482px;
  height: 642px;
  padding: 40px 64px;

  border-radius: 12px;
  border: 1px solid rgba(28, 31, 35, 0.08);
  background: #fff;

  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1),
    0px 0px 1px 0px rgba(0, 0, 0, 0.3);
}

.mobile-frame {
  position: relative;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mobile-panel {
  height: 100%;
  width: 100%;
  padding: 4.88rem 2.5rem 1.88rem;
  background: #fff;
}
