/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { Ref, forwardRef } from 'react';

import cs from 'classnames';
import type { InputProps } from '@douyinfe/semi-ui/lib/es/input';
import { Input as SemiInput } from '@douyinfe/semi-ui';

import s from './index.module.less';

export const Input = forwardRef(
  ({ className, ...props }: InputProps, ref: Ref<HTMLInputElement>) => (
    <SemiInput className={cs(className, s['ui-input'])} {...props} ref={ref} />
  ),
);
