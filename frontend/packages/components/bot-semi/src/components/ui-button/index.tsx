/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type Ref, forwardRef } from 'react';

import cs from 'classnames';
import type { ButtonProps as SemiButtonProps } from '@douyinfe/semi-ui/lib/es/button';
import { Button as SemiButton } from '@douyinfe/semi-ui';

import s from './index.module.less';

export type UIButtonProps = SemiButtonProps;

export const Button = forwardRef(
  ({ className, ...props }: SemiButtonProps, ref: Ref<SemiButton>) => (
    <SemiButton
      {...props}
      className={cs(
        className,
        s.button,
        props.theme !== 'borderless' && s['button-min-width'],
        props.size === 'small' && s['button-size-small'],
        props.size === 'default' && s['button-size-default'],
      )}
      ref={ref}
    />
  ),
);

export type Button = SemiButton;
