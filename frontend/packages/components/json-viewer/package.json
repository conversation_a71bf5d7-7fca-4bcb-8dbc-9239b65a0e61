{"name": "@coze-common/json-viewer", "version": "0.0.1", "description": "通用的 json viewer 组件", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-semi": "workspace:*", "@coze-arch/i18n": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "classnames": "^2.3.2", "lodash-es": "^4.17.21", "use-context-selector": "^1.4.1"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "bignumber.js": "~9.0.0", "react": "~18.2.0", "react-dom": "~18.2.0", "scheduler": ">=0.19.0", "stylelint": "^15.11.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}