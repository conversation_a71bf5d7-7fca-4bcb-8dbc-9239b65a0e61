/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export function PadIcon() {
  return (
    <svg
      width="48"
      height="38"
      viewBox="0 0 48 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="1.83317"
        y="1.49998"
        width="44.3333"
        height="35"
        rx="3.5"
        stroke="currentColor"
        stroke-opacity="0.8"
        stroke-width="2.33333"
      />
      <path
        d="M14.6665 30.6667H33.3332"
        stroke="currentColor"
        stroke-opacity="0.8"
        stroke-width="2.33333"
        stroke-linecap="round"
      />
    </svg>
  );
}
