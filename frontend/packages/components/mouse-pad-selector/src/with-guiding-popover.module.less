.guiding-content {
  width: 277px;
  padding: 16px;
  box-sizing: border-box;

  &-title {
    font-weight: 600
  }

  &-desc {
    font-size: 12px;
    color: rgba(29, 28, 35, 0.6);
  }

  &-button {
    width: 100%;
    height: 32px;
    border-radius: 8px;
    background-color: rgba(77, 83, 232, 1);
  }

  &-mouse-option {
    display: flex;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 16px;

    &-icon {
      width: 32px;
      margin-right: 4px;
      transform: scale(0.577) translate(-5px, 0);
      color: rgba(77, 83, 232, 1);
      text-align: center;

    }
  }

  &-pad-option {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    &-icon {
      width: 32px;
      margin-right: 4px;
      transform: scale(0.577) translate(-13px, 0);
      color: rgba(62, 194, 84, 1);
      text-align: center;
    }
  }
}
