/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { ConfigEntity } from '@flowgram-adapter/fixed-layout-editor';

interface CustomRenderState {
  version: number;
}
/**
 * 渲染相关的全局状态管理
 */
export class CustomRenderStateEntity extends ConfigEntity<CustomRenderState> {
  static type = 'CustomRenderStateEntity';

  private _localVersion = 0;

  getDefaultConfig() {
    return {
      version: 0,
    };
  }

  private bumpVersion() {
    this._localVersion = this._localVersion + 1;
    if (this._localVersion === Number.MAX_SAFE_INTEGER) {
      this._localVersion = 0;
    }
  }

  updateVersion() {
    this.bumpVersion();
    this.updateConfig({
      version: this._localVersion,
    });
  }
}
