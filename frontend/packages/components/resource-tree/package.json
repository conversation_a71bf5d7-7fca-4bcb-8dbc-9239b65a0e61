{"name": "@coze-common/resource-tree", "version": "0.0.1", "description": "resource tree of workflow", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-common/mouse-pad-selector": "workspace:*", "@flowgram-adapter/common": "workspace:*", "@flowgram-adapter/fixed-layout-editor": "workspace:*", "@flowgram-adapter/free-layout-editor": "workspace:*", "bezier-js": "^6.1.4", "classnames": "^2.3.2", "inversify": "^6.0.1", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/bezier-js": "4.1.3", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "react-is": ">= 16.8.0", "styled-components": ">=4", "stylelint": "^15.11.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}