/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { useIDEContainer } from './use-ide-container';

export const IDERendererProvider = Symbol('IDERendererProvider');

export type IDERendererProvider = (props: {
  className?: string;
}) => React.ReactElement<any, any> | null;

export const IDERenderer: React.FC<{ className?: string }> = ({
  className,
}: {
  className?: string;
}) => {
  const container = useIDEContainer();
  const RendererProvider =
    container.get<IDERendererProvider>(IDERendererProvider)!;
  return <RendererProvider className={className} />;
};
