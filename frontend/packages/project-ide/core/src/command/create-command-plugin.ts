/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { pick } from '@flowgram-adapter/common';
import {
  CommandContainerModule,
  type Command,
  type CommandHandler,
  CommandRegistry,
} from '@flowgram-adapter/common';

import { definePluginCreator } from '../common';

export interface CommandPluginOptions {
  commands?: (Command & Partial<CommandHandler>)[];
}

export const createCommandPlugin = definePluginCreator<CommandPluginOptions>({
  onInit: (ctx, options) => {
    const command = ctx.get<CommandRegistry>(CommandRegistry);
    command.init();
    (options.commands || []).forEach(cmd => {
      command.registerCommand(
        pick(cmd, ['id', 'label', 'icon', 'category']),
        cmd.execute
          ? pick(cmd, ['execute', 'isEnabled', 'isVisible', 'isToggled'])
          : undefined,
      );
    });
  },
  containerModules: [CommandContainerModule],
});
