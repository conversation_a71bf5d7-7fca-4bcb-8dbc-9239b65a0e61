/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { describe, it, expect } from 'vitest';

import { URI } from './uri';

describe('uri', () => {
  it('toString', () => {
    const uris = [
      'https://www.abc.com/',
      'file:///root/abc',
      'file:///root/abc?query=1',
      'file:///root/abc?query=1#fragment',
      'abc:///root/abc',
      'abc:///project/:projectId/job/:jobId',
    ];
    for (const uriStr of uris) {
      expect(new URI(uriStr).toString()).toEqual(uriStr);
    }
  });
});
