/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { ContainerModule } from 'inversify';
import { bindContributionProvider } from '@flowgram-adapter/common';

import { LifecycleContribution } from '../common';
import { ShortcutsContribution, ShortcutsService } from './shortcuts-service';
import { KeybindingRegistry } from './keybinding';

export const ShortcutsContainerModule = new ContainerModule(bind => {
  bindContributionProvider(bind, ShortcutsContribution);

  bind(KeybindingRegistry).toSelf().inSingletonScope();
  bind(LifecycleContribution).toService(KeybindingRegistry);

  bind(ShortcutsService).toSelf().inSingletonScope();
  bind(LifecycleContribution).toService(ShortcutsService);
});
