/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { ContainerModule } from 'inversify';
import { bindContributionProvider, bindContributions } from '@flowgram-adapter/common';

import { LifecycleContribution } from '../common';
import { EventRegistry } from './event-registry';
import { EventContribution, EventService } from './event-contribution';

export const EventContainerModule = new ContainerModule(bind => {
  bindContributionProvider(bind, EventContribution);
  bind(EventService).toService(EventRegistry);
  bindContributions(bind, EventRegistry, [LifecycleContribution]);
});
