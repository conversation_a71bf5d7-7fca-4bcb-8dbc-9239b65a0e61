{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "stories", "vitest.config.ts", "vitest.setup.ts", "tailwind.config.ts", "../pages/src/flow-trial/store-preview"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDirs": ["./"], "outDir": "./dist", "types": ["vitest/globals", "@testing-library/jest-dom"], "paths": {"@/*": ["./src/*"]}, "rootDir": "./"}}