{"name": "@coze-community/components", "version": "0.0.1", "description": "business components for community", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts"}, "main": "src/index.ts", "unpkg": "./dist/umd/index.js", "module": "./dist/esm/index.js", "types": "./src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/responsive-kit": "workspace:*", "@coze-common/coze-mitt": "workspace:*", "@coze-foundation/space-store": "workspace:*", "@coze-studio/api-schema": "workspace:*", "@coze-studio/components": "workspace:*", "@douyinfe/semi-illustrations": "^2.36.0", "ahooks": "^3.7.8", "classnames": "^2.3.2"}, "devDependencies": {"@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/tea": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@mui/material": "^5", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-json": "~6.0.0", "@rollup/plugin-node-resolve": "~15.0.1", "@rollup/plugin-replace": "^4.0.0", "@rsbuild/core": "1.1.13", "@swc/core": "^1.3.35", "@swc/helpers": "^0.4.12", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/dompurify": "3.0.5", "@types/lodash-es": "^4.17.10", "@types/prismjs": "1.26.3", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-helmet": "^6.1.11", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.16", "core-js": "^3.37.1", "danmu.js": "^0.5.0", "debug": "^4.3.4", "less": "^3.13.1", "less-loader": "~11.1.3", "postcss": "^8.4.32", "react": "~18.2.0", "react-dom": "~18.2.0", "react-helmet": "^6.1.0", "react-is": ">= 16.8.0", "react-router-dom": "^6.22.0", "rollup": "^4.9.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-node-externals": "^6.1.2", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-ts": "^3.1.1", "scheduler": ">=0.19.0", "styled-components": ">= 2", "tailwindcss": "~3.3.3", "typescript": "~5.8.2", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "webpack": "~5.91.0", "xgplayer-subtitles": "^1.0.21"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}