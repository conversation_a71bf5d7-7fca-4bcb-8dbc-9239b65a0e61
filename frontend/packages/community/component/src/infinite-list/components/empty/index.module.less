.empty {
  overflow: visible;
  height: 100%;



  .spin {
    display: block;
    width: 100%;
    height: 100%;

    :global {
      .semi-spin-wrapper {
        position: absolute;
        display: flex;
        justify-content: center;

        svg {
          width: 24px;
          height: 24px;
        }
      }



      .semi-tabs-content {
        padding: 0;
      }

      .semi-spin-children {
        height: 100%;
      }
    }

    .loading-text {
      margin-left: 8px;

      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      color: var(--semi-color-text-3, rgba(29, 28, 35, 35%));
    }
  }
}
