/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import cls from 'classnames';
import { IconButton } from '@coze-arch/coze-design';
import { IconCollectFilled, IconCollectStroked } from '@coze-arch/bot-icons';

import styles from './index.module.less';

export const FavoriteIcon = (props: {
  isFavorite?: boolean;
  isShowAni: boolean;
  unCollectedIconCls?: string;
  isMobile?: boolean;
  useButton?: boolean;
  className?: string;
}) => {
  const { isFavorite, isShowAni, className, unCollectedIconCls, useButton } =
    props;

  const iconProps = {
    className: cls(
      isFavorite ? styles['icon-filled'] : styles['icon-stroked'],
      isFavorite ? className : unCollectedIconCls,
      {
        [styles['show-ani']]: isFavorite && isShowAni,
        [styles['show-btn']]: useButton,
      },
    ),
  };

  const icon = isFavorite ? (
    <IconCollectFilled {...iconProps} />
  ) : (
    <IconCollectStroked {...iconProps} />
  );

  if (useButton) {
    return <IconButton size="default" color="primary" icon={icon} />;
  }

  return icon;
};
