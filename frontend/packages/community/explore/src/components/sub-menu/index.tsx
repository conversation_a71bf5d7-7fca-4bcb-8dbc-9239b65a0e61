/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { useNavigate } from 'react-router-dom';

import { SubMenuItem } from '@coze-community/components';
import { I18n } from '@coze-arch/i18n';
import {
  IconCozTemplate,
  IconCozTemplateFill,
  IconCozPlugin,
  IconCozPluginFill,
  IconCozDocument,
  IconCozDocumentFill,
} from '@coze-arch/coze-design/icons';
import { Space } from '@coze-arch/coze-design';

import { useExploreRoute } from '../../hooks/use-explore-route';

const getMenuConfig = () => [
  {
    type: 'plugin',
    icon: <IconCozPlugin />,
    activeIcon: <IconCozPluginFill />,
    title: I18n.t('Plugins'),
    isActive: true,
    path: '/explore/plugin',
  },
  {
    icon: <IconCozTemplate />,
    activeIcon: <IconCozTemplateFill />,
    title: I18n.t('template_name'),
    isActive: true,
    type: 'template',
    path: '/explore/template',
  },
  {
    icon: <IconCozDocument />,
    activeIcon: <IconCozDocumentFill />,
    title: I18n.t('navigation_workspace_task', {}, 'Task Management'),
    isActive: true,
    type: 'task',
    path: '/explore/task',
  },
];

export const ExploreSubMenu = () => {
  const navigate = useNavigate();
  const { type } = useExploreRoute();
  const menuConfig = getMenuConfig();
  return (
    <Space spacing={4} vertical>
      {menuConfig.map(item => (
        <SubMenuItem
          {...item}
          isActive={item.type === type}
          onClick={() => {
            navigate(item.path);
          }}
        />
      ))}
    </Space>
  );
};
