/* stylelint-disable declaration-no-important */
@import '@coze-common/assets/style/common.less';
@import '@coze-common/assets/style/mixins.less';

.api-table {
  /* 133.333% */
  table-layout: fixed;
  border-collapse: collapse;

  width: 100%;

  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  color: var(--light-usage-text-color-text-2, rgb(28 31 35 / 60%));
  word-wrap: break-word;

  thead {
    margin-bottom: 4px;

    th {
      height: 26px;
      padding: 0;

      font-size: 12px;
      font-weight: 400;
      line-height: 16px; // 133.333%
      color: var(--light-usage-text-color-text-2, rgb(28 31 35 / 60%));
      text-align: left;
    }
  }

  .api-row {
    td {
      overflow: hidden;
      margin-bottom: 4px;
      // vertical-align: text-top;
    }

    &.border-top {
      cursor: pointer;
      position: relative;

      td {
        margin-top: 8px;
      }
    }

    :global {
      .semi-tag-grey-light {
        background: var(--light-color-white-white, #fff);
        border-radius: 6px;
      }

      .semi-tag-content {
        align-items: center;
        justify-content: space-between;
      }
    }
  }

  .api-method {
    justify-content: space-between;
    width: 100%;
    height: 100%;
    text-align: center;

    span {
      color: var(--light-color-grey-grey-5, rgb(107 107 117 / 100%));

    }

    .icon-copy {
      cursor: pointer;
      .common-svg-icon(16px, rgba(107, 109, 117, 1));
    }
  }

  .api-method-read {
    justify-content: flex-end;
  }

  .api-name,
  .api-desc {
    width: 126px;
    padding-right: 4px;

    font-size: 12px;
    font-weight: 400;
    font-style: normal;
    line-height: 16px; // 133.333%
    color: rgb(29 28 35 / 80%);
  }

  .icon-tips {
    cursor: pointer;
    .common-svg-icon(14px, rgba(107, 109, 117, 1));
  }
}

// 能力模块默认说明文案样式
.tip-text {
  font-size: 14px;
  font-weight: 400;
  font-style: normal;
  line-height: 22px;
  color: var(--light-usage-text-color-text-2, rgb(28 29 35 / 60%));
}

.icon-button-16 {
  cursor: pointer;

  &:hover {
    border-radius: 4px;
  }

  :global {
    .semi-button {
      &.semi-button-size-small {
        height: 16px;
        padding: 1px !important;

        svg {
           @apply text-foreground-2;
        }
      }
    }
  }
}

.default-text {
  .tip-text;
}

// .workflow-modal {}
