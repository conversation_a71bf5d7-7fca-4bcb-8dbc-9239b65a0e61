{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-env/tsconfig.build.json"}, {"path": "../../arch/bot-error/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/idl/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../../arch/report-events/tsconfig.build.json"}, {"path": "../../arch/report-tti/tsconfig.build.json"}, {"path": "../chat-answer-action-adapter/tsconfig.build.json"}, {"path": "../chat-area-plugin-debug-common/tsconfig.build.json"}, {"path": "../chat-components-adapter/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-answer-action/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-area-plugin-reasoning/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-area/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-core/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-uikit/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-workflow-render/tsconfig.build.json"}, {"path": "../../common/chat-area/plugin-chat-background/tsconfig.build.json"}, {"path": "../../common/chat-area/plugin-chat-shortcuts/tsconfig.build.json"}, {"path": "../../common/chat-area/plugin-message-grab/tsconfig.build.json"}, {"path": "../../common/chat-area/plugin-resume/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../onboarding/tsconfig.build.json"}, {"path": "../space-bot/tsconfig.build.json"}, {"path": "../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../../studio/user-store/tsconfig.build.json"}]}