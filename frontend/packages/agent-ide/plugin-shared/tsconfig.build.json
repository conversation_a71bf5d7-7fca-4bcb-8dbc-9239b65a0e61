{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "target": "ES2020", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo", "noImplicitAny": true, "strictNullChecks": true}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/report-events/tsconfig.build.json"}, {"path": "../bot-plugin/tools/tsconfig.build.json"}, {"path": "../../common/assets/tsconfig.build.json"}, {"path": "../../common/flowgram-adapter/free-layout-editor/tsconfig.build.json"}, {"path": "../../community/component/tsconfig.build.json"}, {"path": "../../components/bot-icons/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../foundation/space-store/tsconfig.build.json"}, {"path": "../../studio/bot-utils/tsconfig.build.json"}, {"path": "../../studio/components/tsconfig.build.json"}, {"path": "../../studio/stores/bot-detail/tsconfig.build.json"}]}