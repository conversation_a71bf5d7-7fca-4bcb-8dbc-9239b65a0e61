/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export type IEventCenterEventName = EventCenterEventName | string;

/**
 * 事件中心内置的事件
 */
export const enum EventCenterEventName {
  /**
   * 插件初始化后的事件名
   */
  AbilityInitialed = 'abilityInitialed',
  /**
   * 折叠展开ContentBlock的事件
   */
  ToggleContentBlock = 'toggleContentBlock',
  /**
   * Agent Modal中tab切换的事件
   */
  AgentModalTabChange = 'agentModalTabChange',
  /**
   * Agent Modal中显隐发生变化
   */
  AgentModalVisibleChange = 'agentModalVisibleChange',
}
