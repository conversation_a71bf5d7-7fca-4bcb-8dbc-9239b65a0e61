/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type FC } from 'react';

import { useBotDetailIsReadonly } from '@coze-studio/bot-detail-store';
import { IconCozEdit, IconCozPlus } from '@coze-arch/coze-design/icons';
import { IconButton } from '@coze-arch/coze-design';

import { ToolTooltip } from '../tool-tooltip';
import { type ToolButtonCommonProps } from '../../typings/button';

interface AddButtonProps extends ToolButtonCommonProps {
  iconName?: 'add' | 'edit';
  enableAutoHidden?: boolean;
}

export const AddButton: FC<AddButtonProps> = ({
  onClick,
  tooltips,
  disabled,
  loading,
  iconName = 'add',
  enableAutoHidden,
  ...restProps
}) => {
  const readonly = useBotDetailIsReadonly();

  if (readonly && enableAutoHidden) {
    return null;
  }

  return (
    <ToolTooltip content={tooltips}>
      <div>
        <IconButton
          icon={
            iconName === 'add' ? (
              <IconCozPlus className="text-base coz-fg-secondary" />
            ) : (
              <IconCozEdit className="text-base coz-fg-secondary" />
            )
          }
          loading={loading}
          onClick={onClick}
          size="small"
          color="secondary"
          disabled={!!disabled}
          data-testid={restProps['data-testid']}
        />
      </div>
    </ToolTooltip>
  );
};
