/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect, type FC } from 'react';

import { useIsAllToolHidden } from '../../hooks/public/container/use-tool-all-hidden';
import { useAbilityAreaContext } from '../../context/ability-area-context';

type IProps = Record<string, unknown>;

export const InvisibleToolController: FC<IProps> = () => {
  const isAllToolHidden = useIsAllToolHidden();

  const { eventCallbacks, store } = useAbilityAreaContext();
  const { isInitialed } = store.useToolAreaStore();

  useEffect(() => {
    if (!isInitialed) {
      return;
    }
    eventCallbacks?.onAllToolHiddenStatusChange?.(isAllToolHidden);
  }, [isAllToolHidden, isInitialed]);

  return null;
};
