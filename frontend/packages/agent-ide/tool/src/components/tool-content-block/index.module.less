@common-box-shadow: 0px 2px 8px 0px rgba(31, 35, 41, 0.02),
  0px 2px 4px 0px rgba(31, 35, 41, 0.02), 0px 2px 2px 0px rgba(31, 35, 41, 0.02);

.common-svg-icon(@size: 14px) {
  >svg {
    width: @size;
    height: @size;

    @apply coz-fg-secondary;
  }
}

.content-block {
  width: 100%;
  // border-radius: 8px;
  // background-color: #fff;
  // box-shadow: (@common-box-shadow);
  // margin-bottom: 4px;
  border-bottom: 1px solid theme('colors.stroke.5');

  &.isOpen {
    border-bottom: 1px solid theme('colors.stroke.5') !important;
  }

  &:hover {
    border-bottom: 1px solid transparent;
  }

  &.left {
    .header-content {
      padding: 0 12px;
    }

    .content {
      padding: 8px;
    }

    .content-old {
      padding: 4px 28px;
    }
  }

  &.center {
    .header-content {
      padding: 0 7px;
    }

    .content {
      padding: 4px 0 16px;
    }

    .content-old {
      padding: 8px 8px 12px;
    }
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    width: 100%;
    height: 40px;

    border-radius: 3px;

    &.collapsible {
      &:hover {
        @apply coz-mg-secondary-hovered;
      }

      &:active {
        @apply coz-mg-secondary-pressed;
      }

      .header {
        @apply coz-fg-primary;

        cursor: pointer;
      }
    }

    .header-icon-arrow {
      display: flex;
      align-items: center;
      justify-content: center;

      margin-right: 8px;
      padding: 1px;

      border-radius: 4px;

      :global {
        .semi-icon {
          svg {
            font-size: 14px;

            @apply coz-fg-secondary;
          }
        }
      }
    }

    .header-icon {
      display: flex;
      margin-right: 8px;

      >img {
        width: 16px;
        height: 16px;
      }
    }

    .header {
      display: flex;
      flex: 1 1;
      align-items: center;

      height: 100%;

      font-size: 14px;
      font-weight: 600;
      line-height: 20px;


      .icon {
        margin-left: 8px;
        .common-svg-icon(16px)
      }
    }

    .action-button {
      display: flex;
      align-items: center;
    }

    .setting {
      // margin-right: 10px;
    }
  }

  .content {
    // border-top: 1px solid #efefef;
    overflow: auto;
    height: calc(100% - 48px);
  }

  :global {
    .semi-collapsible-wrapper {
      padding-left: 0 !important;
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;
      box-shadow: none;
    }

    .semi-button {
      svg {
        @apply coz-fg-secondary;
      }
    }
  }
}
