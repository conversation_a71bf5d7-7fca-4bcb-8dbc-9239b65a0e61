/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import {
  type FC,
  type PropsWithChildren,
  createContext,
  useContext,
} from 'react';

import { noop } from 'lodash-es';

import { type IAbilityStoreState } from '../../typings/store';

interface IAbilityStoreContext {
  state: IAbilityStoreState;
  setState: (state: IAbilityStoreState) => void;
}

const AbilityStoreContext = createContext<IAbilityStoreContext>({
  state: {},
  setState: noop,
});

export const AbilityStoreProvider: FC<
  PropsWithChildren<IAbilityStoreContext>
> = ({ children, state, setState }) => (
  <AbilityStoreContext.Provider
    value={{
      state,
      setState,
    }}
  >
    {children}
  </AbilityStoreContext.Provider>
);

export const useAbilityStoreContext = () => useContext(AbilityStoreContext);
