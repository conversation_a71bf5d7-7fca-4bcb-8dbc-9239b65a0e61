/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type AgentModalTabKey } from '@coze-agent-ide/tool-config';

import { useEvent } from '../event/use-event';
import { EventCenterEventName } from '../../typings/scoped-events';
import {
  type IAgentModalTabChangeEventParams,
  type IAgentModalVisibleChangeEventParams,
} from '../../typings/event';

/**
 * 内部使用的方法，不对外使用，用于抛出事件
 */
export const useAgentModalTriggerEvent = () => {
  const { emit } = useEvent();

  const emitTabChangeEvent = (tabKey: AgentModalTabKey) => {
    emit<IAgentModalTabChangeEventParams>(
      EventCenterEventName.AgentModalTabChange,
      { tabKey },
    );
  };

  const emitModalVisibleChangeEvent = (isVisible: boolean) => {
    emit<IAgentModalVisibleChangeEventParams>(
      EventCenterEventName.AgentModalVisibleChange,
      {
        isVisible,
      },
    );
  };

  return { emitTabChangeEvent, emitModalVisibleChangeEvent };
};
