{"name": "@coze-agent-ide/tool", "version": "0.0.1", "description": "tool hooks & components & context", "license": "Apache-2.0", "author": "liushu<PERSON><EMAIL>", "maintainers": [], "main": "src/index.ts", "unpkg": "./dist/umd/index.js", "module": "./dist/esm/index.js", "types": "./src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-agent-ide/bot-editor-context-store": "workspace:*", "@coze-agent-ide/tool-config": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-data/knowledge-modal-base": "workspace:*", "@coze-foundation/global-store": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/components": "workspace:*", "@douyinfe/semi-foundation": "2.36.1-alpha.2", "@douyinfe/semi-icons": "^2.36.0", "ahooks": "^3.7.8", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "eventemitter3": "^5.0.1", "immer": "^10.0.3", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "react-error-boundary": "^4.0.9", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/postcss-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/tailwind-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-json": "~6.0.0", "@rollup/plugin-node-resolve": "~15.0.1", "@rollup/plugin-replace": "^4.0.0", "@swc/core": "^1.3.35", "@swc/helpers": "^0.4.12", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.16", "less": "^4.2.0", "less-loader": "~11.1.3", "postcss": "^8.4.32", "react": "~18.2.0", "react-dom": "~18.2.0", "rollup": "^4.9.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-node-externals": "^6.1.2", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-ts": "^3.1.1", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "typescript": "~5.8.2", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}