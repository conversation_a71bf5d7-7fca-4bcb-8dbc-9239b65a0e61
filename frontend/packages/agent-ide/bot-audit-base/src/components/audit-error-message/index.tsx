/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import { I18n } from '@coze-arch/i18n';

import styles from './index.module.less';

export function AuditErrorMessage({
  link = '/docs/guides/content_principles',
}: {
  link?: string;
}) {
  return (
    <div className={styles['error-message']}>
      {I18n.t('audit_unsuccess_general_type', {
        link: (
          <a
            rel="noreferrer noopener"
            href={link}
            target="_blank"
            className={styles.link}
          >
            {I18n.t('audit_unsuccess_general_type_url')}
          </a>
        ),
      })}
    </div>
  );
}
