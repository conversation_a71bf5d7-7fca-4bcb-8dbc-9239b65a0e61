{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "target": "ES2020", "moduleResolution": "bundler", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../chat-area-plugin-debug-common/tsconfig.build.json"}, {"path": "../chat-area-provider/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-area-plugin-reasoning/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-area/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-core/tsconfig.build.json"}, {"path": "../../common/chat-area/hooks/tsconfig.build.json"}, {"path": "../../common/chat-area/plugin-message-grab/tsconfig.build.json"}, {"path": "../../common/chat-area/plugin-resume/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../studio/stores/bot-detail/tsconfig.build.json"}]}