{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-env/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../../arch/report-events/tsconfig.build.json"}, {"path": "../../arch/report-tti/tsconfig.build.json"}, {"path": "../bot-config-area-adapter/tsconfig.build.json"}, {"path": "../bot-editor-context-store/tsconfig.build.json"}, {"path": "../bot-plugin/entry/tsconfig.build.json"}, {"path": "../bot-plugin/export/tsconfig.build.json"}, {"path": "../chat-debug-area/tsconfig.build.json"}, {"path": "../../common/assets/tsconfig.build.json"}, {"path": "../../common/biz-components/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-answer-action/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-area/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-uikit/tsconfig.build.json"}, {"path": "../../common/chat-area/plugin-chat-shortcuts/tsconfig.build.json"}, {"path": "../../common/editor-plugins/tsconfig.build.json"}, {"path": "../../common/prompt-kit/main/tsconfig.build.json"}, {"path": "../../components/bot-icons/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../context/tsconfig.build.json"}, {"path": "../../data/knowledge/knowledge-modal-adapter/tsconfig.build.json"}, {"path": "../../data/knowledge/knowledge-modal-base/tsconfig.build.json"}, {"path": "../model-manager/tsconfig.build.json"}, {"path": "../onboarding-message-adapter/tsconfig.build.json"}, {"path": "../plugin-area-adapter/tsconfig.build.json"}, {"path": "../plugin-shared/tsconfig.build.json"}, {"path": "../prompt-adapter/tsconfig.build.json"}, {"path": "../space-bot/tsconfig.build.json"}, {"path": "../../studio/autosave/tsconfig.build.json"}, {"path": "../../studio/components/tsconfig.build.json"}, {"path": "../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../tool-config/tsconfig.build.json"}, {"path": "../tool/tsconfig.build.json"}, {"path": "../workflow-card-adapter/tsconfig.build.json"}, {"path": "../../workflow/components/tsconfig.build.json"}, {"path": "../workflow/tsconfig.build.json"}]}