.onboarding-markdown {
  display: flex;
  width: 100%;
  height: 100%;

  .edit-content {
    overflow-y: auto;
    flex-shrink: 0;
    padding: 16px 24px;
  }

  .preview-content {
    overflow-y: auto;
    display: flex;
    flex: 1;
    align-items: center;

    padding: 24px;

    background-color: #FFF;
    border-left: 1px solid rgb(6 7 9 / 10%);
  }

  .preview-content-scroll {
    align-items: flex-start;

  }
}

.opening-text {
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  height: 24px;
  margin-bottom: 8px;
}

.opening-question {
  display: flex;
  column-gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.strong-text {
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  color: rgb(6 7 9 / 96%);
}

.markdown-editor {
  box-sizing: border-box;
  width: 590px;
  height: 50%;
  min-height: 200px;
  margin-bottom: 24px;
}

.modal {
  :global(.semi-modal-content) {
    padding: 0;

    :global(.semi-modal-header) {
      margin: 0;
      padding: 24px;
      border-bottom: 1px solid rgb(6 7 9 / 10%);
    }

    :global(.semi-modal-body) {
      padding: 0;
    }
  }
}

.markdown-tag {
  cursor: pointer;
  height: 24px;
  color: #4E40E5;
  background: rgba(128, 138, 255, 20%);
}

.modal-icon {
  width: 16px;
  height: 16px;
}
