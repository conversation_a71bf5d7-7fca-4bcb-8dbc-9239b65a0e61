{"name": "@coze-agent-ide/onboarding", "version": "0.0.1", "description": "Bot 编辑页面下开场白编辑等功能。", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-error": "workspace:*", "@formily/core": "^2.3.0", "@formily/react": "^2.3.0", "classnames": "^2.3.2", "immer": "^10.0.3"}, "devDependencies": {"@coze-agent-ide/bot-editor-context-store": "workspace:*", "@coze-agent-ide/tool": "workspace:*", "@coze-agent-ide/tool-config": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/bot-utils": "workspace:*", "@coze-studio/components": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "ahooks": "^3.7.8", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "use-event-callback": "~0.1.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "zustand": "^4.4.7"}, "peerDependencies": {"@coze-agent-ide/tool": "workspace:*", "@coze-agent-ide/tool-config": "workspace:*", "@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@douyinfe/semi-webpack-plugin": "^2.38.1", "ahooks": "^3.7.8", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "react": ">=18.2.0", "react-dom": ">=18.2.0", "zustand": "^4.4.7"}}