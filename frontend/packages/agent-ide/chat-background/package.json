{"name": "@coze-agent-ide/chat-background", "version": "0.0.1", "description": "chat background setting", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-agent-ide/chat-background-config-content-adapter": "workspace:*", "@coze-agent-ide/chat-background-shared": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "ahooks": "^3.7.8", "classnames": "^2.3.2", "colorthief": "~2.4.0", "cropperjs": "^1.5.12", "immer": "^10.0.3", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "react-cropper": "^2.3.3", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/bot-utils": "workspace:*", "@coze-studio/components": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}