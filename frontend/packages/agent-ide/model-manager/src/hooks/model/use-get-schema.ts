/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useBotEditor } from '@coze-agent-ide/bot-editor-context-store';

import { getFixedSingleAgentSchema } from '../../utils/model/get-fixed-single-agent-schema';
import { convertModelParamsToSchema } from '../../utils/model/convert-model-params-to-schema';

export const useGetSchema = () => {
  const {
    storeSet: { useModelStore },
  } = useBotEditor();

  return ({
    currentModelId,
    isSingleAgent,
    diffType,
  }: {
    currentModelId: string;
    isSingleAgent: boolean;
    diffType?: 'prompt-diff' | 'model-diff';
  }) => {
    const { getModelById } = useModelStore.getState();

    const modelParams = getModelById(currentModelId)?.model_params ?? [];

    const schema = convertModelParamsToSchema({ model_params: modelParams });

    if (!isSingleAgent || diffType === 'model-diff') {
      return schema;
    }

    return getFixedSingleAgentSchema(schema);
  };
};
