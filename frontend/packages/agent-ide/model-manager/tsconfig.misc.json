{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "__tests__/**/*.json", "vitest.config.ts", "tailwind.config.ts"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDir": "./", "outDir": "./dist", "types": ["vitest/globals"], "strictNullChecks": true, "noImplicitAny": true, "noUncheckedIndexedAccess": true}}