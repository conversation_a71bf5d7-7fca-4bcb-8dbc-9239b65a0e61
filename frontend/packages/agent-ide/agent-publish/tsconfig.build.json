{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "target": "ES2020", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-error/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/idl/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../../arch/report-events/tsconfig.build.json"}, {"path": "../../arch/report-tti/tsconfig.build.json"}, {"path": "../commons/tsconfig.build.json"}, {"path": "../../components/bot-icons/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../foundation/layout/tsconfig.build.json"}, {"path": "../space-bot/tsconfig.build.json"}, {"path": "../../studio/components/tsconfig.build.json"}, {"path": "../../studio/open-platform/open-auth/tsconfig.build.json"}, {"path": "../../studio/premium/premium-components-adapter/tsconfig.build.json"}, {"path": "../../studio/publish-manage-hooks/tsconfig.build.json"}, {"path": "../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../../studio/user-store/tsconfig.build.json"}]}