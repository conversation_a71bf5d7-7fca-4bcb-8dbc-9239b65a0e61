/* stylelint-disable no-descending-specificity */
/* stylelint-disable declaration-no-important */
@import '@coze-common/assets/style/common.less';
@import '@coze-common/assets/style/mixins.less';

.tools-content {
  .tools-table-thead {
    padding-bottom: 6px;

    th {
      padding: var(--spacing-spacing-button-default-padding-top, 6px) 0 var(--spacing-tight, 8px) 0;
    }
  }
}

.api-table {
  table-layout: fixed;
  border-collapse: collapse;

  width: 100%;
  height: 32px;

  font-size: 12px;
  font-weight: 400;
  line-height: 16px; // 133.333%
  color: var(--light-usage-text-color-text-2, rgb(28 31 35 / 60%));
  word-wrap: break-word;

  thead {
    th {
      font-size: 12px;
      font-weight: 400;
      line-height: 16px; // 133.333%
      color: var(--light-color-grey-grey-4,
          var(--light-color-grey-grey-4, #888d92));
      text-align: left;
    }
  }

  .auto-add-api {
    animation: flash 6s linear;
    animation-delay: 300ms;

    td:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    td:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }

  @keyframes flash {
    10% {
      background: rgb(180 186 246 / 24%);
    }

    20% {
      background: unset;
    }

    30% {
      background: rgb(180 186 246 / 24%);
    }

    40% {
      background: unset;
    }

    50% {
      background: rgb(180 186 246 / 24%);
    }

    95% {
      background: rgb(180 186 246 / 24%);
    }
  }

  .api-row {
    border-bottom: 1px solid transparent;

    &.border-top {
      position: relative;

      td {
        margin-top: 4px;
      }
    }

    // &:last-child {
    //   border-bottom: 1px solid #f9f9f9;
    // }
    td {
      overflow: hidden;
      padding: 2px 4px;
    }

    &-name {
      vertical-align: middle;
    }

    :global {
      .semi-tag-grey-light {
        height: 20px;
        background: var(--light-color-white-white, #fff);
        border-radius: var(--spacing-spacing-button-default-padding-top, 6px);
      }

      .semi-tag-content {
        align-items: center;
        justify-content: space-between;
      }
    }

    &.api-disable {
      .api-plugin-name,
      .api-name-text,
      .icon-disabled,
      {
      cursor: default;
      opacity: 0.2;
    }
  }
}

.api-plugin {
  display: flex;
  align-items: center;
  height: 24px;
  padding-right: 10px;

  &-image {
    display: flex;
    flex-shrink: 0;
    margin-right: 8px;

    >img {
      width: 16px;
      height: 16px;
    }
  }

  :global {
    .semi-image-status {
      background-color: rgba(#fff, 0) !important;
    }
  }

  &-name {
    font-size: 12px;
    font-weight: 400;
    line-height: 22px;
    color: var(--light-usage-text-color-text-1, rgb(28 29 35 / 80%));
  }

  &-icon {
    margin-left: 4px;
  }
}

.api-method {
  justify-content: space-between;
  width: 100%;
  height: 100%;
  text-align: center;

  span {
    color: var(--light-color-grey-grey-5, rgb(107 107 117 / 100%));
  }

  .icon-config {
    cursor: pointer;
    color: rgb(107 109 117 / 100%);
  }
}

.api-method-read {
  justify-content: flex-end;
}

.api-name {
  display: flex;
  align-items: center;
  padding: 2px 0;
  padding-right: 10px;

  &-text {
    margin-right: 4px;

    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: rgb(29 28 35 / 80%);
  }

  :global {
    .semi-tag-grey-light {
      background: #fff !important;
    }
  }

  .api-divider {
    height: 12px;
    border-color: var(--light-color-brand-brand-2, #b3c4ff);
  }

  .copy {
    cursor: copy;
  }

  .icon-tips {
    cursor: pointer;
    .common-svg-icon(14px, rgba(107, 109, 117, 1));

    padding: 1px;
    border-radius: 4px;
  }

  &-publish {
    flex-shrink: 0;
    margin-left: 4px;
  }
}
}

.plugin-modal {
  height: 100%;

  .plugin-filter {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;

    width: 218px;

    background: #ebedf0;
  }

  :global {
    .semi-modal-content {
      padding: 0;
    }

    .semi-spin-children {
      height: 100%;
    }
  }
}

.plugin-collapse {
  :global {
    .semi-collapse-header {
      height: 120px !important;
      margin: 0 !important;
      padding: 14px 16px;

      &[aria-expanded='true'] {
        border-radius: 8px 8px 0 0 !important;
      }
    }

    .semi-collapse {
      padding: 16px 0 12px;
    }

    .semi-collapse-content {
      // background-color: #fff;
      padding: 0;
      border-radius: 0 0 8px 8px;
      // border-color: var(
      //   --light-usage-border-color-border,
      //   rgba(28, 31, 35, 0.08)
      // );
      // border-width: 0 1px 1px 1px;
      // border-style: solid;
    }

    .semi-collapse-item {
      // border: 0;
    }
  }
}

.default-text {
  .tip-text;
}

.hide-button-model-wrap {
  .ml20 {
    margin-left: 20px;
  }

  .h56 {
    height: 56px;
  }

  .search-input {
    position: absolute;
    z-index: 9;
    top: 20px;

    width: 100%;

    background: #fff;
  }
}

.plugin-func-collapse {
  .plugin-api-desc {
    cursor: pointer;
    width: 200px !important;
  }
}


.icon-button-16 {
  cursor: pointer;

  &:hover {
    border-radius: 4px;
  }

  :global {
    .semi-button {
      &.semi-button-size-small {
        height: 16px;
        padding: 1px !important;

        svg {
           @apply text-foreground-2;
        }
      }
    }
  }
}

// 能力模块默认说明文案样式
.tip-text {
  font-size: 14px;
  font-weight: 400;
  font-style: normal;
  line-height: 22px;
  color: var(--light-usage-text-color-text-2, rgb(28 29 35 / 60%));
}
