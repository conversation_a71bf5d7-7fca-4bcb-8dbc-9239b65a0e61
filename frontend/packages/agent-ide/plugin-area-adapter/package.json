{"name": "@coze-agent-ide/plugin-area-adapter", "version": "0.0.1", "description": "agent ide plugin area adapter", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-agent-ide/bot-plugin": "workspace:*", "@coze-agent-ide/plugin-content-adapter": "workspace:*", "@coze-agent-ide/plugin-risk-warning": "workspace:*", "@coze-agent-ide/tool": "workspace:*", "@coze-agent-ide/tool-config": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-common/assets": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "classnames": "^2.3.2", "lodash-es": "^4.17.21", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}