/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type ModeOption } from '@coze-agent-ide/space-bot/component';
import { I18n } from '@coze-arch/i18n';
import { IconCozWorkflow } from '@coze-arch/coze-design/icons';
import { IconSingleMode } from '@coze-arch/bot-icons';
import { BotMode } from '@coze-arch/bot-api/developer_api';

export const modeOptionList: ModeOption[] = [
  {
    value: BotMode.SingleMode,
    getIsDisabled: () => false,
    icon: <IconSingleMode />,
    title: I18n.t('singleagent_LLM_mode'),
    desc: I18n.t('singleagent_LLM_mode_desc'),
    showText: true,
  },
  {
    value: BotMode.WorkflowMode,
    getIsDisabled: () => false,
    icon: <IconCozWorkflow />,
    title: I18n.t('singleagent_workflow_mode'),
    desc: I18n.t('singleagent_workflow_mode_desc'),
    showText: true,
  },
];
