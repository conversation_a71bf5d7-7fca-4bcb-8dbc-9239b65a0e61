{"name": "@coze-agent-ide/entry-adapter", "version": "0.0.1", "description": "agent ide entry adapter", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-agent-ide/bot-creator": "workspace:*", "@coze-agent-ide/bot-creator-context": "workspace:*", "@coze-agent-ide/bot-editor-context-store": "workspace:*", "@coze-agent-ide/bot-plugin": "workspace:*", "@coze-agent-ide/bot-plugin-export": "workspace:*", "@coze-agent-ide/chat-debug-area": "workspace:*", "@coze-agent-ide/debug-tool-list": "workspace:*", "@coze-agent-ide/memory-tool-pane-adapter": "workspace:*", "@coze-agent-ide/model-manager": "workspace:*", "@coze-agent-ide/prompt": "workspace:*", "@coze-agent-ide/skills-pane-adapter": "workspace:*", "@coze-agent-ide/space-bot": "workspace:*", "@coze-agent-ide/tool": "workspace:*", "@coze-agent-ide/tool-config": "workspace:*", "@coze-agent-ide/workflow": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-arch/report-tti": "workspace:*", "@coze-common/biz-components": "workspace:*", "@coze-common/chat-answer-action": "workspace:*", "@coze-common/chat-area": "workspace:*", "@coze-common/chat-area-plugins-chat-shortcuts": "workspace:*", "@coze-common/chat-uikit": "workspace:*", "@coze-common/editor-plugins": "workspace:*", "@coze-common/prompt-kit": "workspace:*", "@coze-common/prompt-kit-base": "workspace:*", "@coze-data/knowledge-modal-base": "workspace:*", "@coze-studio/autosave": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/components": "workspace:*", "@coze-workflow/components": "workspace:*", "@douyinfe/semi-foundation": "2.36.1-alpha.2", "ahooks": "^3.7.8", "classnames": "^2.3.2", "immer": "^10.0.3", "query-string": "^8.1.0", "react-router-dom": "^6.22.0", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}