.select-container {
  display: inline-block;

  max-width: 100%;
  padding: 4px 6px;

  background-color: transparent;
  border: none;
  border-radius: 6px;

  &.switch-disabled {
    * {
      /* stylelint-disable-next-line declaration-no-important */
      color: #1D1C23CC !important;
    }
  }

  div {
    span {
      &[aria-label="small_triangle_down"] {
        color: #1D1C23CC;
      }
    }
  }
}

.option-list {
  min-width: 234px;
  max-width: 336px;
  padding: 4px;
  font-size: 12px;

  :global {
    .semi-typography {
      color: #1D1C23;

    }

    .semi-select-option-list {
      min-height: 40px;
    }

    .semi-select-loading-wrapper {
      display: flex;
      justify-content: center;
    }
  }
}

.item-selected {
  overflow: hidden;

  width: 100%;
  height: 16px;

  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  text-overflow: ellipsis;
}

.select-label {
  svg {
    >path {
      /* stylelint-disable-next-line declaration-no-important */
      fill: currentcolor !important;
      fill-opacity: 1;
    }
  }
}

.custom-option-render-focused {
  cursor: pointer;
  background-color: rgb(46 46 56 / 8%);

}

.custom-option-render-selected {
  font-weight: 600;

}

.custom-option-render-disabled {
  cursor: not-allowed;

  :global {
    .semi-typography {
      color: #1D1C2359;

    }
  }
}


.select-option-container {
  display: flex;
  align-items: center;

  height: 32px;
  padding: 8px 8px 8px 16px;

  border-radius: 4px;
}

.divider {
  height: 1px;
  margin: 4px 0;
  background: var(--semi-color-border);
}

.create-container {
  cursor: pointer;

  display: flex;
  align-items: center;

  height: 32px;
  padding: 0 16px;

  color: #4D53E8;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4D53E8;

  .spin-icon {
    margin-right: 8px;
  }


}
