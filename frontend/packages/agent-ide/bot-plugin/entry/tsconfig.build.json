{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"paths": {"@/*": ["./src/*"]}, "types": [], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../../arch/bot-md-box-adapter/tsconfig.build.json"}, {"path": "../../../arch/bot-monaco-editor/tsconfig.build.json"}, {"path": "../../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../../arch/bot-store/tsconfig.build.json"}, {"path": "../../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../../arch/foundation-sdk/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/idl/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../../../arch/report-events/tsconfig.build.json"}, {"path": "../../../arch/report-tti/tsconfig.build.json"}, {"path": "../../../common/assets/tsconfig.build.json"}, {"path": "../../../common/biz-components/tsconfig.build.json"}, {"path": "../../../common/flowgram-adapter/free-layout-editor/tsconfig.build.json"}, {"path": "../../../components/bot-icons/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../export/tsconfig.build.json"}, {"path": "../mock-set/tsconfig.build.json"}, {"path": "../../plugin-content-adapter/tsconfig.build.json"}, {"path": "../../plugin-modal-adapter/tsconfig.build.json"}, {"path": "../plugin-risk-warning/tsconfig.build.json"}, {"path": "../../plugin-shared/tsconfig.build.json"}, {"path": "../../../studio/bot-utils/tsconfig.build.json"}, {"path": "../../../studio/common/file-kit/tsconfig.build.json"}, {"path": "../../../studio/components/tsconfig.build.json"}, {"path": "../../../studio/mockset-edit-modal-adapter/tsconfig.build.json"}, {"path": "../../../studio/mockset-shared/tsconfig.build.json"}, {"path": "../../../studio/plugin-publish-ui-adapter/tsconfig.build.json"}, {"path": "../../../studio/plugin-shared/tsconfig.build.json"}, {"path": "../../../studio/plugin-tool-columns-adapter/tsconfig.build.json"}, {"path": "../../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../../../studio/stores/bot-plugin/tsconfig.build.json"}, {"path": "../../../studio/user-store/tsconfig.build.json"}, {"path": "../../tool-config/tsconfig.build.json"}, {"path": "../../tool/tsconfig.build.json"}, {"path": "../tools/tsconfig.build.json"}, {"path": "../../../workflow/base/tsconfig.build.json"}]}