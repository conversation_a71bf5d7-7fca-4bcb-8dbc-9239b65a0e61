/* stylelint-disable declaration-no-important */
/* stylelint-disable no-descending-specificity */
/* stylelint-disable max-nesting-depth */
/* stylelint-disable selector-class-pattern */
@import '@coze-common/assets/style/common.less';
@import '@coze-common/assets/style/mixins.less';

.plugin-item {
  display: flex;
  align-items: center;
  justify-content: space-between;

  box-sizing: border-box;
  width: calc(100% - 68px);
  margin: 14px 0 0 68px;
  padding-right: 12px;
  padding-bottom: 14px;

  &:not(:last-child) {
    position: relative;
    border-bottom: 1px solid var(--light-usage-border-color-border, rgba(28, 31, 35, 8%));
  }

  .plugin-api-main {
    flex: 1;

    .plugin-api-name {
      width: 100%;

      :global {
        .semi-typography {
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          color: var(--light-usage-text-color-text-0, #1c1d23) !important;
        }
      }
    }

    .plugin-api-desc {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-top: 4px;

      :global {
        .semi-typography {
          font-size: 12px;
          line-height: 16px;
          color: var(--light-usage-text-color-text-2,
              rgba(28, 31, 35, 60%)) !important;
        }
      }

      .api-params {
        display: flex;
        align-items: center;
        margin-top: 12px;

        .params-tags {
          max-width: 500px;

          .tag-item {
            width: fit-content;
            min-width: fit-content;

            font-size: 12px;
            font-weight: 500;
            font-style: normal;
            line-height: 16px;
            color: var(--light-usage-text-color-text-2, rgba(28, 29, 35, 60%));

            border-radius: 6px;

            /* 133.333% */
          }

          &> :not(:first-child) {
            margin-left: 16px;
          }
        }

        .params-desc {
          cursor: pointer;

          flex-shrink: 0;

          font-size: 12px;
          line-height: 16px;
          color: #4d53e8;
          letter-spacing: 0.12px !important;
        }
      }
    }
  }

  .plugin-api-method {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    margin-left: 20px;
  }
}

.plugin-panel-header {
  display: flex;
  flex: 1;
  flex-wrap: nowrap;
  align-items: center;

  width: 0;
  min-width: 0;
  height: 80px;

  font-weight: 400;

  .creator-icon {
    display: flex;
    flex-shrink: 0;
    border-radius: 50%;

    img {
      width: 14px;
      height: 14px;
    }
  }

  .creator-time {
    /* Paragraph/small/EN-Regular */

    font-size: 12px;
    font-weight: 400;
    font-style: normal;
    line-height: 16px;
    color: var(--light-usage-text-color-text-3, rgba(28, 29, 35, 35%));
    text-align: right;

    /* 133.333% */
    letter-spacing: 0.12px;
  }

  .header-icon {
    display: flex;
    flex-shrink: 0;

    img {
      width: 36px;
      height: 36px;
      background: #fff;
    }
  }

  .header-main {
    overflow: hidden;
    flex: 1;

    width: 0;
    min-width: 0;
    margin: 0 16px;

    // margin-top: -12px;
    .header-name {
      display: flex;
      align-items: center;
      width: 100%;

      :global {
        .semi-typography {
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          color: var(--light-usage-text-color-text-0, #1c1d23) !important;
          word-wrap: break-word !important;
        }

        .semi-highlight-tag {
          color: #fda633;
          background-color: transparent;
        }
      }

      .market-link-icon {
        display: none;
      }

    }

    .header-desc {
      width: 100%;

      :global {
        .semi-typography {
          font-size: 12px;
          font-weight: 400;
          color: var(--light-usage-text-color-text-1,
              rgba(28, 29, 35, 80%)) !important;
          letter-spacing: 0.12px;
          word-wrap: break-word !important;
        }
      }
    }
  }

  .header-info {
    /* Paragraph/small/EN-Regular */

    font-size: 12px;
    font-weight: 400;
    font-style: normal;
    line-height: 16px;
    color: var(--light-usage-text-color-text-3, rgba(28, 29, 35, 35%));
    text-align: right;

    /* 133.333% */
    :global {
      .semi-divider-vertical {
        height: 10px;
        color: var(--light-usage-border-color-border-1, rgba(28, 29, 35, 12%));
      }
    }
  }
}

.plugin-content {
  overflow: auto;
  width: 100%;
  height: 100%;
  padding-bottom: 12px;

  .loading-more {
    text-align: center;
  }

  .plugin-content-filter {
    display: flex;
    padding: 0 36px;
    padding-left: 22px;

    .plugin-content-sort {
      width: 150px;
    }

    .bot-tag {
      display: flex;
      align-items: center;
    }

    :global {
      .semi-tabs-content {
        padding: 0;
      }

      .semi-tabs-tab-button.semi-tabs-tab-active {
        color: var(--light-usage-text-color-text-1, rgba(28, 29, 35, 80%));
        background-color: transparent;

        .semi-icon {
          .common-svg-icon(20px, rgba(28, 29, 35, 0.8));
        }
      }

      .semi-tabs-tab-single.semi-tabs-tab-active .semi-icon:not(.semi-icon-checkbox_tick,
        .semi-icon-radio,
        .semi-icon-checkbox_indeterminate) {
        top: 0;
        color: var(--light-usage-text-color-text-1, rgba(28, 29, 35, 80%));
      }

      .semi-tabs-tab-single.semi-tabs-tab .semi-icon:not(.semi-icon-checkbox_tick,
        .semi-icon-radio,
        .semi-icon-checkbox_indeterminate) {
        top: 0;
      }

      .semi-tabs-tab:last-child::before {
        content: '';

        position: absolute;
        top: 12px;
        left: -4px;

        width: 1px;
        height: 16px;

        background-color: var(--light-usage-border-color-border,
            rgba(28, 29, 35, 12%));
      }
    }
  }

  .plugin-collapse {
    width: 100%;

    :global {
      .semi-collapse-item {
        position: relative;
        border: none;
      }

      .semi-collapse-header:hover::before {
        content: '';

        position: absolute;
        top: -1px;
        left: 0;

        width: 100%;
        height: 1px;

        background-color: #f7f7fa;
      }

      .semi-collapse-header {
        margin: 0;
        border-bottom: 1px solid #dfdfdf;
        border-radius: 0;

        &:hover {
          background: var(--light-usage-fill-color-fill-0,
              rgba(46, 47, 56, 5%));
          border-bottom: 1px solid transparent;
          border-radius: 8px;


        }

        &:active {
          background: var(--light-usage-fill-color-fill-0,
              rgba(46, 47, 56, 5%));
        }
      }

      .semi-collapse-header-icon {
        width: auto;
        height: 24px;

        &:hover {
          background: var(--light-usage-fill-color-fill-1,
              rgba(46, 47, 56, 9%));
          border-radius: 5px;
        }
      }
    }

    :global(.semi-collapse-header) {
      &:hover {
        .market-link-icon {
          display: block;
        }
      }
    }

    .item-container {
      padding: 0;
    }

    .collapse-icon {
      .common-svg-icon(16px, rgba(28, 29, 35, 0.35));

      cursor: pointer;
      padding: 4px;
    }

    .activePanel {
      margin-bottom: 8px;
      background: var(--light-usage-fill-color-fill-0, rgba(46, 47, 56, 5%));
      border: none;
      border-radius: 8px;

      :global {
        .semi-collapse-header {
          border-bottom: 1px solid #dfdfdf;
        }

        .semi-collapse-header:hover {
          background: transparent;
        }
      }
    }
  }
}

.plugin-content,
.plugin-collapse {
  :global {
    .semi-collapse-header {
      min-width: 870px;
      height: 140px !important;
      margin: 0 !important;
      padding: 14px 16px;

      &[aria-expanded='true'] {
        border-radius: 8px 8px 0 0 !important;
      }
    }

    .semi-collapse {
      padding: 16px 0 12px;
    }

    .semi-collapse-content {
      // background-color: #fff;
      padding: 0;
      border-radius: 0 0 8px 8px;
      // border-color: var(
      //   --light-usage-border-color-border,
      //   rgba(28, 31, 35, 0.08)
      // );
      // border-width: 0 1px 1px 1px;
      // border-style: solid;
    }

    .semi-collapse-item {
      // border: 0;
    }
  }
}

button.operator-btn {
  width: 98px;

  &.added {
    color: var(--light-usage-primary-color-primary-disabled, #b4baf6);
    background: var(--light-usage-bg-color-bg-0, #fff);
    border: 1px solid var(--light-usage-disabled-color-disabled-border, #f0f0f5);
  }

  &.addedMouseIn {
    color: var(--light-color-red-red-5, #ff441e);
    background: #fff;
    border: 1px solid var(--light-usage-border-color-border-1, rgba(29, 28, 35, 12%));
  }
}

.workflow_count_span {
  display: inline-block;

  width: 16px;
  height: 16px;
  margin-left: 6px;

  font-size: 10px;
  line-height: 17px;
  color: #fff;
  vertical-align: 1px;

  background-color: rgba(77, 83, 232, 100%);
  border-radius: 8px;
}

.store-plugin-tools {
  display: flex;
  margin-top: 8px;
  font-size: 12px;
  color: var(--light-usage-text-color-text-3, rgba(28, 29, 35, 35%));
}

.plugin-total {
  margin-top: 4px;
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--light-usage-text-color-text-3, rgba(28, 29, 35, 35%));
}
