{"name": "@coze-agent-ide/space-bot-publish-to-base", "version": "0.0.1", "description": "我受够了繁文缛节", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@blueprintjs/core": "^5.1.5", "@coze-agent-ide/bot-editor-context-store": "workspace:*", "@coze-agent-ide/bot-input-length-limit": "workspace:*", "@coze-agent-ide/chat-background": "workspace:*", "@coze-agent-ide/tool": "workspace:*", "@coze-agent-ide/tool-config": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-http": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/fetch-stream": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-arch/report-tti": "workspace:*", "@coze-common/assets": "workspace:*", "@coze-common/chat-area": "workspace:*", "@coze-data/database-creator": "workspace:*", "@coze-data/e2e": "workspace:*", "@coze-data/knowledge-modal-base": "workspace:*", "@coze-data/knowledge-resource-processor-core": "workspace:*", "@coze-data/reporter": "workspace:*", "@coze-data/utils": "workspace:*", "@coze-foundation/global-store": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/components": "workspace:*", "@coze-studio/user-store": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "@douyinfe/semi-illustrations": "^2.36.0", "ahooks": "^3.7.8", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.7", "immer": "^10.0.3", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "qs": "^6.11.2", "rc-slider": "10.6.2", "react-hotkeys-hook": "~4.5.0", "react-markdown": "^8.0.3", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/ajv": "~1.0.0", "@types/draft-js": "^0.11.12", "@types/json-schema": "~7.0.15", "@types/lodash-es": "^4.17.10", "@types/node": "^18", "@types/papaparse": "^5.3.9", "@types/qs": "^6.9.7", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-resizable": "^3.0.6", "@vitest/coverage-v8": "~3.0.5", "debug": "^4.3.4", "devcert": "1.2.2", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "utility-types": "^3.10.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}