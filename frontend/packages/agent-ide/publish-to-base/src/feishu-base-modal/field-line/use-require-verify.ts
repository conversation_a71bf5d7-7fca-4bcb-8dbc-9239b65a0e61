/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect, useState } from 'react';

import { useRequireVerifyCenter } from './require-verify-center';

export const useRequireVerify = <T>({
  getVal,
  verify,
  onChange,
}: {
  getVal: () => T;
  verify: (val: T) => boolean;
  onChange?: (isError: boolean) => void;
}) => {
  const [showWarn, setShowWarn] = useState(false);
  const { registerVerifyFn } = useRequireVerifyCenter();

  const onTrigger = () => {
    const val = getVal();
    const verified = verify(val);
    const isError = !verified;
    setShowWarn(isError);
    onChange?.(isError);
  };

  useEffect(() => {
    const unregister = registerVerifyFn(onTrigger);
    return unregister;
  }, []);

  return {
    showWarn,
    onTrigger,
  };
};
