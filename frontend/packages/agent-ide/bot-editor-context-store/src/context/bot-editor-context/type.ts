/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type OnboardingDirtyLogicCompatibilityStore } from '../../store/onboarding-dirty-logic-compatibility';
import { type NLPromptModalStore } from '../../store/nl-prompt-modal';
import { type ModelStore } from '../../store/model';
import { type FreeGrabModalHierarchyStore } from '../../store/free-grab-modal-hierarchy';
import { type DraftBotDatasetsStore } from '../../store/dataset';
import { type DraftBotPluginsStore } from '../../store/bot-plugins';

export interface StoreSet {
  useOnboardingDirtyLogicCompatibilityStore: OnboardingDirtyLogicCompatibilityStore;
  useModelStore: ModelStore;
  useDraftBotPluginsStore: DraftBotPluginsStore;
  useDraftBotDataSetStore: DraftBotDatasetsStore;
  useNLPromptModalStore: NLPromptModalStore;
  useFreeGrabModalHierarchyStore: FreeGrabModalHierarchyStore;
}

export interface BotEditorContextProps {
  storeSet: null | StoreSet;
}
