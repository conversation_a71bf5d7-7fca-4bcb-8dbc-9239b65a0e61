{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-error/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-hooks/tsconfig.build.json"}, {"path": "../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../arch/bot-store/tsconfig.build.json"}, {"path": "../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../../arch/report-events/tsconfig.build.json"}, {"path": "../bot-input-length-limit/tsconfig.build.json"}, {"path": "../bot-plugin/entry/tsconfig.build.json"}, {"path": "../chat-area-provider-adapter/tsconfig.build.json"}, {"path": "../chat-debug-area/tsconfig.build.json"}, {"path": "../../common/assets/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-area/tsconfig.build.json"}, {"path": "../../common/chat-area/chat-core/tsconfig.build.json"}, {"path": "../../common/md-editor-adapter/tsconfig.build.json"}, {"path": "../commons/tsconfig.build.json"}, {"path": "../../components/bot-icons/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../context/tsconfig.build.json"}, {"path": "../../foundation/global-store/tsconfig.build.json"}, {"path": "../../foundation/layout/tsconfig.build.json"}, {"path": "../../foundation/space-store/tsconfig.build.json"}, {"path": "../onboarding/tsconfig.build.json"}, {"path": "../space-bot/tsconfig.build.json"}, {"path": "../../studio/components/tsconfig.build.json"}, {"path": "../../studio/entity-adapter/tsconfig.build.json"}, {"path": "../../studio/publish-manage-hooks/tsconfig.build.json"}, {"path": "../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../../studio/user-store/tsconfig.build.json"}]}