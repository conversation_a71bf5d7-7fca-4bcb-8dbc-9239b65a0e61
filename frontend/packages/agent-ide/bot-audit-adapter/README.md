# @coze-studio/bot-audit-adapter

bot audit pkg

## Overview

This package is part of the Coze Studio monorepo and provides ide features functionality. It includes hook.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-studio/bot-audit-adapter": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-studio/bot-audit-adapter';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook

## API Reference

### Exports

- `botInfoAudit, useBotInfoAuditor`
- `AuditErrorMessage`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
