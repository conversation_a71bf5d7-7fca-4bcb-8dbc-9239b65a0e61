{"name": "@coze-studio/bot-audit-adapter", "version": "0.0.1", "description": "bot audit pkg", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-studio/bot-audit-base": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-space-api": "workspace:*", "@coze-arch/i18n": "workspace:*"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "sucrase": "^3.32.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}