/* stylelint-disable declaration-no-important */
@import './common.less';

.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #f7f7fa;
}

.text {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: calc(100% - 80px);
  flex: 1;
}

.spin {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 100;
  background-color: rgb(255 255 255 / 50%);
}

.playground-neat {
  .message-area {
    min-width: 258px;
  }
}

.develop-area {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.develop-area-scroll {
  overflow: auto;
  flex: 1;
}

.setting-area {
  overflow: hidden;
  display: flex;
  flex: 1 1;
  flex-direction: column;
  border-left: 1px solid rgb(28 29 35 / 12%);

  .setting-title-block {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin: 12px 0;
  }

  .setting-area-scroll {
    overflow: auto;
    flex: 1;
  }

  :global {
    .semi-collapse-item {
      border-bottom: none;
    }

    .semi-collapse-header {
      margin-left: 0;
      margin-right: 0;
    }

    .semi-collapse-content {
      padding-left: 0;
      padding-right: 0;
    }

    .semi-select {
      width: 100%;
    }
  }

  :global {
    .semi-collapsible-wrapper {
      padding-left: 16px;
    }
  }
}

.message-area {
  position: relative;
  width: 100%;
  height: 100%;
  flex-direction: column;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  min-width: 404px;
  transition: min-width 0.2s ease;
}

.playground-neat {
  .message-area {
    min-width: 258px;
  }
}

.title {
  margin: 8px 0 0 4px !important;
}

.sheet-title-node-cover {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bj-cover {
  background-color: #fff;
  box-shadow: 0 2px 4px 0 rgb(0 0 0 / 4%),
  0 0 1px 0 rgb(0 0 0 / 8%);
}

.border-cover {
  border-bottom: none;
}

.spin-wrapper.top-level {
  width: 100%;
  height: 100% !important;

  :global {
    .semi-spin-children {
      .wrapper();
    }
  }
}

.sheet-view-left-header {
  padding: 16px 28px !important;
}

.icon-button-16 {
  cursor: pointer;

  &:hover {
    border-radius: 4px;
  }

  :global {
    .semi-button {
      &.semi-button-size-small {
        padding: 1px !important;
        height: 16px;

        svg {
        @apply text-foreground-2;
        }
      }
    }
  }
}

// 能力模块默认说明文案样式
.tip-text {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  color: var(--light-usage-text-color-text-2, rgb(28 29 35 / 60%));
}
