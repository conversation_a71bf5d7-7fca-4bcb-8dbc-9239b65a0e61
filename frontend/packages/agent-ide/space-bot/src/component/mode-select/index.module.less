.font-normal {
  font-size: 12px;
  font-weight: 400;
  font-style: normal;
  line-height: 16px; /* 133.333% */

  @apply text-foreground-3;
}

.mode-change-title-space {
  margin-left: 4px !important;
  padding: 2px 8px !important;

  .mode-change-title {
    .font-normal();
  }

  .mode-change-icon {
    @apply text-foreground-3;

    margin-left: 4px;

    svg {
      width: 10px;
      height: 10px;
    }
  }
}

.mode-change-title-space:active {
  background: var(--light-usage-fill-color-fill-1, rgba(46, 46, 56, 8%));
}

.mode-change-title-space:focus {
  background: var(--light-usage-fill-color-fill-2, rgba(46, 46, 56, 12%));
}

.mode-change-popover {
  width: 455px;

  background: #f7f7fa;
  border: 1px solid
    var(--light-usage-border-color-border, rgba(28, 31, 35, 8%));
  border-radius: 12px;

  /* --shadow-elevated */
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 10%),
    0 0 1px 0 rgba(0, 0, 0, 30%);

  .mode-change-popover-content {
    padding: 16px;

    :global {
      .semi-radio {
        border: 1px solid
          var(--light-usage-border-color-border, rgba(29, 28, 35, 8%));
      }

      .semi-radio-cardRadioGroup_checked {
        border: 1px solid var(--light-color-brand-brand-5, #4d53e8);
      }
    }
  }
}

.mode-change-disabled {
  display: flex;
  align-items: center;
  margin-left: 4px;
  padding: 2px 8px;

  .icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .label {
    .font-normal();
  }
}
