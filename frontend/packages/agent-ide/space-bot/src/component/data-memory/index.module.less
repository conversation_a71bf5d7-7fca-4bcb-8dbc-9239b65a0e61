/* stylelint-disable declaration-no-important */
/* stylelint-disable max-nesting-depth */
/* stylelint-disable selector-class-pattern */
/* stylelint-disable no-descending-specificity */
@import '../../assets/styles/common.less';
@import '../../assets/styles/mixins.less';
@import '../../assets/styles/index.module.less';

.memory-content {
  user-select: none !important;
}

.memory-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  :global {
    .semi-tag-grey-light {
      cursor: pointer;

      margin: 0 10px 12px 0;

      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      color: var(--light-color-grey-grey-5, #6B6B75);

      background: var(--light-usage-fill-color-fill-1, rgba(46, 46, 56, 8%));
      border-radius: 6px;

      &:hover {
        background: var(--light-usage-fill-color-fill-2, rgba(46, 46, 56, 12%));
      }
    }
  }
}

.template-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .template-cancel-button {
    min-width: 98px;
    background-color: #fff;

    &:hover {
      background-color: rgba(46, 46, 56, 8%) !important;
    }

    >span {
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
      color: var(--light-usage-text-color-text-0, #1C1D23);
    }
  }
}

.template-demo {
  .desc {
    font-size: 12px;
    line-height: 16px;
    color: #000;
  }

  .image {
    width: 100%;
    margin: 16px 0 8px;

    background: #FFF;
    border: 1px solid #ededee;
    border-radius: 10px;

    .image-template {
      display: block;
      width: 100%;

      >img {
        width: 100%;
      }
    }
  }

  .tip {
    margin-bottom: 8px;
    font-size: 10px;
    line-height: 16px;
    color: var(--light-usage-text-color-text-2, rgba(29, 28, 35, 60%));
  }
}

.template-variable-list {
  display: block;
  width: 100%;
  border-radius: 8px;

  >img {
    width: 100%;
  }
}

.use-template-pop-confirm {
  :global {
    .semi-button.semi-button-with-icon-only.semi-button-size-small {
      display: none;
    }
  }
}

.tip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  width: 560px;
  padding: 12px;

  font-size: 12px;
  font-weight: 400;
  font-style: normal;
  line-height: 18px;
  color: var(--light-color-grey-grey-8, #2e3238);

  .tip-top {
    padding: 12px 8px;
    background: var(--light-color-grey-grey-0, #f9f9f9);
    border-radius: 8px;
  }

  .tip-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 4px;
  }
}

.default-text {
  .tip-text;
}

.view-examples {
  display: flex;
  align-items: center;

  margin-bottom: 12px;

  font-size: 14px;
  line-height: 22px;
  color: var(--light-color-brand-brand-5, #4D53E8);

  .view-examples-text,
  .view-examples-icon {
    cursor: pointer;
  }
}

.memory-add-modal {
  background-color: #F7F7FA;

  :global {
    .semi-modal-body {
      display: flex;
      flex-direction: column;
      padding-bottom: 0 !important;
    }

    .semi-modal-content {
      height: calc(100vh - 140px);
      background-color: #F7F7FA;
    }

    .semi-modal-footer {
      margin-top: 12px;
    }
  }

  .add-button-row-fix {
    margin-bottom: 38px;
    padding: 0 16px 12px 32px;
    text-align: left;

    .add-button {
      width: 217px;
      margin: 0 !important;
      padding: 0 48px;
    }

  }

  .modal-add-container {
    display: flex;
    flex: 1;
    flex-direction: column;

    .memory-add-empty {
      margin-top: -8.5%;

      :global {
        .semi-empty-content {
          margin-top: 16px;
        }

        .semi-empty-title {
          font-size: 16px;
          font-weight: 600;
          line-height: 22px;
          color: var(--light-usage-text-color-text-0, #1D1C23);
        }
      }
    }

    &.center {
      justify-content: center;
    }

    .use-template {
      display: flex;
      flex-shrink: 0;
      justify-content: flex-end;

      margin-bottom: 16px;
      padding-top: 8px;
    }

    .memory-edit-table {
      display: flex;
      flex: 1;
      flex-direction: column;

      width: 100%;

      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      color: var(--light-usage-text-color-text-2, rgba(28, 31, 35, 60%));

      thead {
        flex-shrink: 0;

        tr {
          height: 28px;
          padding: 6px 16px 6px 0;
          border-bottom: 1px solid var(--light-usage-border-color-border-1, rgba(29, 28, 35, 12%));
        }

        th {
          font-size: 12px;
          font-weight: 600;
          font-style: normal;
          line-height: 16px;
          color: var(--Fg-COZ-fg-secondary, rgba(27, 41, 73, 62%));
          text-align: start;

          // padding: 0 12px;
          &:last-child {
            padding: 0;
          }
        }
      }

      .memory-row {
        position: relative;

        align-items: flex-start;

        padding: 12px 16px 12px 0;

        border-radius: 8px;

        transition: background linear 300ms;

        &.active-row {
          background: var(--light-color-brand-brand-1, #D9DCFA);
        }

        td {
          padding: 0;
        }
      }

      .add-button-row {
        margin: 12px 0;
        padding: 0 22px;
        text-align: left;

        .add-button {
          width: 217px;
          margin: 0 !important;
          padding: 0 48px;
        }
      }
    }

    .memory-key-err {
      position: relative;
      color: var(--light-color-red-red-5, #f93920);

      .key-error-tip {
        position: absolute;
        bottom: -20px;
        left: 0;
        width: 100%;
      }

      :global {
        .semi-input-wrapper {
          border: 1px solid var(--light-color-red-red-5, #f93920);
        }
      }
    }

    .memory-key-readonly {
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      color: var(--light-color-grey-grey-8, #2e3238);
    }

    .memory-description-readonly {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: var(--light-color-grey-grey-8, #2e3238);
    }

    .readonly-none {
      cursor: not-allowed;

      width: 100%;
      padding: 6px;

      font-size: 12px;
      font-weight: 400;
      font-style: normal;
      line-height: 16px;
      color: var(--semi-color-disabled-text);

      background: var(--light-color-grey-grey-1, #edeff2);
      border: 1px solid var(--semi-color-border);
      border-radius: 8px;
    }

    .memory-description-readonly,
    .memory-key-readonly {
      width: 100%;
      height: 32px;
      padding: 6px;

      font-size: 14px;
      font-weight: 400;
      font-style: normal;
      line-height: 20px;
      color: var(--light-usage-text-color-text-1, rgba(28, 29, 35, 80%));

      background: var(--light-color-grey-grey-1, #edeff2);
      border-radius: 8px;
    }

    .memory-method {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      height: 32px;

      text-align: center;

      :global {
        .semi-space {
          height: 32px;
        }
      }

      &-checkbox {
        display: flex;
        align-items: center;
        justify-content: center;

        width: 24px;
        height: 24px;
      }
    }
  }
}

.sys_item_box {
  min-height: 32px;
  padding-left: 12px;

  font-size: 14px;
  font-weight: 400;
  line-height: 32px;
  color: var(--Light-usage-text---color-text-0, #1D1C23);

  // &.disabled {
  //   color: var(--Light-usage-text---color-text-3, rgba(29, 28, 35, 35%));
  // }
}

.sys_item_group {
  display: flex;
  align-items: center;

  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  color: var(--Light-usage-text---color-text-0, #1D1C23);

  &.disabled {
    color: var(--Light-usage-text---color-text-3, rgba(29, 28, 35, 35%));
  }
}

.group-collapsible {
  display: flex;

  width: 100%;
  padding: 12px 16px;

  font-size: 14px;
  font-weight: 600;
  color: var(--Light-usage-text---color-text-0, #1D1C23);

&-key {
  cursor: pointer;
  display: flex;
  align-items: center;
}

&-value {
  div {
    padding-left: 16px;
  }
}

&-desc {
  div {
    padding-left:8px;
  }
}

}
