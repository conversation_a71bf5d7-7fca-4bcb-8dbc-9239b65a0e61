/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { DEFAULT_ZONE } from '@coze-common/md-editor-adapter';
import type { Editor } from '@coze-common/md-editor-adapter';

const countTextLines = (text: string) => text.split('\n').length;

export const getEditorLines = (editor: Editor) =>
  editor.getContentState().getZoneState(DEFAULT_ZONE)?.length() ?? 0;

export const removeLastLineMarkerOnChange = ({
  text,
  editorLines,
}: {
  text: string;
  editorLines: number;
}) => {
  if (countTextLines(text) > editorLines && text.endsWith('\n')) {
    return text.slice(0, -1);
  }
  return text;
};
