/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useEffect } from 'react';

import { type OnboardingEditorContext } from '../index';

export type UseOnEditorProps = OnboardingEditorContext & {
  onEditorFocus: (e: FocusEvent) => void;
  onEditorBlur: (e: FocusEvent) => void;
};

export const useOnEditor = ({
  editorRef,
  onEditorFocus,
  onEditorBlur,
}: UseOnEditorProps) => {
  useEffect(() => {
    if (!editorRef.current) {
      return;
    }
    editorRef.current
      ?.getRootContainer()
      ?.addEventListener('focus', onEditorFocus, {
        capture: true,
      });
    editorRef.current
      ?.getRootContainer()
      ?.addEventListener('blur', onEditorBlur, {
        capture: true,
      });
  }, [editorRef.current]);
};
