{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "stories", "vitest.config.ts", "tailwind.config.ts"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDir": "./", "outDir": "./dist", "types": ["utility-types", "vitest/globals"], "paths": {"@/*": ["./src/*"]}, "strictNullChecks": false, "noImplicitAny": false, "noImplicitReturns": false, "useUnknownInCatchVariables": false, "strictPropertyInitialization": false, "module": "ESNext"}}