{"name": "@coze-agent-ide/workflow-card-adapter", "version": "0.0.1", "description": "agent ide workflow card adapter", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-agent-ide/navigate": "workspace:*", "@coze-agent-ide/space-bot": "workspace:*", "@coze-agent-ide/tool": "workspace:*", "@coze-agent-ide/workflow-item": "workspace:*", "@coze-agent-ide/workflow-modal": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/i18n": "workspace:*", "@coze-common/assets": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-workflow/base": "workspace:*", "@coze-workflow/components": "workspace:*", "classnames": "^2.3.2", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}