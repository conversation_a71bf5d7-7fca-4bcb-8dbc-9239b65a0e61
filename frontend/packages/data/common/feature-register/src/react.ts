/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useSyncExternalStore } from 'use-sync-external-store/shim';

import type { IExternalStore } from './external-store';

/**
 * 订阅拥有 subscribe 和 getSnapshot 方法的抽象 registry 的变化，内部使用 useSyncExternalStore 实现
 */
export const useRegistryState = <T>(registry: IExternalStore<T>) => {
  const state = useSyncExternalStore(
    registry.subscribe,
    registry.getSnapshot,
    registry.getSnapshot,
  );
  return state;
};
