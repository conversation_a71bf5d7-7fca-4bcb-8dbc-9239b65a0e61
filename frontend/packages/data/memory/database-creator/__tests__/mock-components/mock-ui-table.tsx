/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

export const MockUITable = (props: {
  tableProps: {
    columns: {
      dataIndex: string;
      title: React.ReactElement;
      render: (text, record, index) => React.ReactElement;
    }[];
    dataSource: Record<string, any>[];
  };
}) => {
  const { columns, dataSource } = props.tableProps;

  return (
    <>
      {columns.map(column => {
        const { title, dataIndex, render } = column;
        return (
          <div key={dataIndex}>
            {title}
            {dataSource.map((data, index) => render(undefined, data, index))}
          </div>
        );
      })}
    </>
  );
};
