# @coze-data/database-creator

create or edit database info

## Overview

This package is part of the Coze Studio monorepo and provides data management functionality. It serves as a core component in the Coze ecosystem.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-data/database-creator": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-data/database-creator';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Core functionality for Coze Studio
- TypeScript support
- Modern ES modules

## API Reference

Please refer to the TypeScript definitions for detailed API documentation.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
