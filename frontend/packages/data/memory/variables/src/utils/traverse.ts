/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
// eslint-disable-next-line max-params
export function traverse<
  T extends { [key in K]?: T[] },
  K extends string = 'children',
>(
  nodeOrNodes: T | T[],
  action: (node: T) => void,
  traverseKey: K = 'children' as K,
  maxDepth = Infinity,
  currentDepth = 0,
) {
  const nodes = Array.isArray(nodeOrNodes) ? nodeOrNodes : [nodeOrNodes];
  nodes.forEach(node => {
    action(node);
    if (currentDepth < maxDepth) {
      const children = node[traverseKey] ?? [];
      if (children?.length > 0) {
        traverse(children, action, traverseKey, maxDepth, currentDepth + 1);
      }
    }
  });
}
