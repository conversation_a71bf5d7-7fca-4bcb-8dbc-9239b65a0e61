/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/* eslint-disable @typescript-eslint/naming-convention */

/** 每一级树缩进宽度 */
export const TreeIndentWidth = 30;
/** 树节点展开收起按钮宽度 */
export const TreeCollapseWidth = 24;

// 名称最长50字符
export const MAX_NAME_LENGTH = 50;

// 最大深度限制
export const MAX_LEVEL = 3;
// 最大变量数量限制
export const MAX_JSON_VARIABLE_COUNT = 1;
// 最大JSON长度限制30kb
export const MAX_JSON_LENGTH = 30 * 1024;
