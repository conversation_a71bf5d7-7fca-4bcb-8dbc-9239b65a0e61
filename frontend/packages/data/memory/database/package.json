{"name": "@coze-data/database", "version": "1.0.0", "description": "coze database", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./filebox-list": "./src/components/filebox-list/index.tsx", "./multi-table": "./src/components/database-debug/multi-table.tsx", "./table": "./src/components/database-debug/table/index.tsx"}, "main": "src/index.ts", "unpkg": "./dist/umd/index.js", "module": "./dist/esm/index.js", "types": "./src/index.ts", "typesVersions": {"*": {"filebox-list": ["./src/components/filebox-list/index.tsx"], "multi-table": ["./src/components/database-debug/multi-table.tsx"], "table": ["./src/components/database-debug/table/index.tsx"]}}, "files": ["dist", "README.md"], "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-common/chat-area-plugin-message-grab": "workspace:*", "@coze-data/e2e": "workspace:*", "@coze-data/knowledge-resource-processor-base": "workspace:*", "@coze-data/knowledge-resource-processor-core": "workspace:*", "@coze-studio/user-store": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "ahooks": "^3.7.8", "classnames": "^2.3.2", "dayjs": "^1.11.7", "immer": "^10.0.3", "lodash-es": "^4.17.21", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-common/chat-area": "workspace:*", "@coze-common/chat-core": "workspace:*", "@coze-common/table-view": "workspace:*", "@coze-data/reporter": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@douyinfe/semi-illustrations": "^2.36.0", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-json": "~6.0.0", "@rollup/plugin-node-resolve": "~15.0.1", "@rollup/plugin-replace": "^4.0.0", "@swc/core": "^1.3.35", "@swc/helpers": "^0.4.12", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.16", "less-loader": "~11.1.3", "postcss": "^8.4.32", "react": "~18.2.0", "react-dom": "~18.2.0", "react-router-dom": "^6.22.0", "rollup": "^4.9.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-node-externals": "^6.1.2", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-ts": "^3.1.1", "tailwindcss": "~3.3.3", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}