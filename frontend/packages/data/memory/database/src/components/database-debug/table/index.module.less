
.empty-wrapper-database {
  :global {
    .semi-empty-image {
      align-items: center;
      width: 140px;
      height: 140px;
    }

    .semi-empty-vertical .semi-empty-content {
      margin-top: 16px;
    }
  }
}

.data-table {
  :global {
    .table-wrapper {
      overflow-y: auto;
    }

    .semi-table-wrapper {
      margin-top: 0;
    }

    .semi-table-header-sticky .semi-table-thead>.semi-table-row>.semi-table-row-head {
      background-color: transparent;
      border-width: 1px;
    }

    .semi-table-tbody>.semi-table-row:hover>.semi-table-row-cell {
      background-color: transparent;
      background-image: none;
    }

    .semi-table-tbody>.semi-table-row>.semi-table-row-cell-ellipsis {
      overflow: inherit;
      text-overflow: inherit;
      white-space: normal;
    }

    // 继承高度
    .semi-spin {
      position: static;
      width: 100%;
      height: 100%;
    }

    .semi-spin-children {
      height: 100%;
    }

    .semi-table-small {
      height: 100%;
    }

    .semi-table-container {
      height: 100%;

    }
  }
}

.reset-confirm-modal {
  :global {
    .semi-modal-title {
      color: #1C1D23;
    }

    .semi-modal-body {
      color: #1C1D23;
    }

    .semi-modal-footer .semi-button {
      margin-left: 16px;
    }

    .semi-button-tertiary {
      color: #1C1D23
    }
  }
}

.last-column-min-width {
  min-width: 120px;
}

.last-column-date {
  min-width: 160px;
}

.last-column-text {
  min-width: 300px;
}
