{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo", "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-http/tsconfig.build.json"}, {"path": "../../../arch/bot-store/tsconfig.build.json"}, {"path": "../../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/report-events/tsconfig.build.json"}, {"path": "../../common/e2e/tsconfig.build.json"}, {"path": "../../common/reporter/tsconfig.build.json"}, {"path": "../../common/utils/tsconfig.build.json"}, {"path": "../../../components/bot-icons/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../components/table-view/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../database-v2-adapter/tsconfig.build.json"}, {"path": "../database-v2-base/tsconfig.build.json"}, {"path": "../../knowledge/common/stores/tsconfig.build.json"}, {"path": "../../knowledge/knowledge-resource-processor-base/tsconfig.build.json"}, {"path": "../../knowledge/knowledge-resource-processor-core/tsconfig.build.json"}, {"path": "../../../studio/components/tsconfig.build.json"}, {"path": "../../../studio/stores/bot-detail/tsconfig.build.json"}, {"path": "../../../studio/user-store/tsconfig.build.json"}]}