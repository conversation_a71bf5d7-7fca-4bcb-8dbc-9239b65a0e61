{"name": "@coze-data/database-v2", "version": "0.0.1", "description": "database v2", "license": "Apache-2.0", "author": "liushu<PERSON><EMAIL>", "maintainers": [], "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-http": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-common/table-view": "workspace:*", "@coze-data/database-v2-adapter": "workspace:*", "@coze-data/database-v2-base": "workspace:*", "@coze-data/e2e": "workspace:*", "@coze-data/knowledge-resource-processor-base": "workspace:*", "@coze-data/knowledge-resource-processor-core": "workspace:*", "@coze-data/knowledge-stores": "workspace:*", "@coze-data/reporter": "workspace:*", "@coze-data/utils": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/components": "workspace:*", "@coze-studio/user-store": "workspace:*", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@douyinfe/semi-icons": "^2.36.0", "@douyinfe/semi-illustrations": "^2.36.0", "@douyinfe/semi-ui": "~2.72.3", "ahooks": "^3.7.8", "classnames": "^2.3.2", "date-fns": "^2.23.0", "dayjs": "^1.11.7", "immer": "^10.0.3", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "react-router-dom": "^6.11.1"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "typescript": "~5.8.2", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}