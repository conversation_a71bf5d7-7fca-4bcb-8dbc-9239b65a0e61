/* stylelint-disable block-no-empty */
/* stylelint-disable declaration-no-important */
/* stylelint-disable no-descending-specificity */

.modal-container {
  min-width: 100%;
}

.modal-table-btn {
  display: flex;
  justify-content: flex-end;
}

.modal-temp {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.modal-temp-right {
  position: relative;

  display: flex;
  flex-direction: column;

  height: 409px;
  margin-bottom: 24px;
  padding: 15px 24px;

  background: rgba(255, 255, 255, 100%);
  border: 1px solid rgba(29, 28, 35, 8%);
  border-radius: 12px;

  .modal-temp-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: rgba(29, 28, 35, 100%);
  }

  .modal-temp-image {
    margin-top: 16px;
    margin-bottom: 12px;
    border-radius: 8px;
  }

  .modal-temp-description {
    height: 64px;

    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: rgba(29, 28, 35, 80%);
  }

  .modal-temp-btn-group {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: flex-end;

    margin-top: 16px;
    padding: 8px 0;

    .modal-temp-btn {
      width: 120px;
    }
  }

  .modal-temp-preview {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 329px;
    padding: 16px 21px 24px;

    background: rgba(241, 242, 253, 100%);
    border-radius: 11px 11px 0 0;

    .title {
      padding-bottom: 12px;

      font-size: 14px;
      font-weight: 600;
      line-height: 18px;
      color: rgba(77, 83, 232, 100%);
    }
  }
}

.modal-modify-tips {
  display: flex;
  align-items: center;
  justify-content: space-between;

  margin-bottom: 14px;
  padding: 12px;

  background: var(--light-usage-warning-light-color-warning-light-default,
  #fff8ea);

  .tip-icon {
    margin: 0 20px 0 17px;
    color: rgba(252, 136, 0, 100%);

    >svg {
      width: 20px;
      height: 20px;
    }
  }

  .description {
    display: flex;
    align-items: center;
    margin-right: 65px;
  }

  .link {
    cursor: pointer;
    margin-left: 20px;
    color: #3370ff;
    white-space: nowrap;
  }
}

// 新增样式 @zhangyuanzhou.zyz
.entry {
  display: flex;
  gap: 64px;
  align-items: center;
  justify-content: center;

  height: 409px;
  margin-bottom: 24px;

  background: rgba(255, 255, 255, 100%);
  border: 1px solid rgba(29, 28, 35, 8%);
  border-radius: 12px;

  .entry-method {
    cursor: pointer;

    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;

    color: var(--Light-usage-text---color-text-0, #1d1c23);

    .entry-method-icon {
      width: 24px;
      height: 24px;
    }

    .entry-method-title {
      font-size: 12px;
      font-weight: 600;
      font-style: normal;
      line-height: 16px;
    }
  }

  .entry-method:hover {
    color: var(--Light-color-brand---brand-5, #4d53e8);
  }
}

// 当窗口足够大时，高度固定为641px
// 当窗口太小时，高度随vh变化，333px为内容区距离视窗边缘的距离
.database-table-structure-container {
  overflow: auto;
  width: 100%;
  height: min(641px, calc(100vh - 333px));
}

.title-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .right {
    display: flex;
    gap: 32px;
    align-items: center;
  }
}

.generate-ai-popover-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;

  width: 560px;
  padding: 24px;

  background-color: rgba(247, 247, 250, 100%);
  border-radius: 12px;

  .title {
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
  }

  .input {}

  .button-wrapper {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }

  .text-area {
    :global {
      .semi-input-textarea {
        overflow: auto;
        max-height: 191px;
      }
    }
  }
}

.popover {
  :global {
    .semi-popover {
      border-radius: 12px;
    }

    .semi-popover-content {
      border-radius: 12px;
    }
  }
}

.modal-close-button {
  height: 24px !important;
  padding: 4px !important;
  border-radius: 3px !important;

  &:hover {
    background-color: rgba(46, 50, 56, 5%) !important;
  }
}
