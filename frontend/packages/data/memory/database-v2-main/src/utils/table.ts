/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type ColumnProps } from '@coze-arch/coze-design';

import { type TableRow } from '../components/database-table-data/type';

const FIXED_COLUMN_WIDTH = 60;
const MIN_COLUMN_WIDTH = 100;
/**
 * 表格列伸缩时的回调，用于限制伸缩边界
 * @param column
 * @returns
 */
export const resizeFn = (
  column: ColumnProps<TableRow>,
): ColumnProps<TableRow> => {
  // 多选框/序号列不可伸缩
  if (column.key === 'column-selection') {
    return {
      ...column,
      resizable: false,
      width: FIXED_COLUMN_WIDTH,
    };
  }
  // 固定列（操作列）不可伸缩
  if (column.fixed) {
    return {
      ...column,
      resizable: false,
    };
  }
  // 其余字段列可伸缩，但需要限制最小宽度
  return {
    ...column,
    width:
      Number(column.width) < MIN_COLUMN_WIDTH
        ? MIN_COLUMN_WIDTH
        : Number(column.width),
  };
};
