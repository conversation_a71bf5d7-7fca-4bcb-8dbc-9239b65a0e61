/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useRef } from 'react';

import { type TableData } from '../components/database-table-data/type';

export const useGetTableInstantaneousData = (tableData: TableData) => {
  // 缓存 Data 数据，用于在事件中获取数据
  const dataRef = useRef<TableData>(tableData);
  dataRef.current = tableData;

  return () => dataRef.current;
};
