{"name": "@coze-data/database-v2-base", "version": "0.0.1", "description": "@coze-data/database-v2-base", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {"./components/*": "./src/components/*/index.tsx", "./constants": "./src/constants/index.ts", "./features/*": "./src/features/*/index.tsx", "./types": "./src/types/index.ts"}, "main": "src/index.tsx", "typesVersions": {"*": {"components/*": ["./src/components/*/index.tsx"], "constants": ["./src/constants/index.ts"], "features/*": ["./src/features/*/index.tsx"], "types": ["./src/types/index.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-http": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-common/biz-components": "workspace:*", "@coze-data/e2e": "workspace:*", "@coze-data/reporter": "workspace:*", "@coze-data/utils": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@coze-studio/components": "workspace:*", "ahooks": "^3.7.8", "classnames": "^2.3.2", "lodash-es": "^4.17.21", "nanoid": "^4.0.2"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}