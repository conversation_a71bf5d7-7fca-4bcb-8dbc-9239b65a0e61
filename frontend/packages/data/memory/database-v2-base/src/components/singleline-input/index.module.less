.input-wrapper {
  position: relative;
  width: 100%;

  // semi 原有的 disable 样式不满足需求，需要更明显的文字颜色
  :global {
    // .semi-input-wrapper-disabled {
    //   -webkit-text-fill-color: rgba(29, 28, 35, 100%);
    // }

    // .semi-input-suffix {
    //   -webkit-text-fill-color: var(--semi-color-disabled-text, rgba(56, 55, 67, 20%))
    // }
  }

  span {
    width: 100%;
  }
}

.error-wrapper {
  :global {
    .semi-input-wrapper {
      border: 1px solid @error-red;
    }
  }
}

.error-content {
  height: 20px;
}

.error-float {
  position: absolute;
  width: 100%;
  white-space: nowrap;
}

.error-text {
  position: absolute;
  padding-top: 2px;
  font-size: 12px;
  color: @error-red;
}

.input {
  input {
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.limit-count {
  overflow: hidden;

  padding-right: 12px;
  padding-left: 8px;

  font-size: 12px;
  font-weight: 400;
  font-style: normal;
  line-height: 16px;
  color: var(--light-usage-text-color-text-3, rgba(28, 31, 35, 35%));
}
