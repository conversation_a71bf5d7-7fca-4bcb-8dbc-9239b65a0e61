/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
/**
 * 编辑器对/n不会换行，所以需要转换为<br />标签
 */
export const getInitEditorContent = (content: string) => {
  if (content === '') {
    return '';
  }
  if (!content.includes('\n')) {
    return content;
  }
  return content.replace(/\n/g, '<br />');
};
