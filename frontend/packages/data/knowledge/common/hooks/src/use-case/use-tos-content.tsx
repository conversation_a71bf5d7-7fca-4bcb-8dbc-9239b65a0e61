/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useRequest } from 'ahooks';

export const useTosContent = (tosUrl?: string) => {
  const {
    data: content,
    loading,
    error,
  } = useRequest(
    async () => {
      if (!tosUrl) {
        return null;
      }
      const response = await fetch(tosUrl, { cache: 'no-cache' });
      if (!response.ok) {
        throw new Error('Failed to fetch content');
      }
      return response.json();
    },
    {
      refreshDeps: [tosUrl],
    },
  );

  return { content, loading, error };
};
