{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "target": "ES2020", "moduleResolution": "bundler", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo", "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../common/stores/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../knowledge-ide-base/tsconfig.build.json"}]}