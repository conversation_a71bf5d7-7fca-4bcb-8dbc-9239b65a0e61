/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type StateCreator } from 'zustand';

import {
  createTextSlice,
  getDefaultTextState,
} from '@/features/knowledge-type/text/slice';

import { TextCustomAddUpdateStep } from '../constants';
import {
  type UploadTextCustomAddUpdateStore,
  type UploadTextCustomAddUpdateState,
} from './types';

export const getDefaultTextCustomAddState: () => UploadTextCustomAddUpdateState =
  () => ({
    ...getDefaultTextState(),
    currentStep: TextCustomAddUpdateStep.UPLOAD_CONTENT,
    docName: '',
    docContent: '',
  });

export const createTextCustomAddUpdateSlice: StateCreator<
  UploadTextCustomAddUpdateStore
> = (set, ...arg) => ({
  ...createTextSlice(set, ...arg),
  // overwrite
  ...getDefaultTextCustomAddState(),
  // /** reset state */
  reset: () => {
    set(getDefaultTextCustomAddState());
  },
  setDocContent: (content: string) => {
    set({
      docContent: content,
    });
  },
  setDocName: (name: string) => {
    set({
      docName: name,
    });
  },
});
