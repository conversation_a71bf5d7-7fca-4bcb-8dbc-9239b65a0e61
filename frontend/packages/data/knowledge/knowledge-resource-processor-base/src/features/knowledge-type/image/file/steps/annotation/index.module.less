/* stylelint-disable max-nesting-depth */
/* stylelint-disable selector-class-pattern */
.segment-radio-wrapper {
  .displayNone {
    display: none;
  }

  .custom-wrapper {
    position: relative;
    width: 100%;

    .form-segment {
      margin-top: 33px;
      margin-bottom: 15px;

      .item {
        margin-bottom: 20px;

        .label {
          display: block;

          margin-bottom: 8px;

          font-size: 14px;
          font-weight: 600;
          line-height: 22px;
          color: var(--coz-fg-primary);
        }

        .label-red::after {
          content: "*";
          margin-left: 4px;
          font-weight: 600;
          color: var(--coz-fg-hglt-red);
        }

        .custom-input {
          margin-top: 8px;
        }
      }
    }
  }

  .line {
    &::before {
      content: '';

      position: absolute;
      top: 10px;

      display: inline-block;

      width: 100%;
      height: 1px;
      margin-top: 26px;

      background: var(--light-usage-border-color-border,
      rgb(28 31 35 / 8%));
    }
  }

  :global {
    .semi-radioGroup.semi-radioGroup-vertical {
      row-gap: 16px;
    }

    .semi-radioGroup {
      .semi-radio-content {
        flex-grow: 1;
      }

      .custom-wrapper {
        padding-bottom: 0;

        .semi-input-default,
        .semi-input-wrapper-focus {
          border-radius: 8px;
        }
      }
    }

    .semi-radio-cardRadioGroup {
      padding: 16px 16px 16px 19px;
      border: 1px solid var(--coz-stroke-plus);
      border-radius: 8px;

      &:hover {
        background: var(--coz-mg-secondary-hovered);
      }

      &:active {
        background: var(--coz-mg-secondary-pressed);
      }

      .semi-radio-extra {
        margin-top: 4px;
      }
    }

    .semi-radio-cardRadioGroup_checked {
      background: var(--coz-mg-hglt);
      border: 1px solid var(--coz-stroke-hglt);

      &:hover {
        background: var(--coz-mg-hglt-hovered);
      }

      &:active {
        background: var(--coz-mg-hglt-pressed);
      }
    }

    .semi-radio-addon {
      margin-bottom: 2px;
    }

    .semi-form-field-label {
      margin-bottom: 8px;
    }

    .semi-checkboxGroup-vertical {
      row-gap: 8px;
    }
  }
}
