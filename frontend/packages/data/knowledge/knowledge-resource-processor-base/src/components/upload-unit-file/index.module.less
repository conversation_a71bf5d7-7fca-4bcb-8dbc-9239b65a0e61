.upload {
  width: 100%;

  :global {
    .semi-upload-drag-area {
      height: 202px;
      background-color: var(--coz-mg-card);
      border-radius: 8px;

      &:hover,
      &.semi-upload-drag-area-legal {
        border-radius: var(--default, 8px);
      }
    }

    .semi-upload-drag-area-sub-text {
      color: var(--coz-fg-dim);
    }

    .semi-button-with-icon-only {
      border-radius: 4px;
    }

    .semi-upload-file-list {
      display: none;
    }
  }
}

.upload-icon {
  width: 32px;
  height: 32px;
}

.create-enough-file {
  cursor: not-allowed;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  width: 100%;
  height: 100%;

  .picture {
    width: 122px;
    height: 122px;
    margin-bottom: 14px;
  }

  .text {
    font-size: 12px;
    line-height: 16px;
    color: rgb(28 31 35 / 60%);
  }
}
