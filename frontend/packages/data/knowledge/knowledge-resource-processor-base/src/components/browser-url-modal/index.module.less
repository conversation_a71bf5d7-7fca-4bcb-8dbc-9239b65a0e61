/* stylelint-disable declaration-no-important */
.browse-detail-modal {
  padding-bottom: 40px;

  &-main {
    display: flex;
    align-items: center;

    margin-bottom: 16px;

    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    color: rgb(28 31 35 / 100%);

    &-image {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;

      width: 18px;
      height: 18px;
      margin-right: 7px;

      background-color: rgb(152 205 253 / 100%);
      border-radius: 2px;
    }

    &-title {
      max-width: 40%;
    }

    &-url {
      max-width: 40%;
      margin-left: 7px;

      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      color: rgb(28 31 35 / 35%);
    }
  }

  &-collapse {
    overflow-y: auto;
    max-height: 400px;
    background-color: #f5f5f5;
    border-radius: 4px;

    :global {
      .semi-collapse-item {
        border-color: #efefef !important;
      }

      .semi-input-textarea-wrapper {
        background-color: rgb(0 0 0 / 0%) !important;
        border: none !important;
      }
    }
  }
}

.browse-modal-header {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  padding: 24px 0;

  &-left {
    display: flex;
    align-items: center;

    &-icon {
      margin-right: 8px;
    }
  }
}

.modal.upgrade-level {
  :global {
    .semi-modal-body {
      height: 0;
      padding-bottom: 0;
    }

    .semi-modal-content {
      background-color: var(--semi-color-tertiary-light-default);
    }
  }
}

.browse-detail-tooltip {
  width: 200px;
  word-wrap: break-word;
}

.browse-source-url {
  margin-top: 12px;

  font-size: 12px;
  font-weight: 400;
  font-style: normal;
  line-height: 16px;
  color: rgb(29 28 36 / 35%)
}
