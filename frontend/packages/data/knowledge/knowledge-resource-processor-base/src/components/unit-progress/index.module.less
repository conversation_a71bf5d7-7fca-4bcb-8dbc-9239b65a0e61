@import '../../assets/common.less';

.embed-progress {
  flex: 1;

  .progress-info {
    width: 100%;

    .text {
      display: flex;
      align-items: center;

      height: 32px;
      margin-bottom: 8px;

      font-size: 14px;
      font-weight: 500;
      font-style: normal;
      line-height: 20px;
      color: var(--coz-fg-primary);
    }

    .progress-list {
      overflow-y: auto;
      max-height: 532px;
    }
  }

  .banner {
    width: 100%;
  }

  .progress-success-icon {
    .common-svg-icon(16px, var(--semi-color-primary));
  }
}

.data-processing {
  .finish-text {
    overflow: hidden;

    font-size: 14px;
    font-weight: 400;
    font-style: normal;
    line-height: 20px; /* 142.857% */
    color: var(--coz-fg-primary);
    text-align: right;
    text-overflow: ellipsis;
  }

  :global {
    .process-progress-item-actions {
      /* stylelint-disable-next-line declaration-no-important */
      display: block !important;
    }
  }
}

