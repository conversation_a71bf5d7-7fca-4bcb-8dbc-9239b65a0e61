{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "module": "ESNext", "target": "ES2020", "moduleResolution": "bundler", "paths": {"@/*": ["./src/*"]}, "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-http/tsconfig.build.json"}, {"path": "../../../arch/bot-md-box-adapter/tsconfig.build.json"}, {"path": "../../../arch/bot-store/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../../arch/foundation-sdk/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/idl/tsconfig.build.json"}, {"path": "../../../arch/pdfjs-shadow/tsconfig.build.json"}, {"path": "../../../arch/report-events/tsconfig.build.json"}, {"path": "../../../arch/report-tti/tsconfig.build.json"}, {"path": "../../../arch/utils/tsconfig.build.json"}, {"path": "../common/components/tsconfig.build.json"}, {"path": "../../common/e2e/tsconfig.build.json"}, {"path": "../common/hooks/tsconfig.build.json"}, {"path": "../../common/reporter/tsconfig.build.json"}, {"path": "../common/services/tsconfig.build.json"}, {"path": "../common/stores/tsconfig.build.json"}, {"path": "../../common/utils/tsconfig.build.json"}, {"path": "../../../components/bot-icons/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../components/table-view/tsconfig.build.json"}, {"path": "../../../components/virtual-list/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../knowledge-modal-base/tsconfig.build.json"}, {"path": "../knowledge-resource-processor-core/tsconfig.build.json"}, {"path": "../../../studio/components/tsconfig.build.json"}]}