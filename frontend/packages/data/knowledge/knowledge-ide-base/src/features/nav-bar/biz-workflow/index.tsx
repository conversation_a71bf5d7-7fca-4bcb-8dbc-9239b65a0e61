/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { useShallow } from 'zustand/react/shallow';
import { useKnowledgeStore } from '@coze-data/knowledge-stores';

import { KnowledgeModalNavBar as KnowledgeModalNavBarComponent } from '@/components/knowledge-modal-nav-bar';

import { type KnowledgeIDENavBarProps } from '../module';

export type BizWorkflowKnowledgeIDENavBarProps = KnowledgeIDENavBarProps;

export const BizWorkflowKnowledgeIDENavBar = (
  props: BizWorkflowKnowledgeIDENavBarProps,
) => {
  const { onBack, actionButtons } = props;
  const { dataSetDetail, documentList } = useKnowledgeStore(
    useShallow(state => ({
      dataSetDetail: state.dataSetDetail,
      documentList: state.documentList,
    })),
  );
  return (
    <KnowledgeModalNavBarComponent
      title={dataSetDetail?.name as string}
      onBack={onBack}
      datasetDetail={dataSetDetail}
      docInfo={documentList?.[0]}
      actionButtons={actionButtons}
    />
  );
};
