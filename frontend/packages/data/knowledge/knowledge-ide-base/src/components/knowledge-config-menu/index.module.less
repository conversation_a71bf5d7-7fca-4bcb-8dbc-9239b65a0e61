/* stylelint-disable declaration-no-important */
.table-config-menu-down {
  padding: 4px;
  font-size: 12px;

  .dropdown-item-line {
    width: 100%;
    height: 1px;
    margin: 4px 0;
  }

  :global {
    .semi-dropdown-menu {
      padding: 4px;
    }

    .semi-dropdown-item {
      min-width: 126px;
      height: 32px !important;
      padding: 8px 16px !important;
    }
  }
}

.dropdown-item-link {
  font-size: 12px;
  color: var(--coz-fg-hglt) !important;

  :global {
    .semi-dropdown-item-icon {
      margin-right: 4px;
    }
  }
}

.dropdown-item-link-icon {
  svg {
    width: 12px;
    height: 12px;
  }

  :global {
    .semi-dropdown-item-icon {
      margin-right: 0;
    }
  }
}

.action-btn {
  &-right {
    padding: 4px;
  }

  :global {
    .semi-button-content-left {
      display: flex;
      align-items: center;
      margin-right: 4px;
    }
  }
}
