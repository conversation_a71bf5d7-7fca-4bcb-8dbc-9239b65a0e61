{"name": "@coze-data/knowledge-ide-base", "version": "0.0.1", "description": "Knowledge IDE text base components", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.tsx", "./types": "./src/types/index.ts", "./utils": "./src/utils/index.ts", "./components/*": "./src/components/*/index.tsx", "./features/*": "./src/features/*/index.tsx", "./hooks/*": "./src/hooks/*/index.ts", "./layout": "./src/layout/index.tsx", "./layout/*": "./src/layout/*/index.tsx", "./context/*": "./src/context/*/index.tsx"}, "main": "src/index.tsx", "typesVersions": {"*": {"types": ["./src/types/index.ts"], "components/*": ["./src/components/*/index.tsx"], "features/*": ["./src/features/*/index.tsx"], "utils": ["./src/utils/index.ts"], "hooks/*": ["./src/hooks/*/index.ts"], "layout": ["./src/layout/index.tsx"], "layout/*": ["./src/layout/*/index.tsx"], "context/*": ["./src/context/*/index.tsx"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-monaco-editor": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-arch/report-tti": "workspace:*", "@coze-common/chat-area-utils": "workspace:*", "@coze-data/e2e": "workspace:*", "@coze-data/feature-register": "workspace:*", "@coze-data/knowledge-common-components": "workspace:*", "@coze-data/knowledge-common-hooks": "workspace:*", "@coze-data/knowledge-common-services": "workspace:*", "@coze-data/knowledge-modal-adapter": "workspace:*", "@coze-data/knowledge-modal-base": "workspace:*", "@coze-data/knowledge-resource-processor-adapter": "workspace:*", "@coze-data/knowledge-resource-processor-base": "workspace:*", "@coze-data/knowledge-resource-processor-core": "workspace:*", "@coze-data/knowledge-stores": "workspace:*", "@coze-data/reporter": "workspace:*", "@coze-data/utils": "workspace:*", "@coze-foundation/local-storage": "workspace:*", "@coze-studio/components": "workspace:*", "@coze-studio/premium-components-adapter": "workspace:*", "@coze-studio/premium-store-adapter": "workspace:*", "@douyinfe/semi-illustrations": "^2.36.0", "ahooks": "^3.7.8", "classnames": "^2.3.2", "dayjs": "^1.11.7", "dompurify": "3.0.8", "immer": "^10.0.3", "lodash-es": "^4.17.21", "nanoid": "^4.0.2", "qs": "^6.11.2", "react-router-dom": "^6.22.0", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-common/table-view": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/dompurify": "3.0.5", "@types/lodash-es": "^4.17.10", "@types/qs": "^6.9.7", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "react": "~18.2.0", "react-dom": "~18.2.0", "stylelint": "^15.11.0", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}