{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"types": [], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo", "paths": {"@/*": ["./src/*"]}}, "include": ["src", "../main/src/components/create-knowledge-modal-v2"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-http/tsconfig.build.json"}, {"path": "../../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../../arch/bot-store/tsconfig.build.json"}, {"path": "../../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../../../arch/report-events/tsconfig.build.json"}, {"path": "../../../common/biz-components/tsconfig.build.json"}, {"path": "../../common/e2e/tsconfig.build.json"}, {"path": "../../common/reporter/tsconfig.build.json"}, {"path": "../common/stores/tsconfig.build.json"}, {"path": "../../common/utils/tsconfig.build.json"}, {"path": "../../../components/biz-tooltip-ui/tsconfig.build.json"}, {"path": "../../../components/bot-icons/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../components/table-view/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../knowledge-resource-processor-core/tsconfig.build.json"}, {"path": "../../../studio/components/tsconfig.build.json"}, {"path": "../../../studio/stores/bot-detail/tsconfig.build.json"}]}