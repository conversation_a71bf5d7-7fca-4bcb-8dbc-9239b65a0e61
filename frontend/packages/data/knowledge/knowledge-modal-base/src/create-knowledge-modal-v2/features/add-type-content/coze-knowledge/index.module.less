/* stylelint-disable declaration-no-important */
.create-knowledge-modal {
  :global {
    .semi-modal-content {
      max-height: calc(100vh - 100px) !important;
    }
  }
}

.import-unit-type-label {
  display: block;

  margin-bottom: 8px;

  font-size: 14px;
  font-weight: bold;
  line-height: 20px;
}


.create-form {
  :global {
    .semi-form-field {
      padding-top: 0;
      padding-bottom: 24px !important;
    }

    .semi-form-field-label {
      margin-bottom: 0;
      padding: 0 8px 6px;

      font-size: 12px;
      font-weight: 500;
      font-style: normal;
      line-height: 16px; /* 133.333% */
      color: var(--coz-fg-secondary);
    }
  }
}

.upload-avatar-container {
  width: auto;
}
