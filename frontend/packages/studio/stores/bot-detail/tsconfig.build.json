{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "module": "ESNext", "strictNullChecks": true, "paths": {"@/*": ["./src/*"]}, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../agent-ide/bot-input-length-limit/tsconfig.build.json"}, {"path": "../../../agent-ide/tool-config/tsconfig.build.json"}, {"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-env/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../../arch/bot-store/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/idl/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../../../arch/report-events/tsconfig.build.json"}, {"path": "../../../arch/web-context/tsconfig.build.json"}, {"path": "../../autosave/tsconfig.build.json"}, {"path": "../../bot-utils/tsconfig.build.json"}, {"path": "../../../common/chat-area/chat-core/tsconfig.build.json"}, {"path": "../../../common/flowgram-adapter/common/tsconfig.build.json"}, {"path": "../../../common/flowgram-adapter/free-layout-editor/tsconfig.build.json"}, {"path": "../../../common/websocket-manager-adapter/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}]}