{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "include": ["__tests__", "__mocks__", "stories", "vitest.config.ts"], "exclude": ["./dist"], "references": [{"path": "./tsconfig.build.json"}], "compilerOptions": {"rootDir": "./", "outDir": "./dist", "types": ["vitest/globals"], "module": "ESNext", "strictNullChecks": true, "paths": {"@/*": ["./src/*"]}}}