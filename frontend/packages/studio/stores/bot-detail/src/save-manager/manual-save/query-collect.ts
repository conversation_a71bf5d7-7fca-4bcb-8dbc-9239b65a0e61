/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type UpdateDraftBotInfoAgwResponse } from '@coze-arch/idl/playground_api';
import { type UserQueryCollectConf } from '@coze-arch/bot-api/developer_api';

import { saveFetcher, updateBotRequest } from '../utils/save-fetcher';
import { ItemTypeExtra } from '../types';

export const updateQueryCollect = async (
  queryCollectConf: UserQueryCollectConf,
) => {
  // @ts-expect-error -- linter-disable-autofix
  let updateResult: UpdateDraftBotInfoAgwResponse = null;

  await saveFetcher(async () => {
    const res = await updateBotRequest({
      user_query_collect_conf: queryCollectConf,
    });

    updateResult = res;
    return res;
  }, ItemTypeExtra.QueryCollect);
  return updateResult;
};
