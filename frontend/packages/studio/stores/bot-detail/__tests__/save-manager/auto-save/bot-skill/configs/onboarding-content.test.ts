/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { describe, it, expect } from 'vitest';

import { ItemType } from '../../../../../src/save-manager/types';
import { onboardingConfig } from '../../../../../src/save-manager/auto-save/bot-skill/configs/onboarding-content';

describe('onboardingConfig', () => {
  it('should have correct configuration properties', () => {
    expect(onboardingConfig).toHaveProperty('key');
    expect(onboardingConfig).toHaveProperty('selector');
    expect(onboardingConfig).toHaveProperty('debounce');
    expect(onboardingConfig).toHaveProperty('middleware');
    expect(onboardingConfig.key).toBe(ItemType.ONBOARDING);
    // 验证 debounce 配置
    if (typeof onboardingConfig.debounce === 'object') {
      expect(onboardingConfig.debounce).toHaveProperty('default');
      expect(onboardingConfig.debounce).toHaveProperty('prologue');
      expect(onboardingConfig.debounce).toHaveProperty('suggested_questions');
    }
  });
});
