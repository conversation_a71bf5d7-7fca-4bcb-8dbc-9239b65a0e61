/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { SpaceRoleType } from '@coze-arch/bot-api/plugin_develop';
import { I18n } from '@coze-arch/i18n';
export { type GetUserAuthorityData } from '@coze-arch/bot-api/plugin_develop';
export { CreationMethod } from '@coze-arch/bot-api/plugin_develop';

export const ROLE_TAG_TEXT_MAP = {
  [SpaceRoleType.Owner]: I18n.t('team_management_role_owner', {}, 'Owner'),
  [SpaceRoleType.Admin]: I18n.t('team_management_role_admin', {}, 'Admin'),
  [SpaceRoleType.Member]: I18n.t('team_management_role_member', {}, 'Member'),
  [SpaceRoleType.Default]: '-',
} as const;

export { SpaceRoleType };
