{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"types": [], "paths": {"@/*": ["./src/*"]}, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../../../arch/report-events/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}]}