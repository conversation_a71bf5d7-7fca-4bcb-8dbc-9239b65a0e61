/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import {
  isObject as isObjectBase,
  isFunction as isFunctionBase,
} from 'lodash-es';

import {
  type DebounceConfig,
  type ObjectDebounceTime,
  type SaveMiddlewareHander,
  type FunctionDebounceTime,
} from '../type/index';

export function isFunction(
  value: DebounceConfig,
): value is FunctionDebounceTime {
  return isFunctionBase(value);
}

export function isObject(value: DebounceConfig): value is ObjectDebounceTime {
  return isObjectBase(value);
}

/**
 * 获取保存接口调用时候需要的参数
 */
export const getPayloadByFormatter = async <T>(
  state: T,
  formatter?: SaveMiddlewareHander<T>,
) => {
  if (formatter) {
    return await formatter(state);
  }
  return state;
};
