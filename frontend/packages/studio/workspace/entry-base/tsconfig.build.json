{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.web.json", "compilerOptions": {"paths": {"@/*": ["./src/*"]}, "types": [], "strictNullChecks": true, "noImplicitAny": true, "rootDir": "./src", "outDir": "./dist", "tsBuildInfoFile": "./dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "references": [{"path": "../../../agent-ide/bot-plugin/entry/tsconfig.build.json"}, {"path": "../../../agent-ide/space-bot/tsconfig.build.json"}, {"path": "../../../arch/bot-api/tsconfig.build.json"}, {"path": "../../../arch/bot-error/tsconfig.build.json"}, {"path": "../../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../../arch/bot-space-api/tsconfig.build.json"}, {"path": "../../../arch/bot-store/tsconfig.build.json"}, {"path": "../../../arch/bot-tea/tsconfig.build.json"}, {"path": "../../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../../arch/i18n/tsconfig.build.json"}, {"path": "../../../arch/idl/tsconfig.build.json"}, {"path": "../../../arch/logger/tsconfig.build.json"}, {"path": "../../../arch/report-events/tsconfig.build.json"}, {"path": "../../bot-utils/tsconfig.build.json"}, {"path": "../../../common/biz-components/tsconfig.build.json"}, {"path": "../../../common/chat-area/utils/tsconfig.build.json"}, {"path": "../../../common/coze-mitt/tsconfig.build.json"}, {"path": "../../../common/prompt-kit/adapter/tsconfig.build.json"}, {"path": "../../../common/prompt-kit/main/tsconfig.build.json"}, {"path": "../../../community/component/tsconfig.build.json"}, {"path": "../../../components/bot-semi/tsconfig.build.json"}, {"path": "../../components/tsconfig.build.json"}, {"path": "../../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../../data/common/utils/tsconfig.build.json"}, {"path": "../../../data/knowledge/common/stores/tsconfig.build.json"}, {"path": "../../../data/knowledge/knowledge-ide-adapter/tsconfig.build.json"}, {"path": "../../../data/knowledge/knowledge-ide-base/tsconfig.build.json"}, {"path": "../../../data/knowledge/knowledge-modal-adapter/tsconfig.build.json"}, {"path": "../../../data/knowledge/knowledge-resource-processor-adapter/tsconfig.build.json"}, {"path": "../../../data/knowledge/knowledge-resource-processor-base/tsconfig.build.json"}, {"path": "../../../data/knowledge/knowledge-resource-processor-core/tsconfig.build.json"}, {"path": "../../../data/memory/database-v2-main/tsconfig.build.json"}, {"path": "../../../foundation/account-adapter/tsconfig.build.json"}, {"path": "../../../foundation/enterprise-store-adapter/tsconfig.build.json"}, {"path": "../../../foundation/layout/tsconfig.build.json"}, {"path": "../../../foundation/local-storage/tsconfig.build.json"}, {"path": "../../premium/premium-components-adapter/tsconfig.build.json"}, {"path": "../../premium/premium-store-adapter/tsconfig.build.json"}, {"path": "../project-entity-adapter/tsconfig.build.json"}, {"path": "../../stores/bot-plugin/tsconfig.build.json"}, {"path": "../../user-store/tsconfig.build.json"}, {"path": "../../../workflow/base/tsconfig.build.json"}, {"path": "../../../workflow/components/tsconfig.build.json"}]}