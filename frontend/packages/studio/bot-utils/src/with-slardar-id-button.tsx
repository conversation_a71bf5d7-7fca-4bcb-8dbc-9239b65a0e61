/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { type ReactNode } from 'react';

import copy from 'copy-to-clipboard';
import { getSlardarInstance } from '@coze-arch/logger';
import { I18n } from '@coze-arch/i18n';
import { Button, Toast } from '@coze-arch/coze-design';

export const withSlardarIdButton = (node: ReactNode) => {
  const copySlardarId = () => {
    const id = getSlardarInstance()?.config()?.sessionId;
    copy(id ?? '');
    Toast.success(I18n.t('error_id_copy_success'));
  };

  return (
    <div className="flex flex-row justify-center items-center">
      {node}
      <Button
        className="ml-[8px]"
        onClick={copySlardarId}
        size="small"
        color="primary"
      >
        {I18n.t('copy_session_id')}
      </Button>
    </div>
  );
};
