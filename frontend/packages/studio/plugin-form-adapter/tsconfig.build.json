{"extends": "@coze-arch/ts-config/tsconfig.web.json", "$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "lib": ["DOM", "ESNext"], "target": "ES2020", "noImplicitAny": true, "strictNullChecks": true, "tsBuildInfoFile": "dist/tsconfig.build.tsbuildinfo"}, "include": ["src"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../agent-ide/bot-plugin/tools/tsconfig.build.json"}, {"path": "../../arch/bot-api/tsconfig.build.json"}, {"path": "../../arch/bot-flags/tsconfig.build.json"}, {"path": "../../arch/bot-typings/tsconfig.build.json"}, {"path": "../../arch/bot-utils/tsconfig.build.json"}, {"path": "../../arch/i18n/tsconfig.build.json"}, {"path": "../../arch/logger/tsconfig.build.json"}, {"path": "../../common/biz-components/tsconfig.build.json"}, {"path": "../../components/bot-semi/tsconfig.build.json"}, {"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/stylelint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}, {"path": "../../foundation/enterprise-store-adapter/tsconfig.build.json"}, {"path": "../plugin-shared/tsconfig.build.json"}, {"path": "../premium/premium-store-adapter/tsconfig.build.json"}]}