/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import type {
  PremiumPlan,
  PremiumSubs,
  SKUInfo,
  SubscriptionUserInfo,
} from '../types';

const result = {
  isFree: false,
  isPremiumPlus: false,
  hasLowLevelActive: false,
  hasHighLevelActive: false,
  sub: {},
  activeSub: {},
};
export function formatPremiumType(_props: {
  currentPlan?: SKUInfo;
  plans: PremiumPlan[];
  subs: PremiumSubs;
}): {
  isFree: boolean;
  isPremiumPlus: boolean;
  hasLowLevelActive: boolean;
  hasHighLevelActive: boolean;
  sub: SubscriptionUserInfo;
  activeSub: SubscriptionUserInfo;
} {
  return result;
}
