/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

import cls from 'classnames';

import styles from './index.module.less';

export interface CarouselItemProps {
  className?: string;
  children: React.ReactNode;
}

export const CarouselItem: React.FC<CarouselItemProps> = props => {
  const { children, className } = props;
  return (
    <div className={cls(styles['carousel-item'], className, 'carousel-item')}>
      {children}
    </div>
  );
};
