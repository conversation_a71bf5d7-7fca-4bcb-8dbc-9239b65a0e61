/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

// eslint-disable-next-line @coze-arch/max-line-per-function
export function RecommendNoResult(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="576"
      height="288"
      viewBox="0 0 576 288"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="148" y="136" width="280" height="152" rx="8" fill="#F9F9F9" />
      <rect
        x="148.5"
        y="136.5"
        width="279"
        height="151"
        rx="7.5"
        stroke="url(#paint0_linear_7238_190934)"
        strokeOpacity="0.24"
      />
      <rect
        opacity="0.12"
        x="164"
        y="152"
        width="104"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <rect
        opacity="0.12"
        x="164"
        y="174"
        width="248"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <rect
        opacity="0.12"
        x="164"
        y="196"
        width="248"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <rect
        opacity="0.12"
        x="164"
        y="226"
        width="120"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <g filter="url(#filter0_d_7238_190934)">
        <path
          d="M164 262.4C164 260.16 164 259.04 164.436 258.184C164.819 257.431 165.431 256.819 166.184 256.436C167.04 256 168.16 256 170.4 256H173.6C175.84 256 176.96 256 177.816 256.436C178.569 256.819 179.181 257.431 179.564 258.184C180 259.04 180 260.16 180 262.4V265.6C180 267.84 180 268.96 179.564 269.816C179.181 270.569 178.569 271.181 177.816 271.564C176.96 272 175.84 272 173.6 272H170.4C168.16 272 167.04 272 166.184 271.564C165.431 271.181 164.819 270.569 164.436 269.816C164 268.96 164 267.84 164 265.6V262.4Z"
          fill="url(#paint1_linear_7238_190934)"
        />
        <path
          d="M170.4 272.5H173.6H173.623C174.724 272.5 175.581 272.5 176.268 272.444C176.966 272.387 177.533 272.269 178.043 272.01C178.89 271.578 179.578 270.89 180.01 270.043C180.269 269.533 180.387 268.966 180.444 268.268C180.5 267.581 180.5 266.724 180.5 265.623V265.6V262.4V262.377C180.5 261.276 180.5 260.419 180.444 259.732C180.387 259.034 180.269 258.467 180.01 257.957C179.578 257.11 178.89 256.422 178.043 255.99C177.533 255.731 176.966 255.613 176.268 255.556C175.581 255.5 174.724 255.5 173.623 255.5H173.6H170.4H170.377C169.276 255.5 168.419 255.5 167.732 255.556C167.034 255.613 166.467 255.731 165.957 255.99C165.11 256.422 164.422 257.11 163.99 257.957C163.731 258.467 163.613 259.034 163.556 259.732C163.5 260.419 163.5 261.276 163.5 262.377V262.4V265.6V265.623C163.5 266.724 163.5 267.581 163.556 268.268C163.613 268.966 163.731 269.533 163.99 270.043C164.422 270.89 165.11 271.578 165.957 272.01C166.467 272.269 167.034 272.387 167.732 272.444C168.419 272.5 169.276 272.5 170.377 272.5H170.4Z"
          stroke="white"
        />
      </g>
      <g filter="url(#filter1_d_7238_190934)">
        <path
          d="M176 262.4C176 260.16 176 259.04 176.436 258.184C176.819 257.431 177.431 256.819 178.184 256.436C179.04 256 180.16 256 182.4 256H185.6C187.84 256 188.96 256 189.816 256.436C190.569 256.819 191.181 257.431 191.564 258.184C192 259.04 192 260.16 192 262.4V265.6C192 267.84 192 268.96 191.564 269.816C191.181 270.569 190.569 271.181 189.816 271.564C188.96 272 187.84 272 185.6 272H182.4C180.16 272 179.04 272 178.184 271.564C177.431 271.181 176.819 270.569 176.436 269.816C176 268.96 176 267.84 176 265.6V262.4Z"
          fill="url(#paint2_linear_7238_190934)"
        />
        <path
          d="M182.4 272.5H185.6H185.623C186.724 272.5 187.581 272.5 188.268 272.444C188.966 272.387 189.533 272.269 190.043 272.01C190.89 271.578 191.578 270.89 192.01 270.043C192.269 269.533 192.387 268.966 192.444 268.268C192.5 267.581 192.5 266.724 192.5 265.623V265.6V262.4V262.377C192.5 261.276 192.5 260.419 192.444 259.732C192.387 259.034 192.269 258.467 192.01 257.957C191.578 257.11 190.89 256.422 190.043 255.99C189.533 255.731 188.966 255.613 188.268 255.556C187.581 255.5 186.724 255.5 185.623 255.5H185.6H182.4H182.377C181.276 255.5 180.419 255.5 179.732 255.556C179.034 255.613 178.467 255.731 177.957 255.99C177.11 256.422 176.422 257.11 175.99 257.957C175.731 258.467 175.613 259.034 175.556 259.732C175.5 260.419 175.5 261.276 175.5 262.377V262.4V265.6V265.623C175.5 266.724 175.5 267.581 175.556 268.268C175.613 268.966 175.731 269.533 175.99 270.043C176.422 270.89 177.11 271.578 177.957 272.01C178.467 272.269 179.034 272.387 179.732 272.444C180.419 272.5 181.276 272.5 182.377 272.5H182.4Z"
          stroke="white"
        />
      </g>
      <g filter="url(#filter2_d_7238_190934)">
        <path
          d="M188 262.4C188 260.16 188 259.04 188.436 258.184C188.819 257.431 189.431 256.819 190.184 256.436C191.04 256 192.16 256 194.4 256H197.6C199.84 256 200.96 256 201.816 256.436C202.569 256.819 203.181 257.431 203.564 258.184C204 259.04 204 260.16 204 262.4V265.6C204 267.84 204 268.96 203.564 269.816C203.181 270.569 202.569 271.181 201.816 271.564C200.96 272 199.84 272 197.6 272H194.4C192.16 272 191.04 272 190.184 271.564C189.431 271.181 188.819 270.569 188.436 269.816C188 268.96 188 267.84 188 265.6V262.4Z"
          fill="url(#paint3_linear_7238_190934)"
        />
        <path
          d="M194.4 272.5H197.6H197.623C198.724 272.5 199.581 272.5 200.268 272.444C200.966 272.387 201.533 272.269 202.043 272.01C202.89 271.578 203.578 270.89 204.01 270.043C204.269 269.533 204.387 268.966 204.444 268.268C204.5 267.581 204.5 266.724 204.5 265.623V265.6V262.4V262.377C204.5 261.276 204.5 260.419 204.444 259.732C204.387 259.034 204.269 258.467 204.01 257.957C203.578 257.11 202.89 256.422 202.043 255.99C201.533 255.731 200.966 255.613 200.268 255.556C199.581 255.5 198.724 255.5 197.623 255.5H197.6H194.4H194.377C193.276 255.5 192.419 255.5 191.732 255.556C191.034 255.613 190.467 255.731 189.957 255.99C189.11 256.422 188.422 257.11 187.99 257.957C187.731 258.467 187.613 259.034 187.556 259.732C187.5 260.419 187.5 261.276 187.5 262.377V262.4V265.6V265.623C187.5 266.724 187.5 267.581 187.556 268.268C187.613 268.966 187.731 269.533 187.99 270.043C188.422 270.89 189.11 271.578 189.957 272.01C190.467 272.269 191.034 272.387 191.732 272.444C192.419 272.5 193.276 272.5 194.377 272.5H194.4Z"
          stroke="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_7238_190934"
          x="154.429"
          y="249.286"
          width="35.1429"
          height="35.1429"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2.85714" />
          <feGaussianBlur stdDeviation="4.28571" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_7238_190934"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_7238_190934"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_7238_190934"
          x="166.429"
          y="249.286"
          width="35.1429"
          height="35.1429"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2.85714" />
          <feGaussianBlur stdDeviation="4.28571" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_7238_190934"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_7238_190934"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_7238_190934"
          x="178.429"
          y="249.286"
          width="35.1429"
          height="35.1429"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2.85714" />
          <feGaussianBlur stdDeviation="4.28571" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_7238_190934"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_7238_190934"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_7238_190934"
          x1="288"
          y1="136"
          x2="288"
          y2="288"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#38415A" />
          <stop offset="1" stopColor="#2B3245" stopOpacity="0.4" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_7238_190934"
          x1="172"
          y1="256"
          x2="172"
          y2="272"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F45D68" />
          <stop offset="1" stopColor="#FFCA00" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_7238_190934"
          x1="184"
          y1="256"
          x2="184"
          y2="272"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5CBCB0" />
          <stop offset="1" stopColor="#FAC818" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_7238_190934"
          x1="196"
          y1="256"
          x2="196"
          y2="272"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5CBCB0" />
          <stop offset="1" stopColor="#133BFE" />
        </linearGradient>
      </defs>
    </svg>
  );
}
