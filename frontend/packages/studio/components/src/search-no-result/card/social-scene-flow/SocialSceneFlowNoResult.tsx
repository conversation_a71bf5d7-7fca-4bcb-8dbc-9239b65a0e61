/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

export function SocialSceneFlowNoResult(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="178"
      height="66"
      viewBox="0 0 178 66"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.261719"
        y="0.61499"
        width="177.474"
        height="64.6513"
        rx="5.07069"
        fill="white"
      />
      <rect
        x="0.578637"
        y="0.931908"
        width="176.84"
        height="64.0175"
        rx="4.75377"
        stroke="#060709"
        strokeOpacity="0.1"
        strokeWidth="0.633837"
      />
      <rect
        x="10.4023"
        y="12.0239"
        width="65.919"
        height="8.87371"
        rx="4.43686"
        fill="#060709"
        fillOpacity="0.3"
      />
      <rect
        x="10.4023"
        y="25.9683"
        width="111.555"
        height="8.87371"
        rx="4.43686"
        fill="#060709"
        fillOpacity="0.3"
      />
      <g filter="url(#filter0_d_3854_22323)">
        <path
          d="M129.564 23.4331C129.564 16.4319 135.24 10.7563 142.241 10.7563H154.918C161.919 10.7563 167.595 16.4319 167.595 23.4331C167.595 30.4342 161.919 36.1098 154.918 36.1098H142.241C135.24 36.1098 129.564 30.4342 129.564 23.4331Z"
          fill="url(#paint0_linear_3854_22323)"
        />
        <path
          d="M129.628 23.4331C129.628 16.4669 135.275 10.8197 142.241 10.8197H154.918C161.884 10.8197 167.531 16.4669 167.531 23.4331C167.531 30.3992 161.884 36.0464 154.918 36.0464H142.241C135.275 36.0464 129.628 30.3992 129.628 23.4331Z"
          stroke="black"
          strokeOpacity="0.08"
          strokeWidth="0.126767"
        />
      </g>
      <rect
        x="10.4023"
        y="46.2512"
        width="157.191"
        height="8.87371"
        rx="4.43686"
        fill="#060709"
        fillOpacity="0.3"
      />
      <defs>
        <filter
          id="filter0_d_3854_22323"
          x="124.132"
          y="7.13442"
          width="48.8951"
          height="36.2193"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1.81096" />
          <feGaussianBlur stdDeviation="2.71644" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3854_22323"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_3854_22323"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_3854_22323"
          x1="148.58"
          y1="10.7563"
          x2="148.58"
          y2="36.1098"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F45D68" />
          <stop offset="1" stopColor="#FFCA00" />
        </linearGradient>
      </defs>
    </svg>
  );
}
