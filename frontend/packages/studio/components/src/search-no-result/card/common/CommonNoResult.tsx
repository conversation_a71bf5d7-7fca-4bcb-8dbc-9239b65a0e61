/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

// eslint-disable-next-line @coze-arch/max-line-per-function
export function CommonNoResult(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="360"
      height="248"
      viewBox="0 0 360 248"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="80" width="280" height="72" rx="8" fill="white" />
      <rect
        x="80.5"
        y="0.5"
        width="279"
        height="71"
        rx="7.5"
        stroke="url(#paint0_linear_4434_146978)"
        stopOpacity="0.24"
      />
      <g filter="url(#filter0_d_4434_146978)">
        <path
          d="M96 28.8C96 24.3196 96 22.0794 96.8719 20.3681C97.6389 18.8628 98.8628 17.6389 100.368 16.8719C102.079 16 104.32 16 108.8 16H123.2C127.68 16 129.921 16 131.632 16.8719C133.137 17.6389 134.361 18.8628 135.128 20.3681C136 22.0794 136 24.3196 136 28.8V43.2C136 47.6804 136 49.9206 135.128 51.6319C134.361 53.1372 133.137 54.3611 131.632 55.1281C129.921 56 127.68 56 123.2 56H108.8C104.32 56 102.079 56 100.368 55.1281C98.8628 54.3611 97.6389 53.1372 96.8719 51.6319C96 49.9206 96 47.6804 96 43.2V28.8Z"
          fill="url(#paint1_linear_4434_146978)"
        />
        <path
          d="M96.1 28.8C96.1 26.5581 96.1001 24.8828 96.2087 23.5538C96.3171 22.2259 96.5335 21.2527 96.961 20.4135C97.7184 18.927 98.927 17.7184 100.413 16.961C101.253 16.5335 102.226 16.3171 103.554 16.2087C104.883 16.1001 106.558 16.1 108.8 16.1H123.2C125.442 16.1 127.117 16.1001 128.446 16.2087C129.774 16.3171 130.747 16.5335 131.587 16.961C133.073 17.7184 134.282 18.927 135.039 20.4135C135.467 21.2527 135.683 22.2259 135.791 23.5538C135.9 24.8828 135.9 26.5581 135.9 28.8V43.2C135.9 45.4419 135.9 47.1172 135.791 48.4462C135.683 49.7741 135.467 50.7473 135.039 51.5865C134.282 53.073 133.073 54.2816 131.587 55.039C130.747 55.4665 129.774 55.6828 128.446 55.7913C127.117 55.8999 125.442 55.9 123.2 55.9H108.8C106.558 55.9 104.883 55.8999 103.554 55.7913C102.226 55.6828 101.253 55.4665 100.413 55.039C98.927 54.2816 97.7184 53.073 96.961 51.5865C96.5335 50.7473 96.3171 49.7741 96.2087 48.4462C96.1001 47.1172 96.1 45.4419 96.1 43.2V28.8Z"
          stopOpacity="0.08"
          strokeWidth="0.2"
        />
      </g>
      <rect
        opacity="0.12"
        x="148"
        y="18"
        width="120"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <rect
        opacity="0.12"
        x="148"
        y="40"
        width="196"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <rect y="88" width="280" height="72" rx="8" fill="white" />
      <rect
        x="0.5"
        y="88.5"
        width="279"
        height="71"
        rx="7.5"
        stroke="url(#paint2_linear_4434_146978)"
        stopOpacity="0.24"
      />
      <g filter="url(#filter1_d_4434_146978)">
        <path
          d="M16 116.8C16 112.32 16 110.079 16.8719 108.368C17.6389 106.863 18.8628 105.639 20.3681 104.872C22.0794 104 24.3196 104 28.8 104H43.2C47.6804 104 49.9206 104 51.6319 104.872C53.1372 105.639 54.3611 106.863 55.1281 108.368C56 110.079 56 112.32 56 116.8V131.2C56 135.68 56 137.921 55.1281 139.632C54.3611 141.137 53.1372 142.361 51.6319 143.128C49.9206 144 47.6804 144 43.2 144H28.8C24.3196 144 22.0794 144 20.3681 143.128C18.8628 142.361 17.6389 141.137 16.8719 139.632C16 137.921 16 135.68 16 131.2V116.8Z"
          fill="url(#paint3_linear_4434_146978)"
        />
        <path
          d="M16.1 116.8C16.1 114.558 16.1001 112.883 16.2087 111.554C16.3171 110.226 16.5335 109.253 16.961 108.413C17.7184 106.927 18.927 105.718 20.4135 104.961C21.2527 104.533 22.2259 104.317 23.5538 104.209C24.8828 104.1 26.5581 104.1 28.8 104.1H43.2C45.4419 104.1 47.1172 104.1 48.4462 104.209C49.7741 104.317 50.7473 104.533 51.5865 104.961C53.073 105.718 54.2816 106.927 55.039 108.413C55.4665 109.253 55.6828 110.226 55.7913 111.554C55.8999 112.883 55.9 114.558 55.9 116.8V131.2C55.9 133.442 55.8999 135.117 55.7913 136.446C55.6828 137.774 55.4665 138.747 55.039 139.587C54.2816 141.073 53.073 142.282 51.5865 143.039C50.7473 143.467 49.7741 143.683 48.4462 143.791C47.1172 143.9 45.4419 143.9 43.2 143.9H28.8C26.5581 143.9 24.8828 143.9 23.5538 143.791C22.2259 143.683 21.2527 143.467 20.4135 143.039C18.927 142.282 17.7184 141.073 16.961 139.587C16.5335 138.747 16.3171 137.774 16.2087 136.446C16.1001 135.117 16.1 133.442 16.1 131.2V116.8Z"
          stopOpacity="0.08"
          strokeWidth="0.2"
        />
      </g>
      <rect
        opacity="0.12"
        x="68"
        y="106"
        width="120"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <rect
        opacity="0.12"
        x="68"
        y="128"
        width="196"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <rect x="80" y="176" width="280" height="72" rx="8" fill="white" />
      <rect
        x="80.5"
        y="176.5"
        width="279"
        height="71"
        rx="7.5"
        stroke="url(#paint4_linear_4434_146978)"
        stopOpacity="0.24"
      />
      <g filter="url(#filter2_d_4434_146978)">
        <path
          d="M96 204.8C96 200.32 96 198.079 96.8719 196.368C97.6389 194.863 98.8628 193.639 100.368 192.872C102.079 192 104.32 192 108.8 192H123.2C127.68 192 129.921 192 131.632 192.872C133.137 193.639 134.361 194.863 135.128 196.368C136 198.079 136 200.32 136 204.8V219.2C136 223.68 136 225.921 135.128 227.632C134.361 229.137 133.137 230.361 131.632 231.128C129.921 232 127.68 232 123.2 232H108.8C104.32 232 102.079 232 100.368 231.128C98.8628 230.361 97.6389 229.137 96.8719 227.632C96 225.921 96 223.68 96 219.2V204.8Z"
          fill="url(#paint5_linear_4434_146978)"
        />
        <path
          d="M96.1 204.8C96.1 202.558 96.1001 200.883 96.2087 199.554C96.3171 198.226 96.5335 197.253 96.961 196.413C97.7184 194.927 98.927 193.718 100.413 192.961C101.253 192.533 102.226 192.317 103.554 192.209C104.883 192.1 106.558 192.1 108.8 192.1H123.2C125.442 192.1 127.117 192.1 128.446 192.209C129.774 192.317 130.747 192.533 131.587 192.961C133.073 193.718 134.282 194.927 135.039 196.413C135.467 197.253 135.683 198.226 135.791 199.554C135.9 200.883 135.9 202.558 135.9 204.8V219.2C135.9 221.442 135.9 223.117 135.791 224.446C135.683 225.774 135.467 226.747 135.039 227.587C134.282 229.073 133.073 230.282 131.587 231.039C130.747 231.467 129.774 231.683 128.446 231.791C127.117 231.9 125.442 231.9 123.2 231.9H108.8C106.558 231.9 104.883 231.9 103.554 231.791C102.226 231.683 101.253 231.467 100.413 231.039C98.927 230.282 97.7184 229.073 96.961 227.587C96.5335 226.747 96.3171 225.774 96.2087 224.446C96.1001 223.117 96.1 221.442 96.1 219.2V204.8Z"
          stopOpacity="0.08"
          strokeWidth="0.2"
        />
      </g>
      <rect
        opacity="0.12"
        x="148"
        y="193"
        width="120"
        height="16"
        rx="8"
        fill="#2B3245"
      />
      <rect
        opacity="0.12"
        x="148"
        y="217"
        width="196"
        height="14"
        rx="7"
        fill="#2B3245"
      />
      <defs>
        <filter
          id="filter0_d_4434_146978"
          x="87.4286"
          y="10.2857"
          width="57.1429"
          height="57.1429"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2.85714" />
          <feGaussianBlur stdDeviation="4.28571" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4434_146978"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4434_146978"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_4434_146978"
          x="7.42857"
          y="98.2857"
          width="57.1429"
          height="57.1429"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2.85714" />
          <feGaussianBlur stdDeviation="4.28571" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4434_146978"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4434_146978"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_4434_146978"
          x="87.4286"
          y="186.286"
          width="57.1429"
          height="57.1429"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2.85714" />
          <feGaussianBlur stdDeviation="4.28571" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4434_146978"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4434_146978"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_4434_146978"
          x1="220"
          y1="0"
          x2="220"
          y2="72"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EBEBED" />
          <stop offset="1" stopColor="#CFDBD7" stopOpacity="0.4" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4434_146978"
          x1="116"
          y1="16"
          x2="116"
          y2="56"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F45D68" />
          <stop offset="1" stopColor="#FFCA00" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_4434_146978"
          x1="140"
          y1="88"
          x2="140"
          y2="160"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EBEBED" />
          <stop offset="1" stopColor="#CFDBD7" stopOpacity="0.4" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_4434_146978"
          x1="36"
          y1="104"
          x2="36"
          y2="144"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F45D68" />
          <stop offset="0.0001" stopColor="#5CBCB0" />
          <stop offset="1" stopColor="#FAC818" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_4434_146978"
          x1="220"
          y1="176"
          x2="220"
          y2="248"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EBEBED" />
          <stop offset="1" stopColor="#CFDBD7" stopOpacity="0.4" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_4434_146978"
          x1="116"
          y1="192"
          x2="116"
          y2="232"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3D8DF3" />
          <stop offset="0.0001" stopColor="#5CBCB0" />
          <stop offset="1" stopColor="#133BFE" />
        </linearGradient>
      </defs>
    </svg>
  );
}
