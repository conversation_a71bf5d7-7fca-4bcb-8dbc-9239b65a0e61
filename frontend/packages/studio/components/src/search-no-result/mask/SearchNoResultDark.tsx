/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import React from 'react';

// eslint-disable-next-line @coze-arch/max-line-per-function
export function SearchNoResultDark(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="576"
      height="288"
      viewBox="0 0 576 288"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g opacity="0.4">
        <mask
          id="mask0_4513_103145"
          maskUnits="userSpaceOnUse"
          x="0"
          y="0"
          width="576"
          height="288"
        >
          <rect
            width="576"
            height="288"
            fill="url(#paint0_linear_4513_103145)"
          />
        </mask>
        <g mask="url(#mask0_4513_103145)">
          <mask
            id="mask1_4513_103145"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="576"
            height="288"
          >
            <rect
              width="576"
              height="288"
              fill="url(#paint1_linear_4513_103145)"
            />
          </mask>
          <g mask="url(#mask1_4513_103145)">
            <rect width="48" height="48" stroke="#3C445C" />
            <rect
              x="48"
              width="48"
              height="48"
              fill="url(#paint2_linear_4513_103145)"
              stroke="#3C445C"
            />
            <rect x="96" width="48" height="48" stroke="#3C445C" />
            <rect x="144" width="48" height="48" stroke="#3C445C" />
            <rect x="192" width="48" height="48" stroke="#3C445C" />
            <rect x="240" width="48" height="48" stroke="#3C445C" />
            <rect x="288" width="48" height="48" stroke="#3C445C" />
            <rect x="336" width="48" height="48" stroke="#3C445C" />
            <rect x="384" width="48" height="48" stroke="#3C445C" />
            <rect x="432" width="48" height="48" stroke="#3C445C" />
            <rect x="480" width="48" height="48" stroke="#3C445C" />
            <rect x="528" width="48" height="48" stroke="#3C445C" />
            <rect y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="48" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="96" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="144" y="48" width="48" height="48" stroke="#3C445C" />
            <rect
              x="192"
              y="48"
              width="48"
              height="48"
              fill="url(#paint3_linear_4513_103145)"
              stroke="#3C445C"
            />
            <rect x="240" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="288" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="336" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="384" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="432" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="480" y="48" width="48" height="48" stroke="#3C445C" />
            <rect x="528" y="48" width="48" height="48" stroke="#3C445C" />
            <rect y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="48" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="96" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="144" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="192" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="240" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="288" y="96" width="48" height="48" stroke="#3C445C" />
            <rect
              x="336"
              y="96"
              width="48"
              height="48"
              fill="url(#paint4_linear_4513_103145)"
              stroke="#3C445C"
            />
            <rect x="384" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="432" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="480" y="96" width="48" height="48" stroke="#3C445C" />
            <rect x="528" y="96" width="48" height="48" stroke="#3C445C" />
            <rect y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="48" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="96" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="144" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="192" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="240" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="288" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="336" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="384" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="432" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="480" y="144" width="48" height="48" stroke="#3C445C" />
            <rect x="528" y="144" width="48" height="48" stroke="#3C445C" />
            <rect y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="48" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="96" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="144" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="192" y="192" width="48" height="48" stroke="#3C445C" />
            <rect
              x="240"
              y="192"
              width="48"
              height="48"
              fill="url(#paint5_linear_4513_103145)"
              stroke="#3C445C"
            />
            <rect x="288" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="336" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="384" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="432" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="480" y="192" width="48" height="48" stroke="#3C445C" />
            <rect x="528" y="192" width="48" height="48" stroke="#3C445C" />
            <rect y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="48" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="96" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="144" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="192" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="240" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="288" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="336" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="384" y="240" width="48" height="48" stroke="#3C445C" />
            <rect x="432" y="240" width="48" height="48" stroke="#3C445C" />
            <rect
              x="480"
              y="240"
              width="48"
              height="48"
              fill="url(#paint6_linear_4513_103145)"
              stroke="#3C445C"
            />
            <rect x="528" y="240" width="48" height="48" stroke="#3C445C" />
          </g>
        </g>
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_4513_103145"
          x1="576"
          y1="144"
          x2="-3.75509e-06"
          y2="144"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D9D9D9" stopOpacity="0" />
          <stop offset="0.500048" stopColor="#D9D9D9" />
          <stop offset="1" stopColor="#D9D9D9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4513_103145"
          x1="288"
          y1="0"
          x2="288"
          y2="288"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D9D9D9" stopOpacity="0" />
          <stop offset="0.493585" stopColor="#D9D9D9" />
          <stop offset="1" stopColor="#D9D9D9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_4513_103145"
          x1="72"
          y1="0"
          x2="72"
          y2="48"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3C445C" stopOpacity="0" />
          <stop offset="1" stopColor="#3C445C" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_4513_103145"
          x1="216"
          y1="48"
          x2="216"
          y2="96"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3C445C" stopOpacity="0" />
          <stop offset="1" stopColor="#3C445C" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_4513_103145"
          x1="360"
          y1="96"
          x2="360"
          y2="144"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3C445C" stopOpacity="0" />
          <stop offset="1" stopColor="#3C445C" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_4513_103145"
          x1="264"
          y1="192"
          x2="264"
          y2="240"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3C445C" stopOpacity="0" />
          <stop offset="1" stopColor="#3C445C" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_4513_103145"
          x1="504"
          y1="240"
          x2="504"
          y2="288"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3C445C" stopOpacity="0" />
          <stop offset="1" stopColor="#3C445C" />
        </linearGradient>
      </defs>
    </svg>
  );
}
