.ctn {
  display: flex;
  width: 100%;

  .image-ctn {
    line-height: 0;

    .show-image img,
    .empty-image {
      width: 108px;
      height: 108px;
      object-fit: cover;
      border-radius: 8px;
    }

    .empty-image {
      display: flex;
      align-items: center;
      justify-content: center;

      font-size: 20px;
      color: var(--coz-fg-secondary, #06070980);

      background-color: var(--coz-mg-primary, #0607090A);
    }
  }

  .text-ctn {
    position: relative;
    flex: 1 0 0;
    height: 108px;
    margin-left: 16px;

    :global {
      .semi-input-textarea-wrapper {
        padding: 0;
        background-color: transparent;
        border: none;

        .semi-input-textarea {
          height: 108px;
          padding: 0;
          font-size: 14px;
          line-height: 20px;
        }

        .semi-input-textarea::-webkit-scrollbar {
          display: none;
        }
      }
    }

    .generate-btn {
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
}

.upload-panel {
  padding: 16px;

  background: var(--coz-bg-max, #FFF);
  border: 0.5px solid var(--coz-stroke-primary, rgba(6, 7, 9, 10%));
  border-radius: 8px;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 25%);

  .image-list {
    margin-bottom: 12px;

    .image-item {
      width: 40px;
      height: 40px;
    }
  }

  :global {
    .semi-button-content-right {
      margin-left: 4px;
      line-height: 20px;
    }
  }
}
