.container {
  flex-shrink: 0;
  max-width: 100%;
  height: 18px;

  .avatar {
    overflow: hidden;
    border-radius: 0;
    border-radius: 12px;

    img {
      vertical-align: top;
    }
  }

  .label-icon {
    cursor: pointer;
    width: 12px;
    height: 12px;
    border-radius: 0;
  }


  .txt {
    flex: 1;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;

    @apply coz-fg-dim;

    &.name {
      @apply coz-fg-secondary;
    }


  }

  &.light {
    .txt {
      color: rgba(255, 255, 255, 39%);

      &.name {
        color: rgba(255, 255, 255, 79%);
      }
    }
  }

  &.white {
    .txt {
      color: #FFF;

      &.name {
        color: #FFF;
      }
    }
  }

  &.middle {
    height: 20px;

    .txt {
      font-size: 14px;
      line-height: 20px;

      &.username {
        margin-left: 4px;
      }
    }
  }

  &.large {
    height: 20px;

    .label-icon {
      width: 14px;
      height: 14px;
    }

    .txt {
      font-size: 14px;
    }
  }
}
