.markdown-editor {
  position: relative;

  overflow: hidden;
  display: flex;
  flex-direction: column;

  padding: 6px 12px;

  background-color: #fff;
  // rgb mark
  border: 1px solid;
  border-color: rgb(6 7 9 / 10%);
  border-radius: 8px;

  &.markdown-editor-drag {
    box-shadow: 0 0 0 2px #DADCFB;
  }

  &:focus-within {
    border-color: #34F;
  }

  .markdown-action-bar {
    box-sizing: border-box;
    width: 100%;
    height: 40px;
    margin-bottom: 8px;
    padding: 8px 0;

    border-bottom: 1px solid rgb(6 7 9 / 10%);
  }

  .markdown-editor-content {
    resize: none;

    width: 100%;
    height: 240px;
    padding: 0;


    font-size: 14px;
    font-weight: 400;
    line-height: 22px;

    // rgb mark
    color: #383743;

    border: none;
    outline: none;

    &::selection {
      background: rgb(77 83 232 / 20%);
    }
  }
}
