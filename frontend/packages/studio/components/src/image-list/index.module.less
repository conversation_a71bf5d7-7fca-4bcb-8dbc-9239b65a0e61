.ctn {
  display: flex;
  flex-wrap: nowrap;
  gap: 7px;
  justify-content: center;

  .image-item {
    cursor: pointer;

    position: relative;

    width: 60px;
    height: 60px;

    border-radius: 8px;

    img {
      width: 100%;
      height: 100%;

      object-fit: cover;
      border: 1.5px solid transparent;
      border-radius: 8px;

      &.selected {
        border: 1.5px solid var(--coz-stroke-hglt, #4E40E5);
      }
    }

    .delete-icon {
      position: absolute;
      top: -4px;
      right: -4px;

      display: none;

      font-size: 16px;
      color: var(--coz-fg-hglt-red, #F54A45);
    }

    &:hover .delete-icon {
      display: block;
    }


    .check-icon {
      position: absolute;
      right: 4px;
      bottom: 4px;

      display: flex;
      align-items: center;
      justify-content: center;

      width: 16px;
      height: 16px;

      background-color: var(--coz-mg-hglt-plus, #4E40E5);
      border-radius: 4px;

      svg {
        font-size: 12px;
        color: white;
      }
    }
  }
}
