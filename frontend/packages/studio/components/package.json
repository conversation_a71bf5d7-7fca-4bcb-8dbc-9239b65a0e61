{"name": "@coze-studio/components", "version": "0.0.1", "description": "biz components extract from apps/bot/src/components", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.ts", "./coze-brand": "./src/coze-brand/index.tsx", "./search-no-result": "./src/search-no-result/index.tsx", "./sortable-list": "./src/sortable-list/index.tsx", "./dnd-provider": "./src/dnd-provider/index.tsx", "./sortable-list-hooks": "./src/sortable-list/hooks.ts", "./generate-gif": "./src/generate-gif/index.tsx", "./markdown-editor": "./src/markdown-editor/index.tsx", "./parameters-popover": "./src/plugins/parameters-popover/index.tsx", "./collapsible-role-list": "./src/social-scene/collapsible-role-list/index.tsx", "./monetize": "./src/monetize/index.ts", "./collapsible-icon-button": "./src/collapsible-icon-button/index.tsx", "./table-select-all-popover": "./src/table-select-all-popover/index.tsx"}, "main": "src/index.ts", "typesVersions": {"*": {"coze-brand": ["./src/coze-brand/index.tsx"], "search-no-result": ["./src/search-no-result/index.tsx"], "sortable-list": ["./src/sortable-list/index.tsx"], "dnd-provider": ["./src/dnd-provider/index.tsx"], "sortable-list-hooks": ["./src/sortable-list/hooks.ts"], "generate-gif": ["./src/generate-gif/index.tsx"], "markdown-editor": ["./src/markdown-editor/index.tsx"], "parameters-popover": ["./src/plugins/parameters-popover/index.tsx"], "collapsible-role-list": ["./src/social-scene/collapsible-role-list/index.tsx"], "monetize": ["./src/monetize/index.ts"], "collapsible-icon-button": ["./src/collapsible-icon-button/index.tsx"], "table-select-all-popover": ["./src/table-select-all-popover/index.tsx"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "exit 0"}, "dependencies": {"@blueprintjs/core": "^5.1.5", "@coze-agent-ide/bot-input-length-limit": "workspace:*", "@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-error": "workspace:*", "@coze-arch/bot-flags": "workspace:*", "@coze-arch/bot-hooks": "workspace:*", "@coze-arch/bot-icons": "workspace:*", "@coze-arch/bot-md-box-adapter": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/bot-studio-store": "workspace:*", "@coze-arch/bot-tea": "workspace:*", "@coze-arch/bot-utils": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "@coze-arch/web-context": "workspace:*", "@coze-common/assets": "workspace:*", "@coze-common/biz-components": "workspace:*", "@coze-common/websocket-manager-adapter": "workspace:*", "@coze-data/e2e": "workspace:*", "@coze-foundation/space-store": "workspace:*", "@coze-studio/bot-utils": "workspace:*", "@douyinfe/semi-icons": "^2.36.0", "ahooks": "^3.7.8", "axios": "^1.4.0", "classnames": "^2.3.2", "dayjs": "^1.11.7", "immer": "^10.0.3", "lodash-es": "^4.17.21", "react-dnd": "16.0.1", "react-dnd-html5-backend": "16.0.1", "react-markdown": "^8.0.3", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/bot-space-api": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-studio/bot-detail-store": "workspace:*", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "debug": "^4.3.4", "nanoid": "^4.0.2", "react": "~18.2.0", "react-dom": "~18.2.0", "react-router-dom": "^6.22.0", "stylelint": "^15.11.0", "use-event-callback": "~0.1.0", "utility-types": "^3.10.0", "vite": "^4.3.9", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0", "utility-types": "^3.10.0"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}