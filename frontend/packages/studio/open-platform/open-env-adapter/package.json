{"name": "@coze-studio/open-env-adapter", "version": "0.0.1", "description": "Coze Web ChatApp SDK ", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": ["<EMAIL>"], "sideEffects": false, "exports": {".": "./src/index.ts", "./chat": "./src/chat/index.ts"}, "main": "src/index.ts", "types": "./src/index.ts", "typesVersions": {"*": {"chat": ["./src/chat/index.ts"]}}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "vitest run --coverage"}, "dependencies": {}, "devDependencies": {"@coze-arch/bot-env": "workspace:*", "@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/pkg-root-webpack-plugin": "workspace:*", "@coze-arch/postcss-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/tailwind-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@rspack/plugin-react-refresh": "0.6.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "concurrently": "~8.2.2", "css-loader": "^6.10.0", "debug": "^4.3.4", "style-loader": "^3.3.4", "tailwindcss": "~3.3.3", "ts-node": "^10.9.1", "typescript": "~5.8.2", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "// deps": "debug@^4.3.4 为脚本自动补齐，请勿改动", "botPublishConfig": {"main": "dist/index.js"}}