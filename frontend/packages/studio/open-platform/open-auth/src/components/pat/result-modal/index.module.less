.result-frame {
  :global {
    .semi-modal-body {
      // stylelint-disable-next-line declaration-no-important
      padding: 0 !important;
    }
  }

  .warn-text {
    margin-bottom: 32px;

    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: var(--Light-usage-text---color-text-0, #1d1c23);
  }

  .title-text {
    margin-bottom: 8px;

    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    color: var(--Light-usage-text---color-text-0, #1d1c23);
  }

  .para {
    overflow: hidden;

    max-width: 90%;
    margin-bottom: 36px;

    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: var(--Light-usage-text---color-text-0, #1d1c23);
    text-overflow: ellipsis;
  }

  .key-text {
    overflow: hidden;

    max-width: 487px;

    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: var(--Light-usage-text---color-text-0, #1d1c23);
    text-overflow: ellipsis;
  }

  .sp {
    width: 100%;
    margin-bottom: 44px;

    .icon {
      cursor: pointer;
      width: 16px;
      height: 16px;

      svg {
        color: rgba(77, 83, 232, 100%);
      }
    }
  }
}
