/* stylelint-disable declaration-no-important */
.table-container {
  overflow: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar-thumb {
    background: var(--coz-fg-dim, rgba(6, 7, 9, 30%));
    border-radius: 3px;
  }

  &::-webkit-scrollbar {
    display: block !important;
    width: 4px;
    background-color: transparent;
  }

  &::-webkit-scrollbar:hover {
    width: 4px;
  }

  &::-webkit-scrollbar-button {
    display: none;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}
