{"name": "@coze-studio/open-auth", "version": "0.0.1", "description": "bot open platform", "license": "Apache-2.0", "author": "<EMAIL>", "maintainers": [], "exports": {".": "./src/index.tsx"}, "main": "src/index.tsx", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@coze-arch/bot-api": "workspace:*", "@coze-arch/bot-semi": "workspace:*", "@coze-arch/coze-design": "0.0.6-alpha.346d77", "@coze-arch/i18n": "workspace:*", "@coze-arch/logger": "workspace:*", "@coze-arch/report-events": "workspace:*", "ahooks": "^3.7.8", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.7", "lodash-es": "^4.17.21"}, "devDependencies": {"@coze-arch/bot-typings": "workspace:*", "@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@rsbuild/core": "1.1.13", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/lodash-es": "^4.17.10", "@types/node": "18.18.9", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-helmet": "^6.1.11", "@vitest/coverage-v8": "~3.0.5", "axios": "^1.7.1", "core-js": "^3.37.1", "eventemitter3": "^5.0.1", "less": "^3.13.1", "less-loader": "^7.1.0", "less-plugin-autoprefix": "^2.0.0", "react": "~18.2.0", "react-dom": "~18.2.0", "react-helmet": "^6.1.0", "react-is": ">= 16.8.0", "react-router-dom": "^6.22.0", "scheduler": ">=0.19.0", "styled-components": ">= 2", "stylelint": "^15.11.0", "typescript": "5.7.2", "utility-types": "^3.10.0", "vitest": "~3.0.5", "webpack": "~5.91.0"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}, "// deps": "immer@^10.0.3 为脚本自动补齐，请勿改动"}