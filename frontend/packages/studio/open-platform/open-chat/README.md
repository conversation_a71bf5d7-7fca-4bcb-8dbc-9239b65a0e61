# @coze-studio/open-chat

Coze Web ChatApp SDK

## Overview

This package is part of the Coze Studio monorepo and provides chat & communication functionality. It includes component.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-studio/open-chat": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-studio/open-chat';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Component

## API Reference

### Exports

- `BuilderChat,
  ChatType,
  RawMessageType,`
- `Layout`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- React
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
