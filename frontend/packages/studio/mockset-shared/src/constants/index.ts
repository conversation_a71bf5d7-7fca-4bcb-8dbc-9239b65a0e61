/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
export const FORMAT_SPACE_SETTING = 4;
export const MAX_SUBMIT_LENGTH = 102400;
export const RANDOM_BOOL_THRESHOLD = 0.5;
export const STRING_DISPLAY_PREFIX = '"';
export const STRING_DISPLAY_SUFFIX = '"';
export const RANDOM_SEQUENCE_LENGTH = 10;
export const ROOT_KEY = 'mock';
export const MOCK_SET_ERR_CODE = {
  REPEAT_NAME: 600303100,
};
