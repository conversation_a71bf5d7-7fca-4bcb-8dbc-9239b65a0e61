# @coze-arch/rush-logger

rush logger

## Overview

This package is part of the Coze Studio monorepo and provides utilities functionality. It includes logger.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/rush-logger": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/rush-logger';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Logger

## API Reference

### Exports

- `logger ;`
- `default logger;`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
