# @coze-arch/idl2ts-runtime

A architecture package for the Coze Studio monorepo

## Overview

This package is part of the Coze Studio monorepo and provides architecture functionality. It includes api.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/idl2ts-runtime": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/idl2ts-runtime';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Api

## API Reference

### Exports

- `*`
- `*`
- `type IMeta`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
