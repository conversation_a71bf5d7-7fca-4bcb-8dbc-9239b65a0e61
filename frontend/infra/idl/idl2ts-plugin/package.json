{"name": "@coze-arch/idl2ts-plugin", "version": "0.1.6", "description": "@coze-arch/idl2ts-plugin", "license": "Apache-2.0", "author": "<EMAIL>", "main": "src/index.ts", "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@vitest/coverage-v8": "~3.0.5", "tsx": "^4.19.2", "vitest": "~3.0.5"}}