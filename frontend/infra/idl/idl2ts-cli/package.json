{"name": "@coze-arch/idl2ts-cli", "version": "0.1.7", "description": "@coze-arch/idl2ts-cli", "homepage": "", "license": "Apache-2.0", "author": "<EMAIL>", "bin": {"idl2ts": "./src/cli.js"}, "scripts": {"build": "exit 0", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"@babel/types": "^7.20.7", "@coze-arch/idl2ts-generator": "workspace:*", "@coze-arch/idl2ts-helper": "workspace:*", "@coze-arch/idl2ts-plugin": "workspace:*", "@faker-js/faker": "~9.3.0", "commander": "^12.0.0", "dayjs": "^1.11.7", "ora": "^5.3.1", "prettier": "~3.3.3"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@types/node": "^18", "@vitest/coverage-v8": "~3.0.5", "sucrase": "^3.32.0", "tsx": "^4.19.2", "vitest": "~3.0.5"}}