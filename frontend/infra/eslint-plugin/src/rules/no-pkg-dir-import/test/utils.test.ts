/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 
import { exportPathMatch } from '../utils';

describe('exportPathMatch', () => {
  it.each([
    ['./foo', './foo'],
    ['./foo.js', './*'],
    ['./foo.js', './*.js'],
    ['./foo/baz', './foo/*'],
    ['./foo/baz/baz.js', './foo/*'],
  ])(
    'import path is %s, export path is %s, should be matched',
    (importPath, exportPath) => {
      expect(exportPathMatch(importPath, exportPath)).toBe(true);
    },
  );

  it.each([
    ['./foo', './bar'],
    ['./foo.js', './*.ts'],
    ['./foo.js', './foo.ts'],
    ['./baz/bar', './foo/*'],
    ['./foo/bar/baz.js', './foo/*.js'],
  ])(
    'import path is %s, export path is %s, should NOT be matched',
    (importPath, exportPath) => {
      expect(exportPathMatch(importPath, exportPath)).toBe(false);
    },
  );
});
