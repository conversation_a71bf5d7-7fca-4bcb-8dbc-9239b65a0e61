# @coze-arch/pkg-root-webpack-plugin

> 用于支持 `@` 根目录引用的插件

## Overview

This package is part of the Coze Studio monorepo and provides architecture functionality. It includes hook, plugin.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/pkg-root-webpack-plugin": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/pkg-root-webpack-plugin';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Hook
- Plugin

## API Reference

### Exports

- `default PkgRootWebpackPlugin;`
- `PkgRootWebpackPlugin ;`


For detailed API documentation, please refer to the TypeScript definitions.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
