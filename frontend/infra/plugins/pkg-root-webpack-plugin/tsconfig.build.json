{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@coze-arch/ts-config/tsconfig.node.json", "compilerOptions": {"sourceMap": false, "types": ["vitest/globals", "node"], "strictNullChecks": true, "outDir": "lib", "rootDir": "src", "tsBuildInfoFile": "lib/tsconfig.build.tsbuildinfo"}, "include": ["./src"], "references": [{"path": "../../../config/eslint-config/tsconfig.build.json"}, {"path": "../../../config/ts-config/tsconfig.build.json"}, {"path": "../../../config/vitest-config/tsconfig.build.json"}]}