# @coze-arch/import-watch-loader

A architecture package for the Coze Studio monorepo

## Overview

This package is part of the Coze Studio monorepo and provides architecture functionality. It serves as a core component in the Coze ecosystem.

## Getting Started

### Installation

Add this package to your `package.json`:

```json
{
  "dependencies": {
    "@coze-arch/import-watch-loader": "workspace:*"
  }
}
```

Then run:

```bash
rush update
```

### Usage

```typescript
import { /* exported functions/components */ } from '@coze-arch/import-watch-loader';

// Example usage
// TODO: Add specific usage examples
```

## Features

- Core functionality for Coze Studio
- TypeScript support
- Modern ES modules

## API Reference

Please refer to the TypeScript definitions for detailed API documentation.

## Development

This package is built with:

- TypeScript
- Modern JavaScript
- Vitest for testing
- ESLint for code quality

## Contributing

This package is part of the Coze Studio monorepo. Please follow the monorepo contribution guidelines.

## License

Apache-2.0
