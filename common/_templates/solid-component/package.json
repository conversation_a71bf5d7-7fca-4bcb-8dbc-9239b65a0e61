{"name": "{{ packageName }}", "version": "0.0.1", "description": "{{ description }}", "license": "Apache-2.0", "author": "{{ authorName }}", "maintainers": [], "main": "src/index.ts", "unpkg": "./dist/umd/index.js", "module": "./dist/esm/index.js", "types": "./src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "rm -rf ./dist && NODE_ENV=production rollup --config rollup.config.mjs", "build-storybook": "storybook build", "dev": "storybook dev -p 6006", "lint": "eslint ./ --cache", "test": "vitest --run --passWithNoTests", "test:cov": "npm run test -- --coverage"}, "dependencies": {"classnames": "^2.3.2"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-json": "~6.0.0", "@rollup/plugin-node-resolve": "~15.0.1", "@rollup/plugin-replace": "^4.0.0", "@storybook/addon-essentials": "^7.6.7", "@storybook/addon-interactions": "^7.6.7", "@storybook/addon-links": "^7.6.7", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.7", "@storybook/react": "^7.6.7", "@storybook/react-vite": "^7.6.7", "@storybook/test": "^7.6.7", "@swc/core": "^1.3.35", "@swc/helpers": "^0.4.12", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/react-hooks": "^8.0.1", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.16", "less-loader": "~11.1.3", "postcss": "^8.4.32", "react": "~18.2.0", "react-dom": "~18.2.0", "rollup": "^4.9.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-node-externals": "^6.1.2", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-ts": "^3.1.1", "storybook": "^7.6.7", "stylelint": "^15.11.0", "tailwindcss": "~3.3.3", "vite-plugin-svgr": "~3.3.0", "vitest": "~3.0.5"}, "peerDependencies": {"react": ">=18.2.0", "react-dom": ">=18.2.0"}}