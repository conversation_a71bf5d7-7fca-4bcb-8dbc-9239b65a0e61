{"name": "{{ packageName }}", "version": "0.0.1", "private": true, "description": "{{ description }}", "keywords": [], "license": "Apache-2.0", "author": "{{ authorName }}", "maintainers": [], "sideEffects": false, "scripts": {"build": "rsbuild build", "dev": "rsbuild dev", "lint": "eslint ./ --cache --quiet", "preview": "rsbuild preview", "test": "vitest --run --passWithNoTests", "test:cov": "vitest --run --passWithNoTests --coverage"}, "dependencies": {"@coze-arch/logger": "workspace:*", "react": "~18.2.0", "react-dom": "~18.2.0", "react-error-boundary": "^4.0.9", "react-router-dom": "^6.11.1", "zustand": "^4.4.7"}, "devDependencies": {"@coze-arch/eslint-config": "workspace:*", "@coze-arch/ts-config": "workspace:*", "@coze-arch/vitest-config": "workspace:*", "@coze-arch/stylelint-config": "workspace:*", "@rsbuild/core": "^0.2.18", "@rsbuild/plugin-react": "^0.2.18", "@slardar/web": "1.7.0", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "tailwindcss": "~3.3.3", "vitest": "~3.0.5"}}